package bill

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

// BillOptions contains options for bill generation
type BillOptions struct {
	BillType      string `json:"bill_type"`
	GenerateBill  bool   `json:"generate_bill"`
	GenerateLabel bool   `json:"generate_label"`
}

// ChannelOptions contains options for sending bills to channels
type ChannelOptions struct {
	BillTemplate  string `json:"bill_template"`
	LabelTemplate string `json:"label_template"`
	ZaloMessage   string `json:"zalo_message"`
	Zalo          bool   `json:"zalo"`
	UrgentMessage bool   `json:"urgent_message"`
	Bill          bool   `json:"bill"`
	Labels        bool   `json:"labels"`
}

// MapBillToOrderField maps bill types to order fields
var MapBillToOrderField = map[string]string{
	"bill_for_kitchen":      "bill_url",
	"bill_for_payment":      "bill_for_payment_url",
	"bill_for_complete":     "bill_for_complete_url",
	"bill_for_complete_app": "bill_for_complete_app_url",
	"bill_for_cancel":       "bill_for_cancel_url",
	"bill_for_label":        "", // This is handled separately with label_urls
}

// BillTemplateData represents the data structure for bill templates
type BillTemplateData struct {
	BillName           string     `json:"bill_name"`
	LongOrderID        string     `json:"long_order_id"`
	ShortOrderID       string     `json:"short_order_id"`
	SiteName           string     `json:"site_name"`
	BrandName          string     `json:"brand_name"`
	OrderSource        string     `json:"order_source"`
	HubName            string     `json:"hub_name"`
	HubPhone           string     `json:"hub_phone"`
	OrderCreatedAt     string     `json:"order_created_at"`
	OrderPickAt        string     `json:"order_pick_at"`
	OrderDeliveryAt    string     `json:"order_delivery_at"`
	CustomerName       string     `json:"customer_name"`
	CustomerAddress    string     `json:"customer_address"`
	OrderSubTotal      string     `json:"order_sub_total"`
	OrderTotalDiscount string     `json:"order_total_discount"`
	OrderTotalPaid     string     `json:"order_total_paid"`
	OrderNote          string     `json:"order_note"`
	CancelReason       string     `json:"cancel_reason"`
	Dishes             []DishData `json:"dishes"`
}

// DishData represents dish information for templates
type DishData struct {
	Name          string `json:"name"`
	Description   string `json:"description"`
	Quantity      string `json:"quantity"`
	Price         string `json:"price"`
	DiscountPrice string `json:"discount_price"`
	FinalPrice    string `json:"final_price"`
	Note          string `json:"note"`
	Options       string `json:"options"`
}

// LabelTemplateData represents the data structure for label templates
type LabelTemplateData struct {
	BillName           string `json:"bill_name"`
	LongOrderID        string `json:"long_order_id"`
	ShortOrderID       string `json:"short_order_id"`
	SiteName           string `json:"site_name"`
	HubName            string `json:"hub_name"`
	HubPhone           string `json:"hub_phone"`
	OrderSource        string `json:"order_source"`
	OrderCreatedAt     string `json:"order_created_at"`
	CustomerName       string `json:"customer_name"`
	CustomerAddress    string `json:"customer_address"`
	DishName           string `json:"dish_name"`
	DishDescription    string `json:"dish_description"`
	DishQuantity       string `json:"dish_quantity"`
	DishPrice          string `json:"dish_price"`
	DishDiscountPrice  string `json:"dish_discount_price"`
	DishNote           string `json:"dish_note"`
	DishOptions        string `json:"dish_options"`
	LabelIndex         string `json:"label_index"`
	OrderNote          string `json:"order_note"`
	OrderSubTotal      string `json:"order_sub_total"`
	OrderTotalDiscount string `json:"order_total_discount"`
	OrderTotalPaid     string `json:"order_total_paid"`
}

// RenderBillHTML renders the bill HTML for a given order and bill type using templates
func RenderBillHTML(db *gorm.DB, orderID string, billType string) (string, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return "", err
	}

	// Find site and brand
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return "", err
	}

	var brand models.Brand
	if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		return "", err
	}

	// Find hub
	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return "", err
	}

	// Load template file
	templatePath := filepath.Join("share_files", "bill_templates", billType+".html")
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		templatePath = filepath.Join("..", "share_files", "bill_templates", billType+".html")
		templateContent, err = os.ReadFile(templatePath)
		if err != nil {
			return "", fmt.Errorf("failed to read template file %s: %v", templatePath, err)
		}
	}

	// Get order data mapping using gjson for easier access
	dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
	if err != nil {
		return "", err
	}
	dataMappingJSON := gjson.ParseBytes(dataMappingBytes)

	// Get merchant info
	merchantInfo := map[string]map[string]string{
		"grab":         {"label": "Grab Food"},
		"grab_mart":    {"label": "Grab Mart"},
		"shopee":       {"label": "Shopee Food"},
		"shopee_ecom":  {"label": "Shopee Ecom"},
		"shopee_fresh": {"label": "Shopee Fresh"},
		"gojek":        {"label": "Gojek"},
		"be":           {"label": "Be"},
		"local":        {"label": "Tạo trên NexPos"},
		"he":           {"label": "Đại lý cá nhân"},
		"lazada":       {"label": "Lazada"},
		"tiktok":       {"label": "Tiktok"},
	}

	// Get order source label
	orderSourceLabel := order.Source
	if sourceInfo, ok := merchantInfo[order.Source]; ok {
		orderSourceLabel = sourceInfo["label"]
	}

	// Create template data
	templateData := BillTemplateData{
		BillName:        billType, // Use bill type as default name
		LongOrderID:     order.OrderID,
		ShortOrderID:    dataMappingJSON.Get("order_id").String(),
		SiteName:        site.Name,
		BrandName:       brand.Name,
		OrderSource:     orderSourceLabel,
		HubName:         hub.Name,
		HubPhone:        hub.Phone,
		OrderNote:       dataMappingJSON.Get("note").String(),
		CustomerName:    dataMappingJSON.Get("customer_name").String(),
		CustomerAddress: dataMappingJSON.Get("customer_address").String(),
		CancelReason:    dataMappingJSON.Get("cancel_reason").String(),
	}

	// Format dates
	if orderTime := dataMappingJSON.Get("order_time").String(); orderTime != "" {
		if orderTimeFormatted, err := time.Parse(time.RFC3339, orderTime); err == nil {
			templateData.OrderCreatedAt = orderTimeFormatted.Format("02/01/2006 15:04:05")
		} else {
			templateData.OrderCreatedAt = time.Now().Format("02/01/2006 15:04:05")
		}
	} else {
		templateData.OrderCreatedAt = time.Now().Format("02/01/2006 15:04:05")
	}

	if pickTime := dataMappingJSON.Get("pick_time").String(); pickTime != "" {
		if pickTimeFormatted, err := time.Parse(time.RFC3339, pickTime); err == nil {
			templateData.OrderPickAt = pickTimeFormatted.Format("02/01/2006 15:04:05")
		}
	}

	// Handle delivery time
	shipmentBytes, err := json.Marshal(order.Shipment.Data)
	if err == nil {
		var shipment map[string]interface{}
		if err := json.Unmarshal(shipmentBytes, &shipment); err == nil {
			if schedule, ok := shipment["schedule"].(map[string]interface{}); ok {
				if fromTime, ok := schedule["from_time"].(string); ok && fromTime != "" {
					if fromDateTime, ok := schedule["from_date_time"].(string); ok {
						fromDateTimeFormatted, _ := time.Parse(time.RFC3339, fromDateTime)
						fromStr := fromDateTimeFormatted.Format("02/01/2006 15:04")

						if toDateTime, ok := schedule["to_date_time"].(string); ok {
							toDateTimeFormatted, _ := time.Parse(time.RFC3339, toDateTime)
							toStr := toDateTimeFormatted.Format("15:04")
							templateData.OrderDeliveryAt = fromStr + " - " + toStr
						} else {
							templateData.OrderDeliveryAt = fromStr
						}
					}
				}
			}
		}
	}

	if templateData.OrderDeliveryAt == "" {
		if deliveryTime := dataMappingJSON.Get("delivery_time").String(); deliveryTime != "" {
			if deliveryTimeFormatted, err := time.Parse(time.RFC3339, deliveryTime); err == nil {
				templateData.OrderDeliveryAt = deliveryTimeFormatted.Format("02/01/2006 15:04")
			}
		}
	}

	// Order totals
	if total := dataMappingJSON.Get("total").Float(); total > 0 {
		templateData.OrderSubTotal = FormatCurrency(total)
	}

	if totalDiscount := dataMappingJSON.Get("total_discount").Float(); totalDiscount > 0 {
		templateData.OrderTotalDiscount = FormatCurrency(totalDiscount)
	}

	if totalForBiz := dataMappingJSON.Get("total_for_biz").Float(); totalForBiz > 0 {
		templateData.OrderTotalPaid = FormatCurrency(totalForBiz)
	}

	// Process dishes if available using gjson
	dishesJSON := dataMappingJSON.Get("dishes")
	if dishesJSON.Exists() && dishesJSON.IsArray() {
		var dishes []DishData

		dishesJSON.ForEach(func(key, dish gjson.Result) bool {
			// Get dish properties using gjson
			dishName := dish.Get("name").String()
			dishDescription := dish.Get("description").String()
			dishQuantity := dish.Get("quantity").Float()
			dishPrice := dish.Get("price").Float()
			dishDiscountPrice := dish.Get("discount_price").Float()
			dishNote := dish.Get("note").String()

			// Calculate totals
			dishTotal := dishPrice * dishQuantity
			dishFinalPrice := dishDiscountPrice * dishQuantity
			if dishDiscountPrice == 0 {
				dishFinalPrice = dishTotal
			}

			// Process dish options
			var optionsHTML strings.Builder
			optionsJSON := dish.Get("options")
			if optionsJSON.Exists() && optionsJSON.IsArray() {
				// Group options by name using gjson
				groupedOptions := make(map[string][]gjson.Result)

				optionsJSON.ForEach(func(groupKey, optGroup gjson.Result) bool {
					if optGroup.IsArray() {
						optGroup.ForEach(func(optKey, option gjson.Result) bool {
							optionName := option.Get("option_name").String()
							if optionName == "" {
								return true // continue
							}

							if _, ok := groupedOptions[optionName]; !ok {
								groupedOptions[optionName] = []gjson.Result{}
							}

							groupedOptions[optionName] = append(groupedOptions[optionName], option)
							return true // continue
						})
					}
					return true // continue
				})

				// Generate plain text for options
				for groupName, groupOptions := range groupedOptions {
					optionsHTML.WriteString(fmt.Sprintf("%s\n", groupName))

					for _, opt := range groupOptions {
						optionItem := opt.Get("option_item").String()
						optionQuantity := opt.Get("quantity").Float()
						optionPrice := opt.Get("price").Float()

						priceStr := ""
						if optionPrice > 0 {
							priceStr = " - " + FormatCurrency(optionPrice)
						}

						optionsHTML.WriteString(fmt.Sprintf(
							"- %.0fx %s%s\n",
							optionQuantity, optionItem, priceStr))
					}
				}
			}

			// Create dish data
			dishData := DishData{
				Name:          dishName,
				Description:   dishDescription,
				Quantity:      fmt.Sprintf("%.0f", dishQuantity),
				Price:         FormatCurrency(dishPrice),
				DiscountPrice: FormatCurrency(dishDiscountPrice),
				FinalPrice:    FormatCurrency(dishFinalPrice),
				Note:          dishNote,
				Options:       optionsHTML.String(),
			}

			dishes = append(dishes, dishData)
			return true // continue iteration
		})

		templateData.Dishes = dishes
	}

	// Parse and execute template
	tmpl, err := template.New("bill").Parse(string(templateContent))
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %v", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return "", fmt.Errorf("failed to execute template: %v", err)
	}

	return buf.String(), nil
}

// RenderLabelHTML renders the label HTML for a given order, dish, and label index
func RenderLabelHTML(db *gorm.DB, orderID string, dishIndex int, labelIndex int, size string) (string, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return "", err
	}

	// Find site and hub
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return "", err
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return "", err
	}

	// Load template file
	templatePath := filepath.Join("golang-services", "share_files", "bill_templates", "bill_for_label.html")
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		return "", fmt.Errorf("failed to read template file %s: %v", templatePath, err)
	}

	// Get order data mapping using gjson
	dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
	if err != nil {
		return "", err
	}
	dataMappingJSON := gjson.ParseBytes(dataMappingBytes)

	// Get dishes from data mapping using gjson
	dishesJSON := dataMappingJSON.Get("dishes")
	if !dishesJSON.Exists() || !dishesJSON.IsArray() || dishIndex >= len(dishesJSON.Array()) {
		return "", fmt.Errorf("dish index out of range")
	}

	dishJSON := dishesJSON.Get(fmt.Sprintf("%d", dishIndex))
	if !dishJSON.Exists() {
		return "", fmt.Errorf("invalid dish data")
	}

	// Calculate label index per total using gjson
	totalQuantityBefore := 0
	dishesArray := dishesJSON.Array()
	for i := 0; i < dishIndex; i++ {
		if i >= len(dishesArray) {
			break
		}

		quantity := dishesArray[i].Get("quantity").Float()
		totalQuantityBefore += int(quantity)
	}

	labelIndexPerTotal := totalQuantityBefore + labelIndex + 1

	// Calculate total quantity using gjson
	totalQuantity := 0
	for _, d := range dishesArray {
		quantity := d.Get("quantity").Float()
		totalQuantity += int(quantity)
	}

	// Get merchant info
	merchantInfo := map[string]map[string]string{
		"grab":         {"label": "Grab Food"},
		"grab_mart":    {"label": "Grab Mart"},
		"shopee":       {"label": "Shopee Food"},
		"shopee_ecom":  {"label": "Shopee Ecom"},
		"shopee_fresh": {"label": "Shopee Fresh"},
		"gojek":        {"label": "Gojek"},
		"be":           {"label": "Be"},
		"local":        {"label": "Tạo trên NexPos"},
		"he":           {"label": "Đại lý cá nhân"},
		"lazada":       {"label": "Lazada"},
		"tiktok":       {"label": "Tiktok"},
	}

	// Get order source label
	orderSourceLabel := order.Source
	if sourceInfo, ok := merchantInfo[order.Source]; ok {
		orderSourceLabel = sourceInfo["label"]
	}

	// Create template data
	templateData := LabelTemplateData{
		BillName:          "Label",
		LongOrderID:       order.OrderID,
		ShortOrderID:      dataMappingJSON.Get("order_id").String(),
		SiteName:          site.Name,
		HubName:           hub.Name,
		HubPhone:          hub.Phone,
		OrderSource:       orderSourceLabel,
		CustomerName:      dataMappingJSON.Get("customer_name").String(),
		CustomerAddress:   dataMappingJSON.Get("customer_address").String(),
		DishName:          dishJSON.Get("name").String(),
		DishDescription:   dishJSON.Get("description").String(),
		DishQuantity:      fmt.Sprintf("%.0f", dishJSON.Get("quantity").Float()),
		DishPrice:         FormatCurrency(dishJSON.Get("price").Float()),
		DishDiscountPrice: FormatCurrency(dishJSON.Get("discount_price").Float()),
		DishNote:          dishJSON.Get("note").String(),
		LabelIndex:        fmt.Sprintf("%d/%d", labelIndexPerTotal, totalQuantity),
		OrderNote:         dataMappingJSON.Get("note").String(),
	}

	// Format dates
	if createdAt := dataMappingJSON.Get("created_at").String(); createdAt != "" {
		if createdAtFormatted, err := time.Parse(time.RFC3339, createdAt); err == nil {
			templateData.OrderCreatedAt = createdAtFormatted.Format("02/01/2006 15:04:05")
		} else {
			templateData.OrderCreatedAt = time.Now().Format("02/01/2006 15:04:05")
		}
	} else {
		templateData.OrderCreatedAt = time.Now().Format("02/01/2006 15:04:05")
	}

	// Order totals
	if total := dataMappingJSON.Get("total").Float(); total > 0 {
		templateData.OrderSubTotal = FormatCurrency(total)
	}

	if totalDiscount := dataMappingJSON.Get("total_discount").Float(); totalDiscount > 0 {
		templateData.OrderTotalDiscount = FormatCurrency(totalDiscount)
	}

	if totalForBiz := dataMappingJSON.Get("total_for_biz").Float(); totalForBiz > 0 {
		templateData.OrderTotalPaid = FormatCurrency(totalForBiz)
	}

	// Process dish options for the template
	optionsJSON := dishJSON.Get("options")
	if optionsJSON.Exists() && optionsJSON.IsArray() {
		// Group options by name using gjson
		groupedOptions := make(map[string][]gjson.Result)

		optionsJSON.ForEach(func(groupKey, optGroup gjson.Result) bool {
			if optGroup.IsArray() {
				optGroup.ForEach(func(optKey, option gjson.Result) bool {
					optionName := option.Get("option_name").String()
					if optionName == "" {
						return true // continue
					}

					if _, ok := groupedOptions[optionName]; !ok {
						groupedOptions[optionName] = []gjson.Result{}
					}

					groupedOptions[optionName] = append(groupedOptions[optionName], option)
					return true // continue
				})
			}
			return true // continue
		})

		// Generate plain text for options
		var optionsHTML strings.Builder
		for optionName, toppings := range groupedOptions {
			optionsHTML.WriteString(fmt.Sprintf("%s\n", optionName))

			for _, topping := range toppings {
				optionItem := topping.Get("option_item").String()
				optionQuantity := topping.Get("quantity").Float()
				optionPrice := topping.Get("price").Float()

				priceStr := ""
				if optionPrice > 0 {
					priceStr = " - " + FormatCurrency(optionPrice)
				}

				quantityStr := ""
				if optionQuantity > 0 {
					quantityStr = fmt.Sprintf(" x%.0f", optionQuantity)
				}

				optionsHTML.WriteString(fmt.Sprintf(
					"- %s%s%s\n",
					optionItem, quantityStr, priceStr))
			}
		}

		templateData.DishOptions = optionsHTML.String()
	}

	// Parse and execute template
	tmpl, err := template.New("label").Parse(string(templateContent))
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %v", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return "", fmt.Errorf("failed to execute template: %v", err)
	}

	return buf.String(), nil
}

// GenerateOrderBills generates bills for an order
func GenerateOrderBills(db *gorm.DB, orderID string, options BillOptions) (*models.Order, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return nil, err
	}

	if options.GenerateBill {
		// Render bill HTML
		billHTML, err := RenderBillHTML(db, orderID, options.BillType)
		if err != nil {
			return &order, err
		}

		if billHTML != "" {
			// Convert HTML to image URL using external service
			billURL, err := convertHTMLToBillURL(orderID, billHTML)
			if err != nil {
				return &order, err
			}

			// Update order with bill URL
			field := MapBillToOrderField[options.BillType]
			if field != "" {
				updates := map[string]any{
					field: billURL,
				}
				if err := db.Model(&order).Updates(updates).Error; err != nil {
					return &order, err
				}

				// Refresh order
				if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
					return &order, err
				}
			}
		}
	}

	if options.GenerateLabel {
		// Get order data mapping using gjson
		dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
		if err != nil {
			return &order, err
		}
		dataMappingJSON := gjson.ParseBytes(dataMappingBytes)

		dishesJSON := dataMappingJSON.Get("dishes")
		if !dishesJSON.Exists() || !dishesJSON.IsArray() {
			// No dishes to generate labels for
			return &order, nil
		}

		// Generate labels for each dish based on quantity
		var labelURLs []string
		if order.LabelURLs.Data != nil {
			labelURLs = order.LabelURLs.Data
		}

		dishesArray := dishesJSON.Array()
		for dishIndex, dishJSON := range dishesArray {
			quantity := dishJSON.Get("quantity").Float()
			if quantity <= 0 {
				quantity = 1
			}

			// Generate a label for each quantity
			for labelIndex := 0; labelIndex < int(quantity); labelIndex++ {
				// Render label HTML
				labelHTML, err := RenderLabelHTML(db, orderID, dishIndex, labelIndex, "590px")
				if err != nil {
					fmt.Printf("Error rendering label HTML: %v\n", err)
					continue
				}

				if labelHTML != "" {
					// Convert HTML to image URL
					labelURL, err := convertHTMLToBillURL(orderID, labelHTML)
					if err != nil {
						fmt.Printf("Error converting label HTML to URL: %v\n", err)
						continue
					}

					labelURLs = append(labelURLs, labelURL)
				}
			}
		}

		// Update order with label URLs
		updates := map[string]any{
			"label_urls": models.JSONField[[]string]{Data: labelURLs},
		}
		if err := db.Model(&order).Updates(updates).Error; err != nil {
			return &order, err
		}

		// Refresh order
		if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
			return &order, err
		}
	}

	return &order, nil
}

// SendOrderBillToChannel sends bills to various channels (printers, Zalo, etc.)
func SendOrderBillToChannel(db *gorm.DB, orderID string, options ChannelOptions) error {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return err
	}

	// Find site and hub
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return err
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return err
	}

	// Get Zalo group
	zaloGroup := site.ZaloGroup
	if zaloGroup == "" {
		zaloGroup = hub.ZaloGroup
	}

	var currentBillURL string
	var labelURLs []string

	if options.Bill {
		field := MapBillToOrderField[options.BillTemplate]
		if field != "" {
			// Get bill URL from order
			switch field {
			case "bill_url":
				currentBillURL = order.BillURL
			case "bill_for_payment_url":
				currentBillURL = order.BillForPaymentURL
			case "bill_for_complete_url":
				currentBillURL = order.BillForCompleteURL
			case "bill_for_complete_app_url":
				currentBillURL = order.BillForCompleteAppURL
			case "bill_for_cancel_url":
				currentBillURL = order.BillForCancelURL
			}

			// If bill URL is empty, generate it
			if currentBillURL == "" {
				newOrder, err := GenerateOrderBills(db, orderID, BillOptions{
					BillType:     options.BillTemplate,
					GenerateBill: true,
				})
				if err != nil {
					return err
				}

				// Get the updated bill URL
				switch field {
				case "bill_url":
					currentBillURL = newOrder.BillURL
				case "bill_for_payment_url":
					currentBillURL = newOrder.BillForPaymentURL
				case "bill_for_complete_url":
					currentBillURL = newOrder.BillForCompleteURL
				case "bill_for_complete_app_url":
					currentBillURL = newOrder.BillForCompleteAppURL
				case "bill_for_cancel_url":
					currentBillURL = newOrder.BillForCancelURL
				}
			}
		}
	}

	if options.Labels {
		// Generate labels if needed
		if len(order.LabelURLs.Data) == 0 {
			// Generate labels
			newOrder, err := GenerateOrderBills(db, orderID, BillOptions{
				BillType:      "bill_for_label",
				GenerateLabel: true,
			})
			if err == nil && newOrder != nil {
				// Update order with new label URLs
				updates := map[string]any{
					"label_urls": newOrder.LabelURLs,
				}
				if err := db.Model(&order).Updates(updates).Error; err != nil {
					fmt.Printf("Error updating order with label URLs: %v\n", err)
				}

				// Get the updated label URLs
				labelURLs = newOrder.LabelURLs.Data
			}
		} else {
			labelURLs = order.LabelURLs.Data
		}
	}

	// Send to Zalo if enabled
	if options.Zalo && zaloGroup != "" {
		// Send message if provided
		if options.ZaloMessage != "" {
			err := SendZaloMessage(zaloGroup, options.ZaloMessage, options.UrgentMessage)
			if err != nil {
				fmt.Printf("Error sending Zalo message: %v\n", err)
			}
		}

		// Send bill image if available
		if currentBillURL != "" {
			err := SendZaloImage(zaloGroup, currentBillURL)
			if err != nil {
				fmt.Printf("Error sending bill to Zalo: %v\n", err)
			}
		}
	}

	// Create print queue entries
	if currentBillURL != "" {
		printQueue := models.PrintQueue{
			SiteID:          order.SiteID,
			HubID:           order.HubID,
			OrderID:         order.OrderID,
			OrderPrintCount: 1,
			Source:          order.Source,
			PrintType:       "kitchen",
			Status:          "created",
			Data:            []byte("{}"),
			FileURL:         currentBillURL,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		if err := db.Create(&printQueue).Error; err != nil {
			return err
		}
	}

	// Send message to print_order topic
	// Get Redis client from the SDK
	redisClient := redis.RedisClient
	if redisClient != nil {
		billTemplate := ""
		labelTemplate := ""

		if options.Bill {
			billTemplate = options.BillTemplate
		}

		if options.Labels {
			labelTemplate = options.LabelTemplate
		}

		// Send message to print_order topic
		message := fmt.Sprintf("Order %s is ready to print", orderID)

		data := map[string]any{
			"bill_template":  nil,
			"label_template": nil,
			"hub_code":       hub.Code,
			"order_id":       orderID,
			"site_id":        order.SiteID,
			"bill_url":       nil,
			"label_urls":     nil,
		}

		// Only include bill data if billTemplate is provided
		if billTemplate != "" {
			data["bill_template"] = billTemplate
			data["bill_url"] = currentBillURL
		}

		// Only include label data if labelTemplate is provided
		if labelTemplate != "" {
			data["label_template"] = labelTemplate
			data["label_urls"] = labelURLs
		}

		err := utils.SendMessageToTopic(redisClient, "print_order", message, data)

		if err != nil {
			fmt.Printf("Error sending print order message: %v\n", err)
		}
	}

	return nil
}

// Helper function to convert HTML to bill URL
func convertHTMLToBillURL(orderID string, html string) (string, error) {
	// Use the HTMLToImage function from the bill package
	return HTMLToImage(orderID, html)
}

// formatNumber formats a number with thousands separators
func formatNumber(num float64) string {
	// Convert to integer if it's a whole number
	if num == float64(int(num)) {
		return formatWithCommas(int(num))
	}

	// Format with 2 decimal places
	return formatWithCommas(int(num)) + fmt.Sprintf(",%02d", int(num*100)%100)
}

// formatWithCommas adds thousands separators to an integer
func formatWithCommas(n int) string {
	in := strconv.Itoa(n)
	out := make([]byte, 0, len(in)+(len(in)-1)/3)

	// Process the number from right to left
	for i, c := range in {
		if i > 0 && (len(in)-i)%3 == 0 {
			out = append(out, '.')
		}
		out = append(out, byte(c))
	}

	return string(out)
}

// StringReplacer replaces placeholders in a string with values from a map
func StringReplacer(str string, obj map[string]string) string {
	// Replace {prefix|key} pattern
	prefixPattern := regexp.MustCompile(`\{([^}]+\|[^}]+)\}`)
	str = prefixPattern.ReplaceAllStringFunc(str, func(match string) string {
		// Extract content between curly braces
		content := match[1 : len(match)-1]
		parts := strings.Split(content, "|")
		if len(parts) != 2 {
			return match
		}

		prefix := parts[0]
		key := parts[1]

		if value, ok := obj[key]; ok {
			if value != "" {
				return prefix + value
			}
			return ""
		}
		return match
	})

	// Replace {key} pattern
	simplePattern := regexp.MustCompile(`\{([^}]+)\}`)
	str = simplePattern.ReplaceAllStringFunc(str, func(match string) string {
		// Extract key between curly braces
		key := match[1 : len(match)-1]

		if value, ok := obj[key]; ok {
			return value
		}
		return match
	})

	return str
}

// SendZaloMessage sends a message to a Zalo group
func SendZaloMessage(groupLink string, message string, urgentMessage bool) error {
	// Prepare urgency parameter
	urgency := ""
	if urgentMessage {
		urgency = "2" // Urgent message
	}

	// Create request data
	formData := url.Values{}
	formData.Set("shareLink", groupLink)
	formData.Set("message", message)
	formData.Set("urgency", urgency)

	// Send request to Zalo Mini App service
	resp, err := http.PostForm(
		"https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_message",
		formData,
	)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send Zalo message, status code: %d", resp.StatusCode)
	}

	return nil
}

// SendZaloImage sends an image to a Zalo group
func SendZaloImage(groupLink string, imageURL string) error {
	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Read the image data
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// Create a buffer to hold the form data
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	// Add the group link field
	err = w.WriteField("shareLink", groupLink)
	if err != nil {
		return err
	}

	// Add the image field
	fw, err := w.CreateFormFile("image", "bill.png")
	if err != nil {
		return err
	}
	_, err = fw.Write(imageData)
	if err != nil {
		return err
	}

	// Close the writer
	w.Close()

	// Create the request
	req, err := http.NewRequest("POST", "https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_image", &b)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", w.FormDataContentType())

	// Send the request
	client := &http.Client{}
	resp2, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp2.Body.Close()

	// Check response status
	if resp2.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send Zalo image, status code: %d", resp2.StatusCode)
	}

	return nil
}
