// Package router implements payment callback handling for NexDorPay integrations
package router

import (
	"fmt"
	"log"
	"net/http"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/email"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NexDorPayCallbackData represents the structure of callback data from NexDorPay
type NexDorPayCallbackData struct {
	TransactionID        string    `json:"transaction_id"`
	PartnerTransactionID string    `json:"partner_transaction_id"`
	Amount               int       `json:"amount"`
	Description          string    `json:"description"`
	Status               string    `json:"status"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

func CheckSubscriptionPayment(c *gin.Context) {
	// Extract request ID from query string
	requestID := c.Query("request_id")

	// Log the callback data for debugging purposes
	log.Printf("NexDorPay client callback: request_id=%s", requestID)

	// Validate request ID
	if requestID == "" {
		// Return an error response if no request ID is provided
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing subscription request ID",
		})
		return
	}

	// Fetch subscription details from database
	db := middlewares.GetDB(c)
	var subscriptionRequest models.SubscriptionRequest

	// Find the subscription request in the database
	if err := db.Where("id = ?", requestID).First(&subscriptionRequest).Error; err != nil {
		status := http.StatusBadRequest
		responseData := gin.H{
			"success": false,
		}

		// Add detailed error information to the response
		if err == gorm.ErrRecordNotFound {
			status = http.StatusNotFound
			responseData["error"] = fmt.Sprintf("Subscription request with ID '%s' not found", requestID)
			log.Printf("Payment check failed: %v", responseData["error"])
		} else {
			responseData["error"] = fmt.Sprintf("Database error: %v", err)
			log.Printf("Payment check database error: %v", err)
		}

		c.JSON(status, responseData)
		return
	}

	// Return success response with subscription details
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"request_id": requestID,
			"subscription": gin.H{
				"plan_id":        subscriptionRequest.PlanID,
				"status":         subscriptionRequest.Status,
				"payment_status": subscriptionRequest.PaymentStatus,
				"amount":         subscriptionRequest.Amount,
				"start_time":     subscriptionRequest.StartTime.Format(time.RFC3339),
				"expired_time":   subscriptionRequest.ExpiredTime.Format(time.RFC3339),
			},
		},
	})
}

// SubscriptionPaymentServerCallback handles webhook callbacks from NexDorPay
func SubscriptionPaymentServerCallback(c *gin.Context) {

	// Get database connection
	db := middlewares.GetDB(c)

	// Define and parse the request body
	var req struct {
		TransactionID        string `json:"transaction_id"`
		PartnerTransactionID string `json:"partner_transaction_id"`
		Status               string `json:"status"`
		OrderID              string `json:"order_id"`
		Amount               int    `json:"amount"`
		// CallbackData         *NexDorPayCallbackData `json:"callback_data"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Invalid request format: %v", err),
		})
		return
	}

	// Log the webhook data
	log.Printf("NexDorPay server callback: %+v", req)

	// Check payment status - only proceed if COMPLETED
	if req.Status != "COMPLETED" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Payment notification received, status is not COMPLETED",
		})
		return
	}

	// Find the subscription request
	var subscriptionRequest models.SubscriptionRequest
	if err := db.Where("id = ?", req.OrderID).First(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Subscription request not found",
		})
		return
	}

	// Process the payment in a database transaction
	tx := db.Begin()

	// Update request status
	subscriptionRequest.Status = models.Paid
	subscriptionRequest.PaymentStatus = models.PaymentSuccess
	subscriptionRequest.TransactionID = &req.TransactionID

	if err := tx.Save(&subscriptionRequest).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to update subscription request: %v", err),
		})
		return
	}

	// Process subscription based on request type
	var err error
	if isInitialSubscription(&subscriptionRequest) {
		// For initial subscription - create a new subscription
		err = activateNewSubscription(tx, &subscriptionRequest)
	} else if isPlanChangeSubscription(&subscriptionRequest) {
		// For plan change - handle according to the upgrade and postponement logic
		err = processSubscriptionUpgradeCallback(tx, &subscriptionRequest)
	} else {
		err = fmt.Errorf("unsupported subscription request type")
	}

	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Transaction failed: %v", err),
		})
		return
	}

	// Send appropriate emails after successful payment processing
	if isPlanChangeSubscription(&subscriptionRequest) {
		// For plan changes/upgrades, send both emails
		go sendUpgradeEmail(db, &subscriptionRequest)
	} else {
		// For initial subscriptions, send only the invoice email
		go sendInvoiceEmail(db, &subscriptionRequest)
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment processed successfully",
	})
}

// isInitialSubscription checks if the request is for an initial subscription
// Using the model's IsInitial() method directly
func isInitialSubscription(req *models.SubscriptionRequest) bool {
	return req.IsInitial()
}

// isPlanChangeSubscription checks if the request is for a plan change
// Using the model's IsChange() method directly
func isPlanChangeSubscription(req *models.SubscriptionRequest) bool {
	return req.IsChange()
}

// activateNewSubscription creates a new subscription for the user
func activateNewSubscription(tx *gorm.DB, req *models.SubscriptionRequest) error {
	// Get plan details
	plan, exists := models.GetPlanByID(req.PlanID)
	if !exists {
		return fmt.Errorf("subscription plan not found")
	}

	// Create new user subscription
	userSubscription := models.UserSubscription{
		ID:                 models.ObjectID(utils.GenObjectID()),
		UserID:             req.UserID,
		SubscriptionPlanID: req.PlanID,
		Status:             models.Active,
		StartDate:          time.Now(),
		EndDate:            req.ExpiredTime,
		PaymentMethod:      "NEXDORPAY",
		Limits:             &models.JSONField[models.FeatureLimits]{Data: plan.Limits},
		Period:             req.Period,
	}

	// Save subscription to database
	if err := tx.Create(&userSubscription).Error; err != nil {
		return fmt.Errorf("failed to create subscription: %v", err)
	}

	// Link subscription to request
	req.CreatedSubscriptionID = &userSubscription.ID
	return tx.Save(req).Error
}

// sendInvoiceEmail sends an invoice email to the user after successful payment
// This function is executed in a goroutine to avoid blocking the response
func sendInvoiceEmail(db *gorm.DB, req *models.SubscriptionRequest) {
	// Find the user associated with this subscription request
	var user models.User
	if err := db.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		log.Printf("Error finding user for invoice email: %v", err)
		return
	}

	// Get plan details
	plan, exists := models.GetPlanByID(req.PlanID)
	if !exists {
		log.Printf("Error sending invoice: plan %s not found", req.PlanID)
		return
	}

	// Generate unique invoice number using request ID and timestamp
	invoiceNumber := fmt.Sprintf("INV-%s-%d", req.ID, time.Now().Unix())

	// Format the subscription period in a human-readable way
	var periodText string
	switch req.Period {
	case models.Monthly:
		periodText = "Monthly Subscription"
	case models.Yearly:
		periodText = "Annual Subscription"
	case models.Trial:
		periodText = "Trial Period"
	default:
		periodText = string(req.Period)
	}

	// Determine invoice type based on request type
	var invoiceType string
	if req.IsInitial() {
		invoiceType = "New Subscription"
	} else {
		invoiceType = "Plan Change"
	}

	// Format payment date and due date in ISO format
	paymentDate := time.Now().Format("2006-01-02")
	dueDate := time.Now().AddDate(0, 0, 7).Format("2006-01-02") // Due in 7 days

	// Format amount with proper formatting
	amountFloat := float64(req.Amount)
	formattedAmount := formatCurrency(amountFloat)

	// Prepare comprehensive invoice data for the template
	invoiceData := map[string]any{
		// Customer details
		"user_name":  user.Name,
		"user_email": user.Email,

		// Invoice identification
		"invoice_number": invoiceNumber,
		"invoice_date":   paymentDate,
		"due_date":       dueDate,

		// Subscription details
		"plan_name":    plan.Name,
		"plan_period":  periodText,
		"invoice_type": invoiceType,

		// Financial information
		"amount":       formattedAmount,
		"currency":     "VND",
		"total_amount": formattedAmount, // Total is same as amount since there's no tax

		// Payment details
		"transaction_id": *req.TransactionID,
		"payment_method": "NexDorPay",
		"payment_status": "Paid",

		// Company information
		"company_name":    "NexDor Technology",
		"company_address": "123 Tech Street, Ho Chi Minh City, Vietnam",
		"company_email":   "<EMAIL>",
		"support_email":   "<EMAIL>",
	}

	// Send the email using the invoice template
	if err := email.SendEmail(
		user.Email,
		"Your NexDor Subscription Invoice",
		"sub_invoice", // Uses the sub_invoice.html template
		invoiceData,
	); err != nil {
		log.Printf("Error sending invoice email: %v", err)
		return
	}

	log.Printf("Invoice email sent successfully to %s for subscription %s", user.Email, req.ID)
}

// sendUpgradeEmail sends a notification email to the user about their successful subscription upgrade
// This function should be called after successful payment processing for plan change requests
func sendUpgradeEmail(db *gorm.DB, req *models.SubscriptionRequest) {
	// Find the user associated with this subscription request
	var user models.User
	if err := db.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		log.Printf("Error finding user for upgrade email: %v", err)
		return
	}

	// Get new plan details
	newPlan, exists := models.GetPlanByID(req.PlanID)
	if !exists {
		log.Printf("Error sending upgrade email: new plan %s not found", req.PlanID)
		return
	}

	// Get old plan details if this is a plan change
	var oldPlanName string
	var oldLimit, newLimit int

	// Check if this is a plan change and get the old subscription info
	if req.IsChange() && req.UserSubscriptionID != nil {
		var oldSubscription models.UserSubscription
		if err := db.Where("id = ?", req.UserSubscriptionID).First(&oldSubscription).Error; err == nil {
			oldPlan, exists := models.GetPlanByID(oldSubscription.SubscriptionPlanID)
			if exists {
				oldPlanName = oldPlan.Name
				// Get store limits from old plan
				if oldSubscription.Limits != nil && oldSubscription.Limits.Data != nil {
					oldLimit = oldSubscription.Limits.Data[models.StoresResourceCode]
				} else if oldPlan.Limits != nil {
					oldLimit = oldPlan.Limits[models.StoresResourceCode]
				}
			}
		}
	}

	// Get new store limit
	if newPlan.Limits != nil {
		newLimit = newPlan.Limits[models.StoresResourceCode]
	}

	// Format period text in Vietnamese
	var periodText string
	switch req.Period {
	case models.Monthly:
		periodText = "tháng"
	case models.Yearly:
		periodText = "năm"
	case models.Trial:
		periodText = "dùng thử"
	default:
		periodText = string(req.Period)
	}

	// Format amount with proper formatting
	amountFloat := float64(req.Amount)
	formattedAmount := formatCurrency(amountFloat)

	// Format dates in a more human-readable format for Vietnamese users
	startDate := time.Now().Format("02/01/2006") // dd/mm/yyyy format
	endDate := "-"
	if req.ExpiredTime != nil {
		endDate = req.ExpiredTime.Format("02/01/2006")
	}

	// Prepare upgrade email data for the template
	upgradeData := map[string]any{
		// Flag to indicate this is an upgrade email
		"email_type": "upgrade",

		// Customer details
		"user_name":  user.Name,
		"user_email": user.Email,

		// Subscription details
		"plan_name":     newPlan.Name,
		"old_plan_name": oldPlanName,
		"start_date":    startDate,
		"end_date":      endDate,

		// Limit information, in string format
		"old_limit": fmt.Sprintf("%d", oldLimit),
		"new_limit": fmt.Sprintf("%d", newLimit),

		// Financial information
		"amount":      formattedAmount,
		"currency":    "VNĐ",
		"period_text": periodText,

		// Company information
		"company_name":    "NexDor Technology",
		"company_address": "123 Tech Street, Ho Chi Minh City, Vietnam",
		"company_email":   "<EMAIL>",
		"support_email":   "<EMAIL>",
		"support_phone":   "1900 123 456",
	}

	// Send the email using the same sub_invoice template but with different data
	subject := fmt.Sprintf("Chúc mừng! Bạn đã nâng cấp gói %s thành công", newPlan.Name)
	if err := email.SendEmail(
		user.Email,
		subject,
		"sub_invoice", // Uses the sub_invoice.html template with conditional rendering
		upgradeData,
	); err != nil {
		log.Printf("Error sending upgrade email: %v", err)
		return
	}

	log.Printf("Upgrade notification email sent successfully to %s for subscription %s", user.Email, req.ID)
}

// formatCurrency formats a float value as a string with thousands separator
// For example: 1000000 becomes "1,000,000"
func formatCurrency(value float64) string {
	// For values with no decimal places, format as integer
	if value == float64(int64(value)) {
		return fmt.Sprintf("%d", int64(value))
	}
	// Otherwise format with 2 decimal places
	return fmt.Sprintf("%.2f", value)
}

// processSubscriptionUpgradeCallback handles the upgraded subscription case with postponement
// This implements the same logic as processSubscriptionUpgrade from subscription_payment.go
func processSubscriptionUpgradeCallback(tx *gorm.DB, subscriptionReq *models.SubscriptionRequest) error {
	// Get current subscription
	var currentSubscription models.UserSubscription
	if err := tx.Where("id = ?", subscriptionReq.UserSubscriptionID).First(&currentSubscription).Error; err != nil {
		return fmt.Errorf("current subscription not found: %w", err)
	}

	// Calculate days remaining in current subscription
	daysRemaining := 0
	if currentSubscription.EndDate != nil {
		// Calculate days remaining by getting the duration between now and end date
		duration := currentSubscription.EndDate.Sub(time.Now())
		// Convert duration to days and ensure it's not negative
		if duration > 0 {
			daysRemaining = int(duration.Hours() / 24)
		}
	}

	// Calculate end date for the new subscription based on the requested period
	newEndDate := models.CalculateEndDateFromNow(subscriptionReq.Period)

	// Create new user subscription for the upgrade
	newSubscription := models.UserSubscription{
		ID:                 models.ObjectID(utils.GenObjectID()),
		UserID:             subscriptionReq.UserID,
		SubscriptionPlanID: subscriptionReq.PlanID,
		Status:             models.Active,
		StartDate:          time.Now(),
		EndDate:            &newEndDate,
		Period:             subscriptionReq.Period,
		PaymentMethod:      "NexDorPay", // Default payment method
	}

	// Check if the current subscription is a trial - if it is, don't postpone it
	// as we don't want users to revert to trial when the paid plan expires
	isTrial := currentSubscription.Period == models.Trial

	// Only set postponed subscription reference if not a trial
	if !isTrial {
		// Link to the original subscription only for non-trial subscriptions
		newSubscription.PostponedSubscriptionID = &currentSubscription.ID
	}

	// Set plan limits for new subscription
	plan, exists := models.GetPlanByID(subscriptionReq.PlanID)
	if exists {
		newSubscription.Limits = &models.JSONField[models.FeatureLimits]{Data: plan.Limits}
	}

	// Create the new subscription
	if err := tx.Create(&newSubscription).Error; err != nil {
		return fmt.Errorf("failed to create new subscription: %w", err)
	}

	// Update the original subscription status
	if isTrial {
		// For trial subscriptions, mark as expired rather than postponed
		currentSubscription.Status = models.Expired
	} else {
		// For regular paid subscriptions, mark as postponed
		currentSubscription.Status = models.Postponed
		currentSubscription.PostponedBySubscriptionID = &newSubscription.ID
		currentSubscription.RemainingDays = daysRemaining
	}

	if err := tx.Save(&currentSubscription).Error; err != nil {
		return fmt.Errorf("failed to update original subscription: %w", err)
	}

	// Update subscription request with created subscription ID
	subscriptionReq.CreatedSubscriptionID = &newSubscription.ID

	if err := tx.Save(subscriptionReq).Error; err != nil {
		return fmt.Errorf("failed to update subscription request: %w", err)
	}

	return nil
}
