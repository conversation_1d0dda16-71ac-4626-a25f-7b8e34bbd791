package payment

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

// NexdorpayClient represents a NEXDORPay payment client
type NexdorpayClient struct {
	BaseURL    string
	HTTPClient *resty.Client
}

// NexdorpayRequest represents a NEXDORPay payment request
type NexdorpayRequest struct {
	TransactionID  string `json:"transaction_id"`
	Total          int64  `json:"total"`
	CakeAccount    string `json:"cake_account"`
	Message        string `json:"message"`
	ServerCallback string `json:"server_callback"`
	ClientCallback string `json:"client_callback"`
	Signature      string `json:"signature"`
}

// NexdorpayResponse represents a NEXDORPay payment response
type NexdorpayResponse struct {
	Success bool `json:"success"`
	Data    struct {
		QRCode string `json:"qrcode"`
		PayURL string `json:"pay_url"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

// NexdorpayTransaction represents a NEXDORPay transaction
type NexdorpayTransaction struct {
	TransactionID  string     `json:"transaction_id"`
	OrderID        string     `json:"order_id"`
	Amount         int64      `json:"amount"`
	CakeAccount    string     `json:"cake_account"`
	Message        string     `json:"message"`
	ClientCallback string     `json:"client_callback"`
	ServerCallback string     `json:"server_callback"`
	QRCode         string     `json:"qrcode"`
	Status         string     `json:"status"`
	ExpiredAt      *time.Time `json:"expired_at"`
	CreatedAt      time.Time  `json:"created_at"`
}

// NewNexdorpayClient creates a new NEXDORPay payment client
func NewNexdorpayClient() *NexdorpayClient {
	return &NexdorpayClient{
		BaseURL:    "https://pay.nexdor.tech",
		HTTPClient: resty.New(),
	}
}

// GetProviderName returns the provider name
func (c *NexdorpayClient) GetProviderName() string {
	return "nexdorpay"
}

// CreatePaymentLink creates a new NEXDORPay payment transaction
func (c *NexdorpayClient) CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error) {
	// Generate transaction ID using the same logic as Node.js
	transactionID := c.generateTransactionID(request.OrderID, int64(request.Amount))

	// Prepare NEXDORPay request
	nexdorReq := NexdorpayRequest{
		TransactionID:  transactionID,
		Total:          int64(request.Amount),
		CakeAccount:    token.Username,
		Message:        fmt.Sprintf("THANH TOAN DON HANG %s", request.OrderID),
		ServerCallback: request.ServerCallback,
		ClientCallback: request.ClientCallback,
	}

	// Generate signature
	signature := c.generateSignature(nexdorReq, "createTransaction", token.Password)
	nexdorReq.Signature = signature

	// Send request
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(nexdorReq).
		Post(c.BaseURL + "/api/transactions")

	if err != nil {
		return nil, fmt.Errorf("failed to create NEXDORPay payment: %w", err)
	}

	// Parse response
	var nexdorResp NexdorpayResponse
	if err := json.Unmarshal(resp.Body(), &nexdorResp); err != nil {
		return nil, fmt.Errorf("failed to parse NEXDORPay response: %w", err)
	}

	// Check for success
	if !nexdorResp.Success {
		return nil, fmt.Errorf("NEXDORPay payment creation failed: %s", nexdorResp.Message)
	}

	// Generate pay URL
	payURL := fmt.Sprintf("%s?tx=%s", c.BaseURL, transactionID)

	return &PaymentResponse{
		Success:       true,
		TransactionID: transactionID,
		PayURL:        payURL,
		QRCode:        nexdorResp.Data.QRCode,
		Message:       nexdorResp.Message,
		Data:          nexdorResp,
	}, nil
}

// GetTransactionStatus retrieves NEXDORPay transaction status
func (c *NexdorpayClient) GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error) {
	// Send request to get transaction details
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("%s/api/transactions/%s", c.BaseURL, transactionID))

	if err != nil {
		return nil, fmt.Errorf("failed to get NEXDORPay transaction: %w", err)
	}

	// Parse response
	var result struct {
		Success bool                 `json:"success"`
		Data    NexdorpayTransaction `json:"data"`
		Message string               `json:"message,omitempty"`
	}

	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse NEXDORPay response: %w", err)
	}

	// Check for success
	if !result.Success {
		return nil, fmt.Errorf("failed to get NEXDORPay transaction: %s", result.Message)
	}

	return &TransactionStatus{
		TransactionID: result.Data.TransactionID,
		OrderID:       result.Data.OrderID,
		Amount:        float64(result.Data.Amount),
		Status:        result.Data.Status,
		Message:       result.Data.Message,
		CreatedAt:     result.Data.CreatedAt,
		UpdatedAt:     result.Data.CreatedAt, // NEXDORPay doesn't provide separate updated_at
		ExpiredAt:     result.Data.ExpiredAt,
		Data:          result.Data,
	}, nil
}

// ProcessCallback processes NEXDORPay callback data
func (c *NexdorpayClient) ProcessCallback(data map[string]interface{}) (*CallbackResponse, error) {
	transactionID, _ := data["transaction_id"].(string)
	status, _ := data["status"].(string)
	amount, _ := data["amount"].(float64)
	message, _ := data["message"].(string)

	// Map NEXDORPay status to standard status
	mappedStatus := c.mapStatus(status)
	success := status == "SETTLED" || status == "COMPLETED"

	return &CallbackResponse{
		Success:       success,
		TransactionID: transactionID,
		OrderID:       transactionID, // NEXDORPay uses transaction_id as order reference
		Status:        mappedStatus,
		Amount:        amount,
		Message:       message,
		Data:          data,
	}, nil
}

// ValidateSignature validates NEXDORPay callback signature
func (c *NexdorpayClient) ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool {
	// Implementation would depend on NEXDORPay's signature validation requirements
	// This is a placeholder implementation
	return true
}

// Helper methods
func (c *NexdorpayClient) generateTransactionID(orderID string, amount int64) string {
	// Use the same logic as Node.js: name_to_id(`${order_id}-${total}`).substring(0, 10)
	input := fmt.Sprintf("%s-%d", orderID, amount)
	return utils.NameToID(input)[:10]
}

func (c *NexdorpayClient) mapStatus(status string) string {
	switch status {
	case "SETTLED", "COMPLETED":
		return "COMPLETED"
	case "CANCELLED", "FAILED":
		return "CANCELLED"
	case "UNSETTLED":
		return "PENDING"
	default:
		return "PENDING"
	}
}

func (c *NexdorpayClient) generateSignature(req NexdorpayRequest, operation, secretKey string) string {
	// Define field order for signature generation based on Node.js implementation
	fields := []string{"transaction_id", "total", "cake_account", "message", "server_callback", "client_callback"}

	var parts []string
	for _, field := range fields {
		switch field {
		case "transaction_id":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.TransactionID))
		case "total":
			parts = append(parts, fmt.Sprintf("%s=%d", field, req.Total))
		case "cake_account":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.CakeAccount))
		case "message":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.Message))
		case "server_callback":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.ServerCallback))
		case "client_callback":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.ClientCallback))
		}
	}

	rawSignature := strings.Join(parts, "&")
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawSignature))
	return hex.EncodeToString(h.Sum(nil))
}

// CreatePaymentQRCode creates a payment QR code (alternative method without signature)
func (c *NexdorpayClient) CreatePaymentQRCode(request *PaymentRequest) (*PaymentResponse, error) {
	transactionID := c.generateTransactionID(request.OrderID, int64(request.Amount))

	// Prepare request without signature (as per Node.js implementation)
	reqData := map[string]interface{}{
		"amount":          int64(request.Amount),
		"order_id":        request.OrderID,
		"transaction_id":  transactionID,
		"client_callback": request.ClientCallback,
		"server_callback": request.ServerCallback,
		"signature":       "",
	}

	// Send request
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(reqData).
		Post(c.BaseURL + "/api/transactions")

	if err != nil {
		return nil, fmt.Errorf("failed to create NEXDORPay QR code: %w", err)
	}

	// Parse response
	var nexdorResp NexdorpayResponse
	if err := json.Unmarshal(resp.Body(), &nexdorResp); err != nil {
		return nil, fmt.Errorf("failed to parse NEXDORPay response: %w", err)
	}

	return &PaymentResponse{
		Success:       nexdorResp.Success,
		TransactionID: transactionID,
		PayURL:        nexdorResp.Data.PayURL,
		QRCode:        nexdorResp.Data.QRCode,
		Data:          nexdorResp,
	}, nil
}
