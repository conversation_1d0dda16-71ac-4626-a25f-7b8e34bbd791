package middlewares

import (
	"errors"
	"fmt"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"gorm.io/gorm"
)

// GetUserActiveSubscription retrieves the active subscription and plan for a user.
// It determines the owner (either the user themselves or their owner) and fetches
// their active subscription along with the corresponding plan.
// It also checks if an active subscription has expired based on its end date,
// and updates its status to expired when necessary.
//
// Parameters:
// - db: The database connection
// - user: The user for whom to retrieve the subscription
//
// Returns:
// - The user subscription if found
// - The subscription plan if found
// - Any error encountered during retrieval
func GetUserActiveSubscription(db *gorm.DB, user *models.User) (*models.UserSubscription, *models.SubscriptionPlan, error) {
	// Validate input parameters
	if db == nil {
		return nil, nil, errors.New("database connection is nil")
	}
	if user == nil {
		return nil, nil, errors.New("user is nil")
	}

	// Determine the owner (either the user themselves or their owner)
	ownerID := user.ID
	if user.OwnerID != nil {
		// User belongs to an owner, use owner's ID for subscription
		ownerID = *user.OwnerID
	}

	// Fetch the active subscription for the owner
	var subscription models.UserSubscription
	if err := db.Where("user_id = ? AND status = ?", ownerID, models.Active).
		Order("created_at DESC").
		First(&subscription).Error; err != nil {
		// Check if no subscription record was found
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, fmt.Errorf("no active subscription found for user ID %s", ownerID)
		}
		// For other database errors, return the error
		return nil, nil, fmt.Errorf("error fetching subscription: %w", err)
	}

	// Check if the active subscription has expired based on its end date
	if subscription.EndDate != nil && subscription.EndDate.Before(time.Now()) {
		// Start a transaction to handle both subscription expiration and postponed subscription activation
		tx := db.Begin()

		// Update subscription status to expired within the transaction
		if err := tx.Model(&subscription).
			Select("status", "updated_at").
			Updates(map[string]interface{}{
				"status":     models.Expired,
				"updated_at": time.Now(),
			}).Error; err != nil {
			// Rollback transaction on error
			tx.Rollback()
			return nil, nil, fmt.Errorf("failed to update expired subscription: %w", err)
		}

		// Update the local subscription status
		subscription.Status = models.Expired
		subscription.UpdatedAt = time.Now()

		// Check if this subscription has postponed another subscription
		if subscription.PostponedSubscriptionID != nil {
			// Fetch the postponed subscription within the transaction
			var postponedSub models.UserSubscription
			if err := tx.Where("id = ? AND status = ?", *subscription.PostponedSubscriptionID, models.Postponed).
				First(&postponedSub).Error; err == nil {

				// Check if the postponed subscription is a trial
				// If it is, we don't want to reactivate it - instead create a free subscription
				if postponedSub.Period == models.Trial {
					// Don't reactivate trial subscriptions, create a free subscription instead
					freePlan, freePlanFound := models.GetPlanByID(models.FreePlanID)
					if freePlanFound {
						// Create a new free subscription
						freeSubscription := models.UserSubscription{
							ID:                 models.ObjectID(utils.GenObjectID()),
							UserID:             subscription.UserID,
							SubscriptionPlanID: models.FreePlanID,
							Status:             models.Active,
							StartDate:          time.Now(),
							EndDate:            nil, // Free subscriptions don't have an end date
							Period:             models.Monthly,
							PaymentMethod:      "Free", // Indicate this was automatically assigned
							Limits:             &models.JSONField[models.FeatureLimits]{Data: freePlan.Limits},
						}

						// Save the new free subscription within the transaction
						if err := tx.Create(&freeSubscription).Error; err != nil {
							// Rollback transaction on error
							tx.Rollback()
							return nil, nil, fmt.Errorf("failed to create free subscription: %w", err)
						}

						// Mark the trial subscription as expired instead of reactivating it
						if err := tx.Model(&postponedSub).
							Select("status", "updated_at").
							Updates(map[string]interface{}{
								"status":     models.Expired,
								"updated_at": time.Now(),
							}).Error; err != nil {
							// Rollback transaction on error
							tx.Rollback()
							return nil, nil, fmt.Errorf("failed to expire trial subscription: %w", err)
						}

						// Commit the transaction
						if err := tx.Commit().Error; err != nil {
							return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
						}

						// Return the newly created free subscription
						return &freeSubscription, freePlan, nil
					} else {
						// Free plan not found, commit the transaction for the expired status update only
						if err := tx.Commit().Error; err != nil {
							return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
						}
						return nil, nil, fmt.Errorf("free plan not found, cannot create free subscription after trial")
					}
				}

				// For non-trial postponed subscriptions, reactivate as normal
				// Calculate new end date by adding remaining days to current date
				var newEndDate time.Time
				if postponedSub.RemainingDays > 0 {
					newEndDate = time.Now().AddDate(0, 0, postponedSub.RemainingDays)
				} else {
					// If no remaining days, use the original end date logic based on period
					newEndDate = models.CalculateEndDateFromNow(postponedSub.Period)
				}

				// Update the postponed subscription to active with new end date within the transaction
				if err := tx.Model(&postponedSub).
					Select("status", "updated_at", "end_date").
					Updates(map[string]interface{}{
						"status":     models.Active,
						"updated_at": time.Now(),
						"end_date":   newEndDate,
					}).Error; err != nil {
					// Rollback transaction on error
					tx.Rollback()
					return nil, nil, fmt.Errorf("failed to activate postponed subscription: %w", err)
				}

				// Commit the transaction as both operations succeeded
				if err := tx.Commit().Error; err != nil {
					return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
				}

				// Get the subscription plan for the newly activated subscription
				plan, found := models.GetPlanByID(postponedSub.SubscriptionPlanID)
				if found {
					// Return the newly activated subscription and its plan
					postponedSub.Status = models.Active
					postponedSub.UpdatedAt = time.Now()
					postponedSub.EndDate = &newEndDate
					return &postponedSub, plan, nil
				} else {
					return &postponedSub, nil, fmt.Errorf("invalid subscription plan for postponed subscription: %s", postponedSub.SubscriptionPlanID)
				}
			} else {
				// Commit the transaction since we've successfully updated the expired subscription
				// even though we couldn't find a postponed subscription to activate
				if err := tx.Commit().Error; err != nil {
					return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
				}
			}
		} else {
			// No postponed subscription, create a new free subscription for the user
			// Start by getting the free plan
			freePlan, freePlanFound := models.GetPlanByID(models.FreePlanID)
			if freePlanFound {
				// Create a new free subscription - free plan has no end date as it's permanent
				freeSubscription := models.UserSubscription{
					ID:                 models.ObjectID(utils.GenObjectID()),
					UserID:             subscription.UserID,
					SubscriptionPlanID: models.FreePlanID,
					Status:             models.Active,
					StartDate:          time.Now(),
					EndDate:            nil, // Free subscriptions don't have an end date
					Period:             models.Monthly,
					PaymentMethod:      "System", // Indicate this was automatically assigned
					Limits:             &models.JSONField[models.FeatureLimits]{Data: freePlan.Limits},
				}

				// Save the new free subscription within the transaction
				if err := tx.Create(&freeSubscription).Error; err != nil {
					// Rollback transaction on error
					tx.Rollback()
					return nil, nil, fmt.Errorf("failed to create free subscription: %w", err)
				}

				// Commit the transaction for both expired status update and free plan creation
				if err := tx.Commit().Error; err != nil {
					return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
				}

				// Return the newly created free subscription
				return &freeSubscription, freePlan, nil
			} else {
				// Free plan not found, commit the transaction for the expired status update only
				if err := tx.Commit().Error; err != nil {
					return nil, nil, fmt.Errorf("failed to commit transaction: %w", err)
				}
			}
		}

		// Return error since no active subscription exists anymore and couldn't create a free plan
		return nil, nil, fmt.Errorf("subscription has expired as of %s", subscription.EndDate.Format(time.RFC3339))
	}

	// Get the subscription plan details
	plan, found := models.GetPlanByID(subscription.SubscriptionPlanID)
	if !found {
		return &subscription, nil, fmt.Errorf("invalid subscription plan: %s", subscription.SubscriptionPlanID)
	}

	return &subscription, plan, nil
}
