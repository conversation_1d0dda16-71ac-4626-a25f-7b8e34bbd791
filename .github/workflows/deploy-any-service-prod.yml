name: Deploy Services

on:
  push:
    branches:
      - "deployment/*-service-prod"

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Extract service and environment
        id: extract
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          SERVICE=$(echo $BRANCH_NAME | sed -E 's/deployment\/([a-z]+)-service-.*/\1/')
          echo "SERVICE=$SERVICE" >> $GITHUB_ENV

          echo "Deploying $SERVICE service to dev environment"
      
      - id: "auth"
        uses: "google-github-actions/auth@v1"
        with:
          credentials_json: "${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}"

      - name: Configure Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1

      - name: Connect cluster
        run: |
          gcloud auth configure-docker asia-southeast1-docker.pkg.dev
          gcloud components install gke-gcloud-auth-plugin
          gcloud container clusters get-credentials cluster-nexdor --zone asia-southeast1-a --project friendly-idea-384714

      - name: Build, push and apply docker image
        run: |
          make deploy-${{ env.SERVICE }}-image