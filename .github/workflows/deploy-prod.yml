on:
  push:
    branches: ["master"]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - id: "auth"
        uses: "google-github-actions/auth@v1"
        with:
          credentials_json: "${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}"

      - name: Configure Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1

      - name: Connect cluster
        run: |
          gcloud auth configure-docker asia-southeast1-docker.pkg.dev
          gcloud components install gke-gcloud-auth-plugin
          gcloud container clusters get-credentials cluster-nexdor --zone asia-southeast1-a --project friendly-idea-384714

      - name: Build, push and apply docker image
        run: |
          make deploy-prod
