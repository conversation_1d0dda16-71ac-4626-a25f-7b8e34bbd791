apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
  labels:
    app: email-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-service
  template:
    metadata:
      labels:
        app: email-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: email-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/email-service:latest
          command: ["./email-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /v1/email-service/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: email-service
  labels:
    app: email-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: email-service
---
apiVersion: v1
kind: Service
metadata:
  name: email-lb
  labels:
    app: email-service
spec:
  selector:
    app: email-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
