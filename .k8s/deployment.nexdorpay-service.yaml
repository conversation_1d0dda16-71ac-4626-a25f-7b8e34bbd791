apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexdorpay-service
  labels:
    app: nexdorpay-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nexdorpay-service
  template:
    metadata:
      labels:
        app: nexdorpay-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexdorpay-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexdorpay-service:250228105735
          command: ["./nexdorpay-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 60
            timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nexdorpay-service
  labels:
    app: nexdorpay-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexdorpay-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexdorpay-lb
  labels:
    app: nexdorpay-service
spec:
  selector:
    app: nexdorpay-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
