apiVersion: apps/v1
kind: Deployment
metadata:
  name: zalo-chat-service
  labels:
    app: zalo-chat-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zalo-chat-service
  template:
    metadata:
      labels:
        app: zalo-chat-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: zalo-chat-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/zalo-chat-service:250227172355
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: nexpos-env