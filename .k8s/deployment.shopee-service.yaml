apiVersion: apps/v1
kind: Deployment
metadata:
  name: shopee-service
  labels:
    app: shopee-service
    env: config-map
spec:
  replicas: 10
  selector:
    matchLabels:
      app: shopee-service
  template:
    metadata:
      labels:
        app: shopee-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: shopee-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/shopee-service:250328160428
          # command: ["node","app.js"]
          # args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: shopee-service-lb
  labels:
    app: shopee-service
spec:
  selector:
    app: shopee-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
