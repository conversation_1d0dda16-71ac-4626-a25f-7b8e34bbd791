apiVersion: apps/v1
kind: Deployment
metadata:
  name: ferretdb
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ferretdb
  template:
    metadata:
      labels:
        app: ferretdb
    spec:
      containers:
        - name: ferretdb
          image: ghcr.io/ferretdb/ferretdb-eval:2
          ports:
            - containerPort: 27017
          env:
            - name: POSTGRES_USER
              value: food-app-dev
            - name: POSTGRES_PASSWORD
              value: NEXDORFERRETDB2025
          volumeMounts:
            - name: data
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: ferretdb-data-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ferretdb-data-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi

---
apiVersion: v1
kind: Service
metadata:
  name: ferretdb-service
  namespace: default
spec:
  selector:
    app: ferretdb
  ports:
    - protocol: TCP
      port: 27017
      targetPort: 27017
  type: LoadBalancer
