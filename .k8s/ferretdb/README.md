# FerretDB with PostgreSQL Document DB

This directory contains Kubernetes configuration files for setting up FerretDB with PostgreSQL as a document database backend.

## Components

1. **PostgreSQL with DocumentDB Extension**
   - Uses the `postgres-documentdb` image which includes the DocumentDB extension
   - Automatically initializes the DocumentDB extension on startup
   - Stores data in a persistent volume

2. **FerretDB**
   - MongoDB-compatible interface that uses PostgreSQL as a backend
   - Connects to the PostgreSQL instance using the connection string

## Deployment

To deploy the entire stack:

```bash
# Apply the PostgreSQL init ConfigMap
kubectl apply -f postgres-init-configmap.yaml

# Deploy PostgreSQL
kubectl apply -f postgres.yaml

# Deploy PostgreSQL Service
kubectl apply -f postgres-service.yaml

# Deploy FerretDB
kubectl apply -f ferretdb.yaml
```

## Connection Information

- **MongoDB-compatible endpoint**: `mongodb://localhost:27017` (when port-forwarded)
- **PostgreSQL endpoint**: `postgresql://food-app-dev:NEXDORFERRETDB2025@localhost:5432/postgres` (when port-forwarded)

## Port Forwarding for Local Development

To access FerretDB from your local machine:

```bash
# Forward MongoDB-compatible port
kubectl port-forward svc/ferretdb-service 27017:27017

# Forward PostgreSQL port (if needed for direct access)
kubectl port-forward svc/postgres-ferretdb-service 5432:5432
```

## Usage

You can connect to FerretDB using any MongoDB client or driver. For example:

```javascript
const { MongoClient } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function run() {
  try {
    await client.connect();
    const database = client.db('food-app-dev');
    const collection = database.collection('test');
    
    // Insert a document
    await collection.insertOne({ name: 'Test', value: 123 });
    
    // Query documents
    const results = await collection.find({}).toArray();
    console.log(results);
  } finally {
    await client.close();
  }
}

run().catch(console.error);
```
