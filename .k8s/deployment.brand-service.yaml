apiVersion: apps/v1
kind: Deployment
metadata:
  name: brand-service
  labels:
    app: brand-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: brand-service
  template:
    metadata:
      labels:
        app: brand-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: brand-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/brand-service:250529115254
          command: ["./brand-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /v1/brand-service/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: brand-service
  labels:
    app: brand-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: brand-service
---
apiVersion: v1
kind: Service
metadata:
  name: brand-lb
  labels:
    app: brand-service
spec:
  selector:
    app: brand-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
