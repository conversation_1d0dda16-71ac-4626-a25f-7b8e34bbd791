
helm install mongodb oci://registry-1.docker.io/bitnamicharts/mongodb --set architecture="replicaset" --set replicaSetSize=3
helm upgrade mongodb oci://registry-1.docker.io/bitnamicharts/mongodb --set replicaSetName=rs0 --set replicaSetSize=3 --set externalAccess.enabled=true
helm delete mongodb
kubectl get secret --namespace default mongodb -o jsonpath="{.data.mongodb-root-password}" | base64 -d

kubectl patch service mongodb -p '{"spec": {"type": "LoadBalancer"}}'


db.createUser({user: "admin", pwd: "AdminCyberKit", roles: [{role: "dbOwner", db: "food-app-stag"}]})