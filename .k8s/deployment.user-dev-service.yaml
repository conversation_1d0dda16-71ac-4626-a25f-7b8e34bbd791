apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-dev-service
  labels:
    app: user-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-dev-service
  template:
    metadata:
      labels:
        app: user-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: user-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/user-service:250530105451
          command: ["./user-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
          livenessProbe:
            httpGet:
              path: /v1/user-service/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: user-dev-service
  labels:
    app: user-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: user-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: user-dev-lb
  labels:
    app: user-dev-service
spec:
  selector:
    app: user-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
