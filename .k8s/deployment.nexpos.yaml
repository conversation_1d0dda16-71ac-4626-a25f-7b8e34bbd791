apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-service
  labels:
    app: nexpos-service
    env: config-map
spec:
  replicas: 4
  selector:
    matchLabels:
      app: nexpos-service
  template:
    metadata:
      labels:
        app: nexpos-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:250311081612
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 60
            timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-service
  labels:
    app: nexpos-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-lb
  labels:
    app: nexpos-service
spec:
  selector:
    app: nexpos-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
