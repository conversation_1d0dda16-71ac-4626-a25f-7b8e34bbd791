apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-order-service
spec:
  replicas: 0
  selector:
    matchLabels:
      app: app-order-service
  template:
    metadata:
      labels:
        app: app-order-service
    spec:
      containers:
        - name: app-order-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/app-order-service:230704155400
          envFrom:
            - configMapRef:
                name: nexpos-env
          resources:
            requests:
              memory: "200Mi"
              cpu: "100m"
            limits:
              memory: "200Mi"
              cpu: "200m"

---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: app-order-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-order-service
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 90
