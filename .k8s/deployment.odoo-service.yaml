apiVersion: apps/v1
kind: Deployment
metadata:
  name: odoo-service
spec:
  replicas: 0
  selector:
    matchLabels:
      app: odoo-service
  template:
    metadata:
      labels:
        app: odoo-service
    spec:
      containers:
        - name: odoo-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/odoo-service:240111153317
          envFrom:
            - configMapRef:
                name: nexpos-env
