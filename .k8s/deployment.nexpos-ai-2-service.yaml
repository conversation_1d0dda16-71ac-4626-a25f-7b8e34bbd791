apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-ai-2-service
  labels:
    app: nexpos-ai-2-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nexpos-ai-2-service
  template:
    metadata:
      labels:
        app: nexpos-ai-2-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-ai-2-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos-ai-2-service:241105014309
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 60
            timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-ai-2-service
  labels:
    app: nexpos-ai-2-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-ai-2-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-ai-2-lb
  labels:
    app: nexpos-ai-2-service
spec:
  selector:
    app: nexpos-ai-2-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
