apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb
spec:
  selector:
    matchLabels:
      app: mongodb
  serviceName: mongodb
  replicas: 1
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:5.0
        ports:
          - containerPort: 27017
        volumeMounts:
          - name: mongodb-data
            mountPath: /data/db
        # db.createUser({user: "admin", pwd: "CyberNeposHackme2023***", roles: [{role: "dbOwner", db: "food-app-dev"}]})
            
        command:
          - mongod
          - "--auth"
          - "--bind_ip_all"
        # envFrom:
        #   - configMapRef:
        #       name: app-environment
      volumes:
      - name: mongodb-data
        persistentVolumeClaim:
          claimName: mongodb-data
  volumeClaimTemplates:
  - metadata:
      name: mongodb-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb
spec:
  type: ClusterIP
  selector:
    app: mongodb
  ports:
    - name: mongodb
      port: 27017
      targetPort: 27017
  clusterIP: ***********
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-lb
  labels:
    app: mongodb
spec:
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
  type: LoadBalancer
  loadBalancerIP: "*************"