apiVersion: apps/v1
kind: Deployment
metadata:
  name: merchant-dev-service
  labels:
    app: merchant-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: merchant-dev-service
  template:
    metadata:
      labels:
        app: merchant-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: merchant-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/merchant-service:250331174552
          command: ["./merchant-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
          livenessProbe:
            httpGet:
              path: /v1/merchant-service/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: merchant-dev-service
  labels:
    app: merchant-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: merchant-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: merchant-dev-lb
  labels:
    app: merchant-dev-service
spec:
  selector:
    app: merchant-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
