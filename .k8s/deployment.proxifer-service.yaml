apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxifer-service
  labels:
    app: proxifer-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: proxifer-service
  template:
    metadata:
      labels:
        app: proxifer-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: proxifer-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/proxifer-service:250528101952
          command: ["./proxifer-service"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
---
apiVersion: v1
kind: Service
metadata:
  name: proxifer-service
  labels:
    app: proxifer-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: proxifer-service
---
apiVersion: v1
kind: Service
metadata:
  name: proxifer-lb
  labels:
    app: proxifer-service
spec:
  selector:
    app: proxifer-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
