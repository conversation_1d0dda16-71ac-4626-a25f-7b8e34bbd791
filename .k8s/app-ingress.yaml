apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  annotations:
    # Routing configuration
    nginx.ingress.kubernetes.io/rewrite-target: /$2

    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, x-nexpos-language, x-access-token, isauthorized"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"

    # Request/response configuration
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"

    # SSL configuration
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - host: saas-api.nexpos.io
      http:
        paths:
          - path: /v1/brand-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: brand-service
                port:
                  number: 3000
          - path: /v1/order-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: order-service
                port:
                  number: 3000
          - path: /v1/merchant-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: merchant-service
                port:
                  number: 3000
          - path: /v1/user-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: user-service
                port:
                  number: 3000

    # Development environment
    - host: saas-api-dev.nexpos.io
      http:
        paths:
          - path: /v1/brand-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: brand-dev-service
                port:
                  number: 3000
          - path: /v1/order-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: order-dev-service
                port:
                  number: 3000
          - path: /v1/merchant-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: merchant-dev-service
                port:
                  number: 3000
          - path: /v1/user-service(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: user-dev-service
                port:
                  number: 3000