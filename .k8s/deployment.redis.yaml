apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:latest
        ports:
        - containerPort: 6379
        command: ["redis-server"]
        args: ["--requirepass", "<PERSON>exdor@Redis"]
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP
  clusterIP: ************
---
apiVersion: v1
kind: Service
metadata:
  name: redis-lb
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
    - port: 6379
      targetPort: 6379
  type: LoadBalancer
