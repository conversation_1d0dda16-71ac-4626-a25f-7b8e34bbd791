# Migration from Proxifer Service to Kubernetes Ingress

This document outlines the migration from the custom proxifer-service to Kubernetes Ingress for routing API requests to the appropriate services.

## Why Migrate?

The proxifer-service was a custom solution for routing API requests to different backend services. However, Kubernetes Ingress provides a more standardized, scalable, and maintainable solution with the following benefits:

1. **Native Kubernetes Integration**: Ingress is a native Kubernetes resource, making it easier to manage with standard Kubernetes tools.
2. **Better Scalability**: Ingress controllers are designed to handle high traffic loads and scale automatically.
3. **Advanced Features**: Ingress provides built-in support for SSL termination, path-based routing, and name-based virtual hosting.
4. **Simplified Configuration**: Ingress configurations are declarative and version-controlled.
5. **Reduced Maintenance**: No need to maintain a custom proxy service.

## Migration Steps

The migration process involves the following steps:

1. **Deploy Default Backend**: A custom error backend that returns standardized JSON error responses.
2. **Apply Ingress Configuration**: Configure the Ingress resource to route traffic to the appropriate services.
3. **Scale Down Proxifer**: Gradually reduce traffic to the proxifer-service.
4. **Verify and Monitor**: Ensure the Ingress is working correctly.
5. **Clean Up**: Remove the proxifer-service once the migration is complete.

## Configuration Details

### Ingress Configuration

The Ingress configuration includes:

- **Path-based routing** to direct requests to the appropriate service
- **Host-based routing** to separate production and development environments
- **CORS configuration** to handle cross-origin requests
- **Error handling** with custom error responses
- **TLS configuration** for secure connections

### Default Backend

The default backend provides standardized JSON error responses for various HTTP error codes, ensuring a consistent API experience even when errors occur.

## Rollback Plan

If issues are encountered during the migration, you can roll back by:

1. Scaling up the proxifer-service: `kubectl scale deployment proxifer-service --replicas=1`
2. Scaling up the proxifer-dev-service: `kubectl scale deployment proxifer-dev-service --replicas=1`
3. Removing the Ingress: `kubectl delete ingress app-ingress`
4. Removing the default backend: `kubectl delete -f default-backend.yaml`

## Testing

Before completing the migration, test the following endpoints:

- Production: `https://api.nexpos.io/v1/brand-service/health`
- Development: `https://api-dev.nexpos.io/v1/brand-service/health`

Ensure that all services are accessible and returning the expected responses.

## Automation

A script is provided to automate the migration process:

```bash
.k8s/migrate-to-ingress.sh
```

This script will:
1. Apply the default backend
2. Apply the Ingress configuration
3. Scale down the proxifer services
4. Verify the Ingress status

## Post-Migration Tasks

After the migration is complete:

1. Update DNS records if necessary
2. Monitor the Ingress for any issues
3. Consider removing the proxifer-service deployments entirely once stability is confirmed
