apiVersion: apps/v1
kind: Deployment
metadata:
  name: merchant-service
  labels:
    app: merchant-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: merchant-service
  template:
    metadata:
      labels:
        app: merchant-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: merchant-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/merchant-service:250331174552
          command: ["./merchant-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: merchant-service
  labels:
    app: merchant-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: merchant-service
---
apiVersion: v1
kind: Service
metadata:
  name: merchant-lb
  labels:
    app: merchant-service
spec:
  selector:
    app: merchant-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
