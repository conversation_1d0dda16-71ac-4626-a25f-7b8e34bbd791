apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-dev-service
  labels:
    app: nexpos-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nexpos-dev-service
  template:
    metadata:
      labels:
        app: nexpos-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:250311081612
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-dev-service
  labels:
    app: nexpos-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-dev-lb
  labels:
    app: nexpos-dev-service
spec:
  selector:
    app: nexpos-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
