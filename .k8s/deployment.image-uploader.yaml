apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-uploader-service
  labels:
    app: image-uploader-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-uploader-service
  template:
    metadata:
      labels:
        app: image-uploader-service
    spec:
      containers:
        - name: image-uploader-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/image-uploader:latest
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          env:
            - name: PORT
              value: "3000"
          ports:
            - containerPort: 3000
---
apiVersion: v1
kind: Service
metadata:
  name: image-uploader-service
  labels:
    app: image-uploader-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: image-uploader-service
---
apiVersion: v1
kind: Service
metadata:
  name: image-uploader-lb
  labels:
    app: image-uploader-service
spec:
  selector:
    app: image-uploader-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
