apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxifer-dev-service
  labels:
    app: proxifer-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: proxifer-dev-service
  template:
    metadata:
      labels:
        app: proxifer-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: proxifer-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/proxifer-service:250401224437
          command: ["./proxifer-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
---
apiVersion: v1
kind: Service
metadata:
  name: proxifer-dev-service
  labels:
    app: proxifer-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: proxifer-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: proxifer-dev-lb
  labels:
    app: proxifer-dev-service
spec:
  selector:
    app: proxifer-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
