apiVersion: apps/v1
kind: Deployment
metadata:
  name: subscribe-dev-service
  labels:
    app: subscribe-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: subscribe-dev-service
  template:
    metadata:
      labels:
        app: subscribe-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: subscribe-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/subscribe-service:240924071522
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: nexpos-dev-env