apiVersion: apps/v1
kind: Deployment
metadata:
  name: shopee-otp-service
  labels:
    app: shopee-otp-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shopee-otp-service
  template:
    metadata:
      labels:
        app: shopee-otp-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: shopee-otp-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/shopee-otp-service:230614080656
          # command: ["node","app.js"]
          # args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000  
          envFrom:
            - configMapRef:
                name: nexpos-env     
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10       
---
apiVersion: v1
kind: Service
metadata:
  name: shopee-otp-service-lb
  labels:
    app: shopee-otp-service
spec:
  selector:
    app: shopee-otp-service
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer