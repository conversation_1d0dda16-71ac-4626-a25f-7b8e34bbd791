apiVersion: apps/v1
kind: Deployment
metadata:
  name: subscribe-service
  labels:
    app: subscribe-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: subscribe-service
  template:
    metadata:
      labels:
        app: subscribe-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: subscribe-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/subscribe-service:241220142241
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: nexpos-uat-env