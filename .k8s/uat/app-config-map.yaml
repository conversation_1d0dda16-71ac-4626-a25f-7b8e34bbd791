apiVersion: v1
kind: ConfigMap
metadata:
  name: nexpos-uat-env
  labels:
    env: config-map
data:
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: C3lRwZ7HC9h84Tn1VUJP21W/s9WP9Tw4vbrKXJgv
  API_BASE: https://api-uat.nexpos.io
  API_PUB_BASE: https://api-pub-uat.nexpos.io
  NODE_ENV: uat
  DEBUG: "false"
  USE_MERCHANT_APPS: "false"
  MONGODB_URI: "mongodb+srv://nexdor:*******/food-app?retryWrites=true&w=majority&appName=nexdor-uat-cluster"
  JWT_SECRET: "cyber@hackme"
  SQS_QUEUE_URL: https://sqs.ap-southeast-1.amazonaws.com/************/nexpos-sqs-fetch-site-orders-uat
  REGION: ap-southeast-1
  GOOGLE_PRIVATE_KEY: '{"type":"service_account","project_id":"friendly-idea-384714","private_key_id":"b3c535ee9cf480c656cd464c8d652d4236040fe1","private_key":"-----BEGIN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCNUVw4BMsnjg5m\\naRdJATblx+RJcRdgcjR13Tur+s1ciMCCgr8kwvzMThqbFMukymRiwP3zNMoLuNjT\\nI7WeP15TZ78WG24fZeSSUuRnVa7mojyKq3eOghsf+UaUmpDkq0RsNQRRvpGZM2mL\\nPmy0hueQ02VNKVCu3RNwotdhbw+h+vt867Zi4+ugYHflB9xZH+cJE28L8HvkPZ0I\\narN8tzUrJDfCooCZ0KU+ySBBO+GU/31aUtj7iOVrihrWH0JQPkt/cx6ru994/jZY\\nW+HmTh+f8JDucqKpGRn1iIjfeI/VAvXJ3+045qiQ3xyHafYjs8id83cEX0jGB6gL\\nN6Yyx0UrAgMBAAECggEAEyyC0qbz2X4QvhxsKF+dzzd75esOPOvB6mwF+KOi0XdM\\nh2it/pLmMeI3AeukZcuAK1X8Hs9+ynBeGuexcLMwXSiqaJl4K5JrnN7aVprtjCVf\\nCYGSSyej/oaX6Pm/xwbl1biryVuN7bdoDph9r1DOgnvsNrfwVolwZcvyZSnoeRWd\\nBBtRwpVALe1BOP5Dnl/tqahq24dV4sZXbQ1Bs/e8c3IzK+zZBaOOpFZ7u7Kc+nD9\\n9FvCZviwkHiCvz4Qc65AVhAbca55tYkF7evfgwt++Fv3JfNIenqVVsBvVZtJZ9oK\\ndP++AZ2g7306IGuwmJqXahWLzwCIqu/fN8zuiz0TYQKBgQC/nOg0KzMkUoG+DS2a\\n20eWxyfocmTiohzmof7ZFGdbhLJZGK0ZnMEDkP/QqnBtBqnWN3G0SapFbkhGgbJm\\nPOLdTQpo2uLHv24SDdFutwEggNyIagUVGD3BT8GYPKdth9/E5sdwFb8Q2uUfIwR/\\nV+YWhuuP7bsvCXaYJdFQP5AjpwKBgQC8zeyJw2hg822p4uwz/9+RRu1afsPe+/0u\\nEYdphAYizNTyaQIK54JwDEzSO4VNt4FJePg2AVsgPmgVv2Keh7StwYotpUMsG5m3\\nJnGmO05SCmFR0aFOD+escgTzdMrxnRMAjDOx9N7388FI47XucEZh61uRvURpjrex\\nlvjAWktS3QKBgB69e3szGwdOvUXSM2jOmROVa8+c2paJT32oq4wD+/jwtvQYQ10C\\niuyZjJDbZyH2mbSmPHWiA9ETukClegtw5Sx/uZXM1+kXyv7WfKz4axPd2IOCDsKt\\nmuqycuqLgIjP7ZRXOdO/iWGY3H08TvJnFaCzkIMMrqUDLaWHG6d2KxetAoGAPgXD\\ns4neyyKMnudtwrNGpTnQlQ7Zl3HePPkDK9V0EpCJT8zRH/XncpmHzvRAmUQ0E4fK\\nuir93XzCwoopgnyADShsFQyaIW47s9/MK9iFpQvF5pPf0n4FFDAskjGGzZJVALQN\\n82LGsFuB3kj3pgl6BYbEgP7U73wzugInW0EOR30CgYBQx82Hj/aJmNvfMhe+iZ9V\\n+9/3lEEpY/OQcFWB+BYdFsijQVHBUxAITgmtGtbbnsMNcApBIGPJtLRWgGthrI/N\\nkIS323fzX5UsT9FiBy5+DfvDEZ1RojBzW2B2KSpna4xZdXZGqUnBGNmKfDcU5zeZ\\nEQrL3MxmH7+RP+kue3H0Zg==\\n-----END PRIVATE KEY-----\\n","client_email":"*******","client_id":"115884187507099292496","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/full-access%40friendly-idea-384714.iam.gserviceaccount.com"}'
  PUBSUB_TOPIC_GET_TOKENS: PUBSUB_TOPIC_GET_TOKENS_UAT
  PUBSUB_TOPIC_GET_ORDERS: PUBSUB_TOPIC_GET_ORDERS_UAT
  PUBSUB_TOPIC_GET_TEST_ORDERS: PUBSUB_TOPIC_GET_TEST_ORDERS_UAT
  PUBSUB_TOPIC_GET_HEALTH: PUBSUB_TOPIC_GET_HEALTH_UAT
  PUBSUB_SUB_GET_TOKENS: PUBSUB_SUB_GET_TOKENS_UAT
  PUBSUB_SUB_GET_ORDERS: PUBSUB_SUB_GET_ORDERS_UAT
  PUBSUB_SUB_GET_TEST_ORDERS: PUBSUB_SUB_GET_TEST_ORDERS_UAT
  PUBSUB_SUB_GET_HEALTH: PUBSUB_SUB_GET_HEALTH_UAT
  ODOO_URL: http://66.29.131.103:8000
  ODOO_DATABASE: odoo_uat
  ODOO_USER: *******
  ODOO_PASSWORD: Admin@123
  WEB_URL: https://beta.nexpos.io
  STORE_WEB_URL: https://he-uat.nexpos.io
  PORTAL_WEB_URL: https://portal-dev.nexpos.io
  SENDGRID_API_KEY: *********************************************************************
  INTERNAL_API_KEY: nDRPPJ4E3xM2XMIH
  MOMO_PAYMENT_URL: https://test-payment.momo.vn
  MOMO_PARTNER_CODE: MOMOROMM20240105_TEST
  MOMO_ACCESS_KEY: mftGmzyz8mkFnlf1
  MOMO_SECRET_KEY: ydHoXHlJOyfBJMI0kLu6G6Wz6cqZoXu4
  GOOGLE_MAP_API_KEY: AIzaSyAwwYVOfRtAdgtdg2cFaMUWcHFkWhutn3U
  NUTIFOOD_URL: http://************:3335
  NUTIFOOD_API_KEY: vARdlifBmxJi1Vc8yW1xUnszwhtVTqIJwam29l3CQMIrF7sakOCUhGffxDv9LahRYuS9y5OTgsKPHXGuBlYfBbFQYBnDzKCtWbb9O65dz2TU5ZG5hphwrSwvDNv5TkYSXkTEVpx8zuYIAH6pbxEI7gF/ztd2g7/Nk9tsSQLY81OYkZuXpI/gKC3iPDLKQ4f7IOpeXD3j+lHzfyHJJihTyw5Y6eDWEKE8IUl7gaaR9bfaCnccmZU05GxO23oUuNhk0RHv78BS9kjG5Fluh6rgKLWbUSnqTM6U00zklrytBm32cto5zG7wBH0P8i/VbKsAtAGulV73Y3Ui/6rw0jom2A==
  REDIS_URI: redis://*************:6379
  REDIS_PASSWORD: Nexdor@Redis
  GMAIL_USER: *******
  GMAIL_APP_PASSWORD: vxol wngc lblc nrua
  CAKE_ACCOUNT: "**********"
  NEXDORPAY_PAYMENT_URL: https://pay.nexdor.tech
  COOKIE_TOKEN_KEY: __token
  CORS_ORIGIN: https://uat.nexpos.io,https://oms-uat.nexpos.io,https://he-uat.nexpos.io,https://pms-uat.nexpos.io
