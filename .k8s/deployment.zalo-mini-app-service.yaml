apiVersion: apps/v1
kind: Deployment
metadata:
  name: zalo-mini-app-service
  labels:
    app: zalo-mini-app-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zalo-mini-app-service
  template:
    metadata:
      labels:
        app: zalo-mini-app-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: zalo-mini-app-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/zalo-mini-app-service:240823133857
          command: ["./zalo-mini-app-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: zalo-mini-app-service
  labels:
    app: zalo-mini-app-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: zalo-mini-app-service
---
apiVersion: v1
kind: Service
metadata:
  name: zalo-mini-app-lb
  labels:
    app: zalo-mini-app-service
spec:
  selector:
    app: zalo-mini-app-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
