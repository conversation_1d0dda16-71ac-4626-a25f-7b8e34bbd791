minikube start
minikube addons enable ingress
ssh leo@35.213.141.133 -L 27017:192.168.49.2:30000 -N
kubectl create secret docker-registry regcred --docker-server=gcr.io --docker-username=_json_key --docker-password="$(cat certification.json)" --docker-email=<EMAIL>;

mongodump --uri="mongodb+srv://admin:<EMAIL>/foodapp?retryWrites=true&w=majority" --archive=dump

mongorestore --uri="*************************************************************************************" --archive=dump --nsFrom "foodapp.*" --nsTo "food-app-dev.*"  --nsInclude="*"

kubectl -n kubernetes-dashboard create token admin-user