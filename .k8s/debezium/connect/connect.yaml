apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-connect
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-connect
  template:
    metadata:
      labels:
        app: kafka-connect
    spec:
      containers:
      - name: kafka-connect
        image: debezium/connect:3.0.0.Final
        imagePullPolicy: Always
        ports:
        - containerPort: 8083
        env:
        # Core configs
        - name: BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: GROUP_ID
          value: "1"
        - name: CONFIG_STORAGE_TOPIC
          value: "connect-configs"
        - name: OFFSET_STORAGE_TOPIC
          value: "connect-offsets"
        - name: STATUS_STORAGE_TOPIC
          value: "connect-status"
        - name: CONNECT_PLUGIN_PATH
          value: "/kafka/connect"

        # REST API configs
        - name: CONNECT_REST_ADVERTISED_HOST_NAME
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: CONNECT_REST_PORT
          value: "8083"

        # Converter configs
        - name: KEY_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: VALUE_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "true"
        - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "true"

        # Logging configs
        - name: CONNECT_LOG4J_ROOT_LOGLEVEL
          value: "DEBUG"
        - name: CONNECT_LOG4J_LOGGERS
          value: "org.apache.kafka.connect=DEBUG,org.reflections=ERROR"
        
        # Performance configs
        - name: CONNECT_OFFSET_FLUSH_INTERVAL_MS
          value: "10000"
        - name: CONNECT_OFFSET_FLUSH_TIMEOUT_MS
          value: "5000"
        - name: CONNECT_MAX_REQUEST_SIZE
          value: "10485760"  # 10MB
        
        # Error handling
        - name: CONNECT_ERRORS_TOLERANCE
          value: "all"
        - name: CONNECT_ERRORS_LOG_ENABLE
          value: "true"
        - name: CONNECT_ERRORS_LOG_INCLUDE_MESSAGES
          value: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-connect
spec:
  ports:
  - port: 8083
    targetPort: 8083
  selector:
    app: kafka-connect