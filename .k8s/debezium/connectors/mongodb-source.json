{
    "name": "mongodb-source-connector",
    "config": {
        "connector.class": "io.debezium.connector.mongodb.MongoDbConnector",
        "mongodb.connection.string": "mongodb+srv://nexdor:<EMAIL>/food-app?maxPoolSize=50&minPoolSize=10&maxIdleTimeMS=30000",
        "topic.prefix": "mongo",
        "database.include.list": "food-app",
        "collection.include.list": "food-app.*",
        "key.converter": "org.apache.kafka.connect.json.JsonConverter",
        "value.converter": "org.apache.kafka.connect.json.JsonConverter",
        "key.converter.schemas.enable": "true",
        "value.converter.schemas.enable": "true",
        "transforms": "unwrap,addTopicPrefix",
        "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
        "transforms.unwrap.drop.tombstones": "false",
        "transforms.addTopicPrefix.type": "org.apache.kafka.connect.transforms.RegexRouter",
        "transforms.addTopicPrefix.regex": ".*",
        "transforms.addTopicPrefix.replacement": "mongo.$0",
        "schema.history.internal.kafka.bootstrap.servers": "kafka:9092",
        "schema.history.internal.kafka.topic": "schema-changes.food"
    }
}

{
    "name": "postgresql-sink-connector",
    "config": {
        "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
        "tasks.max": "1",
        "connection.url": "**********************************************",
        "connection.user": "postgres",
        "connection.password": "NexdorAdminPGDB2024",
        "topics.regex": "mongo\\.food-app\\.(.*)",
        "table.name.format": "${topic}",
        "insert.mode": "upsert",
        "pk.fields": "_id",
        "pk.mode": "record_key",
        "auto.create": "true",
        "auto.evolve": "true",
        "key.converter": "org.apache.kafka.connect.json.JsonConverter",
        "value.converter": "org.apache.kafka.connect.json.JsonConverter",
        "transforms": "route,unwrap",
        "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
        "transforms.route.type": "org.apache.kafka.connect.transforms.RegexRouter",
        "transforms.route.regex": "mongo\\.food-app\\.(.*)",
        "transforms.route.replacement": "$1"
    }
}