apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  name: postgresql-sink-connector
  labels:
    strimzi.io/cluster: kafka-connect
spec:
  class: io.confluent.connect.jdbc.JdbcSinkConnector
  tasksMax: 1
  config:
    connection.url: "**********************************************"
    connection.user: "postgres"
    connection.password: "NexdorAdminPGDB2024"
    topics: "dbserver1.food-app.*"
    auto.create: "true"
    insert.mode: "upsert"
    pk.mode: "record_value"
    pk.fields: "_id"  # MongoDB's primary key
    transforms: "unwrap"
    transforms.unwrap.type: "io.debezium.transforms.ExtractNewRecordState"
    transforms.unwrap.drop.tombstones: "false"
    db.timezone: "UTC"
    auto.evolve: "true"
    table.name.format: "${topic}"