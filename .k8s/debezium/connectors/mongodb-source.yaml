apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  name: mongodb-source-connector
  labels:
    strimzi.io/cluster: kafka-connect
spec:
  class: io.debezium.connector.mongodb.MongoDbConnector
  tasksMax: 1
  config:
    mongodb.connection.string: "mongodb+srv://nexdor:<EMAIL>/food-app?maxPoolSize=50&minPoolSize=10&maxIdleTimeMS=30000"
    mongodb.name: "dbserver1"
    database.include.list: "food-app"
    collection.include.list: "food-app.*" 
    key.converter: "org.apache.kafka.connect.json.JsonConverter"
    value.converter: "org.apache.kafka.connect.json.JsonConverter"
    key.converter.schemas.enable: "true"
    value.converter.schemas.enable: "true"
    heartbeat.interval.ms: "5000"
    snapshot.mode: "initial"
    transforms: "unwrap"
    transforms.unwrap.type: "io.debezium.transforms.ExtractNewRecordState"
    transforms.unwrap.drop.tombstones: "false"