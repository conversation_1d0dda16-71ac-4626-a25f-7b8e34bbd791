{"name": "postgresql-sink-connector", "config": {"connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector", "tasks.max": "1", "connection.url": "jdbc:postgresql://<postgres-host>:5432/<database>", "connection.user": "<user>", "connection.password": "<password>", "topics.regex": "mongo\\.food-app\\.(.*)", "table.name.format": "${topic}", "insert.mode": "upsert", "pk.fields": "_id", "pk.mode": "record_key", "auto.create": "true", "auto.evolve": "true", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "transforms": "route,unwrap", "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState", "transforms.route.type": "org.apache.kafka.connect.transforms.RegexRouter", "transforms.route.regex": "mongo\\.<your-database>\\.(.*)", "transforms.route.replacement": "$1"}}