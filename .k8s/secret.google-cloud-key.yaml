apiVersion: v1
kind: Secret
metadata:
  name: google-cloud-key
type: Opaque
stringData:
  key.json: |
    # Replace this with your actual Google Cloud service account key JSON
    # You should apply this secret separately with the actual key content
    # Example:
    # {
    #   "type": "service_account",
    #   "project_id": "your-project-id",
    #   "private_key_id": "key-id",
    #   "private_key": "-----BEGIN PRIVATE KEY-----\nkey-content\n-----END PRIVATE KEY-----\n",
    #   "client_email": "<EMAIL>",
    #   "client_id": "client-id",
    #   "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    #   "token_uri": "https://oauth2.googleapis.com/token",
    #   "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    #   "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/service-account%40project-id.iam.gserviceaccount.com"
    # }
