apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-ai-service
  labels:
    app: nexpos-ai-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nexpos-ai-service
  template:
    metadata:
      labels:
        app: nexpos-ai-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-ai-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos-ai-service:241129103522
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 60
            timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-ai-service
  labels:
    app: nexpos-ai-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-ai-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-ai-lb
  labels:
    app: nexpos-ai-service
spec:
  selector:
    app: nexpos-ai-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
