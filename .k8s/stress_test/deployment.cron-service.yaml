apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron-service
spec:
  replicas: 1 # Don't change it, cron only need 1
  selector:
    matchLabels:
      app: cron-service
  template:
    metadata:
      labels:
        app: cron-service
    spec:
      containers:
        - name: cron-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/cron-service:230621150951
          envFrom:
            - configMapRef:
                name: nexpos-stag-env
