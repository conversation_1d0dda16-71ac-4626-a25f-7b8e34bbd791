apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-order-service
spec:
  replicas: 5
  selector:
    matchLabels:
      app: app-order-service
  template:
    metadata:
      labels:
        app: app-order-service
    spec:
      containers:
        - name: app-order-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/app-order-service:230621152322
          envFrom:
            - configMapRef:
                name: nexpos-stag-env
          # resources:
          #   requests:
          #     memory: "300Mi"
          #     cpu: "200m"
          #   limits:
          #     memory: "300Mi"
          #     cpu: "200m"
---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: app-order-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-order-service
  minReplicas: 20
  maxReplicas: 50
  targetCPUUtilizationPercentage: 90
