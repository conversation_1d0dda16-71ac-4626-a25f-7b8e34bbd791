apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-stag-service
  labels:
    app: nexpos-stag-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nexpos-stag-service
  template:
    metadata:
      labels:
        app: nexpos-stag-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-stag-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:230621145428
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-stag-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-stag-service
  labels:
    app: nexpos-stag-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-stag-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-stag-lb
  labels:
    app: nexpos-stag-service
spec:
  selector:
    app: nexpos-stag-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
