apiVersion: apps/v1
kind: Deployment
metadata:
  name: nexpos-pub-service
  labels:
    app: nexpos-pub-service
    env: config-map
spec:
  replicas: 5
  selector:
    matchLabels:
      app: nexpos-pub-service
  template:
    metadata:
      labels:
        app: nexpos-pub-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: nexpos-pub-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:250311081612
          command: ["node", "app.js"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env

---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-pub-service
  labels:
    app: nexpos-pub-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: nexpos-pub-service
---
apiVersion: v1
kind: Service
metadata:
  name: nexpos-pub-lb
  labels:
    app: nexpos-pub-service
spec:
  selector:
    app: nexpos-pub-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
