# PostgreSQL Database Backup System

This system automatically backs up the PostgreSQL database to Google Cloud Storage every hour.

## Features

- Hourly backups of the PostgreSQL database
- Automatic upload to Google Cloud Storage
- Backup rotation (keeps 7 days of hourly backups)
- Compressed backups to save storage space

## Setup Instructions

### 1. Create a Google Cloud Storage Bucket

If you don't already have a bucket for database backups, create one:

```bash
gcloud storage buckets create gs://nexpos-db-backups --location=asia-southeast1
```

### 2. Create a Service Account for Backups

Create a service account with permissions to write to the storage bucket:

```bash
# Create service account
gcloud iam service-accounts create postgres-backup-sa --display-name="PostgreSQL Backup Service Account"

# Grant storage permissions
gcloud storage buckets add-iam-policy-binding gs://nexpos-db-backups \
  --member="serviceAccount:postgres-backup-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/storage.objectAdmin"

# Create and download key
gcloud iam service-accounts keys create key.json --iam-account=postgres-backup-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### 3. Create Kubernetes Secret for Google Cloud Key

Create a Kubernetes secret with the service account key:

```bash
kubectl create secret generic google-cloud-key --from-file=key.json=./key.json
```

### 4. Create Kubernetes Secret for PostgreSQL Password

```bash
kubectl create secret generic postgres-backup-secrets --from-literal=POSTGRES_PASSWORD=YOUR_POSTGRES_PASSWORD
```

### 5. Apply the CronJob Configuration

```bash
kubectl apply -f .k8s/cronjob.postgres-backup.yaml
```

## Manual Backup

To trigger a manual backup, you can create a job from the CronJob:

```bash
kubectl create job --from=cronjob/postgres-backup manual-backup-$(date +%s)
```

## Restore from Backup

To restore from a backup, download the backup file from Google Cloud Storage and restore it:

```bash
# List available backups
gsutil ls gs://nexpos-db-backups/

# Download a specific backup
gsutil cp gs://nexpos-db-backups/postgres-backup-YYYYMMDD-HHMMSS.sql.gz .

# Restore the backup
gunzip -c postgres-backup-YYYYMMDD-HHMMSS.sql.gz | PGPASSWORD=YOUR_PASSWORD psql -h YOUR_HOST -U YOUR_USER -d YOUR_DATABASE
```

## Monitoring

You can check the status of backup jobs with:

```bash
kubectl get jobs
kubectl get pods
kubectl logs -l job-name=postgres-backup-<job-id>
```
