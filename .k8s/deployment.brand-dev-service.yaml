apiVersion: apps/v1
kind: Deployment
metadata:
  name: brand-dev-service
  labels:
    app: brand-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: brand-dev-service
  template:
    metadata:
      labels:
        app: brand-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: brand-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/brand-service:250326233105
          command: ["./brand-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
          livenessProbe:
            httpGet:
              path: /v1/brand-service/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: brand-dev-service
  labels:
    app: brand-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: brand-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: brand-dev-lb
  labels:
    app: brand-dev-service
spec:
  selector:
    app: brand-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
