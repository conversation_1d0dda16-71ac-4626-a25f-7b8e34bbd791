apiVersion: apps/v1
kind: Deployment
metadata:
  name: inventory-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inventory-service
  template:
    metadata:
      labels:
        app: inventory-service
    spec:
      containers:
        - name: inventory-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/inventory-service:230721103837
          envFrom:
            - configMapRef:
                name: nexpos-env
