#!/bin/bash

# This script migrates from the proxifer-service to Kubernetes Ingress

# Set variables
NAMESPACE="default"
KUBECTL="kubectl"

# Print header
echo "=== Migrating from proxifer-service to Kubernetes Ingress ==="
echo

# Step 1: Apply the default backend for error handling
echo "Step 1: Applying default backend for error handling..."
$KUBECTL apply -f default-backend.yaml
echo "Default backend applied."
echo

# Step 2: Apply the updated Ingress configuration
echo "Step 2: Applying updated Ingress configuration..."
$KUBECTL apply -f app-ingress.yaml
echo "Ingress configuration applied."
echo

# Step 3: Scale down the proxifer service
echo "Step 3: Scaling down proxifer services..."
$KUBECTL scale deployment proxifer-service --replicas=0
$KUBECTL scale deployment proxifer-dev-service --replicas=0
echo "Proxifer services scaled down."
echo

# Step 4: Verify the Ingress is working
echo "Step 4: Verifying Ingress configuration..."
echo "Checking Ingress status:"
$KUBECTL get ingress app-ingress
echo
echo "Checking default backend status:"
$KUBECTL get deployment default-http-backend
$KUBECTL get service default-http-backend
echo

# Step 5: Print completion message
echo "=== Migration completed ==="
echo
echo "The system has been migrated from proxifer-service to Kubernetes Ingress."
echo "To verify the migration, try accessing the following endpoints:"
echo "  - Production: https://api.nexpos.io/v1/brand-service/health"
echo "  - Development: https://api-dev.nexpos.io/v1/brand-service/health"
echo
echo "If you encounter any issues, you can roll back by running:"
echo "  kubectl scale deployment proxifer-service --replicas=1"
echo "  kubectl scale deployment proxifer-dev-service --replicas=1"
echo "  kubectl delete ingress app-ingress"
echo "  kubectl delete -f default-backend.yaml"
echo
