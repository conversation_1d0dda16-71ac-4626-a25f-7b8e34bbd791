apiVersion: apps/v1
kind: Deployment
metadata:
  name: inventory-dev-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inventory-dev-service
  template:
    metadata:
      labels:
        app: inventory-dev-service
    spec:
      containers:
        - name: inventory-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/inventory-service:230810164558
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
