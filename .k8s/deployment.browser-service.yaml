apiVersion: apps/v1
kind: Deployment
metadata:
  name: browser-service
  labels:
    app: browser-service
    env: config-map
spec:
  replicas: 3
  selector:
    matchLabels:
      app: browser-service
  template:
    metadata:
      labels:
        app: browser-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: browser-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/browser-service:241109215754
          # command: ["node","app.js"]
          # args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: browser-service-lb
  labels:
    app: browser-service
spec:
  selector:
    app: browser-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
