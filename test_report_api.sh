#!/bin/bash

# Get the current date in RFC3339 format
START_DATE=$(date -d "yesterday" "+%Y-%m-%dT00:00:00Z")
END_DATE=$(date "+%Y-%m-%dT23:59:59Z")

# Replace with a valid hub_id from your database
HUB_ID="your_hub_id"

# Replace with a valid access token
ACCESS_TOKEN="your_access_token"

# Make the API request
curl -X GET "http://localhost:3000/v1/brand-service/reports?hub_id=${HUB_ID}&start_time=${START_DATE}&end_time=${END_DATE}" \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  -H "Content-Type: application/json" \
  -v
