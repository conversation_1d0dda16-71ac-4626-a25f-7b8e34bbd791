FROM golang:1.23rc2-alpine3.19 AS builder

WORKDIR /app

COPY ./golang-services/go.mod .
COPY ./golang-services/go.sum .
RUN go mod download

COPY ./golang-services .
RUN apk add --no-cache nodejs npm

RUN cd nexdorpay-service && go build -o nexdorpay-service .
RUN cd nexdorpay-service/web && npm install && npm run build

FROM alpine:3.12
RUN apk add ca-certificates
WORKDIR /app
COPY --from=builder /app/nexdorpay-service/nexdorpay-service .
COPY --from=builder /app/nexdorpay-service/web/build ./web/build