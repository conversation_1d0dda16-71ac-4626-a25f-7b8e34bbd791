FROM alpine:latest
RUN apk --no-cache add nodejs npm
WORKDIR /app

COPY ./nodejs-services/nexpos-service/package.json .
RUN npm install --only=production

COPY ./nodejs-services/nexpos-service ./nexpos-service
COPY ./nodejs-services/.shared ./.shared

RUN cd ./.shared && npm install --only=production

WORKDIR /app/nexpos-service

ENV PORT=3000
# Expose the desired port for the API
EXPOSE 3000

# Start the API
CMD [ "node", "app.js" ]