FROM node:18-slim

# Create a non-root user for security
RUN groupadd -r pptruser && useradd -r -g pptruser -G audio,video pptruser \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R pptruser:pptruser /home/<USER>

# Accept EULA for Microsoft fonts
RUN echo "ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula select true" | debconf-set-selections

# Install dependencies required for Puppeteer and fonts
RUN echo "deb http://deb.debian.org/debian stable main contrib" > /etc/apt/sources.list \
    && echo "ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula select true" | debconf-set-selections \
    && apt-get update && apt-get install -y \
    chromium \
    fonts-ipafont-gothic \
    fonts-wqy-zenhei \
    fonts-thai-tlwg \
    fonts-kacst \
    fonts-symbola \
    libfreetype6 \
    fonts-noto-color-emoji \
    fonts-freefont-ttf \
    wget \
    cabextract \
    msttcorefonts \
    --no-install-recommends \
    && fc-cache -f \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set Puppeteer environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium \
    NODE_ENV=production

# Set the working directory
WORKDIR /app

# Copy package files
COPY ./nodejs-services/browser-service/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy the application code
COPY --chown=pptruser:pptruser ./nodejs-services/browser-service/ .

# Switch to non-root user
USER pptruser

# Expose port 3000
EXPOSE 3000

# Run the application
CMD ["node", "app.js"]