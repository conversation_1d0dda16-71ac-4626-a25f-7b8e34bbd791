FROM alpine:latest
RUN apk --no-cache add nodejs npm
WORKDIR /app

COPY ./nodejs-services/nexpos-ai-service/package.json .
RUN npm install --only=production

COPY ./nodejs-services/nexpos-ai-service ./nexpos-ai-service
COPY ./nodejs-services/.shared ./.shared

RUN cd ./.shared && npm install --only=production

WORKDIR /app/nexpos-ai-service

ENV PORT=3000
EXPOSE 3000

# Start the API
CMD [ "node", "app.js" ]