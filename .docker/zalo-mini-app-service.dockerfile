FROM golang:1.20.0-alpine AS builder

WORKDIR /app

COPY ./golang-services/go.mod .
COPY ./golang-services/go.sum .
RUN go mod download

COPY ./golang-services .

RUN cd zalo-mini-app-service && go build -o zalo-mini-app-service .

FROM alpine:3.12
RUN apk add ca-certificates
WORKDIR /app
COPY --from=builder /app/zalo-mini-app-service/zalo-mini-app-service .
COPY --from=builder /app/zalo-mini-app-service/certification.json .