FROM alpine:latest

# Install required dependencies
RUN apk add --no-cache \
    nodejs npm \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Puppeteer environment variables
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY ./micro-services/node-services/shopee-otp-service/package.json .

# Install dependencies
RUN npm install

# Copy the application code
COPY ./micro-services/node-services/shopee-otp-service/ .

# Expose port 3000
EXPOSE 3000

# Run the application
CMD ["node", "app.js"]