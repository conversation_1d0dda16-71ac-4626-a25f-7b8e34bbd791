version: '3.1'
services:
  odoo_dev:
    container_name: odoo_dev
    image: odoo:16.0
    depends_on:
      - db_dev
    ports:
      - '8000:8069'
    volumes:
      - ./odoo_dev_addons:/mnt/extra-addons
    environment:
      - HOST=db_dev
      - USER=odoo
      - PASSWORD=odoonexposdotvn
  odoo_prod:
    container_name: odoo_prod
    image: odoo:16.0
    depends_on:
      - db_prod
    ports:
      - '8001:8069'
    volumes:
      - ./odoo_prod_addons:/mnt/extra-addons
    environment:
      - HOST=db_prod
      - USER=odoo
      - PASSWORD=odoonexposdotvn
  db_prod:
    container_name: db_prod
    image: postgres:15
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_PASSWORD=odoonexposdotvn
      - POSTGRES_USER=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - '5433:5432'
  db_dev:
    container_name: db_dev
    image: postgres:15
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_PASSWORD=odoonexposdotvn
      - POSTGRES_USER=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - '5432:5432'
