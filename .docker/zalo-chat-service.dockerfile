FROM alpine:latest
RUN apk --no-cache add nodejs npm
WORKDIR /app

COPY ./nodejs-services/zalo-chat-service/package.json .
COPY ./nodejs-services/zalo-chat-service/zca-js-1.6.0.tgz .

RUN npm install --only=production

COPY ./nodejs-services/zalo-chat-service ./zalo-chat-service
COPY ./nodejs-services/.shared ./.shared

RUN cd ./.shared && npm install --only=production

WORKDIR /app/zalo-chat-service

# Start the API
CMD [ "node", "app.js" ]