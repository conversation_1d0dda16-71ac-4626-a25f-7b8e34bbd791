FROM golang:1.23.0-alpine AS builder

WORKDIR /app

COPY ./golang-services/go.mod .
COPY ./golang-services/go.sum .
COPY ./golang-services/localize.yml .
RUN go mod download

COPY ./golang-services .

RUN cd order-service && go build -o order-service .

FROM alpine:3.12
RUN apk add ca-certificates
WORKDIR /app
COPY --from=builder /app/order-service/order-service .
COPY --from=builder /app/localize.yml .
COPY --from=builder /app/share_files ./share_files