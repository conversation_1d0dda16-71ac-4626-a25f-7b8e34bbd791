FROM alpine:latest

# Install Node.js, npm, and other required dependencies
RUN apk --no-cache add nodejs npm

# Set working directory
WORKDIR /app

COPY ./nodejs-services/gateway-service/package.json .
RUN npm install --only=production

RUN npm install mongodb pg js-yaml

# Copy the entire gateway-service directory
COPY ./nodejs-services/gateway-service ./gateway-service

# Set working directory to gateway-service
WORKDIR /app/gateway-service

# Command to run the application
CMD ["node", "app.js"]