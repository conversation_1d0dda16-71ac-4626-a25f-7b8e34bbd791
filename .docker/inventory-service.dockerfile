FROM alpine:latest
RUN apk --no-cache add nodejs npm
WORKDIR /app

COPY ./nodejs-services/inventory-service/package.json .
RUN npm install --only=production

COPY ./nodejs-services/inventory-service ./inventory-service
COPY ./nodejs-services/.shared ./.shared

RUN cd ./.shared && npm install --only=production

WORKDIR /app/inventory-service

ENV PORT=3000
# Expose the desired port for the API
EXPOSE 3000

# Start the API
CMD [ "node", "app.js" ]