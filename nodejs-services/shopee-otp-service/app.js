const express = require('express');
const fs = require('fs');
const axios = require('axios');
const puppeteer = require('puppeteer');
const cors = require('cors')

const app = express();
const port = 3000;

app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(cors());

app.get('/health', async (req, res) => res.json({ success: true }));

let browser = null, page = null;
(async () => {
    browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    page = await browser.newPage();
    await page.setRequestInterception(true);
    page.on('request', interceptedRequest => {
        console.log(interceptedRequest.url())
        if (
            interceptedRequest.url().endsWith('.png') ||
            interceptedRequest.url().endsWith('.jpg')
        )
            interceptedRequest.abort();
        else interceptedRequest.continue();
    });
})();

app.post('/shopee/otp/step1', async (req, res) => {
    const { phone_number } = req.body;

    try {
        await page.goto('https://partner.business.accounts.shopee.vn/authenticate/login/otp?client_id=5&next=https%3A%2F%2Fpartner.shopee.vn%2Faccount%2Flogin%2Fauth&state=https%3A%2F%2Fpartner.shopee.vn%2F%3Fbusiness_next%3Dhttps%253A%252F%252Fpartner.shopee.vn%252Flogin%252Fauth%26business_state%3Dhttps%253A%252F%252Fpartner.shopee.vn%26business_client_id%3D1');
        await page.waitForNetworkIdle();
        await page.type('.o2ETrq._tDWz0', phone_number);
        await page.click('div.uKGsIu');
        await page.waitForSelector('img.SVQ7IY');
        const captcha_img = await page.$eval('img.SVQ7IY', (img) => img.src);

        res.json({
            success: true,
            data: captcha_img
        })
    } catch (error) {
        console.log(error.message);
        res.status(500).send('An error occurred');
    }
});

app.post('/shopee/otp/step2', async (req, res) => {
    const { captcha_text } = req.body;

    try {
        await page.waitForSelector('input.XDgkZm');
        await page.type('input.XDgkZm', captcha_text);
        await page.click('.bEq11u')
        res.json({
            success: true,
        })
    } catch (error) {
        console.log(error.message);
        res.status(500).send('An error occurred');
    }
});

app.post('/shopee/otp/step3', async (req, res) => {
    const { otp } = req.body;

    try {
        await page.waitForSelector('input.r6q4yS');
        await page.type('input.r6q4yS', otp);

        await page.waitForSelector('button.etqT64');
        await page.waitForFunction(() => {
            const button = document.querySelector('button.etqT64');
            return button && !button.disabled;
        });

        await page.keyboard.press('Enter');

        // Press Tiếp Tục
        await page.waitForSelector('button.tpSJef');
        await page.click('button.tpSJef')

        await page.waitForSelector('.merchantInfo');

        const names = await page.$$eval('.merchantInfo .ant-typography-ellipsis', elements => elements.map(element => element.textContent.trim()));
        res.json({
            success: true,
            data: names
        })
    } catch (error) {
        console.log(error.message);
        res.status(500).send('An error occurred');
    }
});

app.post('/shopee/otp/step4', async (req, res) => {
    try {

        const merchants = await page.$$('.merchantInfo');
        await merchants[0].click();

        const request = await page.waitForRequest(r => {
            return r.url() === 'https://api.partner.shopee.vn/mss/v1/PartnerReportServer/GetReportList' && r.method() === 'POST'
        })
        const headers = request.headers();
        console.log(headers);
        await browser.close()

        const restaurant_resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.partner.shopee.vn/mss/v1/PartnerServer/GetStoreList',
            headers: {
                'accept': 'application/json, text/plain, */*',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36',
                'X-Merchant-Token': headers['x-merchant-token'],
                'x-foody-api-version': '1',
                'x-foody-app-type': '1025',
                'x-foody-client-language': 'vi',
                'x-foody-client-type': '1',
                'x-foody-client-version': '3.0.0'
            },
            data: { "storeName": "", "lastStoreId": "0", "pageSize": 30 }
        })

        res.json({
            success: true,
            data: {
                token: headers['x-merchant-token'],
                restaurants: restaurant_resp.data.data.list
            }
        })
    } catch (error) {
        console.log(error.message);
        res.status(500).send('An error occurred');
    }
});

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});