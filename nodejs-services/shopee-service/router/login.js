const crypto = require('crypto');
const axios = require('axios');
const CryptoJS = require('crypto-js');
const { DEVICE_ID } = require('../mock');

function encode_password(input) {
    const md5Hash = crypto.createHash('md5')
        .update(input)
        .digest('hex');

    const sha256Hash = crypto.createHash('sha256')
        .update(md5Hash)
        .digest('hex');

    return sha256Hash;
}

async function genDeviceId() {
    const url = 'https://df.infra.sz.shopee.vn/v2/shpsec/web/report';
    const headers = {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'content-type': 'application/json'
    };

    try {
        const response = await axios.post(url, DEVICE_ID, { headers });

        if (response.data.code === 0) {
            return response.data.data.riskToken;
        }
        return false;

    } catch (error) {
        console.error('Error:', error.message);
        return false;
    }
}

const base_headers = {
    'accept': 'application/json',
    'accept-language': 'en-US,en;q=0.9',
    'content-type': 'application/json',
    'origin': 'https://partner.business.accounts.shopee.vn',
    'priority': 'u=1, i',
    'referer': 'https://partner.business.accounts.shopee.vn/authenticate/login/?lang=vn&should_hide_back=true&state=https%3A%2F%2Fpartner.shopee.vn%2F%3Fbusiness_next%3Dhttps%253A%252F%252Fpartner.shopee.vn%252Flogin%252Fauth%26business_state%3Dhttps%253A%252F%252Fpartner.shopee.vn%26business_client_id%3D1&client_id=5&next=https%3A%2F%2Fpartner.shopee.vn%2Faccount%2Flogin%2Fauth',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'x-app-type': '2'
}

const login_by_email_password = async (req, res) => {
    try {
        const { email, password } = req.body;
        console.log(`Logging in with email: ${email}, password: ${password}`);

        const device_id = await genDeviceId();
        let cookies = {};

        const updateCookies = (setCookieHeaders) => {
            if (!setCookieHeaders) return;
            const cookieArray = Array.isArray(setCookieHeaders) ? setCookieHeaders : [setCookieHeaders];
            for (const cookie of cookieArray) {
                const [name, ...rest] = cookie.split('=');
                const value = rest.join('=').split(';')[0];
                cookies[name] = value;
            }
        };

        const getCookieHeader = () => {
            return Object.entries(cookies)
                .map(([key, value]) => `${key}=${value}`)
                .join('; ');
        };

        let config1 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://partner.business.accounts.shopee.vn/api/v4/account/business/check_password_migrate',
            headers: base_headers,
            data: {
                "email": email
            }
        };
        const resp1 = await axios(config1);
        console.log(JSON.stringify(resp1.data, null, 2));
        updateCookies(resp1.headers['set-cookie']);

        let config2 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://partner.business.accounts.shopee.vn/api/v4/account/business/check_account_exist_by_password',
            headers: { ...base_headers, 'cookie': getCookieHeader() },
            data: {
                "email": email,
                "password": password,
            }
        };
        const resp2 = await axios(config2);
        console.log(JSON.stringify(resp2.data, null, 2));
        updateCookies(resp2.headers['set-cookie']);

        let config3 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://partner.business.accounts.shopee.vn/api/v4/account/business/authenticate_toc_by_password',
            headers: { ...base_headers, 'cookie': getCookieHeader() },
            data: {
                "email": email,
                "password": encode_password(password),
            }
        };
        const resp3 = await axios(config3);
        console.log(JSON.stringify(resp3.data, null, 2));
        updateCookies(resp3.headers['set-cookie']);

        let config4 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.partner.shopee.vn/nb/mss/mer-detect-api/PartnerMerchantDetectServer/MerchantDetect',
            headers: {
                ...base_headers,
                'cookie': getCookieHeader(),
                'x-merchant-language': 'vi',
                'x-merchant-login-from': '12',
                'x-merchant-timezone': 'Asia/Jakarta',
                'x-merchant-tob-clientid': 'undefined',
                'x-merchant-toc-nonce': resp3.data.data.toc_nonce,
                'x-merchant-token': ''
            },
            data: {}
        };
        const resp4 = await axios(config4);
        console.log(JSON.stringify(resp4.data, null, 2));
        updateCookies(resp4.headers['set-cookie']);
        let result = []
        for (const merchant of resp4.data.data.selectMerchant.merchantList) {
            let config6 = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://partner.business.accounts.shopee.vn/api/v4/account/business/login_toc',
                headers: {
                    ...base_headers,
                    'cookie': getCookieHeader(),
                    'x-app-type': '2',
                    'x-sz-sdk-version': '3.4.1-2@1.3.1',
                },
                data: {
                    "toc_nonce": resp3.data.data.toc_nonce,
                    "tob_userid": merchant.staffTobUid,
                    "security_device_fingerprint": device_id,
                }
            };
            const resp6 = await axios(config6);
            console.log(JSON.stringify(resp6.data, null, 2));
            updateCookies(resp6.headers['set-cookie']);

            const config7 = {
                method: 'get',
                maxBodyLength: Infinity,
                url: 'https://partner.shopee.vn/account/login/tob/auth',
                headers: {
                    'connection': 'keep-alive',
                    'host': 'partner.shopee.vn',
                    'referer': 'https://partner.business.accounts.shopee.vn/',
                    'upgrade-insecure-requests': '1',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                },
                params: {
                    "code": resp6.data.data.nonce,
                    "lang": "vi",
                    "spc_clientid": "YXd3QUVJY1dlVzF5yzwkndyuzxpjyvrt",
                    "state": "https://partner.shopee.vn/?business_next=https%3A%2F%2Fpartner.shopee.vn%2Flogin%2Fauth&business_state=https%3A%2F%2Fpartner.shopee.vn&business_client_id=1"
                },
                maxRedirects: 0,
                validateStatus: status => true,
            }
            const resp7 = await axios(config7);
            console.log(JSON.stringify(resp7.headers, null, 2));

            const config8 = {
                method: 'get',
                maxBodyLength: Infinity,
                url: resp7.headers.location,
                headers: {
                    'connection': 'keep-alive',
                    'host': 'partner.shopee.vn',
                    'referer': 'https://partner.business.accounts.shopee.vn/',
                    'upgrade-insecure-requests': '1',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
                },
                maxRedirects: 0,
                validateStatus: status => true,
            }

            const resp8 = await axios(config8);
            console.log(JSON.stringify(resp8.headers, null, 2));
            updateCookies(resp8.headers['set-cookie']);

            const config9 = {
                method: 'get',
                maxBodyLength: Infinity,
                url: 'https://partner.shopee.vn/templates/partner-subapp',
                headers: {
                    'cookie': resp8.headers['set-cookie'].join('; '),
                    'host': 'partner.shopee.vn',
                    'referer': 'https://partner.shopee.vn/',
                    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'
                }
            };
            const resp9 = await axios(config9);
            console.log(JSON.stringify(resp9.headers, null, 2));
            console.log(resp9.data)
            const matches = resp9.data.match(/window\['injectData'\]\s*=\s*({.*?});[^{]*?<\/script>/s);
            console.log(matches[1]);
            const row = JSON.parse(matches[1]);

            const config10 = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://app.partner.shopee.vn/mss/app-api/PartnerRNServer/GetStoreList',
                headers: {
                    'a-appversion': '32101',
                    'a-brand': 'unknown',
                    'a-lang': 'vi',
                    'a-os': 'android',
                    'content-type': 'application/json',
                    'host': 'app.partner.shopee.vn',
                    'user-agent': 'okhttp/3.12.4 app_type=2 shopee_rn_bundle_version=285000000',
                    'x-merchant-token': row.User.token
                },
                data: { "page_size": 500 }
            };
            const resp10 = await axios(config10);
            row.store_list = resp10.data.data.store_list
            row.merchant = merchant;
            result.push(row)
        }

        res.json({ success: true, data: result });
    } catch (error) {
        console.error('Error:', error.message);
        res.json({ success: false, message: error.message });
    }
}
module.exports = { login_by_email_password };