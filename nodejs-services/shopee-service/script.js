const axios = require('axios');
const fs = require('fs');

const appendText = `

const mockPlatform = {
    setModule: function (moduleName, moduleExports) {
        this.modules = this.modules || {};
        this.modules[moduleName] = moduleExports;
    }
};

const moduleInitializer = window.mfeModules[0][2](mockPlatform);
moduleInitializer().then(() => {
    if (mockPlatform.modules) {
        const moduleName = Object.keys(mockPlatform.modules)[0];
        const moduleExports = mockPlatform.modules[moduleName];
        const actualModule = moduleExports.default || moduleExports;

        if (typeof actualModule.generateSignEntry === 'function') {
            window.generateSignEntry = actualModule.generateSignEntry;
        }
    }
})`;

const getShopeeScript = async () => {
    try {
        const webResp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://shopee.co.id/',
            headers: {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9',
                'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        })
        const SAPVersions = (webResp.data.match(/{"p":\d+,"v":"[^"]+"}/g) || []).map(JSON.parse);
        console.log(SAPVersions)
        const baseUrl = 'https://deo.shopeemobile.com/shopee/modules-federation/live/0/shopee__web_enhance_sap';


        const filename = SAPVersions[0].v + ".js";
        const url = `${baseUrl}/${filename}`;
        console.log(url)
        const { data: fileContent } = await axios.get(url, {
            responseType: 'arraybuffer',
            timeout: 5000
        });

        const combinedContent = Buffer.concat([
            Buffer.from(fileContent),
            Buffer.from(appendText)
        ]);

        return combinedContent.toString();
    } catch (error) {
        console.error('Error in getShopeeScript:', error);
        throw error;
    }
};
module.exports = { getShopeeScript };