const express = require('express');
const puppeteer = require('puppeteer');
const fs = require('fs');
const cors = require('cors');
const { login_by_email_password } = require('./router/login');
const { getShopeeScript } = require('./script');

const app = express();
app.use(express.json());
app.use(cors());

let browser;
let page;

async function initBrowser() {
    if (browser) {
        await browser.close();
    }

    try {
        browser = await puppeteer.launch({
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ],
        });

        page = await browser.newPage();
        const scriptContent = await getShopeeScript();
        await page.evaluate(scriptContent);
        console.log('Browser reinitialized and script loaded successfully');
    } catch (error) {
        console.error('Browser initialization failed:', error);
        throw error;
    }
}

function scheduleReinitialization() {
    setInterval(async () => {
        console.log('Scheduled browser reinitialization starting...');
        try {
            await initBrowser();
        } catch (error) {
            console.error('Scheduled reinitialization failed:', error);
        }
    }, 2 * 60 * 60 * 1000); // 2 hours
}

app.post('/sign', async (req, res) => {
    const { url, body } = req.body;
    try {
        const result = await page.evaluate(async (url, body) => {
            return window.generateSignEntry(url, body);
        }, url, body);
        res.json({ signedValue: result });
    } catch (error) {
        console.error("Error evaluating script:", error);
        res.status(500).send("Error processing request");
    }
});

app.get('/health', async (req, res) => {
    res.json({ success: true });
});

app.post('/login', login_by_email_password);

process.on('SIGTERM', async () => {
    console.log('Received SIGTERM signal, closing browser...');
    if (browser) {
        await browser.close();
    }
    process.exit(0);
});

const PORT = process.env.PORT || 3000;

async function startServer() {
    try {
        await initBrowser();
        scheduleReinitialization();
        app.listen(PORT, () => {
            console.log(`Server running on port ${PORT}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();