const { MongoClient } = require('mongodb');
const { Pool } = require('pg');
const fs = require('fs');
const yaml = require('js-yaml');
const moment = require('moment');

// PostgreSQL Pool Configuration
const pgConfig = {
   user: 'food-app-dev',
   host: '**************',
   database: 'food-app-dev',
   password: 'Nexdor@2025',
   port: 5432,
   max: 20, // Maximum number of clients in the pool
   idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
   connectionTimeoutMillis: 2000, // How long to wait for a connection before timing out
};

let pgPool = new Pool(pgConfig);

// MongoDB URI
const mongo_uri = "mongodb+srv://nexdor:<EMAIL>/food-app?maxPoolSize=50&minPoolSize=10&maxIdleTimeMS=30000";

// Load column mappings from YAML
let tableMappings;
try {
   tableMappings = yaml.load(fs.readFileSync(__dirname + '/column_mappings.yaml', 'utf8')).tables;
} catch (err) {
   console.error('Failed to load column_mappings.yaml:', err);
   tableMappings = [];
}

const tableColumnMaps = new Map();
tableMappings.forEach(({ table, columns }) => {
   const columnMap = new Map(columns.map(col => {
      const [mongoCol, pgCol] = Object.entries(col)[0];
      return [mongoCol, pgCol];
   }));
   tableColumnMaps.set(table, columnMap);
});

// Reconnection logic for PostgreSQL
async function reconnectPgPool() {
   console.log('Attempting to reconnect to PostgreSQL...');
   pgPool = new Pool(pgConfig);

   pgPool.on('error', (err, client) => {
      console.error('PostgreSQL pool error:', err.stack);
      // Attempt to reconnect after an error
      setTimeout(reconnectPgPool, 5000); // Wait 5 seconds before retrying
   });

   try {
      await pgPool.query('SELECT NOW()'); // Test the connection
      console.log('Successfully reconnected to PostgreSQL');
   } catch (err) {
      console.error('Reconnection failed:', err.stack);
      setTimeout(reconnectPgPool, 5000); // Retry after 5 seconds
   }
}

// Handle initial pool errors
pgPool.on('error', (err, client) => {
   console.error('Unexpected error on idle PostgreSQL client:', err.stack);
   reconnectPgPool();
});

// Utility functions (unchanged from your original code)
function getNestedValue(obj, path) {
   return path.split('.').reduce((acc, part) => acc && acc[part], obj);
}

function quoteIdentifier(name) {
   return `"${name.replace(/"/g, '""')}"`;
}

const isObjectId = (value) => {
   const objectIdRegex = /^[0-9a-fA-F]{24}$/;
   return typeof value === 'string' && objectIdRegex.test(value);
}

// Main MongoDB watch function
async function watchMongoDB() {
   const client = new MongoClient(mongo_uri);
   try {
      await client.connect();
      await initializeSchemas(client);
      const db = client.db("food-app");
      const changeStream = db.watch();
      changeStream.on("change", processChange);
   } catch (err) {
      console.error("Error with MongoDB:", err);
   }
}

async function processChange(change) {
   try {
      const tableName = change.ns.coll;
      switch (change.operationType) {
         case 'insert':
            await handleInsert(tableName, change);
            break;
         case 'update':
            await handleUpdate(tableName, change);
            break;
         case 'delete':
            await handleDelete(tableName, change);
            break;
         default:
            console.log(`Unhandled operation type: ${change.operationType}`);
      }
   } catch (err) {
      console.error("Error processing change:", err);
      if (err.code === 'ECONNREFUSED' || err.code === '57P01') { // Common PostgreSQL connection errors
         await reconnectPgPool();
      }
   }
}

// Ensure table and columns exist
async function ensureTableAndColumns(tableName, fields = {}) {
   const quotedTableName = quoteIdentifier(tableName);

   try {
      await pgPool.query(`
         DO $$
         BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'object_id') THEN
               CREATE DOMAIN OBJECT_ID AS CHAR(24)
               CHECK (VALUE ~ '^[0-9a-fA-F]{24}$');
            END IF;
         END $$;
      `);

      await pgPool.query(`
         CREATE TABLE IF NOT EXISTS ${quotedTableName} (
            id OBJECT_ID PRIMARY KEY
         )
      `);

      const mappedFields = mapColumnNames(tableName, fields);
      for (const [key, value] of Object.entries(mappedFields)) {
         if (key.split('.').length > 1 || key === 'id') continue;
         const quotedColumn = quoteIdentifier(key);
         const columnType = inferColumnType(value);
         const query = `
            DO $$
            BEGIN
               IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns
                  WHERE table_name = '${tableName.toLowerCase()}' AND column_name = '${key.toLowerCase()}'
               ) THEN
                  EXECUTE 'ALTER TABLE ${quotedTableName} ADD COLUMN IF NOT EXISTS ${quotedColumn} ${columnType}';
               END IF;
            END
            $$;
         `;
         await pgPool.query(query);
      }
   } catch (err) {
      console.error('Error ensuring table/columns:', err);
      throw err;
   }
}

// Infer column type (unchanged)
function inferColumnType(value) {
   if (value === null || value === undefined) return 'TEXT';
   if (value instanceof Date) return 'TIMESTAMP';
   if (typeof value === 'string') {
      if (isObjectId(value)) return 'OBJECT_ID';
      if (moment(value, moment.ISO_8601, true).isValid()) return 'TIMESTAMP';
      return 'TEXT';
   }
   if (typeof value === 'number') {
      if (!Number.isInteger(value)) return 'NUMERIC';
      if (value >= -2147483648 && value <= 2147483647) return 'NUMERIC';
      return 'NUMERIC';
   }
   if (typeof value === 'boolean') return 'BOOLEAN';
   if (typeof value === 'object') return 'JSONB';
   return 'TEXT';
}

// Map column names (unchanged)
function mapColumnNames(tableName, doc) {
   const columnMap = tableColumnMaps.get(tableName) || new Map();
   const mappedDoc = {};

   for (const [mongoCol, pgCol] of columnMap) {
      if (mongoCol.includes('.')) {
         const value = getNestedValue(doc, mongoCol);
         if (value !== undefined) mappedDoc[pgCol] = value;
      } else {
         const value = doc[mongoCol];
         if (value !== undefined) mappedDoc[pgCol] = value;
      }
   }

   for (const [key, value] of Object.entries(doc)) {
      if (!columnMap.has(key) && !Object.keys(mappedDoc).includes(key) && key !== '_id') {
         mappedDoc[key] = value;
      }
   }

   return mappedDoc;
}

// Handle insert (unchanged except error logging)
async function handleInsert(tableName, change) {
   const doc = change.fullDocument;
   const mongoId = change.documentKey._id.toString();
   const quotedTableName = quoteIdentifier(tableName);

   const mappedDoc = mapColumnNames(tableName, doc);
   await ensureTableAndColumns(tableName, mappedDoc);

   const columns = ['id', ...Object.keys(mappedDoc)];
   const quotedColumns = columns.map(quoteIdentifier);
   const values = [mongoId, ...Object.values(mappedDoc)
      .map(value => typeof value === 'object' && value !== null ? JSON.stringify(value) : value)];
   const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');

   try {
      const result = await pgPool.query(
         `INSERT INTO ${quotedTableName} (${quotedColumns.join(', ')}) 
          VALUES (${placeholders}) 
          ON CONFLICT (id) DO NOTHING
          RETURNING id`,
         values
      );
      if (result.rows.length > 0) {
         console.log(`Inserted into ${tableName}: ${result.rows[0].id} (Mongo _id: ${mongoId})`);
      } else {
         console.log(`Skipped insert into ${tableName} due to conflict: ${mongoId}`);
      }
   } catch (err) {
      console.error('Insert error:', { tableName, mongoId, columns, values, error: err });
      throw err;
   }
}

// Handle update (unchanged except error logging)
async function handleUpdate(tableName, change) {
   const mongoId = change.documentKey._id.toString();
   const quotedTableName = quoteIdentifier(tableName);
   const updatedFields = change.updateDescription.updatedFields;
   const mappedFields = mapColumnNames(tableName, updatedFields);
   await ensureTableAndColumns(tableName, mappedFields);

   const setClause = Object.keys(mappedFields)
      .map((key, i) => `${quoteIdentifier(key)} = $${i + 2}`)
      .join(', ');
   const values = [mongoId, ...Object.values(mappedFields)
      .map(value => typeof value === 'object' && value !== null ? JSON.stringify(value) : value)];

   const query = `UPDATE ${quotedTableName} 
      SET ${setClause} 
      WHERE id = $1 
      RETURNING id`;

   try {
      const result = await pgPool.query(query, values);
      if (result.rows.length > 0) {
         console.log(`Updated ${tableName} record with id: ${mongoId}`);
      } else {
         console.log(`No record found to update in ${tableName} for id: ${mongoId}`);
      }
   } catch (err) {
      console.error('Update error:', { tableName, mongoId, values, error: err });
      throw err;
   }
}

// Handle delete (unchanged except error logging)
async function handleDelete(tableName, change) {
   const mongoId = change.documentKey._id.toString();
   const quotedTableName = quoteIdentifier(tableName);

   try {
      const result = await pgPool.query(
         `DELETE FROM ${quotedTableName} 
          WHERE id = $1 
          RETURNING id`,
         [mongoId]
      );
      if (result.rows.length > 0) {
         console.log(`Deleted from ${tableName} record with id: ${mongoId}`);
      } else {
         console.log(`No record found to delete in ${tableName} for id: ${mongoId}`);
      }
   } catch (err) {
      console.error('Delete error:', { tableName, mongoId, error: err });
      throw err;
   }
}

// Initialize schemas (unchanged except error handling)
async function initializeSchemas(client) {
   const db = client.db("food-app");
   const collections = await db.listCollections().toArray();

   for (const collection of collections) {
      const collectionName = collection.name;
      const quotedCollectionName = quoteIdentifier(collectionName);

      const records = await db.collection(collectionName)
         .find()
         .sort({ '_id': -1 })
         .limit(1000)
         .toArray();

      for (const record of records) {
         await ensureTableAndColumns(collectionName, record);
         const mongoId = record._id.toString();
         const mappedDoc = mapColumnNames(collectionName, record);

         const columns = ['id', ...Object.keys(mappedDoc)];
         const quotedColumns = columns.map(quoteIdentifier);
         const values = [mongoId, ...Object.values(mappedDoc)
            .map(value => typeof value === 'object' && value !== null ? JSON.stringify(value) : value)];
         const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');

         try {
            const query = `INSERT INTO ${quotedCollectionName} (${quotedColumns.join(', ')}) 
                VALUES (${placeholders}) 
                ON CONFLICT (id) DO NOTHING
                RETURNING id`;
            const result = await pgPool.query(query, values);
            if (result.rows.length > 0) {
               console.log(`Initialized ${collectionName} record with id: ${result.rows[0].id} (Mongo _id: ${mongoId})`);
            } else {
               console.log(`Skipped initialization of ${collectionName} record due to conflict: ${mongoId}`);
            }
         } catch (err) {
            console.error(`Error inserting initial record for ${collectionName}:`, err);
         }
      }
   }
}

// Start the application
async function start() {
   try {
      await pgPool.query('SELECT NOW()'); // Test initial connection
      console.log('Connected to PostgreSQL');
      watchMongoDB();
   } catch (err) {
      console.error('Initial PostgreSQL connection failed:', err.stack);
      await reconnectPgPool();
      watchMongoDB();
   }
}

start();

module.exports = watchMongoDB;