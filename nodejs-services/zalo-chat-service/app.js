const { Zalo, MessageType } = require("zca-js");
const axios = require('axios');
const { ZaloChatMessage } = require("../.shared/database");
const { upload_file } = require('../.shared/storage')
const zalo = new Zalo(
   {
      cookie: "_zlang=vn; _ga=GA1.2.1506273962.1740650529; _gid=GA1.2.1616733499.1740650529; zpsid=b38S.433678590.1.YsB8XzYBXS4curpOr8k5mQtuz_NhkBFsuBwyyQcTFUM4QrQ0sT-z0R-BXS4; zpw_sek=L79X.433678590.a0.vS7vUKNx6Asxay6qPFiOhJxPVSPdmJhl3i8epmEKRvuFo2h3DxfSWW7DJCeonYMFE8eE5M6d-7u4-asLXPmOhG; __zi=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8WzckkZaKXNZtcUvQVTJrMBV9hdeZ4o.1; __zi-legacy=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8WzckkZaKXNZtcUvQVTJrMBV9hdeZ4o.1; ozi=2000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjMXe9fFM8yuc-kXcKvNYJIGhgINGnkEDfIhe9L67uu.1; app.event.zalo.me=4763220479501202835",
      imei: "aed429dd-2542-4da5-ba19-cb13f61cadd7-a11f5da7336cfe2e2fd950a3d968fdb0",
      userAgent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
   },
   {
      selfListen: false,
      checkUpdate: true,
   },
);

(async () => {
   const api = await zalo.login();

   api.listener.on("message", async (message) => {
      console.log(message.data);

      let msg_type = '';
      if (typeof message.data.content === 'string') {
         msg_type = 'text';
      }
      if (message.data.content.href) {
         msg_type = 'file';
      }
      let file_upload;
      if (message.data.content.href) {
         try {
            const file_download_resp = await axios.get(message.data.content.href, { responseType: 'arraybuffer' });
            const timestamp = Date.now();
            const filename = message.data.content.href.split('/').pop() || `file_${timestamp}`;
            const key = `zalo_chat/${timestamp}_${filename}`;
            file_upload = await upload_file({ bucket: 'nexpos-files', key, buff: Buffer.from(file_download_resp.data) });
         } catch (error) {
            console.error("Download/upload failed:", error.message);
         }
      }


      switch (message.type) {
         case MessageType.DirectMessage: {
            if (msg_type === 'text') {
               await ZaloChatMessage.create({
                  target: 'user',
                  to_id: 'Dao Nguyen Account',
                  to_name: 'Dao Nguyen Account',
                  msg_id: message.data.msgId,
                  msg_type: 'text',
                  from_id: message.data.uidFrom,
                  from_name: message.data.dName,
                  message: message.data.content,
                  created_at_unix: message.data.ts,
                  raw: message.data
               })
            }
            if (msg_type === 'file') {
               await ZaloChatMessage.create({
                  target: 'user',
                  to_id: 'Dao Nguyen Account',
                  to_name: 'Dao Nguyen Account',
                  msg_id: message.data.msgId,
                  msg_type: 'file',
                  from_id: message.data.uidFrom,
                  from_name: message.data.dName,
                  message: '',
                  files: [file_upload],
                  created_at_unix: message.data.ts,
                  raw: message.data
               })
            }
            break;
         }
         case MessageType.GroupMessage: {
            const groupInfo = await api.getGroupInfo(message.threadId)
            if (msg_type === 'text') {
               await ZaloChatMessage.create({
                  target: 'group',
                  to_id: message.threadId,
                  to_name: groupInfo.gridInfoMap[message.threadId].name,
                  msg_id: message.data.msgId,
                  msg_type: 'text',
                  from_id: message.data.uidFrom,
                  from_name: message.data.dName,
                  message: message.data.content,
                  created_at_unix: message.data.ts,
                  raw: message.data
               })
            }
            if (msg_type === 'file') {
               await ZaloChatMessage.create({
                  target: 'group',
                  to_id: message.threadId,
                  to_name: groupInfo.gridInfoMap[message.threadId].name,
                  msg_id: message.data.msgId,
                  msg_type: 'file',
                  from_id: message.data.uidFrom,
                  from_name: message.data.dName,
                  message: '',
                  files: [file_upload],
                  created_at_unix: message.data.ts,
                  raw: message.data
               })
            }
            break;
         }
         default: {
            console.log(message.data)
            break;
         }
      }
   });

   api.listener.start();
})();
