const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors')
const { Storage } = require('@google-cloud/storage');

// Google Cloud Auth Credentials
const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode

const storage = new Storage({ credentials });
const bucket = storage.bucket(`nexpos-images`);


const app = express();
const port = 3000;

app.use(express.urlencoded({ extended: true, limit: '100mb' }));
app.use(express.json({ limit: '100mb' }));
app.use(cors());
process.setMaxListeners(0);

app.get('/health', async (req, res) => res.json({ success: true }));

let browser;

// Initialize browser and load script
async function initBrowser() {
    try {
        browser = await puppeteer.launch({
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ],
        });

        console.log('Browser loaded successfully');
    } catch (error) {
        console.error('Browser initialization failed:', error);
        throw error;
    }
}

app.post('/convert', async (req, res) => {
    const { html_string, order_code } = req.body;
    const page = await browser.newPage();
    try {
        await page.setContent(html_string);

        const bodyHandle = await page.$('body');
        const boundingBox = await bodyHandle.boundingBox();

        await page.setViewport({ width: parseInt(boundingBox.width), height: parseInt(boundingBox.height) });

        const fileContent = await page.screenshot({
            encoding: 'binary',
            fullPage: false,
            omitBackground: true,
        });

        const file = bucket.file(`printer/${order_code}_${Date.now()}.png`);
        await file.delete({ ignoreNotFound: true })
        await file.save(fileContent);
        console.log(file.publicUrl())
        res.send(file.publicUrl());
    } catch (error) {
        console.log(error.message);
        res.send("");
    } finally {
        await page.close();
    }
});


// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('Received SIGTERM signal, closing browser...');
    if (browser) {
        await browser.close();
    }
    process.exit(0);
});

// Start server
const PORT = process.env.PORT || 3000;

async function startServer() {
    try {
        await initBrowser();
        app.listen(PORT, () => {
            console.log(`Server running on port ${PORT}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();