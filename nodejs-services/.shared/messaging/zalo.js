const axios = require('axios');
const crypto = require('crypto');
const jimp = require('jimp');
const { upload_file } = require('../storage')

const send_message = async (token, uid, message, images = []) => {
    try {
        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://openapi.zalo.me/v3.0/oa/message/cs',
            headers: {
                'access_token': token,
            },
            data: {
                recipient: {
                    user_id: uid
                },
                message: {
                    text: message
                }
            }
        }
        if (images.length > 0) {
            const resized_images = []
            for (const image of images) {
                const image_buff = await axios.get(image, { responseType: 'arraybuffer' })
                const image_hash = crypto.createHash('md5').update(image).digest('hex')
                const jimp_image = await jimp.read(image_buff.data)
                if (jimp_image.bitmap.width > 800) {
                    const buff = await jimp_image.resize(800, jimp.AUTO).getBufferAsync(jimp.MIME_JPEG)
                    const file = await upload_file({
                        bucket: 'nexpos-files',
                        key: `zalo/${image_hash}.jpg`,
                        buff: buff
                    })
                    resized_images.push(file)
                } else {
                    resized_images.push(image)
                }
            }
            console.log(resized_images)
            config.data.message.attachment = {
                type: 'template',
                payload: {
                    template_type: 'media',
                    elements: resized_images.map(image => ({
                        media_type: 'image',
                        url: image
                    }))
                }
            }
        }
        const response = await axios(config)
        console.log(response.data)
    } catch (error) {
        console.error('Error sending message to Zalo:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
    }
}

module.exports = { send_message };