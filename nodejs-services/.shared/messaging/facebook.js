const axios = require('axios');

const send_message = async (token, page_id, uid, message) => {
    const url = `https://graph.facebook.com/v19.0/${page_id}/messages?access_token=${token}`
    const data = {
        recipient: {
            id: uid
        },
        messaging_type: "RESPONSE",
        message: {
            text: message
        }
    }
    try {
        const response = await axios.post(url, data)
        // console.log(response.data)
    } catch (error) {
        console.error('Error sending message to Facebook:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
    }
}
const send_images = async (token, page_id, uid, images) => {
    const url = `https://graph.facebook.com/v19.0/${page_id}/messages?access_token=${token}`
    const data = {
        recipient: {
            id: uid
        },
        message: {
            attachment: {
                type: "image",
                payload: {
                    url: "",
                    is_reusable: false
                }
            }
        }
    }
    try {
        for (let image of images) {
            data.message.attachment.payload.url = image
            const response = await axios.post(url, data)
            // console.log(response.data)
        }
    } catch (error) {
        console.error('Error sending message to Facebook:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
    }
}

module.exports = { send_message, send_images };