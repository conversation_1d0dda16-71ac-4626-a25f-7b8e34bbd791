const axios = require('axios')
const qs = require('qs')
const FormData = require('form-data');
const { Order, Hub, Site } = require('./database');
const { name_to_id } = require('./helper');
const redis = require('./redis');

exports.ZALO_GROUPS = {
    INTERNAL_ORDER_NOTIFICATION: 'https://zalo.me/g/faqfqf081',
    ORDER_SYSNCING_NOTIFICATION: 'https://zalo.me/g/rhompm863',
}

exports.send_zalo_message_by_order_id = async ({ order_id, message }) => {
    if (process.env.NODE_ENV !== 'prod') {
        return
    }
    const order = await Order.findOne({ order_id })
    const site = await Site.findById(order.site_id)
    const hub = await Hub.findById(order.hub_id)
    const zalo_group = site.zalo_group || hub.zalo_group
    if (zalo_group && zalo_group.startsWith('https://zalo.me/g/')) {
        this.send_zalo_message({ group_link: zalo_group, message })
    }
}

exports.send_zalo_message = async ({ group_link, message, urgency = '', min_duplicate_message_duration = 0 }) => {
    const key = name_to_id(group_link + message)
    if (min_duplicate_message_duration > 0) {
        const exist_message = await redis.getObj(`zalo_message:${key}`)
        if (exist_message) {
            return
        } else {
            await redis.setObj(`zalo_message:${key}`, { group_link, message }, min_duplicate_message_duration)
        }
    }


    if (process.env.NODE_ENV !== 'prod') {
        return
    }
    let data = new FormData();
    data.append('shareLink', group_link);
    data.append('message', message);
    data.append('urgency', urgency);

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_message',
        headers: {
            ...data.getHeaders()
        },
        data: data
    };

    try { await axios(config) } catch (error) { }
}

exports.send_file_to_zalo = async ({ group_link, url }) => {
    try {
        if (process.env.NODE_ENV !== 'prod') {
            return
        }
        console.log(url)
        const response = await axios.get(url, { responseType: 'arraybuffer' });
        const buffer = Buffer.from(response.data, 'binary');

        let data = new FormData();
        data.append('shareLink', group_link);
        data.append('image', buffer, "bill.png");

        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_image',
            headers: {
                ...data.getHeaders()
            },
            data: data
        };
        const resp = await axios(config)
        console.log(resp.data)

    } catch (error) {
        console.log(error)
    }
}