const crypto = require('crypto');
const _ = require('lodash');

function string_to_number(input) {
    const hash = crypto.createHash('md5').update(input).digest('hex');
    const number = parseInt(hash, 16);
    return number % Number.MAX_SAFE_INTEGER;
}

function json_to_md5(input) {
    const json_string = JSON.stringify(_.sortBy(input, (value, key) => key));
    return crypto.createHash('md5').update(json_string).digest('hex');
}

module.exports = {
    string_to_number,
    json_to_md5,
};