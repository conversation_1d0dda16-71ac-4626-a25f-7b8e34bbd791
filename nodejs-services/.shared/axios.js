const axios = require('axios')
const logger = require('./logger');
const curlirize = require('axios-curlirize')

const my_axios = axios.create();
const is_prod = process.env.NODE_ENV === 'prod';

curlirize(my_axios, (result, err) => {
    const { command } = result

})

my_axios.interceptors.request.use((config) => {
    config.timeout = 30000
    config.validateStatus = (status) => status >= 200 && status < 300;
    return config
})

my_axios.interceptors.response.use((response) => {
    if (is_prod) {
        logger.info(JSON.stringify({
            curl: response.config.curlCommand,
            status: response.status,
            data: response.data,
        }), { tag: 'MERCHANT_API' });
    }
    return response;
}, (error) => {
    if (is_prod) {
        if (error.response) {
            logger.error(JSON.stringify({
                status: error.response.status,
                data: error.response.data,
            }), { tag: 'MERCHANT_API' });
        } else {
            logger.error(JSON.stringify({
                message: error.message,
            }), { tag: 'MERCHANT_API' });
        }
    }

    return Promise.reject(error);
});

module.exports = my_axios