const { createClient } = require('redis');
const { promisify } = require('util');
const client = createClient({
    url: process.env.REDIS_URI,
    // username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD,
    database: {
        'prod': 0,
        'uat': 2,
    }[process.env.NODE_ENV] || 1,
    connectTimeout: 15000 // 15 seconds
});

client.on('error', err => console.log('Redis Client Error', err));
client.on('connect', () => console.log('Redis Client Connected'));

(async () => {
    await client.connect();
})()


/**
 * Sets an object in Redis with a specified key and timeout.
 * @param {string} key - The key to set the object with.
 * @param {object} obj - The object to be set.
 * @param {number} [timeout=0] - The timeout value in seconds (default: 0).
 */
client.setObj = async (key, obj, timeout = 0) => {
    await client.set(key, JSON.stringify(obj), { 'EX': timeout }).catch((error) => console.log(error));
}

/**
 * Retrieves an object from Redis based on the specified key.
 * @param {string} key - The key to retrieve the object from.
 * @returns {Promise<object>} The retrieved object.
 */
client.getObj = async (key) => {
    try {
        const data = await client.get(key);
        return JSON.parse(data);
    } catch (error) {
        console.log(error);
    }
};

/**
 * Deletes all keys that start with the specified prefix.
 * @param {string} prefix - The prefix to match keys.
 */
client.deleteKeysWithPrefix = async (prefix, count = 100) => {
    try {
        for await (const key of client.scanIterator({
            TYPE: 'string', // `SCAN` only
            MATCH: `${prefix}*`,
            COUNT: 100
        })) {
            await client.del(key)
        }
    } catch (error) {
        console.log(error);
    }
};


module.exports = client;