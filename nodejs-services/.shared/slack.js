const axios = require('axios')
const qs = require('qs')
const FormData = require('form-data');
const SLACK_TOKEN = 'Bearer xoxs-4905709908484-4926929880864-5292296775430-2873c0a038e48e3fd97bec48a82d2c81368f5a6d2c543346c9c3adfeae8747fc'
const SLACK_FILE_TOKEN = 'Bearer xoxs-5597882231766-5601616695317-5628617945824-bcfc200e93f51908a92eefdf9458ba2fb01757f6ec8c9ff171051502c3ae9415'
const ALERT_CHANNEL = 'C0550H9AE9F'
exports.send_slack_message = async ({ channel = ALERT_CHANNEL, text, block }) => {

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://slack.com/api/chat.postMessage',
        headers: {
            'authorization': SLACK_TOKEN,
            'user-agent': 'slack/22.09.10.0.90011810 (samsung SM-G950F; Android 8.0.0)',
            'accept-language': 'vi-VN,vi;',
            'slack-route': 'T04SMLVSQE8',
            'content-type': 'application/x-www-form-urlencoded',
        },
        data: qs.stringify({
            'as_user': '1',
            'attachments': '[]',
            'unfurl_media': '1',
            'blocks': JSON.stringify([
                {
                    "type": "rich_text",
                    "block_id": "",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": text
                                }
                            ]
                        },
                        {
                            "type": "rich_text_preformatted",
                            "border": 0,
                            "elements": [
                                {
                                    "type": "text",
                                    "text": block
                                }
                            ]
                        }
                    ]
                }
            ]),
            'unfurl': '[]',
            'channel': channel,
            'unfurl_links': '1'
        })
    }
    try { await axios(config) } catch (error) { }
}

exports.send_file_to_slack = async ({ url, channel }) => {
    try {
        console.log(url)
        const file_name = "bill.png"
        const response = await axios.get(url, { responseType: 'arraybuffer' });
        const buffer = Buffer.from(response.data, 'binary');
        const sizeInBytes = buffer.length;

        const step_1 = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://slack.com/api/files.getUploadURL',
            headers: {
                'authorization': SLACK_FILE_TOKEN,
                'user-agent': 'slack/22.09.10.0.90011810 (samsung SM-G950F; Android 8.0.0)',
                'accept-language': 'vi-VN,vi;',
                'content-type': 'application/x-www-form-urlencoded',
            },
            data: qs.stringify({
                'filename': file_name,
                'length': sizeInBytes
            })
        })

        let step_2_data = new FormData();
        step_2_data.append('file', buffer, file_name);

        const step_2 = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: step_1.data.upload_url,
            headers: {
                ...step_2_data.getHeaders()
            },
            data: step_2_data,
        })

        const step_3 = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://slack.com/api/files.completeUpload',
            headers: {
                'authorization': SLACK_FILE_TOKEN,
                'user-agent': 'slack/22.09.10.0.90011810 (samsung SM-G950F; Android 8.0.0)',
                'accept-language': 'vi-VN,vi;',
            },
            data: { "file": step_1.data.file, "title": file_name }
        })

        const step_4 = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://slack.com/api/files.share',
            headers: {
                'authorization': SLACK_FILE_TOKEN,
                'user-agent': 'slack/22.09.10.0.90011810 (samsung SM-G950F; Android 8.0.0)',
                'accept-language': 'vi-VN,vi;',
            },
            data: qs.stringify({
                'files': step_1.data.file,
                channel,
                'resharing_aware': 1,
                'blocks': JSON.stringify([{ "type": "rich_text", "block_id": "", "elements": [{ "type": "rich_text_section", "type": "rich_text_section", "elements": [{ "type": "broadcast", "type": "broadcast", "range": "channel" }] }] }])
            })
        })
        console.log(step_4.data)

    } catch (error) {
        console.log(error)
    }
}

exports.send_slack_msg_as_bot = async ({ channel, text, block, thread_ts }) => {
    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://slack.com/api/chat.postMessage',
        headers: {
            'authorization': 'Bearer *********************************************************',
            'Content-Type': 'application/json',
        },
        data: {
            channel,
            text,
            thread_ts,
            blocks: block && JSON.stringify([
                {
                    "type": "rich_text",
                    "block_id": "",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "broadcast",
                                    "range": "channel"
                                },
                                {
                                    "type": "text",
                                    "text": text
                                }
                            ]
                        },
                        {
                            "type": "rich_text_preformatted",
                            "border": 0,
                            "elements": [
                                {
                                    "type": "text",
                                    "text": block
                                }
                            ]
                        }
                    ]
                }
            ]),
        }
    }

    try { const result = await axios(config)
        return result.data.ts
     } catch (error) { 
        console.log(error)
    }
}