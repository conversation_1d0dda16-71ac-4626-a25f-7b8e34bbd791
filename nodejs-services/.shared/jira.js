const { default: axios } = require('axios')

const create_it_ticket_jira = async ({ title, description, ticket_code }) => {
  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://nexdor-tech.atlassian.net/rest/api/3/issue',
    headers: {
      Authorization:
        'Basic ********************************************************************************************************************************************************************************************************************************************************************************************',
      'Content-Type': 'application/json',
    },
    data: {
      fields: {
        labels: ['ItTicket'],
        description: {
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: description,
                },
              ],
            },
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: `It Ticket Code: ${ticket_code}`,
                  marks: [
                    {
                      type: 'link',
                      attrs: {
                        href: `${process.env.WEB_URL}/it-support?code=${ticket_code}`,
                      },
                    },
                  ],
                },
              ],
            },
          ],
          type: 'doc',
          version: 1,
        },
        issuetype: {
          id: '10014',
        },
        project: {
          id: '10003',
        },
        summary: `It [#${ticket_code}] - ${title}`,
      },
    },
  }

  const data = await axios(config)
  return data.data?.key
}

module.exports = {
  create_it_ticket_jira,
}
