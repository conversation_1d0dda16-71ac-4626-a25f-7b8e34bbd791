const { google } = require('googleapis');
const gsv4 = google.sheets('v4');
const redis = require('../redis');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode


const auth = new google.auth.JWT({
    email: credentials.client_email,
    key: credentials.private_key,
    scopes: [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.file'
    ],
});

const read_sheet = async (spreadsheetId, skipRow = 0, sheet_names = [], useCache = false) => {
    try {

        const cache_key = `google_sheets:${spreadsheetId}_${sheet_names.join('_')}`
        if (useCache) {
            let cache_data = await redis.getObj(cache_key);
            if (cache_data) {
                return cache_data
            }
        }
        const { data: { sheets } } = await gsv4.spreadsheets.get({
            auth,
            spreadsheetId,
            includeGridData: false,
        });

        const sheetData = {};

        for (const sheet of sheets) {
            if (sheet_names.length > 0 && !sheet_names.includes(sheet.properties.title)) {
                continue;
            }

            const { data: { values } } = await gsv4.spreadsheets.values.get({
                auth,
                spreadsheetId,
                range: sheet.properties.title,
            });

            if (!values) {
                sheetData[sheet.properties.title] = []
                continue
            }

            const headers = values[skipRow];
            const sheetJson = []
            values.slice(skipRow + 1).forEach((row, rowIndex) => {
                if (row.length === 0 || String(row[0] ?? '').trim() === '') {
                    return;
                }
                const obj = {};
                for (let i = 0; i < headers.length; i++) {
                    obj[headers[i]] = String(row[i] ?? '').trim();
                }
                sheetJson.push(obj)
            });

            sheetData[sheet.properties.title] = sheetJson

        }
        if (useCache) {
            await redis.setObj(cache_key, sheetData, 60 * 60)
        }

        return sheetData;
    } catch (error) {
        console.error(error);
    }
};

const write_sheet = async (spreadsheetId, sheetData) => {
    try {
        const { data: { sheets } } = await gsv4.spreadsheets.get({
            auth,
            spreadsheetId,
            includeGridData: false,
        });

        for (const sheet of sheets) {
            const sheetTitle = sheet.properties.title;
            const sheetJson = sheetData[sheetTitle];
            const values = [];

            if (sheetJson.length > 0) {
                const mostKeysObj = sheetJson.reduce((prev, curr) => {
                    return Object.keys(prev).length > Object.keys(curr).length ? prev : curr;
                });

                const headers = Object.keys(mostKeysObj);
                values.push(headers);

                sheetJson.forEach((row) => {
                    const rowData = [];
                    headers.forEach((header) => {
                        rowData.push(row[header]);
                    });
                    values.push(rowData);
                });

                gsv4.spreadsheets.values.clear({
                    auth,
                    spreadsheetId,
                    range: sheetTitle,
                });

                await gsv4.spreadsheets.values.update({
                    auth,
                    spreadsheetId,
                    range: sheetTitle,
                    valueInputOption: 'USER_ENTERED',
                    resource: {
                        values,
                    },
                });
            }
        }
    } catch (error) {
        console.error(error);
    }
};

const append_sheet = async (spreadsheetId, sheetData) => {
    try {
        const { data: { sheets } } = await gsv4.spreadsheets.get({
            auth,
            spreadsheetId,
            includeGridData: false,
        });

        for (const sheet of sheets) {
            const sheetTitle = sheet.properties.title;
            const sheetJson = sheetData[sheetTitle];
            const values = [];

            if (sheetJson?.length > 0) {
                const headers = Object.keys(sheetJson[0]);
                sheetJson.forEach((row) => {
                    const rowData = [];
                    headers.forEach((header) => {
                        rowData.push(row[header]);
                    });
                    values.push(rowData);
                });

                await gsv4.spreadsheets.values.append({
                    auth,
                    spreadsheetId,
                    range: sheetTitle,
                    valueInputOption: 'USER_ENTERED',
                    resource: {
                        values,
                    },
                });
            }
        }
    } catch (error) {
        console.error(error);
    }
};

module.exports = {
    read_sheet,
    write_sheet,
    append_sheet,
}