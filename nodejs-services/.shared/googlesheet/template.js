const { read_sheet } = require(".");
const _ = require('lodash');
const { text_slugify, name_to_id, text_compare } = require("../helper");
const { Site, Brand, GoogleSheetFile } = require("../database");
const { GRAB_CATEGORIES } = require("../merchant/grab_mart_official");

const parse_properties = (text) => _.fromPairs(text.split('\n').map(line => line.split('=').map(_.trim)));
const USE_CACHE = true
const google_sheet_to_menu = async (sheet_id, { menu_name, option_name }) => {
    const sheet_data = await read_sheet(sheet_id, 2, [menu_name, option_name], USE_CACHE)

    const categories = []
    const option_categories = []

    const menu_items = sheet_data[menu_name]
    const menu_options = sheet_data[option_name]
    const category_names = _.uniq(menu_items.map(v => v.category))
    for (const category_name of category_names) {
        const items = menu_items.filter(v => v.category === category_name)
        categories.push({
            id: text_slugify(category_name),
            name: category_name,
            code: text_slugify(category_name),
            description: '',
            active: true,
            items: items.map(v => ({
                id: text_slugify(v.name),
                code: text_slugify(v.name),
                version: v.version ?? '',
                name: v.name,
                description: v.description,
                price: Number(v.price?.replace(',', '')?.trim()),
                image: v.image,
                images: [v.image, v.image_1, v.image_2].filter(v => v),
                active: true,
            }))
        })
    }

    const option_category_names = _.uniq(menu_options.map(v => v.option_id))
    for (const option_category_name of option_category_names) {
        const options = menu_options.filter(v => v.option_id === option_category_name)

        option_categories.push({
            id: text_slugify(option_category_name),
            name: option_category_name,
            sources: [],
            options: options.map(v => ({
                id: text_slugify(v.option),
                name: v.option,
                sources: [],
                price: Number(v.option_price?.replace(',', '')?.trim() ?? 0),
                active: true,
            })),
            category_ids: menu_items.
                filter(v => v.option_ids.split('\n').some(o => text_slugify(o) === text_slugify(option_category_name))).
                map(v => text_slugify(v.name)) ?? [],
            rule: {
                type: options[0].max > 1 ? 'SELECT_MANY' : 'SELECT_ONE',
                required: Number(options[0].min) > 0,
                min_quantity: Number(options[0].min ?? 0),
                max_quantity: Number(options[0].max ?? 1),
            },
        })
    }

    return {
        categories,
        option_categories

    }
}

// Menu for GrabMart with subcategories
const google_sheet_to_grab_mart_menu = async (sheet_id, { menu_name, option_name }) => {
    const sheet_data = await read_sheet(sheet_id, 2, [menu_name, option_name], USE_CACHE)

    const categories = []
    const option_categories = []

    const menu_items = sheet_data[menu_name]
    const menu_options = sheet_data[option_name]
    const grab_category_names = _.uniq(menu_items.map(v => v.grab_category))
    for (const category_name of grab_category_names) {
        const grab_category = GRAB_CATEGORIES.find(v => text_slugify(v.name) === text_slugify(category_name));
        if (!grab_category) continue;
        const sub_categories = []
        const grab_sub_category_names = _.uniq(menu_items.map(v => v.grab_sub_category))
        for (const sub_category_name of grab_sub_category_names) {
            const items = menu_items.filter(v => text_slugify(v.grab_category) === text_slugify(category_name) && text_slugify(v.grab_sub_category) === text_slugify(sub_category_name))
            if (items.length > 0) {
                sub_categories.push({
                    id: text_slugify(sub_category_name),
                    name: sub_category_name,
                    code: text_slugify(sub_category_name),
                    items: items.map(v => ({
                        id: text_slugify(v.name),
                        version: v.version ?? '',
                        code: text_slugify(v.name),
                        name: v.name,
                        description: v.description,
                        price: Number(v.price?.replace(',', '')?.trim()),
                        image: v.image,
                        images: [v.image, v.image_1, v.image_2].filter(v => v),
                        active: true,
                    }))
                })
            }
        }
        if (sub_categories.length > 0) {
            categories.push({
                id: text_slugify(category_name),
                name: category_name,
                code: text_slugify(category_name),
                sub_categories,
                items: []
            })
        }
    }

    // TODO: make option_categories

    return {
        categories,
        option_categories

    }
}

const get_site_promotion_from_google_sheet = async (site_id, source) => {
    const site = await Site.findById(site_id)
    const brand = await Brand.findById(site.brand_id, { name: 1, menu_sheet_file_id: 1 })

    const file = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })

    let brand_promotions = file.sheet_data.MD_promotion.filter(v => v.brand === brand.name)
    if (source) {
        brand_promotions = brand_promotions.filter(v => v[source] === 'TRUE')
    }
    return brand_promotions
}


const get_brand_menu_from_google_sheet = async (brand_id, source) => {
    const brand = await Brand.findById(brand_id, { name: 1, menu_sheet_file_id: 1 });
    const file = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id });
    if (!file) return null;

    let brand_menu_items = file.sheet_data.MD_menu.filter(v => v.brand === brand.name);
    if (source) brand_menu_items = brand_menu_items.filter(v => v[source] === 'TRUE');

    const brand_menu_options = file.sheet_data.MD_option.filter(v => v.brand === brand.name);

    const create_menu_item = v => ({
        id: v.id,
        code: v.item_code ?? text_slugify(v.name),
        name: v.name,
        brand_name: v.product_brand,
        version: v.version,
        description: v.description,
        stock_price: Number(v.stock_price?.replace(',', '')?.trim() ?? 0),
        price: Number(v.price?.replace(',', '')?.trim() ?? 0),
        image: v.image,
        images: [v.image, v.image_1, v.image_2].filter(Boolean),
        quantity_unlimited: Number(v.min_stock ?? 0) === 0,
        quantity_minimum: Number(v.min_stock ?? 0),
        combo: Array.from({ length: 40 }, (_, i) => i + 1)
            .map(i => v[`item${i}_code`] ? {
                name: v[`item${i}_name`],
                code: String(v[`item${i}_code`]),
                quantity: Number(v[`item${i}_quantity`] ?? 0),
                price: Number(v[`item${i}_price`] ?? 0)
            } : null)
            .filter(Boolean),
        active: true
    });

    const menu_mapping = {
        categories: _.uniq(brand_menu_items.map(v => v.category)).map(category_name => {
            const items = brand_menu_items.filter(v => v.category === category_name && !v.sub_category);
            const sub_items = brand_menu_items.filter(v => v.category === category_name && v.sub_category);
            const sub_categories = _.uniq(sub_items.map(v => v.sub_category));

            return {
                id: name_to_id(category_name),
                name: category_name,
                code: text_slugify(category_name),
                description: '',
                active: true,
                items: items.map(create_menu_item),
                sub_categories: sub_categories.map(sub_category_name => {
                    const sub_items = brand_menu_items.filter(v => v.category === category_name && v.sub_category === sub_category_name);
                    return {
                        id: name_to_id(sub_category_name),
                        name: sub_category_name,
                        code: text_slugify(sub_category_name),
                        items: sub_items.map(create_menu_item),
                        active: true,
                    };
                }),
            };
        }),

        option_categories: _.uniq(brand_menu_options.map(v => v.option_id)).map(option_category_name => {
            const options = brand_menu_options.filter(v => v.option_id === option_category_name);
            return {
                id: name_to_id(option_category_name),
                name: option_category_name,
                sources: [],
                options: options.map(v => ({
                    id: name_to_id(v.option),
                    name: v.option,
                    sources: [],
                    price: Number(v.option_price?.replace(',', '')?.trim() ?? 0),
                    active: true,
                    combo: Array.from({ length: 40 }, (_, i) => i + 1)
                        .map(i => v[`item${i}_code`] ? {
                            name: v[`item${i}_name`],
                            code: String(v[`item${i}_code`]),
                            quantity: Number(v[`item${i}_quantity`] ?? 0),
                            price: Number(v[`item${i}_price`] ?? 0)
                        } : null)
                        .filter(Boolean),
                })),
                category_ids: brand_menu_items
                    .filter(v => v.option_ids.split('\n')
                        .some(o => text_slugify(o) === text_slugify(option_category_name)))
                    .map(v => v.id) ?? [],
                rule: {
                    type: options[0].max > 1 ? 'SELECT_MANY' : 'SELECT_ONE',
                    required: Number(options[0].min) > 0,
                    min_quantity: Number(options[0].min ?? 0),
                    max_quantity: Number(options[0].max ?? 1),
                },
            };
        })
    };

    console.log(menu_mapping);
    return menu_mapping;
};

const get_brand_menu_from_google_sheet_for_grab_mart = async (brand_id) => {
    const brand = await Brand.findById(brand_id, { name: 1, menu_sheet_file_id: 1 })
    const file = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const brand_menu_items = file.sheet_data.MD_menu.filter(v => v.brand === brand.name && v.grab_mart === 'TRUE')
    const brand_menu_options = file.sheet_data.MD_option.filter(v => v.brand === brand.name)
    let menu_mapping = {
        categories: [],
        option_categories: []
    }

    const grab_category_names = _.uniq(brand_menu_items.map(v => v.grab_category))

    for (const grab_category_name of grab_category_names) {
        const grab_category = GRAB_CATEGORIES.find(v => text_compare(v.name, grab_category_name));
        if (!grab_category) continue;
        const grab_sub_category_names =
            _.uniq(brand_menu_items.filter(v =>
                v.grab_category === grab_category_name &&
                grab_category.subCategories.some(s => text_compare(s.name, v.grab_sub_category))
            ).map(v => v.grab_sub_category))

        const category = {
            id: name_to_id(grab_category_name),
            name: grab_category_name,
            code: text_slugify(grab_category_name),
            description: '',
            active: true,
            sub_categories: grab_sub_category_names.map(grab_sub_category => {
                const menu_items = brand_menu_items.filter(v => text_compare(v.grab_category, grab_category_name) && text_compare(v.grab_sub_category, grab_sub_category))
                return {
                    id: name_to_id(grab_sub_category),
                    name: grab_sub_category,
                    active: true,
                    items: menu_items.map(v => {
                        return {
                            id: v.id,
                            code: v.item_code ?? text_slugify(v.name),
                            name: v.name,
                            description: v.description,
                            price: Number(v.price?.replace(',', '')?.trim()),
                            image: v.image,
                            images: [v.image, v.image_1, v.image_2].filter(v => v),
                            quantity_unlimited: Number(v.min_stock ?? 0) === 0,
                            quantity_minimum: Number(v.min_stock ?? 0),
                            max_item_per_order: Number(v.max_item_per_order ?? 1000),
                            combo: [
                                {
                                    name: v.item1_name,
                                    code: String(v.item1_code ?? ''),
                                    quantity: Number(v.item1_quantity ?? 0),
                                    price: Number(v.item1_price ?? 0),
                                }, {
                                    name: v.item1_name,
                                    code: String(v.item2_code ?? ''),
                                    quantity: Number(v.item2_quantity ?? 0),
                                    price: Number(v.item2_price ?? 0),
                                }, {
                                    name: v.item1_name,
                                    code: String(v.item3_code ?? ''),
                                    quantity: Number(v.item3_quantity ?? 0),
                                    price: Number(v.item3_price ?? 0),
                                }
                            ].filter(c => c.code),
                            active: true,
                        }
                    }),
                }
            }),
        }
        menu_mapping.categories.push(category)
    }

    const option_category_names = _.uniq(brand_menu_options.map(v => v.option_id))
    for (const option_category_name of option_category_names) {
        const options = brand_menu_options.filter(v => v.option_id === option_category_name)

        menu_mapping.option_categories.push({
            id: name_to_id(option_category_name),
            name: option_category_name,
            sources: [],
            options: options.map(v => ({
                id: name_to_id(v.option),
                name: v.option,
                sources: [],
                price: Number(v.option_price?.replace(',', '')?.trim() ?? 0),
                active: true,
            })),
            category_ids: brand_menu_items.
                filter(v => v.option_ids.split('\n').some(o => text_slugify(o) === text_slugify(option_category_name))).
                map(v => v.id) ?? [],
            rule: {
                type: options[0].max > 1 ? 'SELECT_MANY' : 'SELECT_ONE',
                required: Number(options[0].min) > 0,
                min_quantity: Number(options[0].min ?? 0),
                max_quantity: Number(options[0].max ?? 1),
            },
        })
    }

    return menu_mapping
}
module.exports = { google_sheet_to_menu, get_brand_menu_from_google_sheet, get_brand_menu_from_google_sheet_for_grab_mart, get_site_promotion_from_google_sheet }