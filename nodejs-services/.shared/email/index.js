const axios = require('axios')
const ejs = require('ejs')
const nodemailer = require('nodemailer')
const { EmailTemplate } = require('../database')

let email = {}
email.send_email = async (from, to, name, params, gmail = true) => {
  if (gmail) {
    await email.send_email_with_gmail(from, to, name, params)
    return
  }

  const template = await EmailTemplate.findOne({ name })
  try {
    const resp = await axios.post(
      'https://api.sendgrid.com/v3/mail/send',
      {
        personalizations: [
          {
            to: [
              {
                email: to.email,
              },
            ],
            bcc: [
              {
                email: '<EMAIL>',
              },
            ],
          },
        ],
        from: {
          email: from?.email || '<EMAIL>',
          name: from?.name || 'Leo',
        },
        subject: ejs.compile(template.subject)(params),
        content: [
          {
            type: 'text/html',
            value: ejs.compile(template.body)(params),
          },
        ],
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bear<PERSON> ${process.env.SENDGRID_API_KEY}`,
        },
      }
    )

    console.log('Email sent successfully!')
    console.log(resp.data)
  } catch (error) {
    console.error('Error sending email:', error.response?.data, error)
  }
}

email.send_email_with_gmail = async (from, to, name, params) => {
  const template = await EmailTemplate.findOne({ name })
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'cmskqvxibmtxwhql',
    },
  })
  transporter.sendMail(
    {
      from: '<EMAIL>',
      to: to.email,
      subject: ejs.compile(template.subject)(params),
      html: ejs.compile(template.body)(params),
    },
    (error, info) => {
      if (error) {
        console.log('Error sending email', error)
      } else {
        console.log('Email sent:', info.response)
      }
    }
  )
}

module.exports = email