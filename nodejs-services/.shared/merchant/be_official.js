const axios = require('../axios');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const uuid = require('uuid');
const qs = require('qs');
const _ = require('lodash');

const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;

const API_CONFIG = {
    dev: {
        base_url: "https://gw.veep.me/api/v1/be-food-gateway",
    },
    prod: {
        base_url: "https://gw.be.com.vn/api/v1/be-food-gateway",
        client_id: "uINeX97V5k",
        client_secret: "nY06c8VUOFbWkCeTV6CJ",
    }
}[process.env.NODE_ENV || "dev"]

const base_headers = (access_token) => {
    return {
        'content-type': 'application/json; charset=utf-8',
        Authorization: `Bearer ${access_token}`,
    }
}

const base_body = ({ access_token, site_id }) => {
    const site = JSON.parse(site_id)
    return {
        "access_token": access_token,
        "app_version": "151",
        "device_token": "",
        "locale": "vi",
        "device_type": 0,
        "merchant_id": site.merchant_id,
        "store_id": site.restaurant_id,
        "restaurant_id": site.restaurant_id,
        "user_id": site.user_id,
        "vendor_id": site.vendor_id,
    }
}

async function get_token() {
    if (!password) {
        return null
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: API_CONFIG.base_url + '/v1/authorize',
            headers: base_headers(),
            data: {
                "client_id": API_CONFIG.client_id,
                "client_secret": API_CONFIG.client_secret,
            }
        }

        const resp = await axios(config);
        const access_token = resp.data.access_token
        console.log(access_token)
        return {
            access_token,
        }

    } catch (error) {
        console.log(error.message)
        return null
    }
}

const CANCEL_STATUS = [3, 33, 9, 10, 17, 21, 25];

async function get_order_list_v2({ site_id, access_token }) {
    const result = {
        success: true,
        data: {}
    }

    if (!access_token || skip_call_api()) {
        return result
    }

    try {
        const filter_data = {
            "PENDING": {
                fetch_type: 'pending',
                page: 1,
                limit: 100,
                start_date: moment().format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            },
            "DOING": {
                fetch_type: 'in_progress',
                page: 1,
                limit: 100,
                start_date: moment().format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            },
            "FINISH_OR_CANCEL": {
                fetch_type: 'previous',
                page: 1,
                limit: 100,
                start_date: moment().add(-10, 'days').format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            },
        }

        for (const [status, filter] of Object.entries(filter_data)) {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: API_CONFIG.base_url + `/partner/v1/orders`,
                headers: base_headers(access_token),
                data: {
                    "restaurant_id": site_id,
                    ...filter
                }
            };

            try {
                const resp = await axios(config);
                const orders = resp.data.restaurant_orders
                if (status === 'FINISH_OR_CANCEL') {
                    result.data['FINISH'] = orders.filter(v => !CANCEL_STATUS.includes(v.status))
                    result.data['CANCEL'] = orders.filter(v => CANCEL_STATUS.includes(v.status)) // Get only cancelled orders, 9: cancel by merchant, 10: by admin, 21: by system
                } else {
                    result.data[status] = orders
                }
            } catch (error) {
                console.log(error)
                result.success = false
                return result
            }

        }


        return result

    } catch (error) {
        console.log(error)
        result.success = false
        return result
    }
}


// get_order_list_v2({
//     site_id: '6464',
//     access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjA3Nzc3NzAsImlhdCI6MTcyMDE3Mjk3MCwiY2xpZW50X2lkIjoiNDgwNyIsInBlcm1pc3Npb25zIjpudWxsLCJtZXJjaGFudF9pZCI6NDgwN30.ArAH3Kk6rpaWCVBv9n8wcj8TwF6yTP5i1W9yZiIwLmw'
// })

async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {}
    }
    if (!access_token) {
        return result
    }

    const data = {
        fetch_previous_orders: 1,
        start_index: 0,
        page_size: 200,
        start_date: from.format('YYYY-MM-DD'),
        end_date: to.format('YYYY-MM-DD'),
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: API_CONFIG.base_url + `/partner/v1/orders`,
        headers: base_headers(access_token),
        data: {
            "restaurant_id": site_id,
            fetch_type: 'previous',
            page: 1,
            limit: 100,
            start_date: moment().add(-10, 'days').format('YYYY-MM-DD'),
            end_date: moment().format('YYYY-MM-DD'),
        }
    };

    try {
        const resp = await axios(config);
        const orders = resp.data.order_info
        result.data['FINISH'] = orders?.filter(v => !CANCEL_STATUS.includes(v.status))
        result.data['CANCEL'] = orders?.filter(v => CANCEL_STATUS.includes(v.status))
    } catch (error) {
        console.error(error)
    }
    return result

}

async function set_auto_confirm({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: API_CONFIG.base_url + `/partner/v1/restaurant/update_auto_accept`,
            headers: base_headers(access_token),
            data: {
                restaurant_id: site_id,
                auto_accept: 0,
            }
        };
        const resp = await axios(config);
        console.log(resp.data)
    } catch (err) {
        console.log(err)
    }
}

// set_auto_confirm({
//     site_id: '6464',
//     access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjA3Nzc3NzAsImlhdCI6MTcyMDE3Mjk3MCwiY2xpZW50X2lkIjoiNDgwNyIsInBlcm1pc3Npb25zIjpudWxsLCJtZXJjaGFudF9pZCI6NDgwN30.ArAH3Kk6rpaWCVBv9n8wcj8TwF6yTP5i1W9yZiIwLmw'
// })

async function confirm_order({ site_id, access_token }, order_id) {
    if (!access_token) {
        return {}
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: API_CONFIG.base_url + `/partner/v1/order/accept`,
            headers: base_headers(access_token),
            data: {
                "restaurant_id": site_id,
                order_id: Number(order_id),
            }
        };
        const resp = await axios(config);
        console.log(resp.data)
    } catch (err) {
        console.log(err)
    }
    return {}
}

// confirm_order({
//     site_id: '6464',
//     access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjA3Nzc3NzAsImlhdCI6MTcyMDE3Mjk3MCwiY2xpZW50X2lkIjoiNDgwNyIsInBlcm1pc3Npb25zIjpudWxsLCJtZXJjaGFudF9pZCI6NDgwN30.ArAH3Kk6rpaWCVBv9n8wcj8TwF6yTP5i1W9yZiIwLmw'
// }, 53580)


async function cancel_order({ site_id, access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    const result = {
        success: true,
        message: "",
    }

    if (!access_token) {
        return result
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: API_CONFIG.base_url + `/partner/v1/order/reject`,
            headers: base_headers(access_token),
            data: {
                restaurant_id: site_id,
                order_id: Number(order_id),
                reason: cancel_type === 'out_stock' ? 'Hết hàng' : cancel_reason,
            }
        };
        const resp = await axios(config);
        console.log(resp.data)

    } catch (err) {
        console.error(err)
        result.success = false
        result.message = err.message
    }
    return result
}

// cancel_order({
//     site_id: '6464',
//     access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjA3Nzc3NzAsImlhdCI6MTcyMDE3Mjk3MCwiY2xpZW50X2lkIjoiNDgwNyIsInBlcm1pc3Npb25zIjpudWxsLCJtZXJjaGFudF9pZCI6NDgwN30.ArAH3Kk6rpaWCVBv9n8wcj8TwF6yTP5i1W9yZiIwLmw',

// }, 53581, { cancel_type: 'out_stock' })


async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: API_CONFIG.base_url + `/partner/v1/order`,
        headers: base_headers(access_token),
        data: {
            restaurant_id: site_id,
            order_id: Number(order_id),
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.order
    } catch (err) {
        console.log(err)
    }
    return {}
}
// get_order_detail({
//     site_id: '6464',
//     access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjA3Nzc3NzAsImlhdCI6MTcyMDE3Mjk3MCwiY2xpZW50X2lkIjoiNDgwNyIsInBlcm1pc3Npb25zIjpudWxsLCJtZXJjaGFudF9pZCI6NDgwN30.ArAH3Kk6rpaWCVBv9n8wcj8TwF6yTP5i1W9yZiIwLmw'
// }, 53515)


async function sync_site_menu({ site_id, access_token }, { categories, option_categories }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: API_CONFIG.base_url + `/partner/v1/menu/sync_restaurant_menu`,
        headers: base_headers(access_token),
        data: {
            restaurant_id: site_id,
            data: categories.map((category, category_index) => ({
                partner_category_id: category.id,
                category_name: category.name,
                category_index: category_index + 1,
                item_list: category.items.map((item, index) => ({
                    restaurant_item_name: item.name,
                    restaurant_item_details: item.description,
                    restaurant_item_price: item.price,
                    restaurant_item_image: item.images[0],
                    display_index: index + 1,
                    status: item.active ? 1 : 3, // 1: Active  0: Out of stock  3: Disable
                    reference_id: item.id,
                    option_groups: option_categories.filter(v => v.category_ids.some(c => item.id)).map(v => ({
                        reference_id: v.id,
                        merchant_option_group_name: v.name,
                        is_required: v.rule.min_quantity > 0,
                        minimum: v.rule.min_quantity,
                        maximum: v.rule.max_quantity,
                        options: v.options.map(o => ({
                            name: o.name,
                            price: o.price || 0,
                            partner_option_id: uuid.v4(),
                            status: o.active ? 1 : 3, // 1: Active  0: Out of stock  3: Disable
                        }))
                    }))
                }))
            }))
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.order
    } catch (err) {
        console.log(err)
    }
    return {}
}



module.exports = {
    base_headers,
    base_body,
    get_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    cancel_order,
    sync_site_menu,
}