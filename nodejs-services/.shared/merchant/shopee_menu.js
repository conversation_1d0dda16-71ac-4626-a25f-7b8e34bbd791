const axios = require('../axios');
const moment = require('moment-timezone');
const _ = require('lodash');
const { base_headers } = require('./shopee')
const FormData = require('form-data');
const { _textToSlug } = require('./mapping');
const { sign_request_async, get_menu } = require('./shopee');

const upload_image = async function ({ site_id, access_token }, image_url) {
    try {
        if (!image_url)
            return {}

        const resp_image_download = await axios.get(image_url, { responseType: 'arraybuffer' });
        const form = new FormData();
        form.append("picture", resp_image_download.data, { filename: 'dish_item.jpg', contentType: 'image/jpeg' })

        let config_upload = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/picture`,
            headers: {
                ...base_headers({ site_id, access_token }),
                ...form.getHeaders()
            },
            data: form,
        };
        // config_upload = await sign_request_async(config_upload)

        const resp_upload = await axios(config_upload);
        return resp_upload.data.data;
    } catch (error) {
        console.error('Error uploading and downloading the image:', error.message);
        return {}
    }
}
let shopee_menu = {}
shopee_menu.sync_menu = async function ({ site_id, access_token }, { site_menu, item_id }) {
    if (!access_token) {
        return {}
    }

    try {

        let group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes`,
            headers: base_headers({ site_id, access_token }),
        };
        group_menu_config = await sign_request_async(group_menu_config)

        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.data?.catalogs


        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            for (let i = 0; i < site_menu.categories[c].items.length; i++) {
                const item = site_menu.categories[c].items[i];
                if (String(item._id) === item_id) {
                    const new_image = await upload_image({ site_id, access_token }, item.image)

                    const group_menu_category_index = _.findIndex(group_menu, v => v.catalog_name.toUpperCase() === category.name.toUpperCase())
                    // Create or update category
                    if (group_menu_category_index < 0) {
                        let config_create_category = {
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/catalogs/bulk_create`,
                            headers: base_headers({ site_id, access_token }),
                            data: { "catalogs": [{ "name": category.name.toUpperCase() }] }
                        }
                        config_create_category = await sign_request_async(config_create_category)

                        await axios(config_create_category);

                        shopee_menu.sync_menu({ site_id, access_token }, { site_menu, item_id })
                        // Only create category then loopback
                    } else {
                        const group_menu_category = group_menu[group_menu_category_index]
                        const group_menu_category_item_index = _.findIndex(group_menu_category.dishes, v => v.name.toUpperCase() === item.name.toUpperCase())
                        if (group_menu_category_item_index < 0) {
                            // create menu item
                            let config_create_item = {
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dish/create`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "name": item.name.toUpperCase(),
                                    "catalog_id": group_menu_category.catalog_id,
                                    "description": item.description,
                                    "price": item.price,
                                    "picture_id": new_image.picture_id
                                }
                            }
                            config_create_item = await sign_request_async(config_create_item)
                            const group_menu_category_item_resp = await axios(config_create_item);
                            console.log(group_menu_category_item_resp.data)

                        } else {
                            // update menu item with price, active, image and descripton
                            // const group_menu_category_item = group_menu_category.items[group_menu_category_item_index]
                            // No support edit

                        }

                    }
                }

            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

shopee_menu.delete_menu_category_item = async function ({ site_id, access_token }, { category_name, item_name }) {
    if (!access_token) {
        return {}
    }

    try {
        let group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes`,
            headers: base_headers({ site_id, access_token }),
        };
        group_menu_config = await sign_request_async(group_menu_config)
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.data?.catalogs
        for (let c = 0; c < group_menu.length; c++) {
            const category = group_menu[c];
            if (_textToSlug(category_name) === _textToSlug(category.catalog_name)) {
                for (let i = 0; i < category.dishes.length; i++) {
                    const item = category.dishes[i];
                    if (_textToSlug(item_name) === _textToSlug(item.name)) {
                        let config_delete_item = {
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dish/delete`,
                            headers: base_headers({ site_id, access_token }),
                            data: { "dish_id": item.id }
                        }
                        config_delete_item = await sign_request_async(config_delete_item)
                        const delete_category_resp = await axios(config_delete_item)
                        console.log(delete_category_resp.data)
                    }
                }
            }

        }
    } catch (err) {
        console.log(err)
        return null
    }
}

shopee_menu.delete_menu_category = async function ({ site_id, access_token }, { category_name }) {
    if (!access_token) {
        return {}
    }

    try {
        let group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes`,
            headers: base_headers({ site_id, access_token }),
        };
        group_menu_config = await sign_request_async(group_menu_config)
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.data?.catalogs
        for (let c = 0; c < group_menu.length; c++) {
            const category = group_menu[c];
            if (_textToSlug(category_name) === _textToSlug(category.catalog_name)) {
                let config_delete_category = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/catalog/delete`,
                    headers: base_headers({ site_id, access_token }),
                    data: { "catalog_id": category.catalog_id }
                }
                config_delete_category = await sign_request_async(config_delete_category)
                const delete_category_resp = await axios(config_delete_category)
                console.log(delete_category_resp.data)
            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

// shopee_menu.delete_menu_category({
//     site_id: "10041460",
//     access_token: "B:YOknwDKpYkQAm1cYcXJXcONuFTbWKfPEJiOLbkcLSnXoFbBUFshgn4HPsdOilHSop+2TiX9+SIcHIOSCOOgqT+ygGa+dt/uu2+8yUTyBygI=",
// }, {
//     category_name: "test",
//     item_name: "test23",
// })



shopee_menu.active_menu_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await get_menu({ site_id, access_token })
        for (const category of group_menu.categories) {
            D: for (const item of category.dishes) {
                let update_item = _.find(update_items, v => _textToSlug(item.name) === _textToSlug(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue D
                    }
                    update_item = {
                        name: item.name,
                        quantity: 0,
                        active: false,
                    }
                }
                const old_status = item.stock_info?.stock === 1
                const new_status = update_item.active
                if (old_status === new_status)
                    continue

                let config_update_item = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes/stock/bulk_set`,
                    headers: base_headers({ site_id, access_token }),
                    data: {
                        "dishes": [
                            {
                                "dish_id": item.id,
                                "stock": new_status ? 1 : 2
                            }
                        ]
                    }

                }
                config_update_item = await sign_request_async(config_update_item)
                const group_menu_category_item_resp = await axios(config_update_item);
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }

        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// shopee_menu.active_menu_item({
//     access_token: "eyJhbGciOiJkaXIiLCJjdHkiOiJKV1QiLCJlbmMiOiJBMTI4R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0..NvVKBAibziVCQ0dR.tkXh9WEIjfc7LWkSYVstNSVzCif_eUdQQh6PmjqEuvtUuABr7Noe9HpAwZEJkletDlgZOytsXU1kbAGuuTjdPwPXlZ6enrB7n7bJUb3PGJ94bP7lWzgnDh7_gE9OBFLzhTUytsn9x-LEcCxCB4k9pYAh0l7OFUikWkGuU1nubFcNY8wIxENZcvhyBhuPFC-0b6XUtdEj2NVdWqviE5sbXnModBEmX_IDfsoDmdEeDpgzP6v4icwNgr-2jIBvGkx-bM8vGG2pGgkwz3qRADDm1Vg3_iHKyI_soair7YtojhfgSbLbGHxJkyqRYiRQJRWE1sNsmWz_n8rO4-CXBT2kC49ursh8Z54F-eZtQs7fJ30J4aiWB5tpKBWX9x8M-jQYTV0JMjGKDZDvweFhdCJMYh9lt0tNRwN-w468hccbbnmWBCez-dHtB3Sh1ej9YdhB--QwLQekGi4Tc7Nir8UNZc3yiFLmywD8YjzEL5IEZHgGMLiHkQbmTB2ul1Ox-xSf7geAM7s_sWf5DlZ-J8nNuvR_jfqpA5bbQu4mBcVvHpi8jHIgkOoItHUXg7G592GfDJkpKtoWBL4n_tGe323lF9n3zQNcftdlWuPbW2VD2tOGlZLxTq0WCodQYZ7Obhxbeah8JEUKeUSgoNkYWOhQvQD0UGZAzQkVjcGYI1vQnH4KDv-iVSVAoCOmaWVFe48LzrajB3d6MzENwzh3Xgc9-M3qklW6GndjUmel5wF64xYw0Aq5cVj16z7kWJeRALY-os_fsbV7UWECNWe7gkm0SL_3E5xSVHZLGz-TrYEHKUNF8llT9BFnVXVNp578xHtn2ddzuX7HEGkvdLv0cAdQGGTTwhPa3HamSAKeJ5dpnxMsX3lWZvt1ZZUpbfGwAUUYBFDVjC8ejqyFj3DOQzVN24mQ2YHacXDJD0n8_dmB_7Cp5qLMxb0gLeR1cu7JDqx8Tcy5e4kiDEvfWTwXvxB4e-LLYTkaZH1tx66DpHffi9H0nj1nGlEXYre0EBMlRI8ndaW8msyDespUuKszyjIiv3FwQB046rwQONl5WPBuIBGazbbcmiRsK8BhhBOPnBYcjqeLt54y0mlmsxKvNoUtN_O2wSMCVbtOxM1EhfrfGvVwdYEXTQ1HxpvlT1zXUp5uhpoIhPL8_X4lAag1o2VckC-MWVITKcKzF85rAqR--zEagp-zhH_6_hs7o6ZoRLhpCCssSe433KNf6xEpco405-3joKu5z9P9WAPYYsxboqWq7olcyR06vlES-DIgYCNQVh40vy_mmonjOCr8PE-FBLjPqFA_4P5vNacFt10cTfdnbX079gL4PZXmzF_LhQEBgk-lThnoCrwQHMLc6zLJABn4xMQyUBxN9a9Bx5kY07WtwDUfChZmaTI7sKr1s7wXFMN61fPR-Zxbdnmj3uBl_o0xtPRlk-3YAizqxEri--kxwcvwla5gGQSiyjgA-4dLKm-w6UKOuklICgwmjwAhk5k8gIRZiPyf8G6tMQdeVdSEVMrQmXyYpzbC0hWzEvzU-3uLx1hQ3fgSrYNgGTR_tnbLYUqJ-EoXdIvlw3lrwruA_1m1s-PzUDbig9j3WgpCXUXBGdflNrBTc526Ish3TH5BkvofR3LjZLpx7DS2IEdTaMvnatiGINpmKH_TOyU0mBQDxfB15ar5IDD6mRbMRqCBgDe7YhtvdVcHl2VxsW59Mvp6g2xg9JgKyhRvqphgFDKb-uYnYob-NCY4Cj-oYij9mIwAn2gNl6PlfqHX8o01AK7gn6LseikaLjN3wbPOKK_-nVtf7pw35sqjAyomcb8f-MJFw5FxgA2ODgF1zKxnnTFQkJJ6NXJIBubjViJohccgnfE6fr0eVXDy3HIhir7F2yVUa2CUdMdzMNE54cQDl0Sp7Ea8gb3OuhiaemYUcw.vYQfwuP0lS9NbSzj7AVaJA",
//     site_id: "b7571932-df49-46b9-ba34-e8272a8c5c3d",
// }, [{
//     name: "Combo Dinh Dưỡng Tiện Lợi Học Đường",
//     active: true
// }])


// Deactive dish when option is not available
shopee_menu.active_menu_option_item = async function ({ site_id, access_token }, update_items) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await get_menu({ site_id, access_token })
        for (const category of group_menu.option_categories) {
            for (const item of category.options) {
                const update_item = _.find(update_items, v => _textToSlug(category.name) === _textToSlug(v.category_name) && _textToSlug(item.name) === _textToSlug(v.name))
                if (!update_item) {
                    continue
                }

                const old_status = item.stock_info?.stock === 1
                const new_status = update_item.active
                if (old_status === new_status)
                    continue

                let config_update_item = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/options/stock/bulk_set`,
                    headers: base_headers({ site_id, access_token }),
                    data: {
                        "options": [
                            {
                                "option_id": item.id,
                                "stock": new_status ? 1 : 2
                            }
                        ]
                    }
                }
                config_update_item = await sign_request_async(config_update_item)
                const group_menu_category_item_resp = await axios(config_update_item);
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }

        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}


// shopee_menu.active_menu_option_item({
//     access_token: "eyJhbGciOiJkaXIiLCJjdHkiOiJKV1QiLCJlbmMiOiJBMTI4R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0..NvVKBAibziVCQ0dR.tkXh9WEIjfc7LWkSYVstNSVzCif_eUdQQh6PmjqEuvtUuABr7Noe9HpAwZEJkletDlgZOytsXU1kbAGuuTjdPwPXlZ6enrB7n7bJUb3PGJ94bP7lWzgnDh7_gE9OBFLzhTUytsn9x-LEcCxCB4k9pYAh0l7OFUikWkGuU1nubFcNY8wIxENZcvhyBhuPFC-0b6XUtdEj2NVdWqviE5sbXnModBEmX_IDfsoDmdEeDpgzP6v4icwNgr-2jIBvGkx-bM8vGG2pGgkwz3qRADDm1Vg3_iHKyI_soair7YtojhfgSbLbGHxJkyqRYiRQJRWE1sNsmWz_n8rO4-CXBT2kC49ursh8Z54F-eZtQs7fJ30J4aiWB5tpKBWX9x8M-jQYTV0JMjGKDZDvweFhdCJMYh9lt0tNRwN-w468hccbbnmWBCez-dHtB3Sh1ej9YdhB--QwLQekGi4Tc7Nir8UNZc3yiFLmywD8YjzEL5IEZHgGMLiHkQbmTB2ul1Ox-xSf7geAM7s_sWf5DlZ-J8nNuvR_jfqpA5bbQu4mBcVvHpi8jHIgkOoItHUXg7G592GfDJkpKtoWBL4n_tGe323lF9n3zQNcftdlWuPbW2VD2tOGlZLxTq0WCodQYZ7Obhxbeah8JEUKeUSgoNkYWOhQvQD0UGZAzQkVjcGYI1vQnH4KDv-iVSVAoCOmaWVFe48LzrajB3d6MzENwzh3Xgc9-M3qklW6GndjUmel5wF64xYw0Aq5cVj16z7kWJeRALY-os_fsbV7UWECNWe7gkm0SL_3E5xSVHZLGz-TrYEHKUNF8llT9BFnVXVNp578xHtn2ddzuX7HEGkvdLv0cAdQGGTTwhPa3HamSAKeJ5dpnxMsX3lWZvt1ZZUpbfGwAUUYBFDVjC8ejqyFj3DOQzVN24mQ2YHacXDJD0n8_dmB_7Cp5qLMxb0gLeR1cu7JDqx8Tcy5e4kiDEvfWTwXvxB4e-LLYTkaZH1tx66DpHffi9H0nj1nGlEXYre0EBMlRI8ndaW8msyDespUuKszyjIiv3FwQB046rwQONl5WPBuIBGazbbcmiRsK8BhhBOPnBYcjqeLt54y0mlmsxKvNoUtN_O2wSMCVbtOxM1EhfrfGvVwdYEXTQ1HxpvlT1zXUp5uhpoIhPL8_X4lAag1o2VckC-MWVITKcKzF85rAqR--zEagp-zhH_6_hs7o6ZoRLhpCCssSe433KNf6xEpco405-3joKu5z9P9WAPYYsxboqWq7olcyR06vlES-DIgYCNQVh40vy_mmonjOCr8PE-FBLjPqFA_4P5vNacFt10cTfdnbX079gL4PZXmzF_LhQEBgk-lThnoCrwQHMLc6zLJABn4xMQyUBxN9a9Bx5kY07WtwDUfChZmaTI7sKr1s7wXFMN61fPR-Zxbdnmj3uBl_o0xtPRlk-3YAizqxEri--kxwcvwla5gGQSiyjgA-4dLKm-w6UKOuklICgwmjwAhk5k8gIRZiPyf8G6tMQdeVdSEVMrQmXyYpzbC0hWzEvzU-3uLx1hQ3fgSrYNgGTR_tnbLYUqJ-EoXdIvlw3lrwruA_1m1s-PzUDbig9j3WgpCXUXBGdflNrBTc526Ish3TH5BkvofR3LjZLpx7DS2IEdTaMvnatiGINpmKH_TOyU0mBQDxfB15ar5IDD6mRbMRqCBgDe7YhtvdVcHl2VxsW59Mvp6g2xg9JgKyhRvqphgFDKb-uYnYob-NCY4Cj-oYij9mIwAn2gNl6PlfqHX8o01AK7gn6LseikaLjN3wbPOKK_-nVtf7pw35sqjAyomcb8f-MJFw5FxgA2ODgF1zKxnnTFQkJJ6NXJIBubjViJohccgnfE6fr0eVXDy3HIhir7F2yVUa2CUdMdzMNE54cQDl0Sp7Ea8gb3OuhiaemYUcw.vYQfwuP0lS9NbSzj7AVaJA",
//     site_id: "b7571932-df49-46b9-ba34-e8272a8c5c3d",
// }, [{
//     category_name: "CHỌN 1 BÁNH",
//     name: "Bánh Phô Mai Castella Kido's 40g - 600000192",
//     active: false
// }])

module.exports = shopee_menu
