const axios = require('../axios')
const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok')

const base_url = 'https://apis.haravan.com/com'

// nexpos status: enum: ['DRAFT', 'PRE_ORDER', 'WAITING_PAYMENT', 'PENDING', 'DOING', 'FINISH', 'CANCEL']
const haravan_to_nexpos_status = (order) => {
  if (order.cancelled_status === 'cancelled') {
    return 'CANCEL'
  }

  if (order.fulfillment_status === 'fulfilled') {
    return 'FINISH'
  }

  // if (order.fulfillment_status === 'notfulfilled') {
  //   return 'DOING'
  // }

  // if (order.financial_status === 'pending') {
  //   return 'WAITING_PAYMENT'
  // }

  return 'PENDING'
}

async function get_order_list({ access_token }, status) {
  if (!access_token) {
    return []
  }

  try {
    const config = {
      method: 'get',
      url: base_url + `/orders.json`,
      headers: {
        'Authorization': `Bear<PERSON> ${access_token}`
      },
    }

    const { data } = await axios(config)

    return data.orders.map((r) => ({ ...r, status: haravan_to_nexpos_status(r) })).filter((r) => r.status === status)
  } catch (err) {
    console.log(err)
    return []
  }
}

async function get_order_list_by_duration({ access_token }, { from, to }) {
  const result = {
    success: true,
    data: {}
  }
  if (!access_token) {
    return result
  }

  const created_at_min = moment(from).toISOString()
  const created_at_max = moment(to).toISOString()

  try {
    let page = 1
    while (true) {
      const config = {
        method: 'get',
        url: base_url + `/orders.json`,
        headers: {
          'Authorization': `Bearer ${access_token}`
        },
        params: {
          limit: 50,
          created_at_min,
          created_at_max,
          page
        }
      }


      const { data } = await axios(config)
      if (!data.orders.length) {
        break
      }
      result.data.FINISH.push(...data.orders.filter(v => ["PICKED", "DELIVERED", "COMPLETED"].includes(v.status)))
      result.push(...data.orders)
      page++
    }
  } catch (err) {
    console.log(err)
  }
  return result
}

async function get_order_detail({ access_token }, order_id) {
  if (!access_token) {
    return {}
  }

  try {
    const config = {
      method: 'get',
      url: base_url + `/orders/${order_id}.json`,
      headers: {
        'Authorization': `Bearer ${access_token}`
      },
    }

    const { data } = await axios(config)

    if (!data.order) {
      return {}
    }

    return { ...data.order, status: haravan_to_nexpos_status(data.order) }
  } catch (err) {
    console.log(err)
    return {}
  }
}

async function get_store({ access_token }) {
  if (!access_token) {
    return {}
  }

  try {
    const config = {
      method: 'get',
      url: base_url + '/shop.json',
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    }

    const resp = await axios(config)

    return resp.data?.shop
  } catch (error) {
    console.log(error)
    return null
  }
}

async function confirm_order({ access_token }, order_id) {
  if (!access_token) {
    return {}
  }

  return {}
}

async function get_menu({ access_token }) {
  if (!access_token) {
    return {
      categories: [],
      option_categories: [],
    }
  }

  let config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `https://apis.haravan.com/com/products.json`,
    headers: {
      'Authorization': `Bearer ${access_token}`
    }
  };

  try {
    const resp = await axios(config);
    return {
      categories: resp.data.products,
      option_categories: [],
    }
  } catch (error) {
    console.log(error)
    return {
      categories: [],
      option_categories: [],
    }
  }
}


module.exports = {
  get_order_list,
  get_order_detail,
  confirm_order,
  get_order_list_by_duration,
  get_store,
  get_menu,
}
