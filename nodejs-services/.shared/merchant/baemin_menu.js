const axios = require('../axios');
const moment = require('moment');
const { base_headers } = require('./baemin')
const FormData = require('form-data');
const _ = require("lodash");
const { _textToSlug } = require('./mapping');

const upload_image = async function ({ site_id, access_token }, image_url) {
    try {
        if (!image_url)
            return ""
        const resp_image_download = await axios.get(image_url, { responseType: 'arraybuffer' });

        let config_pre_upload = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/file-upload/generate-presigned-url?merchantId=${site_id}`,
            headers: base_headers({ access_token }),
            data: { "target": "DISH", "type": "jpeg", "isTemp": false },
        };

        const resp_pre_upload = await axios(config_pre_upload);
        const form = new FormData();
        for (const field in resp_pre_upload.data.fields) {
            form.append(field, resp_pre_upload.data.fields[field])
        }
        form.append("file", resp_image_download.data)
        await axios.post(resp_pre_upload.data.url, form, {
            headers: form.getHeaders(),
        });
        return resp_pre_upload.data.cdnUrl;
    } catch (error) {
        console.error('Error uploading and downloading the image:', error.message);
        return ""
    }
}

let baemin_menu = {}
baemin_menu.sync_menu = async function ({ site_id, access_token }, { site_menu, item_id }) {
    if (!access_token) {
        return {}
    }

    try {
        const store_resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        });
        const store = store_resp.data
        const group_menu = store.sections

        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            for (let i = 0; i < site_menu.categories[c].items.length; i++) {
                const item = site_menu.categories[c].items[i];
                if (String(item._id) === item_id) {
                    const new_image = await upload_image({ site_id, access_token }, item.image)

                    const group_menu_category_index = _.findIndex(group_menu, v => v.name.toUpperCase() === category.name.toUpperCase())
                    // Create or update category
                    if (group_menu_category_index < 0) {
                        group_menu.push({
                            name: category.name.toUpperCase(),
                            position: (_.max(group_menu.map(v => v.position)) || 0) + 1
                        })
                        const group_menu_category_resp = await axios({
                            method: 'put',
                            maxBodyLength: Infinity,
                            url: `https://api.baemin.vn/v3/menu/stores/${site_id}/sections/bulk`,
                            headers: base_headers({ site_id, access_token }),
                            data: { "sections": group_menu }
                        });

                        const new_group_menu_category_index = _.findIndex(group_menu_category_resp.data.sections, v => v.name.toUpperCase() === category.name.toUpperCase())
                        const group_menu_category_item_resp = await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.baemin.vn/v3/menu/stores/${site_id}/products`,
                            headers: base_headers({ site_id, access_token }),
                            data: {
                                "id": "",
                                "name": item.name.toUpperCase(),
                                "description": item.description,
                                "price": item.price,
                                "sectionId": group_menu_category_resp.data.sections[new_group_menu_category_index].id,
                                "imageUrl": new_image,
                                "thumbnailImageUrl": new_image
                            }
                        });
                        console.log(group_menu_category_item_resp.data)
                    } else {
                        const group_menu_category = group_menu[group_menu_category_index]
                        const group_menu_category_item_index = _.findIndex(store.dishes, v => v.name.toUpperCase() === item.name.toUpperCase() && v.sectionId === group_menu_category.id)
                        if (group_menu_category_item_index < 0) {
                            // create menu item
                            const group_menu_category_item_resp = await axios({
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://api.baemin.vn/v3/menu/stores/${site_id}/products`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "id": "",
                                    "name": item.name.toUpperCase(),
                                    "description": item.description,
                                    "price": item.price,
                                    "sectionId": group_menu_category.id,
                                    "imageUrl": new_image,
                                    "thumbnailImageUrl": new_image
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                        } else {
                            // update menu item with price, active, image and descripton
                            const group_menu_category_item = store.dishes[group_menu_category_item_index]
                            const group_menu_category_item_resp = await axios({
                                method: 'put',
                                maxBodyLength: Infinity,
                                url: `https://api.baemin.vn/v3/menu/stores/${site_id}/products/${group_menu_category_item.id}`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    ...group_menu_category_item,
                                    "description": item.description,
                                    "price": item.price,
                                    "name": item.name.toUpperCase(),
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                        }

                    }
                }

            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

baemin_menu.delete_menu_category_item = async function ({ site_id, access_token }, { category_name, item_name }) {
    if (!access_token) {
        return {}
    }

    try {
        const store_resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        });
        const store = store_resp.data
        const group_menu = store.sections

        for (let c = 0; c < group_menu.length; c++) {
            const category = group_menu[c];
            if (_textToSlug(category_name) === _textToSlug(category.name)) {
                for (let i = 0; i < store.dishes.length; i++) {
                    const item = store.dishes[i];
                    if (item.sectionId === category.id && _textToSlug(item_name) === _textToSlug(item.name)) {
                        const delete_category_item_resp = await axios({
                            method: 'delete',
                            maxBodyLength: Infinity,
                            url: `https://api.baemin.vn/v3/menu/stores/${site_id}/products/${item.id}`,
                            headers: base_headers({ site_id, access_token }),
                        })
                        console.log(delete_category_item_resp.data)
                    }
                }
            }

        }
    } catch (err) {
        console.log(err)
        return null
    }
}

baemin_menu.delete_menu_category = async function ({ site_id, access_token }, { category_name }) {
    if (!access_token) {
        return {}
    }

    try {
        const store_resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        });
        const store = store_resp.data
        const group_menu = store.sections

        const group_menu_category_index = _.findIndex(group_menu, v => _textToSlug(v.name) === _textToSlug(category_name))
        if (group_menu_category_index > 0) {
            group_menu.splice(group_menu_category_index, 1)
            const delete_category_resp = await axios({
                method: 'put',
                maxBodyLength: Infinity,
                url: `https://api.baemin.vn/v3/menu/stores/${site_id}/sections/bulk`,
                headers: base_headers({ site_id, access_token }),
                data: {
                    sections: group_menu,
                }
            })
            console.log(delete_category_resp.data)
        }

    } catch (err) {
        console.log(err)
        return null
    }
}

// [{ category_name, item_name, active = false }]
baemin_menu.active_menu_item = async function ({ site_id, access_token }, update_items) {
    if (!access_token) {
        return []
    }

    let updated_items = []
    try {
        const store_resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        });
        const store = store_resp.data
        const group_menu = store.sections

        for (const update_item of update_items) {
            for (let c = 0; c < group_menu.length; c++) {
                const category = group_menu[c];
                if (_textToSlug(update_item.category_name) === _textToSlug(category.name)) {
                    for (let i = 0; i < store.dishes.length; i++) {
                        const dish = store.dishes[i];
                        if (dish.sectionId === category.id && _textToSlug(update_item.name) === _textToSlug(dish.name)) {
                            if (dish.isSoldOut !== !update_item.active || dish.isAvailable !== update_item.active) {
                                const update_category_item_resp = await axios({
                                    method: 'put',
                                    maxBodyLength: Infinity,
                                    url: `https://api.baemin.vn/v3/stores/${site_id}/dishes/${dish.id}`,
                                    headers: base_headers({ site_id, access_token }),
                                    data: {
                                        "isAvailable": update_item.active,
                                        "isSoldOut": !update_item.active,
                                        "isFeatured": dish.isFeatured
                                    }
                                })
                                console.log(update_category_item_resp.data)
                                updated_items.push(update_item)
                            }
                        }
                    }
                }

            }
        }
    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// baemin_menu.delete_menu_category({
//     site_id: "FRWl2AmMy",
//     access_token: "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
// }, {
//     category_name: "test",
//     item_name: "test23",
// })

// [{ category_name, name, active = false }]
baemin_menu.active_menu_option_item = async function ({ site_id, access_token }, update_items) {
    if (!access_token) {
        return []
    }

    let updated_items = []
    try {
        const store_resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        });
        const store = store_resp.data
        const group_menu = store.dishes

        for (const update_item of update_items) {
            let option_list_in_dishes = []
            for (let c = 0; c < group_menu.length; c++) {
                const dish = group_menu[c];
                for (const option of dish.options) {
                    for (const item of option.items) {
                        if (_textToSlug(update_item.category_name) === _textToSlug(option.name) && _textToSlug(item.name) === _textToSlug(update_item.name)) {
                            if (item.isSoldOut !== !update_item.active || item.isAvailable !== update_item.active) {
                                option_list_in_dishes.push({
                                    optionId: option.id,
                                    id: item.id,
                                    isAvailable: update_item.active,
                                    isSoldOut: !update_item.active,
                                    scheduleConfig: item.scheduleConfig,
                                })
                                updated_items.push(update_item)
                            }
                        }
                    }
                }
            }

            if (option_list_in_dishes.length > 0) {
                const chunked_option_list = _.chunk(option_list_in_dishes, 20);
                for (let i = 0; i < chunked_option_list.length; i++) {
                    const update_category_item_resp = await axios({
                        method: 'put',
                        maxBodyLength: Infinity,
                        url: `https://api.baemin.vn/v3/menu/stores/${site_id}/option-items`,
                        headers: base_headers({ site_id, access_token }),
                        data: {
                            data: chunked_option_list[i]
                        }
                    })
                    console.log(update_category_item_resp.data)
                }
            }
        }
        updated_items = _.uniqBy(updated_items, v => `${v.category_name}_${v.name}`)
    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// baemin_menu.active_menu_option_item({ site_id: "c0Fwj-2kj", access_token: "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" }, {
//     category_name: "Chọn vị gà",
//     option_name: "Ớt cay Địa Trung Hải",
//     active: true
// })
module.exports = baemin_menu