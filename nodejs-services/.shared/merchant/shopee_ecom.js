const axios = require('../axios');
const fs = require('fs');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const _ = require('lodash');

const skip_call_api = () => false;


const base_headers = ({ site_id, access_token }) => {
    return {
        'Cookie': `language=vi;shopee_token=${access_token}; ; SPC_IA=-1`,
        'User-Agent': 'Android app Shopee appver=32334 app_type=1 platform=native_android os=29',
        'x-api-source': 'rn',
        'X-Shopee-Client-Timezone': 'Asia/Ho_Chi_Minh',
        'x-shopee-language': 'vi'
    }
}



async function get_order_list_v2({ access_token, site_id }) {
    const result = {
        success: true,
        data: {}
    }

    if (!access_token || skip_call_api()) {
        return result
    }

    try {
        const filter_data = {
            DRAFT: {
                list_type: 'unpaid',
                order_by_create_date: "desc",
                sc_search_version: 2,
            },
            DOING: {
                list_type: 'toship',
                order_by_create_date: "desc",
                sc_search_version: 2,
            },
            PICK: {
                list_type: 'shipping',
                order_by_create_date: "desc",
                sc_search_version: 2,
            },
            FINISH: {
                list_type: 'completed',
                order_by_create_date: "desc",
                sc_search_version: 2,
            },
            CANCEL: {
                list_type: 'cancelled_all',
                order_by_create_date: "desc",
                sc_search_version: 2,
            },
        }
        for (const [status, filter] of Object.entries(filter_data)) {
            let has_more = true
            let offset = 0
            let limit = 60
            result.data[status] = []
            while (has_more && offset < 100) {
                let config = {
                    method: 'get',
                    maxBodyLength: Infinity,
                    url: 'https://seller-app.shopee.vn/api/app/fulfillment/order/get_order_list',
                    headers: base_headers({ site_id, access_token }),
                    params: filter
                };

                const resp = await axios(config);
                const orders = resp?.data?.data?.orders || []
                result.data[status].push(...orders)
                has_more = result.data[status].length < resp.data.data.meta.total
                offset += limit
            }
        }

        return result
    } catch (err) {
        console.log(err.message)
        result.success = false
        return result
    }
}

async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    return await get_order_list_v2({ access_token, site_id })
}

async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/fulfillment/order/get_one_order?need_invoice_status=1&order_id=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };

    let config2 = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/basicservice/finance/income_t_his_detail?order_id=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };

    let config3 = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/fulfillment/logistics/get_base?order_id=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };


    let order = {}

    try {
        const resp = await axios(config);
        order = resp.data.data

        const resp2 = await axios(config2);
        order.transaction = resp2.data.data

        const resp3 = await axios(config3);
        order.logistic = resp3.data.data
        return order
    } catch (error) {
        console.log(error.message)
        return null
    }

}

async function confirm_order({ site_id, access_token }, order_id) {
    if (!access_token) {
        return {}
    }
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/fulfillment/order/get_package?order_id=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };

    const resp = await axios(config);
    console.log("order.get_package", resp.data)
    const package_list = resp.data?.data?.order_info?.package_list || []
    if (package_list.length === 0) {
        return null
    }
    const package_number = package_list[0].package_number

    let config2 = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/fulfillment/logistics/get_base?order_id=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };

    const resp2 = await axios(config2);
    console.log("order.get_base", resp2.data)

    let config3 = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://seller-app.shopee.vn/api/app/fulfillment/logistics/get_pickup_timeslots?address_id=${resp2.data.data.pickup_address.address_id}&order_id=${order_id}`,
        headers: base_headers({ site_id, access_token }),
    };

    const resp3 = await axios(config3);
    console.log("order.get_pickup_timeslots", resp3.data)

    let config4 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://seller-app.shopee.vn/api/app/fulfillment/logistics/init',
        headers: base_headers({ site_id, access_token }),
        data: {
            "order_id": resp2.data.data.order_id,
            "package_number": package_number,
            "channel_id": resp2.data.data.channel_id,
            "pickup_address_id": resp2.data.data.pickup_address.address_id,
            "pickup_time": resp3.data.data.days[0].value,
            "seller_real_name": ""
        }
    };
    console.log("order.logistic_init", config4.data)

    const resp4 = await axios(config4);

    return resp4.data
}

async function update_store_status({ site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/${status === 'close' ? 'set-busy' : 'set-open'}`,
        headers: base_headers({ site_id, access_token }),
        data: status === 'close' ? {
            "from_time": moment().format('YYYY-MM-DD HH:mm:ss'),
            "to_time": duration ? moment().add(duration, 'minute').format('YYYY-MM-DD HH:mm:ss') : moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        } : {}
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(err)
        return {}
    }

}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/refact/get`,
        headers: base_headers({ site_id, access_token })
    };

    try {
        const resp = await axios(config);
        return !resp.data?.data?.opening_status?.close_status?.close_type && !resp.data?.data?.opening_status?.pause_time?.pause_end_time
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ site_id, access_token }) {
    if (!access_token) {
        return []
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/get`,
        headers: base_headers({ site_id, access_token }),
    };

    const resp = await axios(config);
    return resp.data.data.regular_hours
}

async function update_opening_hour({ site_id, access_token }, working_hours) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: { regular_hours: working_hours }
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_menu({ site_id, access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes`,
        headers: base_headers({ site_id, access_token }),
    };

    const resp = await axios(config);

    config.url = "https://gmerchant.deliverynow.vn/api/v5/seller/store/option-groups"
    const resp_2 = await axios(config);

    return {
        categories: resp.data.data.catalogs,
        option_categories: resp_2.data.data.option_groups,
    }
}

async function get_store({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/get_basic_info?request_action_type=3`,
        headers: base_headers({ site_id, access_token }),
    };
    try {
        const resp = await axios(config);
        return resp.data.data
    } catch (error) {
        console.log(error)
        return null
    }

}

// status: open or close
async function set_special_open_status({ site_id, access_token }, { from, to, status }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "special_hours": [
                {
                    "date_start": moment(from).local().startOf('day').unix() * 1000, //1704042000000,
                    "date_end": moment(to).local().endOf('day').unix() * 1000 + 999, //1704128399999,
                    "date_type": 1,
                    "date_desc": "",
                    "intervals": []
                }
            ]
        }
    };
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}


async function delete_special_open_status({ site_id, access_token }, { from, to, status }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "special_hours": []
        }
    };
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function print_order_bill({ access_token, site_id }, order_id) {
    if (!access_token) {
        return null
    }
    try {
        let config1 = {
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://seller-app.shopee.vn/api/app/fulfillment/order/get_package?order_id=' + order_id,
            headers: base_headers({ site_id, access_token }),
        };

        const resp1 = await axios(config1);
        console.log("order.get_package", resp1.data)

        const package_number = resp1.data.data.order_info.package_list[0].package_number
        let config2 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://seller-app.shopee.vn/api/app/fulfillment/shipping_document/create_job',
            headers: base_headers({ site_id, access_token }),
            data: {
                "package_list": [
                    {
                        "region_id": "VN",
                        "shop_id": Number(site_id),
                        "package_number": package_number
                    }
                ],
                "generate_file_details": [
                    {
                        "file_name": "Phiếu gửi hàng",
                        "file_type": "NORMAL_PDF",
                        "file_contents": [1, 0]
                    }
                ]
            }
        };

        const resp2 = await axios(config2);
        console.log("order.create_job", resp2.data)
        const job_id = resp2.data.data.result_list[0].job_id
        await new Promise(resolve => setTimeout(resolve, 3000)); // Sleep for 3 seconds
        let config3 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://seller-app.shopee.vn/api/app/fulfillment/shipping_document/download_job`,
            headers: base_headers({ site_id, access_token }),
            data: { "job_id": job_id }
        };
        const resp3 = await axios(config3);
        console.log("order.download_job", resp3.data)

        let config4 = {
            method: 'get',
            maxBodyLength: Infinity,
            url: resp3.data.data.download_url,
            headers: base_headers({ site_id, access_token }),
            responseType: 'arraybuffer'
        };
        const resp4 = await axios(config4);
        return resp4.data

    } catch (err) {
        console.log(err.message)
        return null
    }
}
// print_order_bill({ access_token: "F/zocfCwm8KeeE1AychoCfUbzA3+eO4zSVY94YVRfF1lbzSbJQ2G923h3a72VYop", site_id: '1230167130' }, "168044510284923")

module.exports = {
    base_headers,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    update_store_status,
    get_open_status,
    get_opening_hour,
    update_opening_hour,
    get_menu,
    get_store,
    set_special_open_status,
    delete_special_open_status,
    print_order_bill,
}