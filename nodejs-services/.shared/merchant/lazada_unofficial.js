const _ = require('lodash')
const qs = require('qs');
const axios = require('../axios')
const moment = require('moment')
const crypto = require('crypto')

const base_headers = ({ site_id }) => {
  const token = JSON.parse(site_id)
  return {
    'content-type': 'application/x-www-form-urlencoded;charset=UTF-8,application/x-www-form-urlencoded;charset=UTF-8',
    'x-app-ver': '3.12.2',
    'x-appkey': '24755651',
    'x-features': '27',
    'x-pv': '6.2',
    'x-sid': token.sid,
    'x-sign': '',
    'x-t': moment().unix().toString(),
    // 'x-t': '1718813663',
    'x-ttid': '212200@sellerapp_android_3.12.2',
    'x-uid': token.uid,
    'x-utdid': token.utdid,
  }
}
const sign_request_config = async (config) => {
  let match = config.url.match(/\/gw\/([^\/]+)\/([^\/]+)\//)
  const sign_config = {
    "data": config.data.replace('data=', ''),
    "pv": config.headers['x-pv'],
    "deviceId": null,
    "sid": config.headers['x-sid'],
    "uid": config.headers['x-uid'],
    "x-features": config.headers['x-features'],
    "appKey": config.headers['x-appkey'],
    "api": match[1],
    "utdid": config.headers['x-utdid'],
    "ttid": config.headers['x-ttid'],
    "t": config.headers['x-t'],
    "v": match[2],
  }
  try {
    // 14.187.106.134
    // 113.177.133.116
    const resp = await fetch("http://113.177.133.116:5000/getMtopApiSign", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sign_config),
      redirect: "follow"
    }).then(res => res.json())
    console.log(resp)
    config.headers['x-sign'] = resp.result?.split('|')[1]
  } catch (e) {
    console.error(e)
  }

  return config
}

const lazada = {}


lazada.get_order_detail = async function ({ site_id }, order_id) {
  if (!site_id) {
    return null
  }

  let result = null
  try {
    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://acs-m.lazada.vn/gw/mtop.lazada.seller.mobile.order.query.detail/1.0/',
      headers: base_headers({ site_id }),
      data: `data={"tradeOrderId":"${order_id}"}`
    }

    await sign_request_config(config)

    const resp = await axios(config)
    result = resp.data.data
    if (resp.data.data.success) {
      const order = _.keyBy(resp.data.data.data, 'name')
      console.log(JSON.stringify(order, null, 2))
      return order
    }
  } catch (e) {
    console.error(e)
    console.log(resp.config.curlCommand)
  }
  return null
}

module.exports = lazada