const axios = require('../axios');
const redis = require('../redis')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');

const CLIENT_ID = `617a6739664a4ce3a60b70cd1255cb75`
const CLIENT_SECRET = `78ALvfSN2FlfSXvI`

const base_headers = ({ access_token }) => {
    return {
        'Authorization': 'Bearer ' + access_token,
        'Content-Type': 'application/json',
    }
}

async function get_token(user_name, password) {
    try {
        const master_token_resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.grab.com/grabid/v1/oauth2/token',
            headers: {
                'Content-Type': 'application/json'
            },
            data: {
                "client_id": user_name,
                "client_secret": password,
                "grant_type": "client_credentials",
                "scope": "mart.partner_api"
            }
        })
        return {
            access_token: master_token_resp.data.access_token
        }
    } catch (err) {
        console.log(err)
    }
    return null
}


async function get_order_list_v2({ site_id }) {
    const result = {
        success: true,
        data: {}
    }

    const { access_token } = await get_token(CLIENT_ID, CLIENT_SECRET)
    // DOING
    {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://partner-api.grab.com/grabmart/partner/v1/orders`,
            headers: base_headers({ access_token }),
            params: {
                merchantID: site_id,
                date: moment().format('YYYY-MM-DD'),
                page: 1
            }
        };

        try {
            const resp = await axios(config);
            const STATUS_FILTER = {
                'DOING': ["DRIVER_ALLOCATED", "DRIVER_ARRIVED"],
                'PICK': ["COLLECTED"],
                'FINISH': ["DELIVERED"],
                'CANCEL': ["CANCELLED", "FAILED"]
            };

            if (resp?.data?.orders) {
                Object.keys(STATUS_FILTER).forEach(category => {
                    result.data[category] = resp.data.orders.filter(order => STATUS_FILTER[category].includes(order.orderState)) || [];
                });
            }
        } catch (err) {
            console.log(err)
            result.success = false
        }

    }

    return result
}
// get_order_list_v2({ site_id: '5-C3NJCCDCT3EUAA' })

async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: [],
        }
    }

    if (!access_token) {
        return result
    }


    try {
        let page_index = 0
        while (true) {
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=${page_index}&pageSize=100&startTime=${from.toISOString()}&endTime=${to.toISOString()}`,
                headers: base_headers({ site_id, access_token }),
            };

            const resp = await axios(config);
            if (resp.data.statements?.length > 0) {
                result.data.FINISH.push(...resp.data.statements.filter(v => ["COMPLETED"].includes(v.deliveryStatus)))
                result.data.CANCEL.push(...resp.data.statements.filter(v => ["FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"].includes(v.deliveryStatus)))
            }
            page_index++
            if (!resp.data.hasMore)
                break
        }
    } catch (error) {
        console.log(error)
    }
    return result
}

// TODO: Get from API
const GRAB_CATEGORIES = [
    {
        "id": "VNITEDP20210506042859017662",
        "name": "Sales",
        "subCategories": [
            {
                "id": "VNITEDP20210506043137014915",
                "name": "Khuyến mãi cuối tuần"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031535014185",
        "name": "Khác",
        "subCategories": [
            {
                "id": "VNITEDP20200727073238018013",
                "name": "Chuyên đồ Hàn"
            },
            {
                "id": "VNITEDP20200727073257010018",
                "name": "Chuyên đồ Nhật"
            },
            {
                "id": "VNITEDP20200727073316016999",
                "name": "Annam Made"
            },
            {
                "id": "VNITEDP20200727073337015325",
                "name": "Vouchers"
            },
            {
                "id": "VNITEDP20220127033547011678",
                "name": "GrabFood Merchandise"
            }
        ]
    },
    {
        "id": "VNITEDP20210208072525011894",
        "name": "Tết tất tần tật",
        "subCategories": [
            {
                "id": "VNITEDP20210208072558011268",
                "name": "Tròn vị Tết"
            },
            {
                "id": "VNITEDP20211129073542015225",
                "name": "Giỏ quà biếu Tết"
            },
            {
                "id": "VNITEDP20211231023132019078",
                "name": "Trang trí Tết"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031456019938",
        "name": "Thực phẩm chức năng",
        "subCategories": [
            {
                "id": "VNITEDP20200727073004016729",
                "name": "Thành phẩn thảo dược"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031446010491",
        "name": "Làm đẹp",
        "subCategories": [
            {
                "id": "VNITEDP20200727072746010868",
                "name": "Mỹ phẩm"
            },
            {
                "id": "VNITEDP20200727072843014761",
                "name": "Dao cạo"
            },
            {
                "id": "VNITEDP20200727072901018156",
                "name": "Nhíp"
            }
        ]
    },
    {
        "id": "VNITEDP20210506025222016200",
        "name": "Payday",
        "subCategories": [
            {
                "id": "VNITEDP20210506042215010499",
                "name": "Cuối tháng lương về"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031511012712",
        "name": "Khuyến mại",
        "subCategories": [
            {
                "id": "VNITEDP20210428070747016781",
                "name": "Flashsale"
            },
            {
                "id": "VNITEDP20210428070835017038",
                "name": "Lễ hội sắc đẹp"
            },
            {
                "id": "VNITEDP20210428070922016866",
                "name": "Ưu đãi hấp dẫn"
            },
            {
                "id": "VNITEDP20211012034239014024",
                "name": "Quà tặng Giáng Sinh"
            },
            {
                "id": "VNITEDP20220207074846015824",
                "name": "Quà tặng Valentine"
            },
            {
                "id": "VNITEDP20220222070243011406",
                "name": "Quà tặng 8/3"
            },
            {
                "id": "VNITEDP20220505050103012638",
                "name": "Ngày Của Mẹ"
            },
            {
                "id": "VNITEDP20220624074245018470",
                "name": "Quà Tặng Trung Thu"
            },
            {
                "id": "VNITEDP20231002044242011517",
                "name": "Quà Tặng 20.10"
            }
        ]
    },
    {
        "id": "VNITEDP20210824050519014517",
        "name": "Combo Mùa Dịch",
        "subCategories": [
            {
                "id": "VNITEDP20210824050629016525",
                "name": "Combo thực phẩm tươi sống"
            },
            {
                "id": "VNITEDP20210824050942010599",
                "name": "Combo thực phẩm công nghệ"
            },
            {
                "id": "VNITEDP20210824051009017255",
                "name": "Combo hoá phẩm"
            }
        ]
    },
    {
        "id": "VNITEDP20210825045209017686",
        "name": "Tops Độc Quyền - Tops Exclusive",
        "subCategories": [
            {
                "id": "VNITEDP20210825045304019421",
                "name": "My Choice"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031025018104",
        "name": "Các loại bánh",
        "subCategories": [
            {
                "id": "VNITEDP20200727034724010734",
                "name": "Pizza"
            }
        ]
    },
    {
        "id": "VNITEDP20240423093247018242",
        "name": "Nestle",
        "subCategories": [
            {
                "id": "VNITEDP20240423093610019493",
                "name": "Maggi"
            },
            {
                "id": "VNITEDP20240423093721016149",
                "name": "Ưu Đãi"
            }
        ]
    },
    {
        "id": "VNITEDP20240428074915014729",
        "name": "Đồ chơi & Trò chơi",
        "subCategories": [
            {
                "id": "VNITEDP20200727034514010868",
                "name": "Đồ chơi dành cho trẻ em"
            },
            {
                "id": "VNITEDP20240502074002012775",
                "name": "Các loại thẻ bài (boardgame), các lại bộ bài khác"
            }
        ]
    },
    {
        "id": "VNITEDP20240428075022015035",
        "name": "Phụ kiện điện tử",
        "subCategories": [
            {
                "id": "VNITEDP20240502074503016438",
                "name": "Củ sạc, dây cáp sạc có dây và không dây"
            },
            {
                "id": "VNITEDP20240502074521019362",
                "name": "Các phụ kiện khác dành cho điện thoại di động và máy tính bảng"
            },
            {
                "id": "VNITEDP20240502074539011298",
                "name": "Pin, bộ sạc pin, pin sạc dự phòng"
            },
            {
                "id": "VNITEDP20240502074553012136",
                "name": "Đèn LED rời"
            },
            {
                "id": "VNITEDP20240502074612015100",
                "name": "Ốp lưng, bao da cho điện thoại, máy tính bảng và thiết bị di động khác"
            },
            {
                "id": "VNITEDP20240502074647014305",
                "name": "Miếng dán màn hình cho các thiết bị di động"
            },
            {
                "id": "VNITEDP20240502075430013815",
                "name": "Gậy chụp hình, Tay cầm chống rung (gimbal) cho các thiết bị di động"
            },
            {
                "id": "VNITEDP20240502075457018894",
                "name": "Thẻ nhớ và USB"
            }
        ]
    },
    {
        "id": "VNITEDP20240428074524018211",
        "name": "Chăm sóc xe",
        "subCategories": [
            {
                "id": "VNITEDP20200727074511013278",
                "name": "Phụ kiện dành cho xe ô tô"
            },
            {
                "id": "VNITEDP20240521110749017746",
                "name": "Dầu nhớt ô tô"
            },
            {
                "id": "VNITEDP20240521110811014414",
                "name": "Bộ phận ô tô & phụ tùng thay thế"
            },
            {
                "id": "VNITEDP20240521110830019137",
                "name": "Bánh & Vỏ xe"
            },
            {
                "id": "VNITEDP20240521110849012318",
                "name": "Dụng cụ & thiết bị ô tô"
            },
            {
                "id": "VNITEDP20240521110917011517",
                "name": "Ăc quy & phụ kiện"
            },
            {
                "id": "VNITEDP20240521110940017861",
                "name": "Dung dịch/chế phẩm tẩy/rửa xe"
            }
        ]
    },
    {
        "id": "VNITEDP20240428075130014021",
        "name": "Thời trang & Du lịch",
        "subCategories": [
            {
                "id": "VNITEDP20200727075358013081",
                "name": "Chăm sóc giày"
            },
            {
                "id": "VNITEDP20200727080712018542",
                "name": "Áo mưa"
            },
            {
                "id": "VNITEDP20240502081023015596",
                "name": "Thời trang bé gái"
            },
            {
                "id": "VNITEDP20240502081043017598",
                "name": "Thời trang bé trai"
            },
            {
                "id": "VNITEDP20240502081141015244",
                "name": "Thời trang nam"
            },
            {
                "id": "VNITEDP20240502081159016637",
                "name": "Thời trang nữ"
            },
            {
                "id": "VNITEDP20240502081223019737",
                "name": "Thời trang Unisex"
            },
            {
                "id": "VNITEDP20240502081245012743",
                "name": "Vali và túi xách"
            }
        ]
    },
    {
        "id": "VNITEDP20240428075209013853",
        "name": "Văn phòng phẩm và nghề thủ công",
        "subCategories": [
            {
                "id": "VNITEDP20240502053111017146",
                "name": "Tạp chí"
            },
            {
                "id": "VNITEDP20240502063057017037",
                "name": "Sách"
            },
            {
                "id": "VNITEDP20240502063347012642",
                "name": "Bao bì, các tông, bìa đựng"
            },
            {
                "id": "VNITEDP20240502064227017396",
                "name": "Vở, tập viết, sổ ghi"
            },
            {
                "id": "VNITEDP20240502064453016274",
                "name": "Dụng cụ may vá"
            },
            {
                "id": "VNITEDP20240502064543019775",
                "name": "Dụng cụ/phụ kiện cho trường học"
            },
            {
                "id": "VNITEDP20240502064615014700",
                "name": "Dụng cụ/phụ kiện cho văn phòng"
            },
            {
                "id": "VNITEDP20240502064706013732",
                "name": "Quà tặng, gói quà và lưu niệm"
            },
            {
                "id": "VNITEDP20240502064758019278",
                "name": "Sản phẩm khác từ giấy"
            },
            {
                "id": "VNITEDP20240502064831012384",
                "name": "Thiết bị/dụng cụ thủ công"
            },
            {
                "id": "VNITEDP20240502064855015308",
                "name": "Dụng cụ cho nghệ thuật"
            },
            {
                "id": "VNITEDP20240502064919014919",
                "name": "Các loại lịch"
            }
        ]
    },
    {
        "id": "VNITEDP20240428074503014889",
        "name": "Bếp & Phòng ăn",
        "subCategories": [
            {
                "id": "VNITEDP20200727080929017810",
                "name": "Dụng cụ đựng/bảo quản thực phẩm"
            },
            {
                "id": "VNITEDP20240502070130015107",
                "name": "Đồ dùng để ăn"
            },
            {
                "id": "VNITEDP20240502070157010860",
                "name": "Đồ dùng để uống"
            },
            {
                "id": "VNITEDP20240502070323012752",
                "name": "Dụng cụ làm bánh"
            },
            {
                "id": "VNITEDP20240502070453010756",
                "name": "Dụng cụ nấu ăn"
            },
            {
                "id": "VNITEDP20240502070544016730",
                "name": "Dụng cụ sơ chế thực phẩm"
            },
            {
                "id": "VNITEDP20240502070653018782",
                "name": "Khăn trải bàn & Phụ kiện nhà bếp bằng vải"
            }
        ]
    },
    {
        "id": "VNITEDP20200727030936016985",
        "name": "Chăm sóc thú cưng",
        "subCategories": [
            {
                "id": "VNITEDP20200727033833018416",
                "name": "Thức ăn cho chó"
            },
            {
                "id": "VNITEDP20200727033941011804",
                "name": "Thức ăn cho mèo"
            },
            {
                "id": "VNITEDP20200727034003018005",
                "name": "Thức ăn cho cá"
            },
            {
                "id": "VNITEDP20200727034020012056",
                "name": "Thức ăn cho chim"
            },
            {
                "id": "VNITEDP20200727084443018758",
                "name": "Chăm sóc thú cưng khác"
            },
            {
                "id": "VNITEDP20240502081651015900",
                "name": "Chăm sóc cơ thể thú cưng"
            },
            {
                "id": "VNITEDP20240502081723017140",
                "name": "Phụ kiện cho thú cưng"
            },
            {
                "id": "VNITEDP20240502081831013796",
                "name": "Thuốc thú ý và sản phẩm hỗ trợ sức khỏe thú cưng"
            },
            {
                "id": "VNITEDP20240502081911012779",
                "name": "Thức ăn cho vật nuôi khác"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031010014569",
        "name": "Sản phẩm cho mẹ và bé",
        "subCategories": [
            {
                "id": "VNITEDP20200727034158011294",
                "name": "Đồ ăn em bé"
            },
            {
                "id": "VNITEDP20200727034221012066",
                "name": "Sữa công thức"
            },
            {
                "id": "VNITEDP20200727034241011334",
                "name": "Sữa tắm"
            },
            {
                "id": "VNITEDP20200727034258015715",
                "name": "Tã/bỉm cho em bé"
            },
            {
                "id": "VNITEDP20200727034321012664",
                "name": "Phụ kiện"
            },
            {
                "id": "VNITEDP20200727034348015471",
                "name": "Quần áo"
            },
            {
                "id": "VNITEDP20200727034414013268",
                "name": "Dinh dưỡng cho mẹ bầu"
            },
            {
                "id": "VNITEDP20200727065252014526",
                "name": "Chăm sóc sức khỏe cho trẻ"
            },
            {
                "id": "VNITEDP20240502075738010518",
                "name": "Sữa bột / sữa công thức cho trẻ dưới 24 tháng tuổi"
            },
            {
                "id": "VNITEDP20240502075759013810",
                "name": "Sữa bột / sữa công thức cho trẻ trên 24 tháng tuổi"
            },
            {
                "id": "VNITEDP20240502075852012542",
                "name": "Đồ ăn/thức uống cho bé dưới 6 tháng tuổi"
            },
            {
                "id": "VNITEDP20240502075919013925",
                "name": "Khăn ướt cho em bé"
            },
            {
                "id": "VNITEDP20240502075949018502",
                "name": "Dụng cụ ăn dặm cho em bé"
            },
            {
                "id": "VNITEDP20240502080018017730",
                "name": "Ti ngậm, đồ dùng bú sữa và phụ kiện dùng bú sữa"
            },
            {
                "id": "VNITEDP20240502080050014045",
                "name": "Dụng cụ vệ sinh và chăm sóc cơ thể trẻ"
            },
            {
                "id": "VNITEDP20240502080122019211",
                "name": "Máy hút sữa"
            },
            {
                "id": "VNITEDP20240502080141014035",
                "name": "Các sản phẩm chăm sóc da cho em bé"
            },
            {
                "id": "VNITEDP20240502080209014014",
                "name": "Tinh dầu tắm dành cho em bé"
            },
            {
                "id": "VNITEDP20240502080247012095",
                "name": "Sữa rửa mặt dành cho em bé"
            },
            {
                "id": "VNITEDP20240502080309018925",
                "name": "Nước xã vải cho bé"
            },
            {
                "id": "VNITEDP20240502080520014331",
                "name": "Xe tập đi và Ghế ngồi ăn cho bé"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031246011958",
        "name": "Đồ hộp",
        "subCategories": [
            {
                "id": "VNITEDP20200727070135015203",
                "name": "Hoa quả đóng hộp"
            },
            {
                "id": "VNITEDP20200727070223017359",
                "name": "Rau củ đóng hộp"
            },
            {
                "id": "VNITEDP20200727071546018000",
                "name": "Đậu đóng hộp"
            },
            {
                "id": "VNITEDP20200727072035016091",
                "name": "Thịt đóng hộp"
            },
            {
                "id": "VNITEDP20200727072122017178",
                "name": "Hải sản đóng hộp"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031258019981",
        "name": "Chăm sóc sức khỏe",
        "subCategories": [
            {
                "id": "VNITEDP20200727072508014116",
                "name": "Nước rửa tay"
            },
            {
                "id": "VNITEDP20200727072927018414",
                "name": "Bổ sung khoáng chất"
            },
            {
                "id": "VNITEDP20200727073022012673",
                "name": "Dinh dưỡng cho thể thao"
            },
            {
                "id": "VNITEDP20200727073044015240",
                "name": "Thực phẩm chức năng Kiểm soát cân nặng"
            },
            {
                "id": "VNITEDP20200727073119011623",
                "name": "Bỏ 3"
            },
            {
                "id": "VNITEDP20200727073355012170",
                "name": "Dụng cụ sơ cứu"
            },
            {
                "id": "VNITEDP20200727073435010148",
                "name": "Máy trợ thính"
            },
            {
                "id": "VNITEDP20200727073500013541",
                "name": "Đèn trị liệu nhẹ"
            },
            {
                "id": "VNITEDP20200727073546019478",
                "name": "Thuốc không kê đơn"
            },
            {
                "id": "VNITEDP20200727073629012471",
                "name": "Khẩu trang y tế"
            },
            {
                "id": "VNITEDP20240502073249013086",
                "name": "Khẩu trang vải"
            },
            {
                "id": "VNITEDP20240502073331011723",
                "name": "Men vi sinh"
            },
            {
                "id": "VNITEDP20240502073406012209",
                "name": "Thiết bị theo dõi sức khỏe"
            },
            {
                "id": "VNITEDP20240502073519010457",
                "name": "Thực phẩm chức năng Chăm sóc sắc đẹp"
            },
            {
                "id": "VNITEDP20240502073609015375",
                "name": "Thực phẩm chức năng Chăm sóc sức khỏe nam và nữ"
            },
            {
                "id": "VNITEDP20240502073652015404",
                "name": "Thực phẩm chức năng Nhóm cơ xương khớp"
            },
            {
                "id": "VNITEDP20240502073709010982",
                "name": "Thực phẩm chức năng Nhóm dạ dày và gan"
            },
            {
                "id": "VNITEDP20240502073727014409",
                "name": "Thực phẩm chức năng Nhóm hô hấp"
            },
            {
                "id": "VNITEDP20240502073749017357",
                "name": "Thực phẩm chức năng Nhóm Mắt/Tai/Mũi"
            },
            {
                "id": "VNITEDP20240502073810017764",
                "name": "Thực phẩm chức năng Nhóm thần kinh"
            },
            {
                "id": "VNITEDP20240502073829012850",
                "name": "Thực phẩm chức năng Nhóm tim mạch và đường huyết"
            }
        ]
    },
    {
        "id": "VNITEDP20200724103144010169",
        "name": "Cây và hoa",
        "subCategories": [
            {
                "id": "VNITEDP20200727043840012758",
                "name": "Hoa nhân tạo"
            },
            {
                "id": "VNITEDP20200727043900015755",
                "name": "Hoa thiết kế"
            },
            {
                "id": "VNITEDP20200727043923019638",
                "name": "Hoa ăn được"
            },
            {
                "id": "VNITEDP20200727064459015565",
                "name": "Hoa tự nhiên"
            },
            {
                "id": "VNITEDP20200727065201018378",
                "name": "Chăm sóc cây, hoa"
            },
            {
                "id": "VNITEDP20240227042812010786",
                "name": "Hoa nguyên liệu / dụng cụ cắm hoa"
            },
            {
                "id": "VNITEDP20240227042744017849",
                "name": "Hoa cành/nhành lẻ"
            },
            {
                "id": "VNITEDP20240227042722011231",
                "name": "Hoa tặng dịp lễ"
            },
            {
                "id": "VNITEDP20240227042701017132",
                "name": "Hoa cúng"
            },
            {
                "id": "VNITEDP20240227042645013432",
                "name": "Hoa chia buồn"
            },
            {
                "id": "VNITEDP20240227042619011914",
                "name": "Hoa chúc mừng"
            },
            {
                "id": "VNITEDP20200727074058014973",
                "name": "Dụng cụ làm vườn"
            },
            {
                "id": "VNITEDP20240502070933011447",
                "name": "Cây nhân tạo, cây khô"
            },
            {
                "id": "VNITEDP20240502071000019057",
                "name": "Cây tự nhiên"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031208017828",
        "name": "Thực phẩm đông lạnh và đã sơ chế",
        "subCategories": [
            {
                "id": "VNITEDP20200727044159015182",
                "name": "Kem cây, kem hộp đông lạnh"
            },
            {
                "id": "VNITEDP20200727065352019258",
                "name": "Thực phẩm chế biến sẵn đông lạnh (Chưa được nấu chín)"
            },
            {
                "id": "VNITEDP20200727082652014768",
                "name": "Thực phẩm đã sơ chế"
            },
            {
                "id": "VNITEDP20200727092956013273",
                "name": "Hoa quả đông lạnh"
            },
            {
                "id": "VNITEDP20200727093032012218",
                "name": "Thực phẩm chế biến sẵn đông lạnh (đã được nấu chín)"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031420018742",
        "name": "Dầu ăn, nước chấm, gia vị",
        "subCategories": [
            {
                "id": "VNITEDP20200727083021017605",
                "name": "Dầu ăn, dấm"
            },
            {
                "id": "VNITEDP20200727083042012530",
                "name": "Đường, muối"
            },
            {
                "id": "VNITEDP20200727083102018299",
                "name": "Nước chấm"
            },
            {
                "id": "VNITEDP20200727083223016683",
                "name": "Gia vị"
            },
            {
                "id": "VNITEDP20200727083257018507",
                "name": "Sốt"
            },
            {
                "id": "VNITEDP20200727083347012228",
                "name": "Mật ong"
            },
            {
                "id": "VNITEDP20200727083428018745",
                "name": "Nguyên liệu làm bánh"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031409017870",
        "name": "Thực phẩm sơ chế",
        "subCategories": [
            {
                "id": "VNITEDP20200727082449015315",
                "name": "Món ăn nhẹ"
            },
            {
                "id": "VNITEDP20200727082947017770",
                "name": "Thịt nguội thịt sơ chế"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031233013595",
        "name": "Đồ khô & Thực phẩm đóng gói",
        "subCategories": [
            {
                "id": "VNITEDP20200727055549017148",
                "name": "Hạt/Bột ngũ cốc"
            },
            {
                "id": "VNITEDP20200727062302010006",
                "name": "Yến mạch"
            },
            {
                "id": "VNITEDP20200727062609010032",
                "name": "Gạo"
            },
            {
                "id": "VNITEDP20200727062812013497",
                "name": "Bột mì"
            },
            {
                "id": "VNITEDP20200727064603013582",
                "name": "Các loại đậu hạt khô"
            },
            {
                "id": "VNITEDP20200727064705018911",
                "name": "Lúa mì"
            },
            {
                "id": "VNITEDP20200727065241017616",
                "name": "Mì ăn liền"
            },
            {
                "id": "VNITEDP20200727065344011773",
                "name": "Cháo gói, cháo tươi"
            },
            {
                "id": "VNITEDP20200727065551016731",
                "name": "Mì, nui, bún khô"
            },
            {
                "id": "VNITEDP20200727082251012323",
                "name": "Thực phẩm ăn liền đã nấu chín"
            },
            {
                "id": "VNITEDP20230606031924013310",
                "name": "Đồ chay"
            },
            {
                "id": "VNITEDP20240502074118015790",
                "name": "Hủ tiếu, miến, phở, bún ăn liền các loại"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031312017742",
        "name": "Chăm sóc nhà cửa và đời sống",
        "subCategories": [
            {
                "id": "VNITEDP20200727073721016828",
                "name": "Dụng cụ và dung dịch vệ sinh chén bát"
            },
            {
                "id": "VNITEDP20200727073749016618",
                "name": "Chế phẩm diệt côn trùng"
            },
            {
                "id": "VNITEDP20200727073820013295",
                "name": "Khử mùi phòng"
            },
            {
                "id": "VNITEDP20200727073847014126",
                "name": "Dụng cụ vệ sinh phòng tắm"
            },
            {
                "id": "VNITEDP20200727074538010370",
                "name": "Pin"
            },
            {
                "id": "VNITEDP20200727074637012617",
                "name": "Dụng cụ lau dọn"
            },
            {
                "id": "VNITEDP20200727074725018981",
                "name": "Giấy vệ sinh/khăn giấy/khăn ướt các loại"
            },
            {
                "id": "VNITEDP20200727074816012984",
                "name": "Dụng cụ giặt ủi"
            },
            {
                "id": "VNITEDP20200727075637016887",
                "name": "Đồ chứa rác"
            },
            {
                "id": "VNITEDP20200727075900014452",
                "name": "Dụng cụ nhà bếp"
            },
            {
                "id": "VNITEDP20200727080758011944",
                "name": "Đồ dùng khác"
            },
            {
                "id": "VNITEDP20220919102345019622",
                "name": "Vật phẩm thờ cúng"
            },
            {
                "id": "VNITEDP20240502072821017057",
                "name": "Đồ dùng phòng ngủ"
            },
            {
                "id": "VNITEDP20240502072913019849",
                "name": "Đồ dùng phòng tắm"
            },
            {
                "id": "VNITEDP20240502072956014614",
                "name": "Dụng cụ điện"
            },
            {
                "id": "VNITEDP20240502073041017611",
                "name": "Phụ kiện trang trí"
            },
            {
                "id": "VNITEDP20240502073144017359",
                "name": "Vệ sinh sàn nhà"
            }
        ]
    },
    {
        "id": "VNITEDP20200708074509017555",
        "name": "Bánh kẹo, đồ ăn vặt các loại",
        "subCategories": [
            {
                "id": "VNITEDP20200708074525015003",
                "name": "Trái cây sấy, hạt khô (có thể ăn liền)"
            },
            {
                "id": "VNITEDP20200727034555011381",
                "name": "Bánh mì"
            },
            {
                "id": "VNITEDP20200727034614013826",
                "name": "Bánh kem"
            },
            {
                "id": "VNITEDP20200727034630017026",
                "name": "Bánh mềm, bánh tươi"
            },
            {
                "id": "VNITEDP20200727034852016256",
                "name": "Bánh quy"
            },
            {
                "id": "VNITEDP20200727034925019375",
                "name": "Bánh truyền thống"
            },
            {
                "id": "VNITEDP20200727035000019257",
                "name": "Bánh/thanh ngũ cốc"
            },
            {
                "id": "VNITEDP20200727035254018438",
                "name": "Snack, rong biển ăn liền"
            },
            {
                "id": "VNITEDP20200727035317010014",
                "name": "Bánh quy giòn"
            },
            {
                "id": "VNITEDP20200727035340013437",
                "name": "Thịt / hải sản khô"
            },
            {
                "id": "VNITEDP20200727035446019715",
                "name": "Bắp rang"
            },
            {
                "id": "VNITEDP20200727035521016820",
                "name": "Pudding"
            },
            {
                "id": "VNITEDP20200727035543019494",
                "name": "Bánh quy, bánh gạo"
            },
            {
                "id": "VNITEDP20200727035557010720",
                "name": "Các loại hạt"
            },
            {
                "id": "VNITEDP20200727035609010583",
                "name": "Chocolate"
            },
            {
                "id": "VNITEDP20200727035623014030",
                "name": "Các loại kẹo"
            },
            {
                "id": "VNITEDP20200727064217011532",
                "name": "Tráng miệng"
            },
            {
                "id": "VNITEDP20200727064259016945",
                "name": "Ăn vặt khác"
            },
            {
                "id": "VNITEDP20200915032907010093",
                "name": "Bánh Trung Thu"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031432011829",
        "name": "Chăm sóc cá nhân & Làm đẹp",
        "subCategories": [
            {
                "id": "VNITEDP20200727072043019589",
                "name": "Chăm sóc cơ thể"
            },
            {
                "id": "VNITEDP20200727072117014131",
                "name": "Chăm sóc lưng"
            },
            {
                "id": "VNITEDP20200727072141019231",
                "name": "Chăm sóc da vùng mắt"
            },
            {
                "id": "VNITEDP20200727072157011851",
                "name": "Chăm sóc da"
            },
            {
                "id": "VNITEDP20200727072224016675",
                "name": "Chăm sóc tai"
            },
            {
                "id": "VNITEDP20200727072247015536",
                "name": "Chăm sóc da tay, da chân"
            },
            {
                "id": "VNITEDP20200727072303015520",
                "name": "Chăm sóc tóc"
            },
            {
                "id": "VNITEDP20200727072319013234",
                "name": "Chăm sóc răng miệng"
            },
            {
                "id": "VNITEDP20200727072333012439",
                "name": "Dầu mát xa"
            },
            {
                "id": "VNITEDP20200727072354012262",
                "name": "Chăm sóc da mặt"
            },
            {
                "id": "VNITEDP20200727072417017419",
                "name": "Nước rửa phụ khoa/Dung dịch vệ sinh"
            },
            {
                "id": "VNITEDP20200727072434017522",
                "name": "Khăn giấy/ướt các loại"
            },
            {
                "id": "VNITEDP20200727072453015063",
                "name": "Tã/bỉm cho người lớn"
            },
            {
                "id": "VNITEDP20200727072548016011",
                "name": "Dụng cụ tắm và vệ sinh cá nhân"
            },
            {
                "id": "VNITEDP20200727072724010249",
                "name": "Dụng cụ trang điểm"
            },
            {
                "id": "VNITEDP20200727072812013337",
                "name": "Nước/dầu tẩy trang, tẩy da chết"
            },
            {
                "id": "VNITEDP20200727072829019371",
                "name": "Nước hoa, Khử mùi và ngăn mồ hôi"
            },
            {
                "id": "VNITEDP20200729093621011077",
                "name": "Bao cao su"
            },
            {
                "id": "VNITEDP20200729093643018775",
                "name": "Dung dịch bôi trơn"
            },
            {
                "id": "VNITEDP20240502071131013525",
                "name": "Dụng cụ làm đẹp"
            },
            {
                "id": "VNITEDP20240502071159011336",
                "name": "Chống nắng cho cơ thể"
            },
            {
                "id": "VNITEDP20240502071230012640",
                "name": "Dụng cụ mát xa"
            },
            {
                "id": "VNITEDP20240502071359012995",
                "name": "Sữa dưỡng ẩm cơ thể, sữa dưỡng thể"
            },
            {
                "id": "VNITEDP20240502071429012934",
                "name": "Tẩy lông cơ thể"
            },
            {
                "id": "VNITEDP20240502071452012443",
                "name": "Tẩy tế bào chết cơ thể"
            },
            {
                "id": "VNITEDP20240502071517015057",
                "name": "Bột dưỡng da cơ thể"
            },
            {
                "id": "VNITEDP20240502071535018321",
                "name": "Xà phòng thanh và sữa tắm"
            },
            {
                "id": "VNITEDP20240502071628011959",
                "name": "Băng vệ sinh"
            },
            {
                "id": "VNITEDP20240502071656011986",
                "name": "Tinh dầu dưỡng thể"
            },
            {
                "id": "VNITEDP20240502071723017146",
                "name": "Mỹ phẩm dành cho da mặt"
            },
            {
                "id": "VNITEDP20240502071742012061",
                "name": "Mỹ phẩm dành cho mắt"
            },
            {
                "id": "VNITEDP20240502071800014601",
                "name": "Mỹ phẩm dành cho môi"
            }
        ]
    },
    {
        "id": "VNITEDP20200724103117010195",
        "name": "Đồ uống",
        "subCategories": [
            {
                "id": "VNITEDP20200727035900010334",
                "name": "Cà phê uống liền"
            },
            {
                "id": "VNITEDP20200727035916012077",
                "name": "Bột cà phê"
            },
            {
                "id": "VNITEDP20200727035957013034",
                "name": "Trà sữa"
            },
            {
                "id": "VNITEDP20200727040021015983",
                "name": "Trà uống liền"
            },
            {
                "id": "VNITEDP20200727040113011363",
                "name": "Bột/gói trà"
            },
            {
                "id": "VNITEDP20200727042823019843",
                "name": "Bột chocolate"
            },
            {
                "id": "VNITEDP20200727042842013302",
                "name": "Nước trái cây"
            },
            {
                "id": "VNITEDP20200727042900014385",
                "name": "Đồ uống sức khỏe"
            },
            {
                "id": "VNITEDP20200727042920019731",
                "name": "Đồ uống tăng lực"
            },
            {
                "id": "VNITEDP20200727042939018147",
                "name": "Đồ uống bổ sung"
            },
            {
                "id": "VNITEDP20200727042952015426",
                "name": "Nước"
            },
            {
                "id": "VNITEDP20200727043008013173",
                "name": "Nước ngọt"
            },
            {
                "id": "VNITEDP20240502074237014492",
                "name": "Đồ uống homemade"
            },
            {
                "id": "VNITEDP20240502074314012281",
                "name": "Siro mùi"
            },
            {
                "id": "VNITEDP20240502074348011036",
                "name": "Bột pha nước trái cây"
            }
        ]
    },
    {
        "id": "VNITEDP20200727030918015871",
        "name": "Đồ uống có cồn",
        "subCategories": [
            {
                "id": "VNITEDP20200727032928012154",
                "name": "Bia"
            },
            {
                "id": "VNITEDP20200727033247013891",
                "name": "Cocktails"
            },
            {
                "id": "VNITEDP20200727033340013131",
                "name": "Nước ép có cồn"
            },
            {
                "id": "VNITEDP20200727033418010772",
                "name": "Các loại rượu khác"
            },
            {
                "id": "VNITEDP20200727033535017419",
                "name": "Rượu vang"
            },
            {
                "id": "VNITEDP20200727084311012875",
                "name": "Đồ uống có cồn khác"
            }
        ]
    },
    {
        "id": "VNITEDP20200708074635013756",
        "name": "Sữa và các chế phẩm từ sữa",
        "subCategories": [
            {
                "id": "VNITEDP20200708074652011592",
                "name": "Sữa tươi"
            },
            {
                "id": "VNITEDP20200727043031018461",
                "name": "Sữa hạt"
            },
            {
                "id": "VNITEDP20200727043455012404",
                "name": "Sữa chua"
            },
            {
                "id": "VNITEDP20200727043516014913",
                "name": "Phô mai"
            },
            {
                "id": "VNITEDP20200727043627011817",
                "name": "Bơ các loại"
            },
            {
                "id": "VNITEDP20200727043646014631",
                "name": "Sữa đặc"
            },
            {
                "id": "VNITEDP20200727043720011453",
                "name": "Kem tươi"
            },
            {
                "id": "VNITEDP20200727072946012103",
                "name": "Sữa bột/sữa công thức dành cho người lớn"
            },
            {
                "id": "VNITEDP20240502080703019030",
                "name": "Sữa Tiệt Trùng"
            },
            {
                "id": "VNITEDP20240502080727019448",
                "name": "Sữa Thanh Trùng"
            },
            {
                "id": "VNITEDP20240502080812016517",
                "name": "Sữa Kem"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031357010717",
        "name": "Thịt, cá, trứng, hải sản",
        "subCategories": [
            {
                "id": "VNITEDP20200710074423010768",
                "name": "Trứng"
            },
            {
                "id": "VNITEDP20200727081116016865",
                "name": "Thịt tươi"
            },
            {
                "id": "VNITEDP20200727081452018059",
                "name": "Hải sản tươi"
            },
            {
                "id": "VNITEDP20200727081647013318",
                "name": "Thịt đông lạnh"
            },
            {
                "id": "VNITEDP20200727081714013458",
                "name": "Hải sản đông lạnh"
            },
            {
                "id": "VNITEDP20200727081801015147",
                "name": "Hải sản khô chưa đóng gói"
            }
        ]
    },
    {
        "id": "VNITEDP20200727031221010048",
        "name": "Rau củ trái cây",
        "subCategories": [
            {
                "id": "VNITEDP20200727044720018778",
                "name": "Hoa quả sấy"
            },
            {
                "id": "VNITEDP20200727044808013094",
                "name": "Nguyên liệu rau củ quả hạt sấy khô"
            },
            {
                "id": "VNITEDP20200727044825013013",
                "name": "Đậu hạt khô"
            },
            {
                "id": "VNITEDP20200727044841017463",
                "name": "Trái cây tươi"
            },
            {
                "id": "VNITEDP20200727045036014942",
                "name": "Rau tươi"
            },
            {
                "id": "VNITEDP20200727045055011999",
                "name": "Nước sốt hoa quả"
            },
            {
                "id": "VNITEDP20200727045129016124",
                "name": "Củ, quả"
            },
            {
                "id": "VNITEDP20200727045157011965",
                "name": "Rau thơm"
            },
            {
                "id": "VNITEDP20200727045213019315",
                "name": "Trái cây cắt sẵn"
            },
            {
                "id": "VNITEDP20230606031430019034",
                "name": "Nấm"
            }
        ]
    }
]


module.exports = {
    get_token,
    GRAB_CATEGORIES
}