const axios = require('../axios');
const _ = require('lodash');
const moment = require('moment');
const slugify = require('slugify');
const { v4 } = require('uuid');
const PhoneNumber = require('libphonenumber-js');
const { name_to_id } = require('../helper');
const parsePhoneNumber = (phone) => {
    if (!phone) return '';
    try {
        return PhoneNumber(String(phone), 'VN')?.format('E.164') || ''
    } catch (e) {
        return '';
    }
};

const toNumber = (value) => {
    const cleanValue = _.isString(value) ? value.replace(/[^\d-]/g, '') : value;
    const number = _.toNumber(cleanValue);
    return isNaN(number) ? 0 : number;
}

const OrderMapping = {
    id: String,
    order_id: String,
    source: String,
    order_time: String,
    pick_time: String,
    delivery_time: String,
    delivery_time_unix: Number,
    order_time_sort: Number,
    driver_name: String,
    driver_phone: String,
    customer_name: String,
    customer_address: String,
    customer_phone: String,
    dishes: [{
        name: String,
        description: String,
        options: [{
            name: String,
            quantity: Number,
            option_name: String,
            option_item: String,
            option_price: Number, // Giá gốc tùy chọn đã nhân số lượng
            option_discount_price: Number, // Giá bán tùy chọn đã nhân số lượng
        }],
        quantity: Number,
        price: Number, // Giá gốc đã nhân số lượng
        discount: Number,  // Giảm giá đã nhân số lương
        discount_price: Number, // Giá bán đã nhân số lượng
        note: String,
    }],
    dish_changed: Boolean,
    total: Number,
    commission: Number,
    total_discount: Number,
    total_for_biz: Number,
    total_shipment: Number, // Giá ship sau cùng (sau khuyến mãi)
    shipment_discount: Number, // Giảm giá ship
    shipment_fee: Number, // Giá ship trước khuyến mãi
    transaction_fee: Number, // Phí giao dịch
    note: String,
    cancel_type: { type: String, enum: ['out_stock', 'merchant_busy', 'incorrect_order'] },
    cancel_reason: String,
    cancel_by: { type: String, enum: ['merchant', 'system', 'user', 'driver'] },
    coupons: [{
        name: String,
        code: String,
        total: Number,
    }],
    payments: [{
        method: String,
        total: Number,
        status: String,
        note: String,
    }],
    stock_dishes: [], // Món theo tồn kho
    customer_data: {
        original_price: Number, // Giá gốc
        sell_price: Number, // Giá bán
        shipment_fee: Number, // Tiền ship gốc
        order_discount: Number, // Giảm giá đơn hàng
        shipment_discount: Number, // Giảm giá ship
        additional_income: Number, // Phụ thu từ khách
        total_paid: Number, // Tổng tiền khách trả
    },
    finance_data: {
        original_price: Number, // Giá gốc
        sell_price: Number, // Giá bán (sau gạch giá)
        co_fund_promotion_price: Number, // Các khuyến mãi đơn hàng khác
        other_promotion_price: Number, // Khuyến mãi khác
        total_promotion_price: Number, // Tổng khuyến mãi
        gross_received: Number,// Doanh thu sau khuyến mãi
        commission: Number, // Commission
        transaction_fee: Number, // Phí giao dịch
        adjustment_fee: Number, // Phí điều chỉnh / Phí phạt
        other_fee: Number, //  Phí khác
        total_shipment: Number, // Tiền ship ban đầu
        shipping_fee: Number, // Tiền ship sau giảm
        shipping_discount: Number, // Giảm giảm
        net_received: Number, // Thực nhận sau commission
        additional_income: Number, // Phụ thu từ khách or shiper or bo
        real_received: Number, // Thu nhập thực tế từ merchant và khách
    },
    raw: JSON,
};

const CANCEL_REASON_MESSAGE = {
    merchant: "Hủy bởi quán",
    system: "Hủy bởi hệ thống",
    user: "Hủy bởi khách",
    driver: "Hủy bởi tài xế",
}
function _startCase(text) {
    if (typeof text !== 'string' || text.length === 0) {
        return text;
    }

    return text.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

const _textToSlug = (text) => slugify(text.toLowerCase(), { replacement: '_', locale: 'vi', trim: true, strict: true })

function map_order(source, order) {
    let new_order = {}
    if (source === 'baemin') {
        new_order = {
            id: order.orderId,
            order_id: order.csId,
            source: 'baemin',
            order_time: moment(order.createdAt).toISOString(),
            pick_time: moment(order.readyToPickupAt).toISOString(),
            delivery_time: moment(order.updatedAt).toISOString(),
            delivery_time_unix: moment(order.updatedAt).unix(),
            order_time_sort: moment(order.createdAt).unix(),
            driver_name: order.rider?.name,
            driver_phone: order.rider?.phone,
            customer_phone: order.toPhone,
            customer_name: order.toName,
            customer_address: order.toAddress,
            dishes: order.dishes.map(v => {
                return {
                    name: v.name,
                    description: v.description,
                    options: v.options.map(o => {
                        return o.items.map(item => {
                            return {
                                name: o.name + " " + item.name,
                                quantity: item.quantity,
                                option_name: o.name,
                                option_item: item.name,
                            }
                        }).filter(o2 => o2.quantity > 0)
                    }),
                    quantity: v.quantity,
                    price: (v.originalPrice + (_.sum(v.options.flatMap(v => v.items).map(v => v.price * v.quantity)) || 0)) * v.quantity,
                    note: v.memo,
                }
            }),
            commission: 0,
            total: order.originalDishTotalPrice,
            total_for_biz: order.dishTotalPrice,
            note: order.memo || "",
            cancel_reason: order.cancelReason,
            coupons: [...order.cartRulesDiscount?.items?.map(d => {
                return {
                    name: d.name,
                    code: d.code,
                    total: d.methodValue,
                }
            }), ...order.discounts?.items.filter(c => order.coupon?.code?.includes(c.code))?.map(d => {
                return {
                    name: d.name,
                    code: d.code,
                    total: d.methodValue,
                }
            })] || [],
            payments: [{
                method: 'BAEMIN',
                total: order.dishTotalPrice,
                status: 'COMPLETED',
                note: 'Thanh toán qua Baemin'
            }],
            raw: order,
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }
    if (source === 'shopee' || source === 'shopee_fresh') {
        const official_data = order.official_data
        if (official_data && !order.code) {
            new_order = map_order('shopee_official', order)
            new_order.source = source
            return new_order
        }
        new_order = {
            id: order.code,
            order_id: order.code,
            source,
            order_time: moment.unix(order.order_time).toISOString(),
            pick_time: moment.unix(order.actual_pick_time).toISOString(),
            delivery_time: moment.unix(order.deliver_time).toISOString(),
            delivery_time_unix: order.deliver_time,
            order_time_sort: order.order_time,
            driver_name: order.assignee?.name,
            driver_phone: parsePhoneNumber(order.assignee?.phone),
            customer_name: order.deliver_address?.contact_name || order.order_user?.name,
            customer_address: order.deliver_address?.address,
            customer_phone: parsePhoneNumber(order.order_user?.phone),
            dishes: order.order_items.map(v => {
                return {
                    name: v.dish.name,
                    description: v.dish.description,
                    options: v.options_groups ? v.options_groups.map(o => o.options.map(o2 => {
                        return {
                            name: o.name + " " + o2.name,
                            quantity: o2.quantity,
                            option_name: o.name,
                            option_item: o2.name,
                            option_price: o2.original_price * v.quantity,
                            option_discount_price: o2.discount_price * v.quantity,
                        }
                    }).filter(o2 => o2.quantity > 0)) : [],
                    quantity: v.quantity,
                    price: v.original_price * v.quantity,
                    discount: (v.original_price - v.discount_price) * v.quantity,
                    discount_price: v.discount_price * v.quantity,
                    note: v.note || "",
                }
            }),
            stock_dishes: order.stock_dishes || [],
            total: order.order_value_amount,
            commission: order.commission.amount,
            total_for_biz: order.total_value_amount,
            note: [order.is_remove_plastic ? "Không cần dùng cụ ăn uống nhựa" : "Cần dụng cụ ăn uống", order.notes?.order_note || ""].filter(v => v).join(', '),
            cancel_reason: "",
            coupons: order.merchant_discounts?.map(d => {
                return {
                    name: d.name,
                    code: d.code,
                    total: d.amount,
                }
            }) || [],
            payments: [{
                method: 'SHOPEE',
                total: order.total_value_amount,
                status: 'COMPLETED',
                note: 'Thanh toán qua Shopee'
            }],
            customer_data: {
                original_price: _.sumBy(order.order_items, v => v.original_price * v.quantity), // Giá gốc
                sell_price: _.sumBy(order.order_items, v => v.discount_price * v.quantity), // Giá bán
                shipment_fee: order.customer_bill.shipping_fee, // Tiền ship gốc
                order_discount: order.customer_bill.total_discount - order.customer_bill.item_discount, // Giảm giá đơn hàng
                shipment_discount: 0, // Giảm giá ship
                additional_income: 0, // Phụ thu từ khách
                total_paid: order.customer_bill.total_amount,// Tổng tiền khách trả
            },
            finance_data: {
                original_price: _.sumBy(order.order_items, v => v.original_price * v.quantity), // Giá gốc
                sell_price: _.sumBy(order.order_items, v => v.discount_price * v.quantity), // Giá bán (sau gạch giá)
                co_fund_promotion_price: _.sumBy(order.merchant_discounts.filter(v => v.type === 1 || v.type === 9), v => v.amount), // Các khuyến mãi đơn hàng khác
                // other_promotion_price: 0,
                // total_promotion_price: 0,
                // gross_received: 0, // Doanh thu sau khuyến mãi
                commission: order.commission.amount, // Commission
                other_fee: 0,
                total_shipment: 0, // Tiền ship thu thêm
                net_received: order.total_value_amount, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: order.total_value_amount,// Thu nhập thực tế từ merchant và khách
            },
            raw: order,
        }
        new_order.finance_data.other_promotion_price = _.sumBy(new_order.dishes, 'discount')
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price
        if (order.cancel_info?.type) {
            new_order.cancel_by = { 2: "merchant", 3: "system", 4: "user", 5: "driver" }[order.cancel_info?.type] || "merchant"
            new_order.cancel_reason = CANCEL_REASON_MESSAGE[new_order.cancel_by]
            if (order.cancel_info?.reason) {
                new_order.cancel_reason += `, Chi tiết: ${order.cancel_info?.reason}`
            }
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }

    if (source === 'shopee_official') {
        const official_data = order.official_data
        new_order = {
            id: official_data.code,
            order_id: official_data.code,
            // source: 'shopee_fresh',
            order_time: moment(official_data.order_time).toISOString(),
            pick_time: moment(official_data.pick_time).toISOString(),
            delivery_time: moment(official_data.delivery_time).toISOString(),
            delivery_time_unix: moment(official_data.delivery_time).unix(),
            order_time_sort: moment(official_data.order_time).unix(),
            driver_name: official_data.assignee?.name,
            driver_phone: parsePhoneNumber(official_data.assignee?.phone),
            customer_name: order.deliver_address?.contact_name || order.order_user?.name,
            customer_address: order.deliver_address?.address,
            customer_phone: parsePhoneNumber(order.order_user?.phone),
            dishes: official_data.dish_groups.map(v => v.dishes).flat().map(v => {
                return {
                    name: v.dish_name,
                    description: '',
                    options: v.topping_groups ? v.topping_groups.map(o => o.toppings.map(o2 => {
                        return {
                            name: o.group_name + " " + o2.topping_name,
                            quantity: o2.quantity,
                            option_name: o.group_name,
                            option_item: o2.topping_name,
                            option_price: o2.price.value * v.quantity,
                            option_discount_price: o2.price.value * v.quantity,
                        }
                    }).filter(o2 => o2.quantity > 0)) : [],
                    quantity: v.quantity,
                    price: v.original_price.value * v.quantity,
                    discount: (v.original_price.value - v.price.value) * v.quantity,
                    discount_price: v.price.value * v.quantity,
                    note: v.note || "",
                }
            }),
            stock_dishes: order.stock_dishes || [],
            total: official_data.order_value.value,
            commission: official_data.commission_amount.value,
            total_for_biz: official_data.total_value.value,
            note: '',
            cancel_reason: '',
            coupons: official_data.merchant_discounts?.map(d => {
                return {
                    name: d.name,
                    code: d.code || '',
                    total: d.merchant_discount.value,
                }
            }) || [],
            payments: [{
                method: 'SHOPEE',
                total: official_data.total_value.value,
                status: 'COMPLETED',
                note: 'Thanh toán qua Shopee'
            }],
            finance_data: {},
            raw: order,
        }
        new_order.customer_data = {
            original_price: _.sumBy(new_order.dishes, 'price'), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, 'discount_price'), // Giá bán
            shipment_fee: 0, // Tiền ship gốc
            order_discount: 0, // Giảm giá đơn hàng
            shipment_discount: 0, // Giảm giá ship
            additional_income: 0, // Phụ thu từ khách
            total_paid: official_data.customer_bill.total_amount.value,// Tổng tiền khách trả
        }
        new_order.finance_data = {
            original_price: _.sumBy(new_order.dishes, 'price'), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, 'discount_price'), // Giá bán (sau gạch giá)
            co_fund_promotion_price: _.sumBy(official_data.merchant_discounts.filter(v => v.name === 'Mã khuyến mãi' || v.name === 'Giảm trực tiếp'), v => v.merchant_discount.value), // Các khuyến mãi đơn hàng khác
            other_promotion_price: _.sumBy(new_order.dishes, 'discount'),
            total_promotion_price: 0,
            gross_received: 0, // Doanh thu sau khuyến mãi
            commission: new_order.commission, // Commission
            other_fee: 0,
            total_shipment: 0, // Tiền ship thu thêm
            net_received: official_data.total_value.value, // Thực nhận sau commission
            additional_income: 0, // Phụ thu từ khách or shiper
            real_received: official_data.total_value.value,// Thu nhập thực tế từ merchant và khách
        }
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price
        if (order.cancel_info?.type) {
            new_order.cancel_by = { 2: "merchant", 3: "system", 4: "user", 5: "driver" }[order.cancel_info?.type] || "merchant"
            new_order.cancel_reason = CANCEL_REASON_MESSAGE[new_order.cancel_by]
            if (order.cancel_info?.reason) {
                new_order.cancel_reason += `, Chi tiết: ${order.cancel_info?.reason}`
            }
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }

    if (source === 'shopee_ecom') {
        new_order = {
            id: String(order.order_id),
            order_id: order.order_sn,
            source,
            order_time: moment.unix(order.create_time).toISOString(),
            pick_time: order.delivery_time > 0 ? moment.unix(order.delivery_time).toISOString() : null,
            delivery_time: order.complete_time > 0 ? moment.unix(order.complete_time).toISOString() : null,
            delivery_time_unix: order.complete_time,
            order_time_sort: order.create_time,
            driver_name: '',
            driver_phone: '',
            customer_name: order.buyer_user.user_name,
            customer_address: order.shipping_address,
            customer_phone: order.buyer_address_phone,
            dishes: order.order_items.map(v => {
                return {
                    name: v.product.name,
                    description: '',
                    options: [],
                    quantity: v.amount,
                    price: v.amount * Number(v.order_price),
                    discount: 0,
                    discount_price: v.amount * Number(v.order_price),
                    note: v.note || "",
                }
            }),
            total: _.sumBy(order.order_items, v => v.amount * Number(v.order_price)),
            shipment_fee: order.transaction?.payment_info?.shipping_subtotal?.shipping_fee_paid_by_buyer || 0,
            total_shipment: (order.transaction?.payment_info?.shipping_subtotal?.shipping_fee_paid_by_buyer || 0) + (order.transaction?.payment_info?.shipping_subtotal?.shipping_rebate_from_shopee || 0) + (order.transaction?.payment_info?.shipping_subtotal?.shipping_fee_paid_by_shopee_on_your_behalf || 0),
            shipment_discount: -1 * (order.transaction?.payment_info?.shipping_subtotal?.shipping_rebate_from_shopee || 0) + -1 * (order.transaction?.payment_info?.shipping_subtotal?.shipping_fee_paid_by_shopee_on_your_behalf || 0),
            transaction_fee: -1 * order.transaction.payment_info.fees_and_charges.ams_commission_fee - 1 * order.transaction.payment_info.fees_and_charges.transaction_fee - 1 * order.transaction.payment_info.fees_and_charges.service_fee, // Phí giao dịch
            adjustment_fee: 0,
            commission: 0,
            total_for_biz: order.transaction?.amount_after_adjustment || 0,
            note: '',
            cancel_reason: {
                1: "Hủy bởi người bán, Lý do: Không có hàng",
                3: "Hủy bởi nguời mua do thay đổi đơn hàng",
                9: "Hủy bởi nguời mua do đổi ý không muốn mua",
                100: "Hủy bởi hệ thống do người mua thanh toán không đúng hạn",
                502: "Hủy bởi người mua",
                506: "Hủy bởi người mua, đổi ý, không muốn mua",
            }[order.cancel_reason_ext] || order.cancel_reason_ext,
            cancel_by: { 1: "merchant", 3: "merchant", 9: "merchant", 100: "system", 502: "user", 506: "user" }[order.cancel_reason_ext] || "merchant",
            coupons: [],
            payments: [{
                method: 'SHOPEE_ECOM',
                total: order.transaction?.amount_after_adjustment || 0,
                status: 'COMPLETED',
                note: 'Thanh toán qua Shopee Ecom'
            }],
            customer_data: {
                original_price: order.transaction?.buyer_payment_info?.merchant_subtotal || 0, // Giá gốc
                sell_price: order.transaction?.buyer_payment_info?.merchant_subtotal || 0, // Giá bán
                shipment_fee: 0, // Tiền ship gốc
                order_discount: -1 * (order.transaction?.buyer_payment_info?.shopee_voucher + order.transaction?.buyer_payment_info?.seller_voucher + order.transaction?.buyer_payment_info?.shopee_coins_redeemed), // Giảm giá đơn hàng
                shipment_discount: 0, // Giảm giá ship
                additional_income: 0, // Phụ thu từ khách
                total_paid: _.toNumber(order.paid_amount),// Tổng tiền khách trả
            },
            finance_data: {
                original_price: order.transaction?.buyer_payment_info?.merchant_subtotal || 0, // Giá gốc
                sell_price: order.transaction?.buyer_payment_info?.merchant_subtotal || 0, // Giá bán (sau gạch giá)
                co_fund_promotion_price: -1 * order.transaction?.buyer_payment_info?.seller_voucher || 0, // Các khuyến mãi đơn hàng khác
                other_promotion_price: 0,
                total_promotion_price: -1 * order.transaction?.buyer_payment_info?.seller_voucher || 0,
                // gross_received: 0, // Doanh thu sau khuyến mãi
                commission: 0, // Commission
                transaction_fee: -1 * (
                    order.transaction.payment_info.fees_and_charges.transaction_fee +
                    order.transaction.payment_info.fees_and_charges.service_fee +
                    order.transaction.payment_info.fees_and_charges.ams_commission_fee +
                    order.transaction.payment_info.fees_and_charges.commission_fee), // Phí giao dịch
                adjustment_fee: 0,
                other_fee: 0,
                shipment_fee: 0,
                total_shipment: 0,
                net_received: order.transaction?.amount_after_adjustment, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                adjustment_fee: 0, // Phí phạt từ sàn
                real_received: order.transaction?.amount_after_adjustment,// Thu nhập thực tế từ merchant và khách
            },
            raw: order,
        }
        new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price
        new_order.total_discount = 0
    }

    if (source === 'gojek') {
        if (order.source2) {
            return map_order('gojek2', order)
        }
        new_order = {
            id: order.order_number,
            order_id: order.order_number,
            source: 'gojek',
            order_time: moment.unix(order.order_created_at).toISOString(),
            pick_time: moment.unix(order.order_created_at).toISOString(), // fake
            delivery_time: moment.unix(order.order_created_at).toISOString(),
            delivery_time_unix: order.order_created_at,
            order_time_sort: order.order_created_at,
            driver_name: order.driver_name,
            driver_phone: parsePhoneNumber(order.driver_phone),
            customer_phone: order.customer_phone || "",
            customer_name: parsePhoneNumber(order.customer_name),
            customer_address: "",
            dishes: order.menu_items.map(v => {
                return {
                    name: v.name,
                    description: "",
                    options: v.variants ?
                        v.variants.map(o => {
                            return [{
                                name: o.category_name + " " + o.name,
                                quantity: 1,
                                option_name: o.category_name,
                                option_item: o.name,
                            }]
                        })
                        : [],
                    quantity: v.quantity,
                    price: v.quantity * v.price,
                    note: v.notes || "",
                }
            }),
            commission: 0,
            total: order.shopping_price,
            total_for_biz: order.net_price,
            note: "",
            cancel_reason: order.cancel_reason || "",
            cancel_by: {
                "Không thể liên lạc với nhà hàng": 'driver',
                "Món đã hết": 'merchant',
                "Không tìm thấy tài xế": 'system',
                "Đã huỷ": 'merchant',
                "Bị huỷ bởi khách hàng - sai địa chỉ giao hàng": 'user',
                "Nhà hàng đóng cửa": 'driver',
                "Bị huỷ bởi tài xế - nhà hàng đóng cửa": 'driver',
                "Bị huỷ bởi khách hàng - nhà hàng đóng cửa": 'user',
                "Bị huỷ bởi khách hàng": 'user',
                "Bị huỷ bởi khách hàng - quên dùng mã khuyến mãi": 'user',
                "Bị huỷ bởi khách hàng - vô tình đặt đơn hàng": 'user',
                "Bị huỷ bởi khách hàng - chọn nhầm nhà hàng": 'user',
                "Bị huỷ bởi khách hàng - chờ đợi quá lâu": 'user',
                "Bị huỷ bởi khách hàng - hết món": 'user',
                "Bị huỷ bởi nhà hàng - lý do khác": 'merchant',
                "Bị huỷ bởi tài xế - hết món": 'driver',
                "Giá món ăn chưa được cập nhật": 'merchant',
            }[order.cancel_reason] || "",
            coupons: [],
            payments: [{
                method: 'GOJEK',
                total: order.net_price,
                status: 'COMPLETED',
                note: 'Thanh toán qua Gojek'
            }],
            finance_data: null,
            raw: order,
        }

        if (order.transaction) {
            new_order.finance_data = {
                original_price: order.transaction.metadata.transaction.gross_amount / 100, // Giá gốc
                sell_price: order.transaction.metadata.transaction.real_gross_amount / 100, // Giá bán (sau gạch giá)
                co_fund_promotion_price: order.transaction.transaction_share[0].metadata.variables.voucher_amount / 100, // Các khuyến mãi đơn hàng khác
                other_promotion_price: 0,
                total_promotion_price: order.transaction.transaction_share[0].metadata.variables.voucher_amount / 100,
                // gross_received: 0, // Doanh thu sau khuyến mãi
                commission: order.transaction.transaction_share[0].metadata.variables.commission / 100, // Commission
                other_fee: 0,
                total_shipment: 0,
                shipping_fee: 0, // Tiền ship thu thêm thu sau khuyến mãi
                commission: order.transaction.transaction_share[0].metadata.variables.commission / 100, // Commission
                other_fee: 0,
                net_received: order.transaction.transaction_share[0].amount / 100, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: order.transaction.transaction_share[0].amount / 100,// Thu nhập thực tế từ merchant và khách
            }
            new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }

    if (source === 'gojek2') {
        const order2 = order.source2
        const new_order = {
            id: order2.order_number,
            order_id: order2.order_number,
            source: 'gojek',
            order_time: moment(order2.ordered_at).toISOString(),
            pick_time: moment(order2.event_timestamp).toISOString(),
            delivery_time: moment(order2.event_timestamp).toISOString(),
            delivery_time_unix: new Date(order2.event_timestamp).getTime() / 1000, // convert to Unix timestamp
            order_time_sort: new Date(order2.ordered_at).getTime() / 1000, // convert to Unix timestamp
            driver_name: order2.product_specific.goresto.driver_name,
            driver_phone: parsePhoneNumber(order2.product_specific.goresto.driver_phone),
            customer_phone: order2.product_specific.goresto.customer_phone || "",
            customer_name: order2.product_specific.goresto.customer_name || "",
            customer_address: "",
            dishes: order2.items.map(v => {
                return {
                    name: v.name,
                    description: "",
                    options: v.variants ?
                        v.variants.map(o => {
                            return [{
                                name: o.category_name + " " + o.name,
                                quantity: 1,
                                option_name: o.category_name,
                                option_item: o.name,
                            }]
                        })
                        : [],
                    quantity: v.quantity,
                    price: v.price * v.quantity,
                    note: v.note || "",
                    discount_price: v.price * v.quantity,
                    discount: 0,
                }
            }),
            commission: 0,
            total: order2.gross_amount,
            shipment_discount: 0,
            total_for_biz: 0,
            note: "",
            cancel_reason: order2.product_specific.goresto.cancel_reason_description || "",
            cancel_by: {
                "Không thể liên lạc với nhà hàng": 'driver',
                "Món đã hết": 'merchant',
                "Không tìm thấy tài xế": 'system',
                "Đã huỷ": 'merchant',
                "Bị huỷ bởi khách hàng - sai địa chỉ giao hàng": 'user',
                "Nhà hàng đóng cửa": 'driver',
                "Bị huỷ bởi tài xế - nhà hàng đóng cửa": 'driver',
                "Bị huỷ bởi khách hàng - nhà hàng đóng cửa": 'user',
                "Bị huỷ bởi khách hàng": 'user',
                "Bị huỷ bởi khách hàng - quên dùng mã khuyến mãi": 'user',
                "Bị huỷ bởi khách hàng - vô tình đặt đơn hàng": 'user',
                "Bị huỷ bởi khách hàng - chọn nhầm nhà hàng": 'user',
                "Bị huỷ bởi khách hàng - chờ đợi quá lâu": 'user',
                "Bị huỷ bởi khách hàng - hết món": 'user',
                "Bị huỷ bởi nhà hàng - lý do khác": 'merchant',
                "Bị huỷ bởi tài xế - hết món": 'driver',
                "Giá món ăn chưa được cập nhật": 'merchant',
            }[order2.product_specific.goresto.cancel_reason_description] || '',
            coupons: order2.product_specific.goresto.campaign_discounts.map(d => ({
                campaign_id: d.campaign_id,
                discount_amount: d.discount_amount,
                merchant_budget_share_percent: d.merchant_budget_share_percent,
                redeemed_amount: d.redeemed_amount
            })),
            payments: [{
                method: 'GOPAY',
                total: order2.gross_amount,
                status: 'COMPLETED',
                note: 'Thanh toán qua Gopay'
            }],
            finance_data: null,
            raw: order,
        };

        if (order.transaction) {
            new_order.finance_data = {
                original_price: order.transaction.metadata.transaction.gross_amount / 100, // Giá gốc
                sell_price: order.transaction.metadata.transaction.real_gross_amount / 100, // Giá bán (sau gạch giá)
                co_fund_promotion_price: order.transaction.transaction_share[0].metadata.variables.voucher_amount / 100, // Các khuyến mãi đơn hàng khác
                other_promotion_price: 0,
                total_promotion_price: order.transaction.transaction_share[0].metadata.variables.voucher_amount / 100,
                // gross_received: 0, // Doanh thu sau khuyến mãi
                commission: order.transaction.transaction_share[0].metadata.variables.commission / 100, // Commission
                other_fee: 0,
                total_shipment: 0,
                shipment_discount: 0,
                shipping_fee: 0, // Tiền ship thu thêm thu sau khuyến mãi\
                other_fee: 0,
                net_received: order.transaction.transaction_share[0].amount / 100, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: order.transaction.transaction_share[0].amount / 100,// Thu nhập thực tế từ merchant và khách
            }
            new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price
        }
        new_order.total = new_order.finance_data.original_price
        new_order.total_discount = new_order.finance_data.total_promotion_price
        new_order.commission = new_order.finance_data.commission
        new_order.total_for_biz = new_order.finance_data.net_received


        return new_order;
    }

    if (source === 'grab' || source === 'grab_mart') {
        new_order = {
            id: order.orderID,
            order_id: order.displayID,
            source,
            order_time: moment(order.times?.displayedAt || order.times?.createdAt).toISOString(),
            pick_time: moment(order.times?.readyAt || order.times?.deliveredAt).toISOString(),
            delivery_time: moment(order.times?.deliveredAt).toISOString(),
            delivery_time_unix: moment(order.times?.deliveredAt).unix(),
            order_time_sort: moment(order.times?.displayedAt || order.times?.createdAt).unix(),
            driver_name: order.driver?.name,
            driver_phone: parsePhoneNumber(order.driver?.mobileNumber),
            customer_phone: parsePhoneNumber(order.eater.mobileNumber),
            customer_name: order.eater.name || "",
            customer_address: order.eater.address?.keywords || "",
            dishes: order.itemInfo.items.map(v => {
                const dish = {
                    name: v.name,
                    description: v.menu_item?.description || "",
                    options: v.modifierGroups ? v.modifierGroups.map(o => o.modifiers.map(m => {
                        return {
                            name: o.modifierGroupName + ": " + m.modifierName,
                            quantity: m.quantity,
                            option_name: o.modifierGroupName,
                            option_item: m.modifierName,
                            option_price: parseInt(m.priceDisplay.replace(/\./g, ''), 10) * v.quantity,
                            option_discount_price: parseInt(m.priceDisplay.replace(/\./g, ''), 10) * v.quantity,
                        }
                    }).filter(o2 => o2.quantity > 0)) : [],
                    quantity: v.quantity,
                    price: v.quantity == 0 ? 0 : parseInt(v.fare.priceDisplay.replace(/\./g, ''), 10),
                    discount: _.sumBy(v.discountInfo, d => parseInt(d.itemDiscountPriceDisplay.replace(/\./g, ''), 10)) || 0,
                    discount_price: 0,
                    note: v.comment || "",
                }
                dish.discount_price = dish.price - dish.discount
                return dish
            }),
            stock_dishes: order.stock_dishes || [],
            commission: 0,
            total: parseInt(order.fare.subTotalDisplay.replace(/\./g, ''), 10),
            total_for_biz: parseInt(order.fare.totalDisplay.replace(/\./g, ''), 10),
            note: order.cutlery === 1 ? "Cần dụng cụ ăn uống" : "Không cần dùng cụ ăn uống nhựa",
            cancel_reason: order.cancelBy ? order.cancelBy + (order.cancelledMsg ? ", Chi tiết: " + order.cancelledMsg : "") : "",
            cancel_by: {
                CANCELLED_OPERATOR: 'system',
                CANCELLED_MAX: 'merchant',
                CANCELLED_PASSENGER: 'user',
                CANCELLED_DRIVER: 'driver',
            }[order.cancelBy] || '',
            coupons: order.orderLevelDiscounts?.map(d => {
                return {
                    name: 'Giảm giá',
                    code: d.discountName,
                    total: d.discountAmountValueInMin,
                }
            }) || [],
            payments: [{
                method: 'GRAB',
                total: parseInt(order.fare.totalDisplay.replace(/\./g, ''), 10),
                status: 'COMPLETED',
                note: 'Thanh toán qua Grab'
            }],
            finance_data: null,
            raw: order,
        }

        if (order.orderBookings?.length > 1) {
            for (const subOrder of order.orderBookings) {
                for (const subDish of subOrder.items.items) {
                    const dish = new_order.dishes.find(d => d.name === subDish.name)
                    dish.note = [dish.note, `Có  ${subDish.quantity} món thuộc đơn ${subOrder.shortOrderID}`].filter(v => v).join(', ')
                }
            }
            new_order.order_id = order.orderBookings.map(v => v.shortOrderID).join('_')
            new_order.note = [new_order.note, `Đơn hàng tách thành ${order.orderBookings.length} đơn: ${order.orderBookings.map(v => v.shortOrderID + ` Tài xế: ${v.driver?.name} (${v.driver?.mobileNumber})`).join(', ')}`].filter(v => v).join(', ')
        }

        new_order.customer_data = {
            original_price: parseInt(order.fare.subTotalDisplay.replace(/\./g, ''), 10), // Giá gốc
            sell_price: parseInt(order.fare.revampedSubtotalDisplay.replace(/\./g, ''), 10), // Giá bán (sau gạch giá)
            shipment_fee: parseInt(order.fare.deliveryFeeDisplay.replace(/\./g, ''), 10), // Tiền ship gốc
            order_discount: parseInt(order.fare.promotionDisplay.replace(/\./g, ''), 10), // Giảm giá đơn hàng
            shipment_discount: 0, // Giảm giá ship
            additional_income: 0, // Phụ thu từ khách
            total_paid: parseInt(order.fare.reducedPriceDisplay.replace(/\./g, ''), 10),
        }
        new_order.customer_data.additional_income = new_order.customer_data.sell_price
            + new_order.customer_data.shipment_fee - new_order.customer_data.order_discount - new_order.customer_data.total_paid


        if (order.transaction) {
            new_order.finance_data = {
                original_price: order.transaction?.order_value || 0, // Giá gốc
                sell_price: parseInt(order.fare.revampedSubtotalDisplay.replace(/\./g, ''), 10),// Giá bán (sau gạch giá)
                co_fund_promotion_price: _.sumBy(
                    order.orderLevelDiscounts?.filter(v => v.discountType === 'order'),
                    v => v.discountAmountValueInMin
                ), // Các khuyến mãi đơn hàng khác
                other_promotion_price: _.sumBy(order.itemInfo.items, v => _.sumBy(v.discountInfo, d => parseInt(d.itemDiscountPriceDisplay.replace(/\./g, ''), 10)) || 0) || 0, // Khuyến mãi khác
                total_promotion_price: -1 * order.transaction.mex_fund_discount, // Tổng khuyến mãi
                gross_received: order.transaction?.net_sales || 0, // Doanh thu sau khuyến mãi
                commission: -1 * order.transaction?.gf_total_commission || 0, // Commission
                other_fee: 0,
                total_shipment: 0,
                shipping_fee: 0, // Tiền ship thu thêm
                net_received: order.transaction?.net_total || 0, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: order.transaction?.net_total || 0,// Thu nhập thực tế từ merchant và khách
            }
            new_order.finance_data.other_promotion_price = new_order.finance_data.total_promotion_price - new_order.finance_data.co_fund_promotion_price
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }

    if (source === 'be') {
        new_order = {
            id: String(order.order_id),
            order_id: String(order.order_id),
            source: 'be',
            order_time: moment(order.order_time).add(7, 'hours').toISOString(),
            pick_time: moment(order.delivery_details?.pickup_time || order.order_time).add(7, 'hours').toISOString(),
            delivery_time: moment(order.to_be_delivered_at || order.delivered_at || order.order_created_at).toISOString(),
            delivery_time_unix: moment(order.to_be_delivered_at || order.delivered_at || order.order_created_at).unix(),
            order_time_sort: moment(order.order_time).unix(),
            driver_name: order.driver_name,
            driver_phone: parsePhoneNumber(order.driver_phone_no),
            customer_phone: parsePhoneNumber(order.phone_no),
            customer_name: [order.user_name, order.user_email].join(' '),
            customer_address: order.delivery_details?.drop_location_address,
            dishes: order.order_items.map(v => {
                return {
                    name: v.item_name,
                    description: v.item_details,
                    options: (v.customize_item && v.customize_item !== "[]") ? v.customize_item.map(o => {
                        return o.customize_options.map(o2 => {
                            return {
                                name: o.customize_item_name + ' ' + o2.customize_option_name,
                                quantity: 1,
                                option_name: o.customize_item_name,
                                option_item: o2.customize_option_name,
                                option_price: o2.customize_price * v.item_quantity,
                                option_discount_price: o2.customize_price * v.item_quantity,
                            }
                        })
                    }) : [],
                    quantity: v.item_quantity,
                    price: v.original_amount,
                    discount: v.original_amount - v.item_amount,
                    discount_price: v.item_amount,
                    note: v.item_note || "",
                }
            }),
            stock_dishes: order.stock_dishes || [],
            total: order.order_amount,
            commission: order.jugnoo_commission,
            total_for_biz: order.final_amount,
            note: order.delivery_note || "",
            cancel_reason: {
                3: "Hủy bởi khách",
                33: "Hủy bởi khách: chờ quá lâu",
                9: "Hủy bởi quán",
                10: "Hủy bởi admin",
                17: "Hủy bởi admin",
                21: "Hủy bởi Be: Order cancelled by system after Z time",
                25: "Hủy bởi Be",
            }[order.order_status] || "",
            coupons: order.offers?.food_discounts?.map(d => {
                return {
                    name: 'Giảm giá',
                    code: d.title,
                    total: d.partner_discount,
                }
            }) || [],
            payments: [{
                method: 'BE',
                total: order.final_amount,
                status: 'COMPLETED',
                note: 'Thanh toán qua Be'
            }],
            customer_data: {
                original_price: _.sumBy(order.order_items, v => v.original_amount), // Giá gốc
                sell_price: _.sumBy(order.order_items, v => v.item_amount), // Giá bán
                shipment_fee: 0, // Tiền ship gốc
                order_discount: 0, // Giảm giá đơn hàng
                shipment_discount: 0, // Giảm giá ship
                additional_income: 0, // Phụ thu từ khách
                total_paid: order.order_billable_amount, // Tổng tiền khách trả
            },
            finance_data: {
                original_price: _.sumBy(order.order_items, v => v.original_amount), // Giá gốc
                sell_price: _.sumBy(order.order_items, v => v.item_amount), // Giá bán (sau gạch giá)
                co_fund_promotion_price: order.discount, // Các khuyến mãi đơn hàng khác
                // other_promotion_price: _.sumBy(order.order_items, v => v.original_amount - v.item_amount),
                // total_promotion_price: order.discount + _.sumBy(order.order_items, v => v.original_amount - v.item_amount),
                // gross_received: _.sumBy(order.order_items, v => v.item_amount) - order.discount - _.sumBy(order.order_items, v => v.original_amount - v.item_amount),
                commission: order.jugnoo_commission,
                other_fee: 0,
                total_shipment: 0,
                shipping_fee: 0, // Tiền ship thu thêm
                net_received: order.final_amount, // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: order.final_amount,// Thu nhập thực tế từ merchant và khách
            },
            raw: order,
        }
        new_order.finance_data.other_promotion_price = _.sumBy(new_order.dishes, 'discount')
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price - new_order.finance_data.total_promotion_price

        new_order.total_discount = new_order.finance_data.total_promotion_price + new_order.commission
    }

    if (source === 'vill') {
        new_order = {
            id: String(order.id),
            order_id: order.code,
            source: 'vill',
            order_time: order.purchased_at,
            pick_time: order.confirmed_at,
            delivery_time: order.done_at,
            delivery_time_unix: moment(order.done_at).unix(),
            order_time_sort: moment(order.purchased_at).unix(),
            driver_name: order.driver.name,
            driver_phone: order.driver.phone,
            customer_name: order.user.name,
            customer_address: order.destination_location.address,
            customer_phone: order.user.phone,
            dishes: order.foodOrders.map(item => ({
                name: item.food.name,
                description: item.food.description,
                options: item.orderFoodOptions.map(option => ({
                    name: option.name,
                    quantity: option.quantity,
                    option_name: option.option_name,
                    option_item: option.option_item,
                })),
                quantity: item.quantity,
                price: item.price,
                discount: item.food.price - item.price,
                discount_price: item.price,
                note: item.note,
            })),
            dish_changed: false, // Assuming no dish change information is provided
            total: order.total_price,
            commission: order.trade_discount,
            total_discount: order.discount + order.trade_discount,
            total_for_biz: order.total_price - order.trade_discount,
            total_shipment: order.delivery_fee,
            shipment_discount: 0, // Assuming no shipment discount information is provided
            shipment_fee: order.delivery_fee,
            transaction_fee: order.service_fee,
            note: order.note,
            cancel_type: null, // Assuming no cancel type information is provided
            cancel_reason: null, // Assuming no cancel reason is provided
            cancel_by: null, // Assuming no cancel by information is provided
            coupons: order.promotions.map(promo => ({
                name: promo.name,
                code: promo.code,
                total: promo.amount,
            })),
            payments: [{
                method: order.payment_type,
                total: order.total_price,
                status: order.payment_status === 1 ? 'COMPLETED' : 'PENDING',
                note: `Thanh toán qua ${order.payment_type}`,
            }],
            stock_dishes: order.stock_dishes || [],
            customer_data: {
                original_price: order.sub_total_price,
                sell_price: order.total_price - order.delivery_fee - order.service_fee,
                shipment_fee: order.delivery_fee,
                order_discount: order.discount,
                shipment_discount: 0, // Assuming no shipment discount information is provided
                additional_income: 0, // Assuming no additional income information is provided
                total_paid: order.total_price,
            },
            finance_data: {
                original_price: order.sub_total_price,
                sell_price: order.total_price - order.delivery_fee - order.service_fee,
                co_fund_promotion_price: order.vill_promotion_fee,
                other_promotion_price: order.merchant_promotion_fee,
                total_promotion_price: order.discount,
                gross_received: order.total_price - order.delivery_fee - order.service_fee,
                commission: order.trade_discount,
                transaction_fee: order.service_fee,
                adjustment_fee: 0, // Assuming no adjustment fee information is provided
                other_fee: 0, // Assuming no other fee information is provided
                total_shipment: order.delivery_fee,
                shipping_fee: order.delivery_fee,
                shipping_discount: 0, // Assuming no shipping discount information is provided
                net_received: order.total_price - order.trade_discount - order.delivery_fee - order.service_fee,
                additional_income: 0, // Assuming no additional income information is provided
                real_received: order.total_price - order.trade_discount - order.delivery_fee - order.service_fee,
            },
            raw: order,
        }
    }

    if (source === 'tiktok') {
        const order2 = order.unofficial_data;
        new_order = {
            id: order2.main_order_id,
            order_id: order2.main_order_id,
            source: 'tiktok',
            allow_change_site: false,
            order_time: moment.unix(order2.main_order_create_time).toISOString(),
            pick_time: moment.unix(order2.main_order_create_time).toISOString(),
            delivery_time: moment.unix(order2.latest_rts_timestamp).toISOString(),
            delivery_time_unix: moment.unix(order2.latest_rts_timestamp).unix(),
            order_time_sort: order2.main_order_create_time,
            driver_name: '',
            driver_phone: '',
            customer_phone: order2.contact_info?.phone || '',
            customer_name: order2.contact_info?.name || '',
            customer_address: order2.contact_info?.address || '',
            dishes: order2.skus.map(sku => {
                return {
                    name: sku.product_name,
                    description: '',
                    options: [],
                    quantity: sku.quantity,
                    price: sku.quantity * toNumber(sku.unit_price.format_price),
                    discount: 0,
                    discount_price: sku.quantity * toNumber(sku.unit_price.format_price),
                    note: '',
                };
            }),
            total: 0, // calculate
            commission: 0,
            total_discount: 0,
            shipment_fee: toNumber(order2.payment_info?.shipping_origin_fee?.format_price),
            shipment_discount: toNumber(order2.payment_info?.shipping_fee_discount_platform?.format_price) + toNumber(order2.payment_info?.shipping_fee_discount_seller?.format_price),
            transaction_fee: 0,
            total_shipment: toNumber(order2.payment_info?.shipping_fee?.format_price),
            total_for_biz: toNumber(order2.payment_info?.grand_total?.format_price),
            note: '',
            cancel_reason: '',
            coupons: [],
            payments: [{
                method: 'TIKTOK',
                total: toNumber(order2.payment_info?.grand_total?.format_price),
                status: 'COMPLETED',
                note: 'Thanh toán qua TikTok'
            }],
            customer_data: {
                original_price: toNumber(order2.payment_info?.main_order_origin_sale_price?.format_price), // Giá gốc
                sell_price: toNumber(order2.payment_info?.main_order_origin_sale_price?.format_price), // Giá bán
                shipment_fee: 0, // Tiền ship gốc
                order_discount: 0, // Giảm giá đơn hàng
                shipment_discount: 0, // Giảm giá ship
                additional_income: 0, // Phụ thu từ khách
                total_paid: 0, // Tổng tiền khách trả
            },
            finance_data: {
                original_price: toNumber(order2.payment_info?.main_order_origin_sale_price?.format_price), // Giá gốc
                sell_price: toNumber(order2.payment_info?.main_order_origin_sale_price?.format_price), // Giá bán (sau gạch giá)
                co_fund_promotion_price: toNumber(order2.payment_info?.seller_discount_total?.format_price), // Các khuyến mãi đơn hàng khác
                other_promotion_price: 0, // Gạch giá
                total_promotion_price: 0,
                gross_received: toNumber(order2.transaction?.earning_amount?.amount), // Doanh thu sau KM             
                total_shipment: 0,
                shipment_discount: 0,
                shipping_fee: 0, // Tiền ship thu thêm
                transaction_fee: -1 * toNumber(order2.transaction?.fees?.amount),
                adjustment_fee: 0,
                net_received: toNumber(order2.transaction?.settlement_amount?.amount), // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shiper
                real_received: toNumber(order2.transaction?.settlement_amount?.amount),// Thu nhập thực tế từ merchant và khách
            },
            raw: order,
        }
        new_order.total = _.sumBy(new_order.dishes, 'price')
        new_order.finance_data.total_promotion_price = new_order.finance_data.co_fund_promotion_price + new_order.finance_data.other_promotion_price
    }

    if (source === 'lazada') {
        const order2 = order.unofficial_data;

        const customer_info = order2?.basicInfomation.detailBasicInfo;
        const shipping_address = order2?.basicInfomation.shippingAddress;
        const shop_payment_info = _.keyBy(order2?.myPaymentInformation.dataSource, v => v.name.replace(/:/g, ''))
        const customer_payment_info = _.keyBy(order2?.buyerPaymentInformation.dataSource, v => v.name.replace(/:/g, ''))
        new_order = {
            id: String(order.order_number),
            order_id: String(order.order_number),
            source: 'lazada',
            order_time: moment(order.created_at, "YYYY-MM-DD HH:mm:ss Z").toISOString(),
            order_time_sort: moment(order.created_at, "YYYY-MM-DD HH:mm:ss Z").unix(),
            delivery_time_unix: moment(order.updated_at, "YYYY-MM-DD HH:mm:ss Z").unix(),
            driver_name: '',  // No driver name in the data
            driver_phone: '',
            customer_phone: shipping_address?.receiverPhone || '',
            customer_name: customer_info?.customName || '',
            customer_address: shipping_address?.detailAddress || '',
            dishes: order.order_items.map((item) => {
                return {
                    name: item.name,
                    description: '',
                    options: [],
                    quantity: item.quantity || 1,
                    price: Number(item.item_price),
                    discount: 0,
                    discount_price: Number(item.item_price),
                    note: '',
                };
            }),
            total: Number(order.price),
            shipment_fee: 0,
            total_shipment: 0,
            shipment_discount: 0,
            transaction_fee: -1 * (_.toNumber(shop_payment_info?.['Payment Fee']?.total) + _.toNumber(shop_payment_info?.['Wrong Weight Adjustment']?.total)),
            commission: 0,
            total_for_biz: _.toNumber(shop_payment_info?.["Grand Total"]?.total),
            note: order.buyer_note || '',
            cancel_reason: '',
            cancel_by: '',
            coupons: [],
            payments: [{
                method: 'LAZADA',
                total: _.toNumber(shop_payment_info?.["Grand Total"]?.total),
                status: 'COMPLETED',
                note: 'Thanh toán qua Lazada'
            }],
            customer_data: {
                original_price: _.toNumber(customer_payment_info?.["Subtotal"]?.total),
                sell_price: _.toNumber(customer_payment_info?.["Subtotal"]?.total),
                shipment_fee: _.toNumber(customer_payment_info?.["Shipping Fee"]?.total),
                order_discount: order.voucher,
                shipment_discount: order.shipping_fee_discount_platform,
                additional_income: 0,
                total_paid: _.toNumber(customer_payment_info?.["Grand Total"]?.total),
            },
            finance_data: {
                original_price: _.toNumber(customer_payment_info?.["Subtotal"]?.total), // Giá gốc
                sell_price: _.toNumber(customer_payment_info?.["Subtotal"]?.total), // Giá bán (sau gạch giá)
                co_fund_promotion_price: order.voucher + order.shipping_fee_discount_platform + order.shipping_fee_discount_seller, // Các khuyến mãi đơn hàng khác
                other_promotion_price: 0,
                // total_promotion_price: 0,
                commission: 0, // Commission
                other_fee: 0,
                shipment_fee: 0,
                total_shipment: 0,
                gross_received: null, // Doanh thu sau khuyến mãi
                transaction_fee: -1 * _.toNumber(shop_payment_info?.['Payment Fee']?.total),
                adjustment_fee: -1 * _.toNumber(shop_payment_info?.['Wrong Weight Adjustment']?.total), // Phí phạt
                net_received: _.toNumber(shop_payment_info?.['Grand Total']?.total), // Thực nhận sau commission
                additional_income: 0, // Phụ thu từ khách or shipper
                real_received: _.toNumber(shop_payment_info?.["Grand Total"]?.total), // Thu nhập thực tế từ merchant và khách
            },
        };
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price + new_order.finance_data.additional_income - new_order.finance_data.total_promotion_price
        new_order.total_discount = new_order.finance_data.total_promotion_price

    }

    if (source === 'haravan') {
        const payment_method = {
            'Chuyển khoản qua ngân hàng': 'BANK_TRANSFER',
            'Thanh toán khi giao hàng (COD)': 'COD',
            'Thanh toán online qua ví MoMo': 'MOMO',
        }

        new_order = {
            id: String(order.id),
            order_id: String(order.id),
            source: 'haravan',
            allow_change_site: true,
            order_time: moment(order.created_at).toISOString(),
            pick_time: order.fulfillments[0]?.picking_date ? moment(order.fulfillments[0].picking_date).toISOString() : null,
            delivery_time: order.fulfillments[0]?.delivered_date ? moment(order.fulfillments[0].delivered_date).toISOString() : null,
            delivery_time_unix: order.fulfillments[0]?.delivered_date ? moment(order.fulfillments[0].delivered_date).unix() : null,
            order_time_sort: moment(order.created_at).unix(),
            driver_name: '',
            driver_phone: '',
            customer_phone: order.billing_address.phone || '',
            customer_name: order.billing_address.name || '',
            customer_address: [order.billing_address.address1, order.billing_address.address2, order.billing_address.district, order.billing_address.province, order.billing_address.country].filter(v => v).join(', '),
            dishes: order.line_items.map(v => {
                return {
                    name: v.name,
                    description: '',
                    options: [],
                    quantity: v.quantity,
                    price: v.price * v.quantity,
                    note: '',
                }
            }),
            commission: 0,
            total: order.total_price,
            total_for_biz: order.total_price,
            note: order.note,
            cancel_reason: order.cancel_reason,
            coupons: [],
            payments: order.transactions?.filter(v => v.kind === 'capture').map(v => ({
                method: payment_method[v.gateway] || 'CASH',
                total: v.amount,
                status: 'COMPLETED',
            })),
            raw: order,
        }
        new_order.total_discount = new_order.total - new_order.total_for_biz - new_order.commission
        if (new_order.total_discount == null || new_order.total_discount == "NaN" || new_order.total_discount == NaN) {
            new_order.total_discount = 0
        }
    }

    if (source === 'he' || source === 'momo') {
        new_order = {
            id: order.id,
            order_id: order.order_id,
            source,
            order_time: order.order_time,
            pick_time: order.pick_time,
            delivery_time: order.delivery_time || order.order_time,
            delivery_time_unix: order.delivery_time_unix || order.order_time_sort,
            order_time_sort: order.order_time_sort,
            driver_name: order.driver_name,
            driver_phone: parsePhoneNumber(order.driver_phone),
            customer_phone: parsePhoneNumber(order.customer_phone),
            customer_name: order.customer_name,
            customer_address: order.customer_address,
            dishes: order.dishes.map(v => {
                const dish = {
                    name: v.name,
                    code: v.code,
                    description: v.description,
                    options: v.options ? v.options.map(o => o.map(m => {
                        return {
                            code: m.code,
                            combo: m.combo,
                            name: m.name,
                            quantity: m.quantity,
                            option_name: m.option_name,
                            option_item: m.option_item,
                        }
                    })) : [],
                    quantity: v.quantity,
                    price: toNumber(v.price), // v.price: Giá gốc đã nhân số lượng CSKH.SELLER-240520-027
                    discount: v.discount, // v.discount: đã nhân số lượng
                    discount_price: toNumber(v.price) - toNumber(v.discount),
                    note: v.note || "",
                    quantity_unlimited: v.quantity_unlimited,
                    combo: v.combo,
                    is_gift: v.is_gift,
                }
                return dish
            }),
            commission: 0,
            total: _.sumBy(order.dishes, v => v.price),
            total_discount: null,
            shipment_fee: toNumber(order.shipment_fee) + toNumber(_.sumBy(order.ship_discount, v => toNumber(v.amount))),
            shipment_discount: toNumber(_.sumBy(order.ship_discount, v => toNumber(v.amount))),
            total_shipment: toNumber(order.shipment_fee),
            total_for_biz: toNumber(order.total_for_biz),
            note: order.note,
            cancel_reason: order.cancel_reason,
            coupons: order.coupons || [],
            payments: order.payments || [],
            last_transaction: {},
            finance_data: null,
            phone_report: order.phone_report,
            raw: order,
        }
        if (order.last_transaction) {
            const { vendor, callback_data } = order.last_transaction
            if (vendor === 'momo') {
                new_order.last_transaction = {
                    vendor,
                    transaction_id: callback_data?.transId,
                    success: callback_data?.resultCode === '0',
                    total: Number(callback_data?.amount),

                }
            }
        }
        new_order.customer_data = {
            original_price: _.sumBy(new_order.dishes, v => v.price), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, v => v.discount_price),// Giá bán (sau gạch giá)
            shipment_fee: new_order.shipment_fee || 0,
            order_discount: order.total_discount, // Giảm giá đơn hàng
            shipment_discount: 0, // Giảm giá ship
            additional_income: 0, // Phụ thu từ khách
            total_paid: order.total_for_biz,
        }

        new_order.finance_data = {
            original_price: _.sumBy(new_order.dishes, v => v.price), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, v => v.discount_price),// Giá bán (sau gạch giá)
            co_fund_promotion_price: new_order.shipment_discount || 0, // Các khuyến mãi đơn hàng khác
            other_promotion_price: _.sumBy(order.dishes, v => v.discount) || 0, // Khuyến mãi giá
            total_promotion_price: null, // Tổng khuyến mãi
            gross_received: null, // Doanh thu sau khuyến mãi
            commission: 0, // Commission
            other_fee: 0,
            shipment_fee: new_order.shipment_fee || 0,
            total_shipment: new_order.shipment_fee || 0,
            net_received: order.total_for_biz || 0, // Thực nhận sau commission
            additional_income: 0, // Phụ thu từ khách or shiper
            real_received: order.total_for_biz || 0,// Thu nhập thực tế từ merchant và khách
        }
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price + new_order.finance_data.additional_income - new_order.finance_data.total_promotion_price
        new_order.total_discount = new_order.finance_data.total_promotion_price
    }

    if (source === 'local') {
        new_order = {
            id: order.id,
            order_id: order.order_id,
            source,
            order_time: order.order_time,
            pick_time: order.pick_time,
            delivery_time: order.delivery_time || order.order_time,
            delivery_time_unix: order.delivery_time_unix || order.order_time_sort,
            order_time_sort: order.order_time_sort,
            driver_name: order.driver_name,
            driver_phone: parsePhoneNumber(order.driver_phone),
            customer_phone: parsePhoneNumber(order.customer_phone),
            customer_name: order.customer_name,
            customer_address: order.customer_address,
            dishes: order.dishes.map(v => {
                const dish = {
                    name: v.name,
                    code: v.code,
                    description: v.description,
                    options: v.options ? v.options.map(o => o.map(m => {
                        return {
                            code: m.code,
                            name: m.name,
                            quantity: m.quantity,
                            option_name: m.option_name,
                            option_item: m.option_item,
                            combo: m.combo,
                        }
                    })) : [],
                    quantity: v.quantity,
                    price: toNumber(v.price), // v.price: Giá sau KM đã nhân số lượng CSKH.SELLER-240520-027
                    discount: v.discount, // v.discount: đã nhân số lượng
                    discount_price: toNumber(v.price) - toNumber(v.discount),
                    note: v.note || "",
                    quantity_unlimited: v.quantity_unlimited,
                    combo: v.combo,
                    is_gift: v.is_gift,
                }
                return dish
            }),
            shipment_fee: toNumber(order.shipment_fee) + toNumber(_.sumBy(order.ship_discount, v => toNumber(v.amount))), // tien ship ban dau
            commission: 0,
            total_discount: 0, // Calculate
            total_shipment: toNumber(order.total_shipment), // tien ship cuoi cung
            shipment_discount: toNumber(_.sumBy(order.ship_discount, v => toNumber(v.amount))), // tien KM ship
            total_for_biz: order.total_for_biz,
            note: order.note,
            cancel_reason: order.cancel_reason,
            coupons: order.coupons || [],
            payments: order.payments || [],
            last_transaction: {},
            finance_data: null,
            phone_report: order.phone_report,
        }

        if (order.last_transaction) {
            const { vendor, callback_data } = order.last_transaction
            if (vendor === 'momo') {
                new_order.last_transaction = {
                    vendor,
                    transaction_id: callback_data?.transId,
                    success: callback_data?.resultCode === '0',
                    total: Number(callback_data?.amount),

                }
            }
        }
        new_order.customer_data = {
            original_price: _.sumBy(new_order.dishes, v => v.price), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, v => v.discount_price),// Giá bán (sau gạch giá)
            shipment_fee: new_order.shipment_fee || 0, // Tiền ship gốc
            order_discount: order.total_discount, // Giảm giá đơn hàng
            shipment_discount: toNumber(_.sumBy(order.ship_discount, v => toNumber(v.amount))), // Giảm giá ship
            additional_income: 0, // Phụ thu từ khách
            total_paid: _.sumBy(order.payments ?? [], 'total')
        }
        new_order.finance_data = {
            original_price: _.sumBy(new_order.dishes, v => v.price), // Giá gốc
            sell_price: _.sumBy(new_order.dishes, v => v.discount_price),// Giá bán (sau gạch giá)
            co_fund_promotion_price: (new_order.shipment_discount ?? 0) + (_.sumBy(new_order.coupons, v => v.total) ?? 0), // Các khuyến mãi đơn hàng khác
            other_promotion_price: _.sumBy(new_order.dishes, v => v.discount) ?? 0, // Khuyến mãi giá
            total_promotion_price: null, // Tổng khuyến mãi
            gross_received: null, // Doanh thu sau khuyến mãi
            commission: 0, // Commission
            other_fee: 0,
            shipment_fee: new_order.shipment_fee || 0, // Tiền ship sau giảm
            total_shipment: new_order.total_shipment || 0, // Tiền ship ban đầu
            net_received: order.total_for_biz || 0, // Thực nhận sau commission
            additional_income: Number(order.total_surcharge || 0), // Phụ thu từ khách or shiper
            real_received: order.total_for_biz || 0,// Thu nhập thực tế từ merchant và khách
        }
        new_order.finance_data.total_promotion_price = new_order.finance_data.other_promotion_price + new_order.finance_data.co_fund_promotion_price
        new_order.finance_data.gross_received = new_order.finance_data.original_price + new_order.finance_data.additional_income - new_order.finance_data.total_promotion_price
        new_order.total_discount = new_order.finance_data.total_promotion_price
        new_order.total = new_order.finance_data.original_price
    }

    new_order.driver_phone = new_order.driver_phone?.replace(/\s/g, '')?.replace('+84', '0')
    new_order.customer_phone = new_order.customer_phone?.replace(/\s/g, '')?.replace('+84', '0')
    new_order.total_display = new_order.total?.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })

    if (order.stock_dishes?.length > 0) {
        for (const new_order_dish of new_order.dishes) {
            const order_dish = order.stock_dishes.find(d => d.name === new_order_dish.name)
            if (order_dish) {
                new_order_dish.stocks = order_dish.stocks
                new_order_dish.has_stock_config = order_dish.has_stock_config
            }
        }
    }
    return new_order
}

function map_order_status(source, order) {
    if (source === 'baemin') {
        return {
            'READY_TO_PICK': 'PENDING',
            'PICKED': 'FINISH',
            'DELIVERED': 'FINISH',
            'COMPLETED': 'FINISH',
            'CANCELED': 'CANCEL',
        }[order.status] || null
    }
    if (source === 'shopee' || source === 'shopee_fresh') {
        return {
            '11': 'DOING',
            '22': 'DOING',
            '6': 'FINISH',
            '7': 'FINISH',
            '9': 'FINISH',
            '4': 'PRE_ORDER',
            '8': 'CANCEL',
        }[order.order_status] || null
    }

    if (source === 'shopee_ecom') {
        return {
            '1': 'DOING',
            '2': 'PICK',
            '5': 'CANCEL',
            '4': 'FINISH',
        }[order.status] || null
    }

    if (source === 'gojek') {
        return {
            'ONGOING': 'DOING',
            'UNFULFILLED': 'CANCEL',
            'COMPLETED': 'FINISH',
        }[order.status] || null
    }

    if (source === 'gojek2') {
        const order2 = order.source2
        return {
            'ONGOING': 'DOING',
            'UNFULFILLED': 'CANCEL',
            'COMPLETED': 'FINISH',
        }[order2.status.goresto] || null
    }

    if (source === 'grab' || source === 'grab_mart') {
        return {
            'COMPLETED': 'FINISH',
            'CANCELLED': 'CANCEL',
            'FAILED': 'CANCEL',
        }[order.deliveryTaskpoolStatus] || null
    }

    if (source === 'be') {
        const status_map = {
            FINISH: [2, 18, 22, 5, 8, 0, -1, 23],
            CANCEL: [10, 3, 9, 25, 21, 33, 17]
        };
        for (const [status, codes] of Object.entries(status_map)) {
            if (codes.includes(order.order_status)) {
                return status;
            }
        }
        return null;
    }

    if (source === 'tiktok') {
        return {
            '1000': 'CANCEL',
            '2000': 'FINISH',
        }[order.unofficial_data.main_order_status] || null
    }

    if (source === 'lazada') {
        if (order.statuses.includes('confirmed')) {
            return 'FINISH'
        }
        if (order.statuses.includes('canceled')) {
            return 'CANCEL'
        }
        return null
    }
    return null
}

function map_site_finance(source, order) {
    let new_order = null
    if (source === 'grab' || source === 'grab_mart') {
        new_order = {
            id: order.transaction_id,
            order_id: "",
            type: {
                payment: "payment",
                advertisement: "marketing",
                adjustment: "adjustment",
            }[order.transaction_category] ?? "other",
            sub_total: order.order_value,
            discount: -1 * order.mex_fund_discount,
            gross_total: order.net_sales,
            commission: -1 * order.gf_total_commission,
            net_total: order.net_total,
            description: order.description,
            created_at_unix: moment(order.created_at).add(-7, 'hours').unix(), // Grab assume UTC as UTC+7 
        }
        if (order.long_order_id && order.short_order_number) {
            new_order.order_id = order.long_order_id
        }
    }
    if (source === 'tiktok') {
        const sub_totals = order.in_come.fee_list.map(f => toNumber(f.sub_fees.find(s => s.type === 'subtotal_before_discount')?.amount?.amount))
        const discounts = order.in_come.fee_list.map(f => toNumber(f.sub_fees.find(s => s.type === 'seller_discount')?.amount?.amount))
        const gross_total = toNumber(order.in_come?.amount?.amount)
        const total_fee = -1 * toNumber(order.out_come?.amount?.amount ?? 0)
        new_order = {
            id: order.statement_detail_id,
            order_id: order.trade_order_id,
            type: "payment",
            sub_total: _.sum(sub_totals),
            discount: -1 * _.sum(discounts),
            gross_total: gross_total,
            commission: total_fee,
            net_total: toNumber(order.settlement_amount.amount),
            description: '',
            created_at_unix: parseInt(Number(order.placed_time) / 1000), // Grab assume UTC as UTC+7 
        }
    }
    return new_order
}

const day_of_week_map = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
const day_of_week_map2 = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

function map_working_hour(source, working_hours) {
    const new_working_hour = Object.fromEntries(day_of_week_map.map(day => [day, { start: null, end: null, closed: false }]));

    if (source === 'shopee' || source === 'shopee_fresh') {
        working_hours.map(({ weekday, intervals, is_closed }) => {
            const start = intervals ? moment.utc(intervals[0].start_relative_sec * 1000).format('HH:mm') : null
            const end = intervals ? moment.utc(intervals[0].end_relative_sec * 1000).format('HH:mm') : null
            const dayOfWeek = day_of_week_map2[weekday - 1];
            return { [dayOfWeek]: { start, end, closed: is_closed } };
        }).forEach(day => Object.assign(new_working_hour, day));
    }

    if (source === 'baemin') {
        Object.entries(working_hours).forEach(([key, value]) => {
            const dayOfWeek = day_of_week_map[key];
            new_working_hour[dayOfWeek] = {
                closed: value.length === 0,
                start: value.length === 0 ? null : moment.utc(value[0].openAt * 60000).format('HH:mm'),
                end: value.length === 0 ? null : moment.utc(value[0].closeAt * 60000).format('HH:mm'),
            }
        });
    }

    if (source === 'grab' || source === 'grab_mart') {
        working_hours.map(({ ranges }, i) => ({
            [day_of_week_map[i]]: {
                start: ranges.length > 0 ? `${ranges[0].start}` : null,
                end: ranges.length > 0 ? `${ranges[0].end}` : null,
                closed: ranges.length === 0
            }
        })).forEach(day => Object.assign(new_working_hour, day));
    }

    if (source === 'gojek') {
        day_of_week_map.forEach((dayOfWeek, i) => {
            const { closed, slots } = working_hours[dayOfWeek];
            Object.assign(new_working_hour[dayOfWeek], { start: slots[0] ? `${slots[0].open_time}` : null, end: slots[0] ? `${slots[0].close_time}` : null, closed });
        });
    }

    if (source === 'be') {
        day_of_week_map.forEach((dayOfWeek, i) => {
            const { timings } = working_hours[i];
            Object.assign(new_working_hour[dayOfWeek], {
                start: timings.length == 0 ? null : moment(timings[0].start_time, 'HH:mm:ss').format('HH:mm'),
                end: timings.length == 0 ? null : moment(timings[0].end_time, 'HH:mm:ss').format('HH:mm'),
                closed: timings.length == 0
            });
        });
    }

    return new_working_hour;
}

function map_special_working_hour(source, working_hours) {
    const result = []
    if (source === 'shopee' || source === 'shopee_fresh') {
        for (const item of working_hours) {
            result.push({
                source,
                name: 'Đóng mở cửa đặt biệt',
                from_date: moment.unix(item.date_start / 1000).toISOString(),
                to_date: moment.unix(item.date_end / 1000).toISOString(),
                closed: item.date_type === 1,
                open_hours: []
            });
        }
    }

    if (source === 'grab' || source === 'grab_mart') {
        for (const item of working_hours) {
            result.push({
                source,
                name: item.metadata.description,
                from_date: moment(item.startDate, 'YYYY-MM-DD').startOf('day').toISOString(),
                to_date: moment(item.endDate, 'YYYY-MM-DD').endOf('day').toISOString(),
                closed: item.openingHours.openPeriodType === 'ClosedAllDay',
                open_hours: []
            });
        }
    }

    return result;
}


function reverse_map_working_hour(source, new_working_hour) {
    if (source === 'shopee' || source === 'shopee_fresh') {
        const working_hours = [];
        for (let i = 0; i < 7; i++) {
            const start = new_working_hour[day_of_week_map2[i]].start;
            const end = new_working_hour[day_of_week_map2[i]].end;
            const closed = new_working_hour[day_of_week_map2[i]].closed;

            const open_time = moment.duration(start).asSeconds();
            const close_time = moment.duration(end).asSeconds();
            working_hours.push({
                weekday: i + 1,
                intervals: closed ? null : [{
                    start_relative_sec: open_time,
                    end_relative_sec: close_time,
                }],
                config_enabled: closed ? 0 : 1,
            });
        }
        return working_hours;
    }

    if (source === 'baemin') {
        const working_hours = {};
        for (let i = 0; i < day_of_week_map.length; i++) {
            const dayOfWeek = day_of_week_map[i];
            const start = new_working_hour[dayOfWeek].start;
            const end = new_working_hour[dayOfWeek].end;
            const closed = new_working_hour[day_of_week_map[i]].closed;
            const open_time = moment.duration(start).asMinutes();
            const close_time = moment.duration(end).asMinutes();

            working_hours[i.toString()] = closed ? [] : [
                {
                    openAt: open_time,
                    closeAt: close_time,
                },
            ]
        }
        return working_hours;
    }

    if (source === 'grab' || source === 'grab_mart') {
        const working_hours = [];
        for (let i = 0; i < day_of_week_map.length; i++) {
            const dayOfWeek = day_of_week_map[i];
            const startTime = new_working_hour[dayOfWeek].start;
            const endTime = new_working_hour[dayOfWeek].end;
            const closed = new_working_hour[dayOfWeek].closed;

            working_hours[i] = {
                ranges: closed ? [] : [
                    {
                        start: startTime, // 10:30
                        end: endTime, // 21:30
                    },
                ],
            };
        }
        return working_hours;
    }

    if (source === 'gojek') {
        const working_hours = {};
        for (let i = 0; i < day_of_week_map.length; i++) {
            const dayOfWeek = day_of_week_map[i];
            const startTime = new_working_hour[dayOfWeek].start;
            const endTime = new_working_hour[dayOfWeek].end;
            const closed = new_working_hour[dayOfWeek].closed;
            working_hours[dayOfWeek] = {
                closed: closed,
                spill_over: false,
                twenty_four_hours_open: false,
                slots: closed ? [] : [
                    {
                        open_time: startTime,
                        close_time: endTime,
                    },
                ]
            };

        }
        return working_hours;
    }

    if (source === 'be') {
        const working_hours = [];
        for (let i = 0; i < day_of_week_map.length; i++) {
            const start_time = new_working_hour[day_of_week_map[i]].start;
            const end_time = new_working_hour[day_of_week_map[i]].end;
            const closed = new_working_hour[day_of_week_map[i]].closed;

            working_hours[i] = {
                day_id: i + 1,
                name: {
                    "monday": "Thứ hai",
                    "tuesday": "Thứ ba",
                    "wednesday": "Thứ tư",
                    "thursday": "Thứ năm",
                    "friday": "Thứ sáu",
                    "saturday": "Thứ bảy",
                    "sunday": "Chủ nhật",
                }[day_of_week_map[i]],
                timings: closed ? [] : [
                    {
                        time_id: -1,
                        start_time: moment(start_time, 'HH:mm').format('HH:mm'), // 10:30:00
                        end_time: moment(end_time, 'HH:mm').format('HH:mm'), // 21:30:00
                    },
                ],
            };
        }
        return working_hours;
    }

    return null

}


function map_menu(source, { categories, option_categories }) {
    let new_categories = []
    let new_option_categories = []
    if (source === 'shopee' || source === 'shopee_fresh') {
        new_categories = categories.map(m => {
            return {
                id: m.catalog_id,
                name: _startCase(m.catalog_name),
                code: _textToSlug(m.catalog_name),
                description: "",
                active: true,
                sources: [source],
                items: m.dishes.map(i => {
                    return {
                        id: i.id,
                        name: _startCase(i.name),
                        code: _textToSlug(i.name),
                        sources: [source],
                        description: i.description,
                        image: i.picture_url,
                        price: i.price,
                        active: i.stock_info.stock === 1,
                    }
                }),
                // raw: m
            }
        })
        new_option_categories = option_categories.map(v => {
            return {
                id: String(v.id),
                name: _startCase(v.name),
                sources: [source],
                options: v.options.map(m => {
                    return {
                        id: String(m.id),
                        name: _startCase(m.name),
                        sources: [source],
                        price: m.price,
                        active: m.is_active,
                    }
                }),
                category_ids: [],
                rule: {
                    type: "SELECT_ONE",
                    required: true,
                    min_quantity: 1,
                    max_quantity: 1,
                },
                // raw: v
            }
        })
    }

    if (source === 'shopee_ecom') {
        new_categories = categories.map(m => {
            return {
                id: m.catalog_id,
                name: _startCase(m.catalog_name),
                code: _textToSlug(m.catalog_name),
                description: "",
                active: true,
                sources: [source],
                items: m.dishes.map(i => {
                    return {
                        id: i.id,
                        name: _startCase(i.name),
                        code: _textToSlug(i.name),
                        sources: [source],
                        description: i.description,
                        image: i.picture_url,
                        price: i.price,
                        active: i.stock_info.stock === 1,
                    }
                }),
                // raw: m
            }
        })
        new_option_categories = option_categories.map(v => {
            return {
                id: String(v.id),
                name: _startCase(v.name),
                sources: [source],
                options: v.options.map(m => {
                    return {
                        id: String(m.id),
                        name: _startCase(m.name),
                        sources: [source],
                        price: m.price,
                        active: m.is_active,
                    }
                }),
                category_ids: [],
                rule: {
                    type: "SELECT_ONE",
                    required: true,
                    min_quantity: 1,
                    max_quantity: 1,
                },
                // raw: v
            }
        })
    }

    if (source === 'baemin') {
        new_categories = categories.map(m => {
            let dishes = option_categories.filter(v => v.sectionId === m.id)
            return {
                id: m.id,
                name: _startCase(m.name),
                code: _textToSlug(m.name),
                sources: [source],
                description: m.description || "",
                image: m.iconUrl,
                price: 0,
                active: true,
                items: dishes.map(i => {
                    return {
                        id: i.id,
                        name: _startCase(i.name),
                        code: _textToSlug(i.name),
                        sources: [source],
                        description: i.description || "",
                        image: i.imageUrl,
                        price: i.price,
                        active: i.isAvailable,
                    }
                }),
                // raw: m
            }
        })
        // option_categories === dishes
        option_categories.map(dish => {
            dish.options.map(v => {
                new_option_categories.push({
                    id: v.id,
                    name: _startCase(v.name),
                    sources: [source],
                    options: v.items.map(m => {
                        return {
                            id: m.id,
                            name: _startCase(m.name),
                            sources: [source],
                            price: m.price,
                            active: m.isActive,
                        }
                    }),
                    category_ids: [dish.id],
                    rule: {
                        type: "SELECT_ONE",
                        required: true,
                        min_quantity: 1,
                        max_quantity: 1,
                    },
                    // raw: v
                })
            })
        })
    }

    if (source === 'grab') {
        new_categories = categories.map(m => {
            return {
                id: m.categoryID,
                name: _startCase(m.categoryName),
                code: _textToSlug(m.categoryName),
                sources: [source],
                description: "",
                active: m.availableStatus === 1,
                items: m.items.map(i => {
                    return {
                        // id: i.itemID,
                        id: name_to_id(_startCase(i.itemName)),
                        name: _startCase(i.itemName),
                        code: _textToSlug(i.itemName),
                        sources: [source],
                        description: i.description,
                        image: i.imageURL,
                        price: i.priceInMin,
                        active: i.availableStatus === 1,
                    }
                }),
                // raw: m
            }
        })
        new_option_categories = option_categories.map(v => {
            return {
                id: v.modifierGroupID,
                name: _startCase(v.modifierGroupName),
                sources: [source],
                options: v.modifiers.map(m => {
                    return {
                        id: m.modifierID,
                        name: _startCase(m.modifierName),
                        sources: [source],
                        price: m.priceInMin,
                        active: m.availableStatus === 1,
                    }
                }),
                category_ids: new_categories.flatMap(category => category.items).
                    filter(item => v.relatedItemIDs.some(m => m === item.id)).
                    map(m => m.id.toString()),
                rule: {
                    type: v.selectionRangeMax > 1 ? "SELECT_MANY" : "SELECT_ONE",
                    required: v.selectionRangeMin > 0,
                    min_quantity: v.selectionRangeMin,
                    max_quantity: v.selectionRangeMax,
                },
                // raw: v
            }
        })
    }

    if (source === 'grab_mart') {
        new_categories = categories.map(m => {
            return {
                id: m.itemClassID,
                name: _startCase(m.itemClassName),
                code: _textToSlug(m.itemClassName),
                sources: [source],
                description: "",
                active: true,
                items: [],
                sub_categories: m.subDepartments.map(sub => {
                    return {
                        id: sub.itemClassID,
                        name: _startCase(sub.itemClassName),
                        code: _textToSlug(sub.itemClassName),
                        sources: [source],
                        items: sub.items.map(i => {
                            return {
                                // id: i.itemID,
                                id: name_to_id(_startCase(i.itemName)),
                                name: _startCase(i.itemName),
                                code: _textToSlug(i.itemName),
                                sources: [source],
                                description: i.description,
                                image: i.imageURL,
                                price: i.priceInMin,
                                active: i.availableStatus === 1,
                            }
                        }),
                    }
                }),
            }
        })
        new_option_categories = option_categories.map(v => {
            return {
                id: v.modifierGroupID,
                name: _startCase(v.modifierGroupName),
                sources: [source],
                options: v.modifiers.map(m => {
                    return {
                        id: m.modifierID,
                        name: _startCase(m.modifierName),
                        sources: [source],
                        price: m.priceInMin,
                        active: m.availableStatus === 1,
                    }
                }),
                category_ids: new_categories.flatMap(category => category.sub_categories).flatMap(s => s.items).
                    filter(item => v.relatedItemIDs.some(m => m === item.id)).
                    map(m => m.id.toString()),
                rule: {
                    type: v.selectionRangeMin <= 1 ? "SELECT_ONE" : "SELECT_MANY",
                    required: v.selectionRangeMin > 0,
                    min_quantity: v.selectionRangeMin,
                    max_quantity: v.selectionRangeMax,
                }
            }
        }).filter(v => v.category_ids.length > 0)
    }

    if (source === 'gojek') {
        new_categories = categories.map(m => {
            return {
                id: m.common_id,
                name: _startCase(m.name),
                code: _textToSlug(m.name),
                sources: [source],
                description: "",
                active: m.active,
                items: m.menu_items.map(i => {
                    return {
                        id: i.common_id,
                        name: _startCase(i.name),
                        code: _textToSlug(i.name),
                        sources: [source],
                        description: i.description,
                        image: i.image,
                        price: i.price,
                        active: i.active,
                    }
                })
            }
        })

        const group_option_categories = _.groupBy(option_categories, obj => _textToSlug(obj.name));

        new_option_categories = Object.keys(group_option_categories).map(category_key => {
            const variants = group_option_categories[category_key].flatMap(v => v?.variants || []);
            const group_variants = _.uniqBy(variants, obj => _textToSlug(obj.name));

            const option_category = group_option_categories[category_key][0];

            return {
                id: option_category.common_id,
                name: _startCase(option_category.name),
                sources: [source],
                options: group_variants?.map(m => {
                    return {
                        id: m.common_id,
                        name: _startCase(m.name),
                        sources: [source],
                        price: m.price,
                        active: m.active,
                    }
                }),
                category_ids: categories.
                    flatMap(v => v.menu_items).
                    flatMap(v => v.variant_category_common_ids).
                    filter(v => group_option_categories[category_key].some(c => c.common_id === v)),
                rule: option_category?.rules?.selection,
            }
        })
    }

    if (source === 'be') {
        new_categories = categories.map(m => {
            return {
                id: m.category.category_id,
                name: _startCase(m.category.name),
                code: _textToSlug(m.category.name),
                sources: [source],
                description: "",
                active: true,
                items: m.items.map(i => {
                    return {
                        // id: i.restaurant_item_id,
                        id: name_to_id(_startCase(i.item_name)),
                        name: _startCase(i.item_name),
                        code: _textToSlug(i.item_name),
                        sources: [source],
                        description: i.item_details,
                        image: i.item_image,
                        price: i.price,
                        active: i.is_active === 1,
                    }
                }),
                // raw: m
            }
        })
    }

    if (source === 'haravan') {
        new_categories = [{
            id: v4(),
            name: 'Haravan Menu',
            code: 'haravan_menu',
            sources: [source],
            description: "",
            active: true,
            items: categories.map(i => {
                return {
                    id: String(i.id),
                    name: _startCase(i.title),
                    code: _textToSlug(i.title),
                    sources: [source],
                    description: i.body_html,
                    image: i.images?.length > 0 ? i.images[0].src : "",
                    price: i.variants[0].price,
                    active: true,
                }
            })
        }]

    }

    return { categories: new_categories, option_categories: new_option_categories.filter(v => v.category_ids.length > 0) };
}

function map_order_feedback(source, data) {
    if (source === 'shopee' || source === 'shopee_fresh') {
        return {
            ref_id: String(data.id),
            order_id: data.order_code,
            rating: data.rating_star - 100,
            comment: [
                ...(data.comments || []),
                ...(data.reasons || []),
            ].join(', '),
            images: data.photos?.map(v => v.photo_url) ?? [],
            customer_name: data.user_info?.name || '',
            created_at_unix: data.create_time,
            data,
        }
    }
    if (source === 'grab' || source === 'grab_mart') {
        return {
            ref_id: data.bookingCode,
            order_id: data.orderID,
            rating: data.rating ?? 5,
            comment: data.description,
            images: [],
            customer_name: data.eaterName,
            created_at_unix: moment(data.createdAt).unix(),
            data,
        }
    }
    if (source === 'be') {
        return {
            ref_id: String(data.rating_id),
            order_id: String(data.order_id),
            rating: data.rating ?? 5,
            comment: data.feedback,
            images: data.images ?? [],
            customer_name: data.user_name,
            created_at_unix: moment(data.rated_at, 'HH:mm DD/MM/YYYY').unix(), // 09:15 31/07/2024
            data,
        }
    }
    if (source === 'gojek') {
        return {
            ref_id: data.order.id,
            order_id: data.order.id,
            rating: data.rating ?? 5,
            comment: data.text,
            images: [],
            created_at_unix: moment(data.created_at).unix(),
            data,
        }
    }
    return null
}


function map_site_feedback(source, data) {
    if (source === 'shopee' || source === 'shopee_fresh') {
        const feedback_summary = data.find(v => v.time_range === 1)

        return {
            avg_rating: feedback_summary.avg_rating,
            total_rating: feedback_summary.total_rating,
            total_star1: feedback_summary.ratings_by_star.find(v => v.star === 101)?.total_rating ?? 0,
            total_star2: feedback_summary.ratings_by_star.find(v => v.star === 102)?.total_rating ?? 0,
            total_star3: feedback_summary.ratings_by_star.find(v => v.star === 103)?.total_rating ?? 0,
            total_star4: feedback_summary.ratings_by_star.find(v => v.star === 104)?.total_rating ?? 0,
            total_star5: feedback_summary.ratings_by_star.find(v => v.star === 105)?.total_rating ?? 0,
        }
    }
    if (source === 'grab' || source === 'grab_mart') {
        return {
            avg_rating: data.aggregatedRatingScore,
            total_rating: data.ratingCount,
            total_star1: parseInt(data.ratingDistribution.find(v => v.score === 1).countPercentage / 100 * data.ratingCount),
            total_star2: parseInt(data.ratingDistribution.find(v => v.score === 2).countPercentage / 100 * data.ratingCount),
            total_star3: parseInt(data.ratingDistribution.find(v => v.score === 3).countPercentage / 100 * data.ratingCount),
            total_star4: parseInt(data.ratingDistribution.find(v => v.score === 4).countPercentage / 100 * data.ratingCount),
            total_star5: parseInt(data.ratingDistribution.find(v => v.score === 5).countPercentage / 100 * data.ratingCount),
        }
    }
    // if (source === 'be') {
    //     return {
    //         order_id: String(data.order_id),
    //         rating: data.rating ?? 5,
    //         comment: data.feedback,
    //         images: data.images ?? [],
    //         created_at_unix: moment(data.rated_at, 'HH:mm DD/MM/YYYY').unix(), // 09:15 31/07/2024
    //         data,
    //     }
    // }
    // if (source === 'gojek') {
    //     return {
    //         order_id: data.order.id,
    //         rating: data.rating ?? 5,
    //         comment: data.text,
    //         images: [],
    //         created_at_unix: moment(data.created_at).unix(),
    //         data,
    //     }
    // }
    return null
}

function map_order_incident(source, data) {
    if (source === 'grab' || source === 'grab_mart') {
        const status_detail = {
            401: 'Chưa giải quyết',
            402: 'Grab đã hoàn tiền',
            403: 'Grab đã gửi voucher',
            501: 'Grab đã bồi hoàn'
        }[data.appeaseStatus] ?? ''
        return {
            order_id: data.orderID,
            description: data.description?.trim(),
            status_detail,
            images: data.imgURL ?? [],
            refunded_amount: data.refundAmount,
            created_at_unix: moment(data.incidentClosedAt).unix(),
            data,
        }
    }
    return null
}

module.exports = {
    _textToSlug,
    map_order,
    map_order_status,
    map_site_finance,
    map_menu,
    map_working_hour,
    map_special_working_hour,
    reverse_map_working_hour,
    map_order_feedback,
    map_site_feedback,
    map_order_incident,
}