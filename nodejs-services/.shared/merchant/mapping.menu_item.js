function reverse_map_menu_item(source, menu_categories, item_name, { active }) {
    if (source === 'shopee') {

    }

    if (source === 'baemin') {

    }


    for (let i = 0; i < menu_categories.length; i++) {
        const category = menu_categories[i];
        for (let j = 0; j < category.items.length; j++) {
            const item = category.items[j];
            if (item.name.toUpperCase() == item_name.toUpperCase()) {
                if (source === 'gojek') {
                    return {
                        id: item.id,
                        image_url: item.image,
                        name: item.name,
                        signature: false,
                        description: item.description,
                        active: active,
                        menu_common_id: category.id,
                        price: item.price
                    }
                }
                if (source === 'grab' || source === 'grab_mart') {
                    return {
                        availableStatus: active ? 1 : 3,
                        itemIDs: [
                            item.id
                        ]
                    }
                }
                if (source === 'be') {
                    return {
                        is_active: active ? 1 : 0,
                        restaurant_item_id: [
                            item.id
                        ]
                    }
                }

            }

        }
    }

    return null;
}


module.exports = { reverse_map_menu_item }