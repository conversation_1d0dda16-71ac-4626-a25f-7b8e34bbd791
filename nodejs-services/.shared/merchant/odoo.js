const axios = require('../axios');
const { text_slugify } = require('../helper')

const get_cookie = async () => {
    try {
        const response = await axios({
            method: 'POST',
            url: process.env.ODOO_URL + '/web/session/authenticate',
            headers: { 'Content-Type': 'application/json' },
            data: {
                jsonrpc: '2.0',
                params: {
                    "db": process.env.ODOO_DATABASE,
                    "login": process.env.ODOO_USER,
                    "password": process.env.ODOO_PASSWORD
                }
            }
        })


        const cookie = response.headers.get("Set-Cookie");

        return cookie;
    } catch (error) {
        console.error(error)
        return null
    }
}

let odoo = {}

odoo.get_menu_items = async () => {
    const odoo_items = []

    const cookie = await get_cookie();
    let limit = 100
    let offset = 0
    do {
        const resp = await axios({
            method: 'POST',
            url: `${process.env.ODOO_URL}/web/dataset/call_kw/product.template/web_search_read`,
            headers: { 'Content-Type': 'application/json', 'Cookie': cookie },
            data: {
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    "model": "product.template",
                    "method": "web_search_read",
                    "args": [],
                    "kwargs": {
                        "limit": limit,
                        "offset": offset,
                        "order": "",
                        "context": {
                            "lang": "en_US",
                            "tz": "Asia/Saigon",
                            "uid": 2,
                            "allowed_company_ids": [
                                1
                            ],
                            "bin_size": true,
                            "search_default_filter_to_availabe_pos": 1,
                            "default_available_in_pos": true,
                            "create_variant_never": "no_variant"
                        },
                        "count_limit": 10000,
                        "domain": [
                            [
                                "available_in_pos",
                                "=",
                                true
                            ]
                        ],
                        "fields": [
                            "id",
                            "product_variant_count",
                            "currency_id",
                            "activity_state",
                            "__last_update",
                            "name",
                            "product_brand_id",
                            "priority",
                            "default_code",
                            "list_price",
                            "qty_available",
                            "uom_id",
                            "type",
                            "show_on_hand_qty_status_button"
                        ]
                    }
                }
            }
        })
        offset += limit
        odoo_items.push(...resp.data.result.records)
        if (resp.data.result.records.length === 0)
            break
    } while (true);
    return odoo_items
}

module.exports = odoo