const axios = require('../axios');
const _ = require('lodash');
const qs = require('qs')
const xlsx = require('xlsx');
const moment = require('moment-timezone');
const { text_slugify, retry_operation } = require('../helper');
moment.tz.setDefault('Asia/Bangkok');

const skip_call_api = () => moment().hour() < 8 || moment().hour() > 21;


const base_headers = ({ site_id, access_token }) => {
    return {
        'host': 'gmerchant.deliverynow.vn',
        'operate-source': 'partnerapp',
        'user-agent': 'appver=32300 secid=4002 (Linux; Android 10; samsung Build/QP1A.190711.020) food_rn_ver=3272 spp_rn_ver=2915 app_type=2 shopee_rn_bundle_version=285000000',
        'x-foody-access-token': access_token,
        'x-foody-api-version': '1',
        'x-foody-app-type': '1024',
        'x-foody-client-id': 'GG+mO/PW1/A8ErH3hX9hxGEF4yYeIDMmalqFvTtvowA=',
        'x-foody-client-language': 'vi',
        'x-foody-client-type': '1',
        'x-foody-client-version': '3.0.0',
        'x-foody-entity-id': site_id,
    }
}

const base_headers_2 = ({ access_token }) => {
    return {
        'a-appversion': '32101',
        'a-brand': 'unknown',
        'a-lang': 'vi',
        'a-os': 'android',
        'content-type': 'application/json',
        'host': 'app.partner.shopee.vn',
        'user-agent': 'okhttp/3.12.4 app_type=2 shopee_rn_bundle_version=285000000',
        'x-merchant-token': access_token
    }
}

const web_base_headers = ({ site_id, access_token }) => {
    return {
        'authority': 'gmerchant.deliverynow.vn',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
        'origin': 'https://partner.shopee.vn',
        'referer': 'https://partner.shopee.vn/',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-foody-access-token': access_token,
        'x-foody-entity-id': String(site_id),
        'x-foody-api-version': '1',
        'x-foody-app-type': '1025',
        'x-foody-client-id': 'ffffffff-c9a4-034c-ffff-ffffc2e834d9',
        'x-foody-client-language': 'vi',
        'x-foody-client-type': '1',
        'x-foody-client-version': '3.0.0',
    }
}

const sign_request_async = async (config) => {
    try {
        const { url = '', method = 'GET', data, params } = config

        let body = data
        if (method.toLowerCase() === 'post') {
            body = JSON.stringify(data)
        }
        let request_url = url
        if (!_.isEmpty(params)) {
            const queryString = qs.stringify(params, { sort: (a, b) => a.localeCompare(b) })
            if (queryString.length > 0) {
                if (request_url[request_url.length - 1] === '?') {
                    request_url += queryString
                } else if (request_url.includes('?')) {
                    request_url = `${request_url}&${queryString}`
                } else {
                    request_url = `${request_url}?${queryString}`
                }
            }
        }
        const sign_resp = await retry_operation(async () => await axios.post(`https://shopee.nexpos.io/sign`, { url: request_url, body: body || '' }), 3, 'Sign request failed')
        if (sign_resp.data.signedValue) {
            config.headers['x-sap-ri'] = sign_resp.data.signedValue['x-sap-ri']
            config.headers['x-sap-sec'] = sign_resp.data.signedValue['x-sap-sec']
        }
    } catch (error) {
        console.log(error)
    }

    return config
}

async function get_token(site_id, refresh_token) {
    if (!refresh_token) {
        return ""
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://gsso.deliverynow.vn/api/auth/exchange_token',
        headers: base_headers({ site_id, access_token: refresh_token })
    };

    const resp = await axios(config);
    return resp.data.reply.access_token
}


async function get_order_list_v2({ access_token, site_id }) {
    const result = {
        success: true,
        data: {}
    }


    if (!access_token || skip_call_api()) {
        return result
    }
    try {
        const current_date = moment().endOf('days').unix();
        const from_date = moment().add(-3, 'days').unix();
        const filter_data = {
            PRE_ORDER: {
                order_filter_type: 10,
                next_item_id: "",
                request_count: 100,
                sort_type: 5
            },
            PENDING: {
                order_filter_type: 30,
                next_item_id: "",
                request_count: 100,
                sort_type: 6
            },
            DOING: {
                order_filter_type: 31,
                next_item_id: "",
                request_count: 100,
                sort_type: 5
            },
            FINISH_OR_CANCEL: {
                order_filter_type: 40,
                next_item_id: "",
                request_count: 100,
                from_time: from_date,
                to_time: current_date,
                sort_type: 12
            },
        }

        for (const [status, filter] of Object.entries(filter_data)) {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://gmerchant.deliverynow.vn/api/v5/order/get_list',
                headers: base_headers({ site_id, access_token }),
                data: filter
            };

            config = await sign_request_async(config)

            const resp = await axios(config);
            if (status === "FINISH_OR_CANCEL") {
                result.data['FINISH'] = resp.data.data.orders.filter(v => v.order_status !== 8)
                result.data['CANCEL'] = resp.data.data.orders.filter(v => v.order_status === 8)
            } else {
                result.data[status] = resp.data.data.orders
            }
        }
        return result
    } catch (err) {
        console.log(err.message)
        result.success = false
        return result
    }
}


async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: [],
        }
    }

    if (!access_token) {
        return result
    }

    let next_item_id = ""

    try {
        while (true) {
            const data = {
                order_filter_type: 40,
                next_item_id: next_item_id,
                request_count: 100,
                from_time: from.unix(),
                to_time: to.unix(),
                sort_type: 12
            }

            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://gmerchant.deliverynow.vn/api/v5/order/get_list',
                headers: base_headers({ site_id, access_token }),
                data: data
            };
            config = await sign_request_async(config)
            const resp = await axios(config);

            result.data.FINISH.push(...resp.data.data.orders.filter(v => v.order_status !== 8))
            result.data.CANCEL.push(...resp.data.data.orders.filter(v => v.order_status === 8))
            next_item_id = resp.data.data.next_item_id
            if (!resp.data.data.has_more)
                break
        }

    } catch (error) {
        result.success = false
        console.log(error.message)
    }
    return result
}

async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://gmerchant.deliverynow.vn/api/v5/order/get_detail?order_code=' + order_id,
        headers: base_headers({ site_id, access_token }),
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data.data.order
    } catch (error) {
        console.log(error.message)
        return null
    }
}

async function confirm_order({ site_id, access_token }, order_id) {
    if (!access_token) {
        return {}
    }
    const order = await get_order_detail({ site_id, access_token }, order_id)
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://gmerchant.deliverynow.vn/api/v5/order/confirm',
        headers: base_headers({ site_id, access_token }),
        data: {
            "order_id": String(order.id),
            "order_code": order.code,
            "serial": order.serial
        }
    };
    config = await sign_request_async(config)
    const resp = await axios(config);
    return resp.data


}

// cancel_reason: out_stock: Hết món, merchant_busy: Quán bận
async function cancel_order({ site_id, access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    const result = {
        success: true,
        message: "",
    }
    if (!access_token) {
        return result
    }
    try {
        const order = await get_order_detail({ site_id, access_token }, order_id)
        const { id, code, serial } = order

        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://gmerchant.deliverynow.vn/api/v5/order/cancel',
            headers: base_headers({ site_id, access_token }),
            data: {
                "order_id": String(id),
                "order_code": code,
                "serial": serial,
                "reason_id": cancel_type === 'out_stock' ? 79 : 81, // 79: Hết món, 81: Quán bận
                "cancel_note": cancel_reason,
            }
        };
        config = await sign_request_async(config)
        const resp = await axios(config);
        result.success = resp.data.msg === "success"
        result.message = resp.data.msg
    } catch (error) {
        result.success = false
        result.message = error.message
    }
    return result


}

async function update_store_status({ site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/${status === 'close' ? 'set-busy' : 'set-open'}`,
        headers: base_headers({ site_id, access_token }),
        data: status === 'close' ? {
            "from_time": moment().format('YYYY-MM-DD HH:mm:ss'),
            "to_time": duration ? moment().add(duration, 'minute').format('YYYY-MM-DD HH:mm:ss') : moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        } : {}
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        console.log(resp.data)
        return resp.data
    } catch (error) {
        console.log(error)
        return {}
    }

}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/refact/get`,
        headers: base_headers({ site_id, access_token })
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return !resp.data?.data?.opening_status?.close_status?.close_type && !resp.data?.data?.opening_status?.pause_time?.pause_end_time
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ site_id, access_token }) {
    if (!access_token) {
        return []
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/get`,
        headers: base_headers({ site_id, access_token }),
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data.data.regular_hours
    } catch (error) {
        console.error(error)
    }
    return []

}

async function get_special_hour({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/get`,
        headers: base_headers({ site_id, access_token }),
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data.data.special_hours ?? []
    } catch (error) {
        console.error(error)
    }
    return []

}

async function update_opening_hour({ site_id, access_token }, working_hours) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: { regular_hours: working_hours }
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_menu({ site_id, access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }
    try {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes`,
            headers: base_headers({ site_id, access_token }),
        };
        config = await sign_request_async(config)

        const resp = await axios(config);

        config.url = "https://gmerchant.deliverynow.vn/api/v5/seller/store/option-groups"
        config = await sign_request_async(config)
        const resp_2 = await axios(config);

        return {
            categories: resp.data.data.catalogs,
            option_categories: resp_2.data.data.option_groups,
        }
    } catch (error) {
        console.log(error.message)
        console.log(JSON.stringify(error.response?.data ?? {}, null, 2))
        return {
            categories: [],
            option_categories: [],
        }
    }

}

async function get_store({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/get_basic_info?request_action_type=3`,
        headers: base_headers({ site_id, access_token }),
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_store_list({ access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://app.partner.shopee.vn/mss/app-api/PartnerRNServer/GetStoreList',
        headers: base_headers_2({ access_token }),
        data: { "page_size": 500 }
    };
    try {
        const resp = await axios(config);
        return resp.data.data.store_list
    } catch (error) {
        console.log(error)
        return null
    }

}


// status: open or close
async function set_special_open_status({ site_id, access_token }, { from, to, status }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "special_hours": [
                {
                    "date_start": moment(from).local().startOf('day').unix() * 1000, //1704042000000,
                    "date_end": moment(to).local().endOf('day').unix() * 1000 + 999, //1704128399999,
                    "date_type": 1,
                    "date_desc": "",
                    "intervals": []
                }
            ]
        }
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}



async function delete_special_open_status({ site_id, access_token }, { from }) {
    if (!access_token) {
        return {}
    }


    try {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/get`,
            headers: base_headers({ site_id, access_token }),
        };
        config = await sign_request_async(config)
        const resp = await axios(config);

        const special_hours = resp.data.data.special_hours || [];
        let config2 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/set`,
            headers: base_headers({ site_id, access_token }),
            data: {
                "special_hours": special_hours.filter(v => v.date_start !== moment(from).local().startOf('day').unix() * 1000)
            }
        };
        config2 = await sign_request_async(config2)
        const resp2 = await axios(config2);
        return resp2.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function delete_all_special_open_status({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/special-hours/refact/set`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "special_hours": []
        }
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function get_finance_data({ site_id, access_token }, { from, to }) {
    if (!access_token) {
        return {}
    }

    const store = await get_store({ site_id, access_token })

    let result = []
    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/report/request_order_report`,
            headers: web_base_headers({ site_id, access_token }),
            data: {
                "from_date": moment(from).format('YYYY-MM-DD'),
                "to_date": moment(to).format('YYYY-MM-DD'),
                "restaurant_ids": [store.restaurant_id],
                "time_filter_type": 2
            }
        };
        config = await sign_request_async(config)
        const resp = await axios(config);

        let counter = 0;
        while (counter < 10) {
            let config2 = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/report/get_list_report`,
                headers: web_base_headers({ site_id, access_token }),
            };
            config2 = await sign_request_async(config2)
            const resp2 = await axios(config2);
            const reports = resp2.data.data.reports;
            const report = reports[0];
            if (report.status == 4) {
                let config3 = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gmerchant.deliverynow.vn/api/v5/seller/store/report/get_download_urls`,
                    headers: web_base_headers({ site_id, access_token }),
                    data: { "report_ids": [Number(report.report_id)] }
                };
                config3 = await sign_request_async(config3)
                const resp3 = await axios(config3);
                const download_url = resp3.data.data.reports[0].download_url;
                console.log(download_url)
                const excel_file = await axios.get(download_url, { responseType: 'arraybuffer' });
                const workbook = xlsx.read(excel_file.data, { type: 'buffer' });
                const sheetName = workbook.SheetNames[0];
                const sheet = workbook.Sheets[sheetName];
                result = xlsx.utils.sheet_to_json(sheet);
                break
            }
            await new Promise(r => setTimeout(r, 5000))
            counter++;
        }

        const order_map = _.groupBy(result, 'Mã đơn hàng');
        return order_map
    }
    catch (err) {
        console.log(err)
    }
}

async function get_order_feedbacks({ site_id, access_token }, limit) {
    if (!access_token) {
        return []
    }
    let result = []
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/feedback/get_list_feedbacks`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "limit": 50,
            "time_range": 4
        }
    };
    try {
        while (true) {
            config = await sign_request_async(config)
            const resp = await axios(config);
            console.log(site_id, access_token, resp.data)
            result.push(...(resp.data.data.feedbacks ?? []))
            if (resp.data.data.next_update_time === 0 || result.length > limit)
                break
            config.data.next_update_time = resp.data.data.next_update_time
        }
    } catch (error) {
        console.log(error)
    }
    return result
}

async function get_site_feedback_summary({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/feedback/get_feedback_summary`,
        headers: base_headers({ site_id, access_token }),
    };
    config = await sign_request_async(config)
    try {
        const resp = await axios(config);
        return resp.data.data.feedback_summaries
    } catch (error) {
        console.log(error)
    }
    return null
}


async function get_promotions({ site_id, access_token }) {
    try {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/promotion/query_infos`,
            headers: base_headers({ site_id, access_token }),
            params: {
                from_date: moment().add(-60, 'days').format('YYYY-MM-DD'),
                to_date: moment().format('YYYY-MM-DD'),
                discount_types: 5,
                status: 2
            }
        };
        config = await sign_request_async(config)

        const resp = await axios(config);

        return {
            success: resp.data.code === 0,
            data: resp.data.data?.promotions ?? []
        }
    } catch (error) {
        console.error(error)
        return {
            success: false,
            data: []
        }
    }
}
async function delete_all_promotions({ site_id, access_token }) {
    const promotions = await get_promotions({ site_id, access_token })
    const user_promotions = promotions.data.filter(v => v.creator === 1)

    for (const promotion of user_promotions) {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gmerchant.deliverynow.vn/api/v5/promotion/update_status`,
            headers: base_headers({ site_id, access_token }),
            data: {
                "promotion_id": promotion.id,
                "promotion_type": promotion.promotion_type,
                "status": 4
            }
        };
        config = await sign_request_async(config)
        try {
            const resp = await axios(config);
            console.log(resp.data)
        } catch (error) {
            console.error(error)
        }

    }

    return {
        success: true
    }
}

// items: [{name, discount_price}]
async function set_menu_item_promotions({ site_id, access_token }, { start_time, end_time, items }) {
    const menu = await get_menu({ site_id, access_token })
    const menu_dishes = menu.categories.map(v => v.dishes).flat()
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gmerchant.deliverynow.vn/api/v5/promotion/price_slash/request`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "start_time": parseInt(moment(start_time).unix() / 100) * 100,
            "end_time": parseInt(moment(end_time).unix() / 100) * 100,
            "promotion_name": "Price slash discount",
            "discount_type": 1,
            "discount_dishes": _.chain(items)
                .map(item => {
                    const menu_dish = _.find(menu_dishes, v => text_slugify(v.name) === text_slugify(item.name));
                    return menu_dish ? { sell_price: item.sell_price, dish_id: menu_dish.id } : null;
                })
                .compact()
                .groupBy('sell_price')
                .map((group, sell_price) => ({
                    discount_price: Number(sell_price),
                    dish_ids: _.uniq(_.map(group, 'dish_id'))
                }))
                .value()
        }
    };
    config = await sign_request_async(config)

    const resp = await axios(config);
    console.log(resp.data)
    return {
        success: resp.data.code === 0,
        data: resp.data
    }
}
// set_menu_item_promotions(
//     { site_id: '10292756', access_token: 'B:xgL2Gil7POTFJFWoP40a2Mn0Br3tzi4yMGk0HH8570bqt6xMOjn2MiJKchHRtJu3JE2IV6A5ZwAtlXfxJsU9hQWy3GLS739OQWfZhwI/sHk=' }, {})

module.exports = {
    base_headers,
    sign_request_async,
    get_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    cancel_order,
    update_store_status,
    get_open_status,
    get_opening_hour,
    get_special_hour,
    update_opening_hour,
    get_menu,
    get_store,
    get_store_list,
    set_special_open_status,
    delete_special_open_status,
    delete_all_special_open_status,
    get_finance_data,
    get_order_feedbacks,
    delete_all_promotions,
    set_menu_item_promotions,
    get_site_feedback_summary,
}
