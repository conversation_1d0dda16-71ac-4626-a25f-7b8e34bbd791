const _ = require('lodash')
const moment = require('moment')
const axios = require('../axios')

const skip_call_api = () => false;

const base_headers = ({ site_id }) => {
  const token = JSON.parse(site_id)
  return {
    'content-type': 'application/json',
    'host': 'api.tiktokglobalshopv.com',
    'passport-sdk-version': '30990',
    'sdk-version': '2',
    'user-agent': 'com.tiktokshop.seller/30900 (Linux; U; Android 9; en_US; unknown; Build/PI;tt-ok/*********)',
    'x-tt-token': token?.token || '03aeb8fa70d414924910d205db6c3df2e002b8fce9bbdeb07d7cc54bf439d3d90bbad1adbf99043cf2a2775431946b61d1a1abcf7d8d8472996185b14e8eee57687ddecdc248ae6d547388b9294907c223f94-1.0.0',
  }
}

const tiktok = {}

tiktok.get_order_list_v2 = async function ({ site_id }) {
  const result = {
    success: true,
    data: {}
  }

  if (!site_id || skip_call_api()) {
    return result
  }

  try {
    const filter_data = {
      DRAFT: {
        search_tab: ["1010"],
        sort_info: "6",
      },
      DOING: {
        search_tab: ["1100", "1200"],
        sort_info: "1",
      },
      PICK: {
        search_tab: ["1300", "1400"],
        sort_info: "6",
      },
      FINISH: {
        search_tab: ["103"],
        sort_info: "6",
      },
      CANCEL: {
        search_tab: ["104"],
        sort_info: "6",
      }
    }
    for (const [status, filter] of Object.entries(filter_data)) {
      result.data[status] = []
      for (const search_tab of filter.search_tab) {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: 'https://api.tiktokglobalshopv.com/api/fulfillment/order/orders_list?aid=7743',
          headers: base_headers({ site_id }),
          data: {
            "pagination_type": 1,
            "offset": 0,
            "count": 100,
            "search_cursor": "",
            "extra_data_list": [
              "free_sample_tag_v1",
              "split_combine_tag_v1",
              "replacement_order_tag_v1",
              "risk_order_tag_v1"
            ],
            "search_condition": {
              "condition_list": {
                "search_tab": {
                  "value": [
                    search_tab
                  ]
                }
              }
            },
            "sort_info": filter.sort_info,
            "overview_key_list": [
              "1010",
              "1100",
              "1200",
              "1300",
              "1400",
              "2110",
              "2200",
              "2310",
              "2200",
              "2400"
            ]
          }
        };

        const resp = await axios(config);
        const orders = resp?.data?.data?.main_orders || []
        result.data[status] = orders.map(v => _.pick(v, ['main_order_id', 'main_order_status', 'main_order_display_status', 'main_order_display_sub_status', 'fulfillment_status']))
      }

    }
  } catch (err) {
    console.log(err.message)
    result.success = false
  }

  return result
}
// tiktok.get_order_list_v2({
//   site_id: JSON.stringify({
//     "token": "03aeb8fa70d414924910d205db6c3df2e002b8fce9bbdeb07d7cc54bf439d3d90bbad1adbf99043cf2a2775431946b61d1a1abcf7d8d8472996185b14e8eee57687ddecdc248ae6d547388b9294907c223f94-1.0.0",
//   })
// })

tiktok.get_order_detail = async function ({ site_id }, order_id) {
  if (!site_id) {
    return null
  }

  let result = null


  const config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `https://api.tiktokglobalshopv.com/api/app/trade/orders/get?main_order_id=${order_id}&aid=7743`,
    headers: base_headers({ site_id }),
  }

  const resp = await axios(config)
  result = resp.data.data
  console.log(JSON.stringify(result, null, 2))
  result = resp.data.code === 0 ? resp.data.data.main_order : {}
  result.contact_info = {}
  try {
    let config1 = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://api.tiktokglobalshopv.com/api/fulfillment/orders/buyer_contact_info/get?aid=7743',
      headers: base_headers({ site_id }),
      data: { "contact_info_type": 0, "main_order_id": order_id }
    };

    const resp1 = await axios(config1);
    result.contact_info.name = resp1.data.code === 0 && resp1.data.data.plain_text_name ? resp1.data.data.plain_text_name : ""
    config1.data.contact_info_type = 1
    const resp2 = await axios(config1);
    if (resp2.data.code === 0 && resp2.data.data.plain_text_address) {
      result.contact_info.address = [
        ...resp2.data.data.plain_text_address.items.map(item => item.value).reverse(),
        ...resp2.data.data.plain_text_address.districts.map(d => d.name).reverse()
      ].filter(Boolean).join(', ')
    }
    config1.data.contact_info_type = 2
    const resp3 = await axios(config1);
    result.contact_info.phone = resp3.data.code === 0 && resp3.data.data.plain_text_phone_number ? resp3.data.data.plain_text_phone_number : ""
  } catch (e) {
    console.error(e)
    console.log(resp.config.curlCommand)
  }

  try {
    const finance_list = await tiktok.get_finance_list({ site_id }, {})
    result.transaction = finance_list.payments.find(t => t.trade_order_id === order_id)
  } catch (e) {
    console.error(e)
    console.log(resp.config.curlCommand)
  }
  delete result.contact_buyer_link   // prevent change md5
  delete result.ship_on_time_left
  delete result.avatar

  return { unofficial_data: result }
}
// tiktok.get_order_detail({
//   site_id: JSON.stringify({
//     "token": "03aeb8fa70d414924910d205db6c3df2e002b8fce9bbdeb07d7cc54bf439d3d90bbad1adbf99043cf2a2775431946b61d1a1abcf7d8d8472996185b14e8eee57687ddecdc248ae6d547388b9294907c223f94-1.0.0",
//   }),
// }, '578981455606352880')


tiktok.get_order_list_by_duration = async function ({ site_id }, { from, to }) {
  let result = {
    success: true,
    data: {
      FINISH: [],
      CANCEL: [],
    }
  }

  if (!site_id) {
    return result
  }

  result = await tiktok.get_order_list_v2({ site_id })
  return result
}

tiktok.get_finance_list = async function ({ site_id }, { from, to }) {
  const result = []
  if (!site_id) {
    return result
  }

  let hasMore = true;
  let cursor = '';
  while (hasMore) {
    try {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.tiktokglobalshopv.com/api/app/pay/statement/order/list',
        params: {
          aid: '7743',
          pagination_type: '2',
          cursor: cursor,
          size: '50',
          page_type: '15',
          no_need_sku_record: 'false',
          need_total_amount: 'true',
          place_time_lower: moment(from).unix() * 1000,
          place_time_upper: moment(to).unix() * 1000,
        },
        headers: base_headers({ site_id }),
      };
      const resp = await axios(config);
      for (const record of resp.data.data.order_records) {
        result.push(record);
      }
      hasMore = resp.data.data.search_next_has_more;
      cursor = resp.data.data.search_next_cursor;
    } catch (error) {
      console.log(error);
      break;
    }
  }
  return result
}

tiktok.get_finance_detail = async function ({ site_id }, { transaction_id }) {
  let result = null

  if (!site_id) {
    return null
  }

  try {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `https://api.tiktokglobalshopv.com/api/app/pay/statement/transaction/detail?statement_detail_id=${transaction_id}&page_type=8`,
      headers: base_headers({ site_id }),

    };
    const resp = await axios(config);
    result = resp.data.data.order_record
  } catch (error) {
    console.log(error)
  }
  return result
}



tiktok.print_order_bill = async function ({ site_id }, order_id) {
  if (!site_id) {
    return null
  }
  const order = await tiktok.get_order_detail({ site_id }, order_id)
  try {
    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://api.tiktokglobalshopv.com/api/app/fulfillment/shipping_doc/generate?aid=7743',
      headers: base_headers({ site_id }),
      data: {
        "request_time": moment().unix() * 1000,
        "fulfill_unit_id_list": order.unofficial_data?.fulfill_unit_ids || [],
        "template_type": 0,
        "content_type_list": [
          2,
          1
        ]
      }
    };
    const resp = await axios(config);
    const file_url = resp.data.data.doc_url
    const file_buffer = await axios({ url: file_url, responseType: 'arraybuffer' })
    return file_buffer.data
  } catch (error) {
    console.log(error)
  }
  return null
}

tiktok.confirm_order = async function ({ site_id }, order_id) {
  if (!site_id) {
    return null
  }
  const order = await tiktok.get_order_detail({ site_id }, order_id)
  let config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://api.tiktokglobalshopv.com/api/app/trade/orders/rts?aid=7743',
    headers: base_headers({ site_id }),
    data: {
      "rts_fulfill_units": order.unofficial_data?.fulfill_unit_ids.map(fulfill_unit_id => ({
        "pick_up_end": "0",
        "fulfill_unit_id": fulfill_unit_id,
        "time_zone": "Asia\/Jakarta",
        "pick_up_start": "0",
        "pick_up_type": 1
      })),
      "rts_type": 0
    }
  };
  const resp = await axios(config);
  if (resp.data.message === 'success') {
    return {
      success: true,
    }
  }
  return null

}


module.exports = tiktok