const qs = require('qs')
const _ = require('lodash')
const axios = require('../axios');
const moment = require('moment-timezone')
const crypto = require('crypto')
const lazada_unofficial = require('./lazada_unofficial')
const { map_order } = require('./mapping')

moment.tz.setDefault('Asia/Bangkok')

// AUTH: https://auth.lazada.com/oauth/authorize?response_type=code&force_auth=true&client_id=119458
// API Docs: https://open.lazada.com/apps/doc/api

const API_KEY = '119458'
const APP_SECRET = '9J4SAeCsxlXW0HUMLFZWEWV6KEoi7t21'
const BASE_URL = 'https://api.lazada.vn/rest'
const SIGN_METHOD = 'sha256'


function sign_params(api_name, params, body) {
  // Sort all parameters by key
  const sorted_params = Object.keys(params)
    .sort()
    .reduce((acc, key) => {
      if (params[key] !== null && params[key] !== undefined) {
        acc[key] = params[key]
      }
      return acc
    }, {})

  // Concatenate sorted parameters and their values
  let query = api_name
  for (const key in sorted_params) {
    query += key + sorted_params[key]
  }

  // Add the request body if it exists
  if (body) {
    query += body
  }

  // Calculate the HMAC_SHA256 hash
  const hmac = crypto.createHmac(SIGN_METHOD, APP_SECRET)
  hmac.update(query, 'utf8')
  const signature = hmac.digest('hex').toUpperCase()

  return signature
}

const sign_request_config = config => {
  config.params.app_key = API_KEY
  config.params.sign_method = SIGN_METHOD
  config.params.timestamp = Date.now().toString()
  const api_name = config.url.split('/rest')[1]
  const sign = sign_params(api_name, config.params, config.data || null)
  config.params.sign = sign
  config.timeout = 30000

  return config
}

const lazada = {}

lazada.get_login_url = (redirect_uri) => {
  return `https://auth.lazada.com/oauth/authorize?response_type=code&force_auth=true&client_id=${API_KEY}`
}

lazada.get_token = async function (code) {

  const config = {
    params: { code },
    method: 'get',
    url: 'https://auth.lazada.com/rest/auth/token/create',
  }
  sign_request_config(config)
  const resp = await axios(config)
  return resp.data
}

lazada.get_token_by_refresh_token = async (refresh_token) => {
  const params = { refresh_token };

  const config = {
    params,
    method: 'post',
    url: 'https://api.lazada.vn/rest/auth/token/refresh',
  };
  sign_request_config(config)
  try {
    const response = await axios(config);
    console.log(response.data);
    return {
      username: response.data.account,
      access_token: response.data.access_token,
      refresh_token: response.data.refresh_token,
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null
  }
}
// lazada.get_token_by_refresh_token("50001101c38cf1gXdCx8lVWdFdxi0ez1FcXFqsftQ1tqo0ZiRG17499edcVRvXzO")

const revertStatusMapping = {
  unpaid: 'WAITING_PAYMENT',
  pending: 'PENDING',
  canceled: 'CANCEL',
  ready_to_ship: 'DOING',
  delivered: 'FINISH',
  returned: 'FINISH',
  shipped: 'FINISH',
  failed: 'FINISH',
  topack: 'PENDING',
  toship: 'DOING',
  shipping: 'DOING',
  lost: 'FINISH',
}

lazada.get_order_list_v2 = async function ({ access_token }) {
  const result = {
    success: true,
    data: {
      PENDING: [],
      DOING: [],
      FINISH: [],
      CANCEL: [],
      PICK: [],
      RETURNED: [],
    }
  }


  if (!access_token) {
    return result
  }
  try {
    const from_date = moment().add(-60, 'days');


    const config = {
      method: 'get',
      url: 'https://api.lazada.vn/rest/orders/get',
      params: {
        access_token,
        created_after: from_date.toISOString(),
        limit: 50
      },
    };
    sign_request_config(config)

    const resp = await axios(config);
    const orders = resp.data.data.orders

    const filter_data = {
      DRAFT: ['unpaid'],
      PENDING: ['pending'],
      DOING: ['topack', 'packed', 'ready_to_ship', 'toship'],
      PICK: ['shipping', 'shipped'],
      FINISH: ['confirmed', 'delivered'],
      CANCEL: ['canceled', 'lost'],
    }
    for (const [status, merchant_statuses] of Object.entries(filter_data)) {
      result.data[status] = orders.filter(order => merchant_statuses.some(v => order.statuses.includes(v)))
    }
    return result
  } catch (err) {
    console.log(err.message)
    result.success = false
    return result
  }
}


lazada.get_order_list_by_duration = async function ({ access_token }, { from, to }) {
  const result = {
    success: true,
    data: {
      FINISH: [],
      CANCEL: [],
    }
  }

  if (!access_token) {
    return result
  }

  let limit = 50
  let offset = 0

  while (true) {
    try {
      const config = {
        method: 'get',
        url: 'https://api.lazada.vn/rest/orders/get',
        params: {
          access_token,
          created_after: moment(from).toISOString(),
          limit,
          offset,
        },
      };
      offset += limit
      sign_request_config(config)
      const resp = await axios(config);
      const orders = resp.data.data.orders
      if (orders.length === 0) {
        break
      }

      const filter_data = {
        FINISH: ['confirmed', 'delivered'],
        CANCEL: ['canceled', 'lost'],
      }
      for (const [status, merchant_statuses] of Object.entries(filter_data)) {
        result.data[status] = orders.filter(order => merchant_statuses.some(v => order.statuses.includes(v)))
      }
    } catch (error) {
      console.log(error)
      break
    }
  }


  return result
}

lazada.get_order_detail = async function ({ access_token, site_id }, order_id) {
  if (!access_token) {
    return null
  }

  let result = null
  try {
    const config = {
      params: { access_token, order_id },
      method: 'get',
      url: 'https://api.lazada.vn/rest/order/get',
    }
    sign_request_config(config)

    const resp = await axios(config)

    result = resp.data.data
  } catch (e) {
    console.error(e)
  }
  try {
    const config = {
      params: { access_token, order_id },
      method: 'get',
      url: 'https://api.lazada.vn/rest/order/items/get',
    }
    sign_request_config(config)

    const resp = await axios(config)
    result.order_items = resp.data.data
  } catch (e) {
    console.error(e)
  }
  try {
    const config = {
      params: {
        access_token,
        start_time: moment().add(-90, 'days').format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD'),
        trade_order_id: String(order_id),
        // trade_order_line_id: String(result.order_items[0].order_item_id)
      },
      method: 'get',
      url: 'https://api.lazada.vn/rest/finance/transaction/details/get',
    }
    sign_request_config(config)

    const resp = await axios(config)
    result.item_transactions = resp.data.data
  } catch (e) {
    console.error(e)
  }

  if (site_id) {
    const order2 = await lazada_unofficial.get_order_detail({ site_id }, order_id)
    if (order2) {
      result.unofficial_data = order2
    }
  }
  return result
}

// lazada.get_order_detail({
//   access_token: '50000101128ya2dZ0H1hiTCsOcAdfqCtxtDmfNlV1087147dFwwEe1mSRFYfBT2u',
//   site_id: JSON.stringify({
//     sid: '3b441010ba5bbc952879c74c27c9357c',
//     uid: '************',
//     utdid: 'ZnMHURRmYlMDABNg7iXvTSEK',
//   })
// }, '476469016920083')


lazada.get_store = async function ({ access_token }) {
  if (!access_token) {
    return null
  }

  const api_name = '/seller/get'
  const config = {
    params: bundle_params(api_name, { access_token, sign_method: SIGN_METHOD, app_key: API_KEY }),
    method: 'get',
    url: BASE_URL + api_name,
  }

  try {
    const resp = await axios(config)
    return resp.data
  } catch (e) {
    console.error(e)
    return null
  }
}

lazada.confirm_order = async function ({ access_token }, order_id) {
  if (!access_token) {
    return null
  }

  const order = await lazada.get_order_detail({ access_token }, order_id).catch(console.error)
  try {
    const config1 = {
      params: {
        access_token,
        packReq: JSON.stringify({
          pack_order_list: [{
            order_item_list: order.order_items.map(v => ({ order_item_id: v.order_item_id })),
            order_id: order.order_id,
          }],
          delivery_type: 'dropship',
          shipping_allocate_type: 'TFS',
        }),
      },
      method: 'post',
      url: 'https://api.lazada.vn/rest/order/fulfill/pack',

    };

    sign_request_config(config1);
    const resp1 = await axios(config1).catch(console.error);

    const config2 = {
      params: {
        access_token,
        readyToShipReq: JSON.stringify({
          packages: _.uniq(order.order_items.map(v => ({ package_id: v.package_id })).filter(v => v.package_id))
        })
      },
      method: 'post',
      url: 'https://api.lazada.vn/rest/order/package/rts',
    }
    sign_request_config(config2)
    const resp2 = await axios(config2).catch(console.error);

    return {
      success: true
    }
  } catch (e) {
    console.error(e)
  }
}
// lazada.confirm_order({ access_token: '50000101128ya2dZ0H1hiTCsOcAdfqCtxtDmfNlV1087147dFwwEe1mSRFYfBT2u' }, '476469016920083')



lazada.print_order_bill = async function ({ access_token }, order_id) {
  try {
    const order = await lazada.get_order_detail({ access_token }, order_id)
    const config = {
      params: {
        access_token,
        getDocumentReq: JSON.stringify({
          doc_type: "PDF",
          packages: _.uniq(order.order_items.map(v => ({ package_id: v.package_id })).filter(v => v.package_id))
        })
      },
      method: 'get',
      url: 'https://api.lazada.vn/rest/order/package/document/get',
    }
    sign_request_config(config)

    const resp = await axios(config)
    const file = resp.data.result.data.pdf_url
    const file_buffer = await axios.get(file, { responseType: 'arraybuffer' })
    return file_buffer.data
  } catch (e) {
    console.error(e)
  }
}
// lazada.print_order_bill({ access_token: '50000101128ya2dZ0H1hiTCsOcAdfqCtxtDmfNlV1087147dFwwEe1mSRFYfBT2u' }, '476469016920083')


module.exports = lazada