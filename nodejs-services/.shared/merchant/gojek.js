const axios = require('../axios');
const moment = require('moment-timezone');
const { map_order } = require('./mapping');
moment.tz.setDefault('Asia/Bangkok');

const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;
const random_hex_string = (length) => [...Array(length)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
function generate_hex_string(text) {
    const hash = text.split('').reduce((acc, char) => {
        acc = ((acc << 5) - acc) + char.charCodeAt(0);
        return acc & acc;
    }, 0);
    return Math.abs(hash).toString(16).slice(0, 16);
}

const base_headers = ({ username, site_id, access_token }) => {
    const headers = {
        'x-user-type': 'merchant',
        'x-client-id': 'go-biz-mobile',
        'x-client-secret': 'sPC0qVk7gi76JUoGVfOfcgd7FfuaBv',
        'accept-language': 'vi',
        'x-user-locale': 'vi-VN',
        'x-platform': 'Android',
        'x-appversion': '4.13.1',
        'x-appid': 'com.gojek.resto',
        'x-deviceos': 'Android 28',
        'x-phonemake': 'samsung',
        'x-phonemodel': 'SM-N950F',
        'accept': 'application/json',
        'gojek-country-code': 'VN',
        'gojek-timezone': 'Asia/Ho_Chi_Minh',
        'content-type': 'application/json; charset=UTF-8',
        'user-agent': 'okhttp/3.12.10',
        "x-pushtokentype": "FCM",
        "x-uniqueid": generate_hex_string(username || 'love_gojek'),

    }
    if (site_id)
        headers['restaurantuuid'] = site_id
    if (access_token) {
        headers['authorization'] = 'Bearer ' + access_token
        headers['authentication-type'] = 'go-id'
    }
    return headers
}

const web_base_headers = ({ access_token }) => {
    const headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        'authentication-type': 'go-id',
        'authorization': 'Bearer ' + access_token,
        'content-type': 'application/json',
        'origin': 'https://app.gobiz.com',
        'referer': 'https://app.gobiz.com/',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
    }
    return headers
}


async function get_token(username, password, site_id) {
    if (!username || !password) {
        return {}
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.gobiz.co.id/gobiz/goid/token',
        headers: base_headers({ username, site_id }),
        data: {
            "grant_type": "password",
            "client_id": "YEZympJ5WqYRh7Hs",
            "client_secret": "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
            "data": {
                "email": username,
                "password": password
            }
        }
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error.message)
        return null
    }
}
// get_token("<EMAIL>", "Nexdor@123", "bd08ca25-e68b-46e2-b3ef-7a65f913004e")

async function get_token_by_refresh_token(username, site_id, refresh_token) {
    if (!refresh_token) {
        return null
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://goid.gojekapi.com/goid/token',
        headers: base_headers({ username, site_id }),
        data: {
            "grant_type": "refresh_token",
            "client_id": "go-biz-mobile",
            "client_secret": "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
            "data": {
                "refresh_token": refresh_token
            }
        }
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error.message)
        return null
    }
}

async function request_otp(phone) {
    if (!phone) {
        return ""
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://goid.gojekapi.com/goid/login/request',
        headers: base_headers({ username: phone }),
        data: {
            "client_id": "go-biz-mobile",
            "client_secret": "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
            "country_code": "+84",
            "phone_number": phone.substring(1),
        }
    };
    console.log(JSON.stringify(base_headers({})))

    const resp = await axios(config);

    return resp.data.data
}

async function get_token_by_otp(username, otp_token, otp) {
    if (!otp) {
        return {}
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://goid.gojekapi.com/goid/token',
        headers: base_headers({ username }),
        data: {
            "client_id": "go-biz-mobile",
            "client_secret": "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
            "data": {
                "otp": otp,
                "otp_token": otp_token
            },
            "grant_type": "otp"
        }
    };

    const resp = await axios(config);
    console.log(resp.data)
    const resp_2 = await axios({
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.gojekapi.com/gofood/merchant/v1/config',
        headers: base_headers({ username, access_token: resp.data.access_token }),
    })

    return { ...resp.data, site_id: resp_2.data.uuid }
}

async function get_token_by_password(username, password) {
    if (!username || !password) {
        return {}
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.gobiz.co.id/gobiz/goid/token',
        headers: base_headers({ username }),
        data: {
            "client_id": "YEZympJ5WqYRh7Hs",
            "grant_type": "password",
            "data": {
                "email": username,
                "password": password
            }
        }
    };

    const resp = await axios(config);
    console.log(resp.data)
    const resp_2 = await axios({
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.gojekapi.com/gofood/merchant/v1/config',
        headers: base_headers({ username, access_token: resp.data.access_token }),
    })

    return { ...resp.data, site_id: resp_2.data.uuid }
}




async function get_order_list_v2({ username, site_id, access_token }) {
    const result = {
        success: true,
        data: {}
    }

    if (!access_token || skip_call_api()) {
        return result
    }
    const filter_data = {
        "PENDING": "ONGOING",
        // "DOING": "ONGOING",
        "FINISH": "COMPLETED",
        "CANCEL": "UNFULFILLED",
    }

    for (const [status, filter] of Object.entries(filter_data)) {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/mocha/v3/orders?restaurant_id=${site_id}&status=${filter}&limit=50&page=1`,
            headers: base_headers({ username, site_id, access_token }),
        };
        try {
            const resp = await axios(config);

            result.data[status] = resp.data.orders || []
            if (status == 'PENDING') {
                result.data['PENDING'] = result.data[status].filter(v => !v.cards)
                result.data['DOING'] = result.data[status].filter(v => v.cards)
            }
        } catch (error) {
            result.success = false
            break
        }
    }

    return result
}
// get_order_list_v2({ username: '', site_id: '57d3ec2d-d4fb-477a-9b84-d2d76e1f6b67', access_token: 'eyJhbGciOiJkaXIiLCJjdHkiOiJKV1QiLCJlbmMiOiJBMTI4R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0..6sXFKroLfikvkT8R.5dZjQqD1eFyCz5LXF5J1QaetQ46CyFlZHV3fHDnBa5AHhSCTnwxQjqBWQr9dq-vmrNxZj28dgeYJmauwfW3HFCmgzwdpg20a3ZNjxEK2y57Rnuq08BnuPPgZXeR8utn3-4B4kUGg2iZzfXwiBJDwSPsdD4YdKxszVnf5g9bc0tAw9P95iA4rEKHlwEFKTvkglvnIs7qbpL1H1aE552VO01T6n_i_a_DBybBetxYVsTMF9raGVjL-3aOY0KTp6w6IExaSqCuUEvGz0ptiYipPNTZH8clWT6F387j0T3ZcficC-yrhuxe1NPZ6gHetbql80g5paOGj8suuR35coARkDys30eHS2sjJ9C_N6RrY6kUlfsRI0MnVwMuJIvIWwznNWabf-jBWxTLbQWQvx_gCLwLZZk0DlUXXmzz8Bo2Lne6IIQEEfBVUxVofAQG5zCnTpNod2UJp14DEJiWwhKaDh7-3LIhxra3vEqxVOFJtgusxCw8vla08saGebKqv0APFhNKqFVxMvRJPilF1ou8_jhrZ7cnZXQJOedKJeSUYSgUOqtXdSSfvzlOOx29W9gQXGVM3Q9Uoeq-ZFb6eEBcFF6Y4h_ZVf8hQpdVQ13LJC9ZrtrgAaVKlKUt4RI3Z_325RbwyUGwb0oZYuf3RXx0qp4cI91-AOzrRNI5-5UWMEtQ6BPWHX8dOFiq7akypBIpsmyvH3w9So5ULPsPtng3pDJsrOuoJjkgpgtOHRjBO9fpLYoLC5x3Imz6NOF9mpTkqXucV2PbsbDUdri8_xblp-C1v4L1DbCBkzCcs_S0BpJQxLwFKlwlM5y_IGeFi_BoCBtUtRXF5s5377BEgam01DLCHieMqrJ3vL3Hjl34t7aGfUDfq2Bm4INIn7iikpHsSw0U1fHdb2LhNDlJJx0I3WxPLAPTsUKDPDvmC7BJZhPU9_r8O_917f66gVdKRFUDPe0ZWFyQgWMRNmUy8K0w0r-3N3ytgwlcD0MNJZ_oNbWNIflXufxuIogeGWJ0uvDdzpmYCceGfGStruPJqf2wEUei2TS9uf6w53xpL2au1gd1YLAQ_5s7xWRvzoqPfU97aTYIQMRRtB4mMlIaM5Rw02k2PJ2qq4kl1FdzcjZ8XYiD8wfq4US3ffRxDpUELGMzamoBrS7EPFuleNijPix0OlVZyRVCWUgYXhFgnznXWQs6KaZ5m96SmdCGz7G9tv51ZXJ5k9_eo2YLfCTj3otZFHSEAF14Z1-gjAZaFtK-XPBGnmLXHXc2YFcANC63S8Janekng2VakSsqfDbCsNbm17s8WoSp9c8QEwWWi6-9_ishUr0gxnjYuJT9Vwt0UEEwi2Yf0a5sApdh81OosS2oE30-oiJWzigm1vaTJu1dX-zJMXBB4j9znZWGNKJ7QmQwBBV3EArz6P1Q2vtf6cqcYx-bp6tQoUSJCjCpZLC29aOnOE00f3tlNha1UslCMpiNy5E8V5Nkvy2-lsGDF8bXk2i5YAmGb3EpXNUdQypvQw4AyGaVLox73Dg2vJZuygQ8pw2JJPsdGt6ppykF5TU4v8g5BpMNJY7KJ8ZhcmO0bbbwQXxn9lCfWCsB8PP4Npr7b67C4cY8WAmB6OBbilo5NVW04-JzGFiBwN_3XX9selK9eTfvSvfP8u0cOtl-v_zkWLHvjG8EET-e964iu2a0zoqorW6g_-MkMSIpDjiiINO5-uXZwWKzRNVC13q9cG8I4SGyhpCvqqL2SqDxX2Lie_0S4osXlOHIT3qcPcw6rxhD1gKGAR4ygIUnP6p4edQl3G3T52stNw3sWJJ2afQVpe3ZGAnvnfXapdDDcW1bBF_D4xc0dPqXRqiXb5FHetA9nk9OkmeBY2uNt0AXNFFinVxiEn5Uw.6nh89-zeTGG7eN7Dym6CGA' })

async function get_order_list_by_duration({ username, site_id, access_token }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
        }
    }

    if (!access_token) {
        return result
    }

    var config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://api.gobiz.co.id/journals/search`,
        headers: base_headers({ username, site_id, access_token }),
        data: {
            "from": 0,
            "included_categories": {
                "incoming": [
                    "transaction_share",
                    "action"
                ]
            },
            "query": [
                {
                    "clauses": [
                        {
                            "field": "metadata.transaction.status",
                            "op": "in",
                            "value": [
                                "settlement"
                            ]
                        },
                        {
                            "clauses": [
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "cash"
                                            ]
                                        },
                                        {
                                            "field": "metadata.source",
                                            "op": "in",
                                            "value": [
                                                "goresto_online",
                                                "GORESTO_ONLINE_PICKUP",
                                                "GOMART_ONLINE"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "cash"
                                            ]
                                        },
                                        {
                                            "field": "metadata.transaction.transaction_source.source",
                                            "op": "in",
                                            "value": [
                                                "pos"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "offline_credit_card",
                                                "credit_card"
                                            ]
                                        },
                                        {
                                            "field": "metadata.card_type",
                                            "op": "in",
                                            "value": [
                                                "debit",
                                                "credit"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "qris"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "shopee_food",
                                                "grab_food"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.transaction.payment_type",
                                            "op": "in",
                                            "value": [
                                                "offline_ovo",
                                                "offline_telkomsel_cash"
                                            ]
                                        }
                                    ],
                                    "op": "and"
                                },
                                {
                                    "clauses": [
                                        {
                                            "field": "metadata.card_type",
                                            "op": "equal",
                                            "value": "prepaid"
                                        }
                                    ],
                                    "op": "and"
                                }
                            ],
                            "op": "not"
                        },
                        // {
                        //     "field": "metadata.transaction.merchant_id",
                        //     "op": "equal",
                        //     "value": "VN326640366"
                        // }
                    ],
                    "op": "and"
                }
            ],
            "size": 500,
            "sort": {
                "time": {
                    "order": "desc"
                }
            },
            "source": [
                "metadata.source",
                "metadata.transaction.id",
                "metadata.transaction.transaction_source.source",
                "metadata.transaction.transaction_source.service",
                "metadata.transaction.order_id",
                "metadata.transaction.transaction_time",
                "metadata.transaction.real_gross_amount",
                "metadata.transaction.payment_type",
                "metadata.transaction.pop.id",
                "metadata.card_type",
                "metadata.transaction_type",
                "metadata.transaction.status",
                "metadata.merchant_mid",
                "metadata.merchant_tid",
                "metadata.merchant_code",
                "metadata.batch_number",
                "metadata.reference_number",
                "metadata.reference_id",
                "metadata.auth_id_response",
                "metadata.transaction.status",
                "metadata.transaction_number",
                "metadata.masked_card",
                "metadata.transaction.customer",
                "metadata.transaction.custom_field1",
                "metadata.transaction.custom_field2",
                "metadata.transaction.custom_field3",
                "metadata.transaction.metadata",
                "metadata.acquiring_bank",
                "metadata.on_us",
                "metadata.point_balance"
            ],
            "time_range": {
                "gte": from.toISOString(),
                "lte": to.toISOString(),
            }
        }
    };

    try {
        const resp = await axios(config);
        result.data.FINISH = resp.data.hits?.map(v => ({
            order_number: v.metadata.transaction.order_id
        })) || []
    } catch (err) {
        console.log(err)
        return result
    }
    return result
}

async function get_all_order_transaction({ username, site_id, access_token }) {
    if (!access_token) {
        return []
    }

    var config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://api.gobiz.co.id/journals/search`,
        headers: base_headers({ username, site_id, access_token }),
        data: {
            "from": 0,
            "included_categories": {
                "incoming": [
                    "transaction_share",
                    "action"
                ]
            },
            "query": [
                {
                    "clauses": [
                        {
                            "field": "metadata.source",
                            "op": "in",
                            "value": [
                                "goresto_online"
                            ]
                        }
                    ],
                    "op": "and"
                }
            ],
            "size": 500,
            "sort": {
                "time": {
                    "order": "desc"
                }
            },
            "source": [
                "*"
            ]
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.hits
    } catch (err) {
        console.log(err)
        return []
    }
}

async function confirm_order({ username, site_id, access_token }, order_id, status) {
    if (!access_token) {
        return {}
    }

    try {
        const current_timestamp = new Date().getTime()
        const config = {
            method: 'put',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/waiter/v1/orders/${order_id}/merchant/action`,
            headers: base_headers({ username, site_id, access_token }),
            data: { "timestamp": current_timestamp, "action_name": "FOOD_PREPARED_FOR_DELIVERY" }
        };

        const resp = await axios(config);
        return resp.data

    } catch (err) {
        console.log(err)
        return null
    }
}

async function cancel_order({ username, site_id, access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    if (!access_token) {
        return {}
    }

    try {
        const config = {
            method: 'put',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/waiter/v1/orders/${order_id}/merchant/cancelled`,
            headers: base_headers({ username, site_id, access_token }),
            data: {
                cancel_reason_code: "OTHERS",
                cancel_reason_description: cancel_type === 'out_stock' ? "Quán hết món" : "Quán quá bận"
            }
        };

        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
        return null
    }
}


// https://api.gojekapi.com/mocha/v3/orders/F-2046514882
async function get_order_detail({ username, site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }

    let result = null
    try {
        const config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://api.gojekapi.com/mocha/v3/orders/' + order_id,
            headers: base_headers({ username, site_id, access_token }),
        };
        const resp = await axios(config);
        result = resp.data
    } catch (err) {
        console.log(err)
    }

    if (!result) {
        try {
            const config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://api.gobiz.co.id/cosmo/v1/orders/search',
                headers: web_base_headers({ access_token }),
                data: { "query": { "term": { "order_number": order_id } } }
            };
            const resp = await axios(config)
            if (resp.data.data.hits?.length > 0) {
                result = { source2: resp.data.data.hits[0] }
            } else {
                return null
            }
        } catch (err) {
            console.log(err)
        }

    }

    try {
        const trans_config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.gobiz.co.id/journals/search',
            headers: base_headers({ username, site_id, access_token }),
            data: {
                "from": 0,
                "included_categories": {
                    "incoming": [
                        "transaction_share",
                        "action"
                    ]
                },
                "query": [
                    {
                        "clauses": [
                            {
                                "field": "metadata.transaction.order_id",
                                "op": "in",
                                "value": [
                                    order_id
                                ]
                            }
                        ],
                        "op": "and"
                    }
                ],
                "size": 10,
                "sort": {
                    "time": {
                        "order": "desc"
                    }
                },
                "source": [
                    "*"
                ]
            }
        };
        const trans_resp = await axios(trans_config)
        result.transaction = trans_resp.data?.hits[0]
        const test = map_order('gojek', result)
        console.log(JSON.stringify(test, null, 2))
        console.log('DONE')

    } catch (err) {
        console.log(err)
    }
    return result
}

// store_id: '7293cbd5-4c28-4aec-98ff-694666876698'
async function update_store_status({ username, site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return null
    }

    const data = {
        "force_close": status === 'close'
    }
    if (duration)
        data["temporary_close_duration_in_minutes"] = duration

    var config = {
        method: 'patch',
        maxBodyLength: Infinity,
        url: 'https://api.gojekapi.com/gofood/merchant/v2/restaurants/' + site_id,
        headers: base_headers({ username, site_id, access_token }),
        data
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
        return {}
    }

}

async function get_open_status({ username, site_id, access_token }) {
    if (!access_token) {
        return null
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}/open_status`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        return resp.data?.open_status?.status === 'OPEN'
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ username, site_id, access_token }) {
    if (!access_token) {
        return []
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v1/restaurants/${site_id}/operational_hours`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        return resp.data.operational_hours
    } catch (err) {
        console.log(err)
        return []
    }
}

async function update_opening_hour({ username, site_id, access_token }, working_hours) {
    if (!access_token) {
        return null
    }

    var config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v1/restaurants/${site_id}/operational_hours`,
        headers: base_headers({ username, site_id, access_token }),
        data: { operational_hours: working_hours },
    };

    try {
        const resp = await axios(config);
        return resp.data.operational_hours
    } catch (err) {
        console.log(err)
        return null
    }
}



async function get_menu({ username, site_id, access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }
    const store = await get_store({ username, site_id, access_token })
    if (!store) {
        return {
            categories: [],
            option_categories: [],
        }
    }
    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${store.menu_group_id}/menus`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        config.url = `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${store.menu_group_id}/variant_categories`
        const resp_2 = await axios(config);
        return {
            store,
            categories: resp.data.menus,
            option_categories: resp_2.data.variant_categories,
        }
    } catch (err) {
        console.log(err)
        return {
            categories: [],
            option_categories: [],
        }
    }
}

async function update_menu_item({ username, site_id, access_token }, data) {
    if (!access_token) {
        return null
    }

    const store = await get_store({ username, site_id, access_token })
    var config = {
        method: 'patch',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${store.menu_group_id}/menu_items/${data.id}`,
        headers: base_headers({ username, site_id, access_token }),
        data,
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
    }
}

async function get_store({ username, site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
        return null
    }
}

// status: open or close
async function set_special_open_status({ username, site_id, access_token }, { name, from, to, status }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}/special_operational_hours`,
        headers: base_headers({ username, site_id, access_token }),
        data: {
            "slots": [],
            "start_date": moment(from).format('YYYY-MM-DD'),
            "name": name,
            "end_date": moment(to).format('YYYY-MM-DD'),
        }
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function delete_special_open_status({ username, site_id, access_token }, { name }) {
    if (!access_token) {
        return []
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v1/restaurants/${site_id}/operational_hours`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        const special_hour = resp.data.special_operational_hours.find(v => v.name.toUpperCase() === name.toUpperCase())
        if (special_hour) {
            var config2 = {
                method: 'delete',
                maxBodyLength: Infinity,
                url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}/special_operational_hours/${special_hour.id}`,
                headers: base_headers({ username, site_id, access_token }),
            };
            const resp = await axios(config2);
            console.log(resp.data)
        }
    } catch (err) {
        console.log(err)
        return []
    }
}

async function delete_all_special_open_status({ username, site_id, access_token }) {
    if (!access_token) {
        return []
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v1/restaurants/${site_id}/operational_hours`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        for (const special_hour of resp.data.special_operational_hours) {
            var config2 = {
                method: 'delete',
                maxBodyLength: Infinity,
                url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}/special_operational_hours/${special_hour.id}`,
                headers: base_headers({ username, site_id, access_token }),
            };
            const resp = await axios(config2);
            console.log(resp.data)
        }
    } catch (err) {
        console.log(err)
        return []
    }
}


async function get_order_feedbacks({ username, site_id, access_token }) {
    let result = []
    if (!access_token) {
        return result
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.gojekapi.com/gofood/merchant/v5/restaurants/${site_id}/reviews`,
        headers: base_headers({ username, site_id, access_token }),
    };

    try {
        const resp = await axios(config);
        const review_template = resp.data?.data?.cards?.find(v => v.card_template === 'GOFOOD_REVIEW_HIGHLIGHTS_V1')
        if (!review_template) {
            return result
        }

        result = review_template?.content?.reviews || []
    } catch (err) {
        console.log(err)
    }
    return result
}


module.exports = {
    get_token,
    get_token_by_refresh_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_all_order_transaction,
    get_order_detail,
    confirm_order,
    cancel_order,
    update_store_status,
    get_opening_hour,
    update_opening_hour,
    get_menu,
    update_menu_item,
    request_otp,
    get_token_by_otp,
    get_store,
    get_open_status,
    set_special_open_status,
    delete_special_open_status,
    delete_all_special_open_status,
    get_token_by_password,
    get_order_feedbacks,
}