const axios = require('../axios')
const moment = require('moment-timezone')
const crypto = require('crypto')

moment.tz.setDefault('Asia/Bangkok')
const skip_call_api = () => false;

const cfg = process.env.NODE_ENV === 'prod' ? {
  app_key: '6ad6h88886qi7',
  app_secret: 'fb736a92bb88c323d8ded51fb2d8d4f31f5ea5f9',
  base_url: 'https://open-api.tiktokglobalshop.com',
  login_url: 'https://services.tiktokshop.com/open/authorize?service_id=7293393513140389637',
  token_url: 'https://auth.tiktok-shops.com/api/v2/token/get'
} : {
  app_key: '69ovlg6jufpfl',
  app_secret: 'dbb64324817b850d05d288a1952671a6e74eb6b4',
  base_url: 'https://open-api-sandbox.tiktokglobalshop.com',
  login_url: 'https://auth-sandbox.tiktok-shops.com/oauth/authorize?app_key=69ovlg6jufpfl',
  token_url: 'https://auth-sandbox.tiktok-shops.com/api/v2/token/get'
}

const get_login_url = () => {
  return cfg.login_url + `&state=${crypto.randomUUID()}`
}

// UNPAID = 100;
// - ON_HOLD = 105;
// - AWAITING_SHIPMENT = 111;
// - AWAITING_COLLECTION = 112;
// - PARTIALLY_SHIPPING = 114;
// - IN_TRANSIT = 121;
// - DELIVERED = 122;
// - COMPLETED = 130;
// - CANCELLED = 140;
// enum: ['WAITING_PAYMENT', 'PRE_ORDER', 'PENDING', 'DOING', 'FINISH', 'CANCEL'],

const MapOrderStatus = {
  100: 'WAITING_PAYMENT',
  105: 'PENDING',
  111: 'DOING',
  112: 'DOING',
  114: 'DOING',
  121: 'DOING',
  122: 'FINISH',
  130: 'FINISH',
  140: 'CANCEL',
}

const RevertOrderStatusMap = {
  WAITING_PAYMENT: [100],
  PRE_ORDER: [],
  PENDING: [105],
  DOING: [111, 112, 114, 121],
  FINISH: [130],
  CANCEL: [140],
}

const generateSHA256 = (path, queries, secret) => {
  const keys = Object.keys(queries)
    .filter((key) => key !== 'sign' && key !== 'access_token')
    .sort()
  let input = path

  for (const key of keys) {
    input += key + queries[key]
  }
  console.log(input)
  input = secret + input + secret

  const hmac = crypto.createHmac('sha256', secret)
  hmac.update(input)

  return hmac.digest('hex')
}

const bundle_params = (path, queries, secret) => {
  const timestamp = Math.floor(Date.now() / 1000).toString()
  const sign = generateSHA256(path, { ...queries, timestamp }, secret)
  return { ...queries, timestamp, sign }
}

async function get_token(auth_code) {
  const params = { app_key: cfg.app_key, app_secret: cfg.app_secret, auth_code, grant_type: 'authorized_code' }

  try {
    const resp = await axios({
      params,
      method: 'get',
      url: cfg.token_url,
    })
    return resp.data.data
  } catch (err) {
    console.log(err)
    null
  }

}

async function get_token_by_refresh_token(refresh_token) {
  const params = {
    app_key: cfg.app_key,
    app_secret: cfg.app_secret,
    refresh_token,
    grant_type: 'refresh_token',
  }

  try {
    const resp = await axios({
      params,
      method: 'get',
      url: 'https://auth.tiktok-shops.com/api/v2/token/refresh',
    })
    return {
      access_token: resp.data.data.access_token,
      refresh_token: resp.data.data.refresh_token,
    }
  } catch (err) {
    console.log(err)
    null
  }

}

async function get_order_list_v2({ access_token }) {
  const result = {
    success: true,
    data: {}
  }

  if (!access_token || skip_call_api()) {
    return result
  }

  try {
    const filter_data = {
      DRAFT: [100, 105],
      DOING: [111, 112],
      PICK: [114, 121, 122],
      FINISH: [130],
      CANCEL: [140],
    }

    const config = {
      method: 'post',
      url: cfg.base_url + `/api/orders/search`,
      params: bundle_params('/api/orders/search', { access_token, app_key: cfg.app_key }, cfg.app_secret),
      data: {
        page_size: 100,
      },
      headers: {
        'content-type': 'application/json; charset=UTF-8',
      },
    };

    const resp = await axios(config);
    const orders = resp?.data?.data?.order_list || []
    for (const [status, filter] of Object.entries(filter_data)) {
      const order_list = orders.filter((order) => filter.includes(order.order_status))
      if (order_list.length > 0) {
        result.data[status] = order_list
      }
    }

  } catch (err) {
    console.log(err.message)
    return result.success = false
  }
  return result
}


async function get_order_list_by_duration({ access_token }, { from, to }) {
  const result = {
    success: true,
    data: {
      FINISH: [],
      CANCEL: [],
    }
  }

  if (!access_token) {
    return result
  }

  const create_time_to = to.unix()
  const create_time_from = from.unix()

  const config = {
    method: 'post',
    url: cfg.base_url + `/api/orders/search`,
    params: bundle_params('/api/orders/search', { access_token, app_key }, app_secret),
    data: {
      page_size: 100,
      create_time_from,
      create_time_to,
    },
    headers: {
      'content-type': 'application/json; charset=UTF-8',
    },
  }

  while (true) {
    try {
      const res = await axios(config)
      const docs = res.data.data
      result.data.FINISH.push(...docs.order_list.filter(v => [122, 130].includes(v.order_status)))
      if (!docs.more) {
        break
      }
    } catch (error) {
      console.log(error)
      break
    }
  }

  return result
}

async function get_order_detail({ access_token }, order_id) {
  if (!access_token) {
    return {}
  }

  const config = {
    method: 'post',
    url: cfg.base_url + '/api/orders/detail/query',
    params: bundle_params('/api/orders/detail/query', { access_token, app_key: cfg.app_key }, cfg.app_secret),
    data: { order_id_list: [order_id] },
  }
  try {
    const resp = await axios(config)
    return resp.data.data.order_list[0]
  } catch (error) {
    console.log(error.message)
    return null
  }
}

async function get_store({ access_token }) {
  if (!access_token) {
    return {}
  }

  try {
    const config = {
      method: 'get',
      url: cfg.base_url + '/api/shop/get_authorized_shop',
      params: bundle_params('/api/shop/get_authorized_shop', { access_token, app_key }, app_secret),
    }

    const resp = await axios(config)

    return resp.data.data
  } catch (error) {
    console.log(error)
    return null
  }
}

async function confirm_order({ access_token }, order_id) {
  if (!access_token) {
    return {}
  }

  let config = {}

  if (status === 'confirm') {
    config = {
      method: 'post',
      url: cfg.base_url + '/api/fulfillment/rts',
      params: bundle_params('/api/fulfillment/rts', { access_token, app_key }, app_secret),
      data: { package_id: 'package_id' },
    }
  }

  if (status === 'reject') {
    config = {
      method: 'post',
      url: cfg.base_url + '/api/reverse/order/cancel',
      params: bundle_params('/api/reverse/order/cancel', { access_token, app_key }, app_secret),
      data: { order_id, cancel_reason_key: '' },
    }
  }

  try {
    const resp = await axios(config)
    return resp.data
  } catch (err) {
    console.log(err)
    return {}
  }
}

module.exports = {
  get_token,
  get_token_by_refresh_token,
  get_order_list_v2,
  get_order_detail,
  confirm_order,
  get_order_list_by_duration,
  get_store,
  get_login_url
}
