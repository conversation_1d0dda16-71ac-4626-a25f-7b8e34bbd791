const axios = require('../axios');
const moment = require('moment');
const _ = require('lodash');
const { VendorRequest } = require('../database')
const { send_slack_message } = require('../slack')
const helper = require('../helper')

let zalo = {}

// username = app_id, password = secret_key
zalo.get_token = async function ({ refresh_token }) {
    if (!refresh_token) {
        return {
            access_token: '',
            refresh_token: '',
        }
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://oauth.zaloapp.com/v4/oa/access_token',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'secret_key': 'R2POMbJ6GNGVVEYFX3EI'
        },
        data: {
            'refresh_token': refresh_token,
            'app_id': '1616787572616286289',
            'grant_type': 'refresh_token'
        }
    };

    try {
        const resp = await axios(config);
        return {
            access_token: resp.data.access_token,
            refresh_token: resp.data.refresh_token
        }
    } catch (error) {
        console.log(error.message)
        return null
    }
}

zalo.send_request_user_info = async ({ access_token }, { user_id, title, subtitle, image_url }) => {
    try {
        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://openapi.zalo.me/v3.0/oa/message/cs',
            headers: {
                'Content-Type': 'application/json',
                'access_token': access_token
            },
            data: {
                recipient: {
                    user_id
                },
                message: {
                    attachment: {
                        type: 'template',
                        payload: {
                            template_type: 'request_user_info',
                            elements: [
                                {
                                    title,
                                    subtitle,
                                    image_url
                                }
                            ]
                        }
                    }
                }
            }
        })
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

zalo.send_message_to_user_id = async ({ access_token }, { user_id, text }) => {
    try {
        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://openapi.zalo.me/v3.0/oa/message/cs',
            headers: {
                'Content-Type': 'application/json',
                'access_token': access_token
            },
            data: {
                recipient: {
                    user_id
                },
                message: {
                    text
                }
            }
        })
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}
// zalo.send_message_to_user_id({
//     access_token: "wOjPSlmHet_lY5bu_HMYV-6FNNBy3Vm5ZzbwNCv2q5ohWtW6m1p60vV3NcNV1g0QhP8g5_zmZ6USkWv_oIAg9ORUFM3n98KSekOS4Eaivs-3hNuAwrx9HV--HGQJ9T90p_vxBRWmxodPrXTQl1Nl9Dx_4qEfOxOC_ASzJxPzc0d4ZdWFjqR0SAMcIY_kRSj0ZBDFEfn0zbxmmNb6u0ZI7glIRc774imtrOvJNO5ur5_9jbXJY2FwDztLUNMKJjvnwjHW7OTCrqlCgt87tMg9M9AgBpxTP9PxgVaeEDXcZrkbcWm-o0wFJvxc8p783x57jimqDiKfddIJwm82sNAORPA4Dn3XR8bNcgDz5SPMun6ZZcrXq7360PcwGNRSJlqRlOfUTCvLnHw2ban0asZWB5SahCqr-GcbSG",
// }, {
//     user_id: "1309444552055904324",
//     text: "Hi <b>https://he.nexpos.io/HE.VARNA.CT</b><i>123</i>",
// })

zalo.send_template_message_to_phone = async ({ access_token }, { phone, template_id, template_data }) => {
    try {
        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://business.openapi.zalo.me/message/template',
            headers: {
                'Content-Type': 'application/json',
                'access_token': access_token
            },
            data: {
                "phone": phone,
                "template_id": template_id,
                "template_data": template_data,
                "tracking_id": moment().unix().toString()
            }
        }
        const resp = await axios(config)

        await VendorRequest.create({
            vendor: 'zalo',
            type: 'send_zns',
            url: config.url,
            method: 'post',
            headers: config.headers,
            success: true,
            request_data: config.data,
            response_data: resp.data,
        })

        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

// zalo.send_template_message_to_phone({
//     access_token: 'E1Y2Z3X4C5V6B7N8M9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P1Q2W3E4R5T6Y7U8I9O0P',
// },
//     {
//         phone: "84907507560",
//         template_id: "295611",
//         template_data: {
//             name: "Leo",
//             phone: "0908280493",
//             address: "122 Pho Quang"
//         },
//         tracking_id: "xxxxxxxxxxxx"
//     }
// )

module.exports = zalo