const axios = require('../axios');
const url = require('url');
const _ = require('lodash');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { text_slugify } = require('../helper');
const gojek = require('./gojek');

const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;
const random_hex_string = (length) => [...Array(length)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');

const base_headers = ({ access_token }) => {
    const headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
    }

    if (access_token) {
        headers['authorization'] = 'Bearer ' + access_token
    }
    return headers
}

const base_headers_backup = ({ site_id, access_token }) => {
    const headers = {
        'x-user-type': 'merchant',
        'x-client-id': 'go-biz-mobile',
        'x-client-secret': 'sPC0qVk7gi76JUoGVfOfcgd7FfuaBv',
        'accept-language': 'vi',
        'x-user-locale': 'vi-VN',
        'x-platform': 'Android',
        'x-appversion': '4.8.0',
        'x-appid': 'com.gojek.resto',
        'x-deviceos': 'Android 26',
        'x-phonemake': 'samsung',
        'x-phonemodel': 'SM-G950F',
        'accept': 'application/json',
        'gojek-country-code': 'VN',
        'gojek-timezone': 'Asia/Ho_Chi_Minh',
        'content-type': 'application/json; charset=UTF-8',
        'user-agent': 'okhttp/3.12.10',
        "x-pushtokentype": "FCM",
        "x-uniqueid": random_hex_string(16),

    }
    if (site_id)
        headers['restaurantuuid'] = site_id
    if (access_token) {
        headers['authorization'] = 'Bearer ' + access_token
        headers['authentication-type'] = 'go-id'
    }
    return headers
}

const upload_image = async function ({ site_id, access_token }, image_url) {
    try {
        if (!image_url)
            return ""
        const parse_url = url.parse(image_url);
        const file_name = parse_url.pathname.split('/').pop();


        const response = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/gofood/merchant/v1/images/cloud_storage_url`,
            headers: base_headers({ site_id, access_token }),
            data: { "file_name": file_name }
        });
        const image_upload_url = Buffer.from(response.data.image_upload_url, 'base64').toString('ascii');;
        const image_download_url = Buffer.from(response.data.image_download_url, 'base64').toString('ascii');;

        const image_resp = await axios.get(image_url, { responseType: 'arraybuffer', });

        await axios.put(image_upload_url, image_resp.data, {
            headers: {
                'Content-Type': 'image/jpeg',
            },
        });

        return image_download_url;
    } catch (error) {
        console.error('Error uploading and downloading the image:', error.message);
        return ""
    }
}
let gojek_menu = {}
gojek_menu.sync_menu = async function ({ site_id, access_token }, { site_menu, item_id }) {
    if (!access_token) {
        return {}
    }

    try {
        const restaurent_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/gofood/merchant/v2/restaurants/${site_id}`,
            headers: base_headers({ site_id, access_token }),
        };
        const restaurent_resp = await axios(restaurent_config);
        const restaurent = restaurent_resp.data

        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${restaurent.menu_group_id}/menus`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.menus


        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            for (let i = 0; i < site_menu.categories[c].items.length; i++) {
                const item = site_menu.categories[c].items[i];
                if (String(item._id) === item_id) {
                    const new_image = await upload_image({ site_id, access_token }, item.image)

                    const group_menu_category_index = _.findIndex(group_menu, v => v.name.toUpperCase() === category.name.toUpperCase())
                    // Create or update category
                    if (group_menu_category_index < 0) {
                        const group_menu_category_resp = await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${restaurent.menu_group_id}/menus`,
                            headers: base_headers({ site_id, access_token }),
                            data: { "name": category.name.toUpperCase(), "active": true }
                        });

                        const group_menu_category_item_resp = await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${restaurent.menu_group_id}/menu_items`,
                            headers: base_headers({ site_id, access_token }),
                            data: {
                                "menu_common_id": group_menu_category_resp.data.common_id,
                                "signature": false,
                                "price": item.price,
                                "image_url": new_image,
                                "description": item.description,
                                "active": true,
                                "name": item.name.toUpperCase(),
                            }
                        });
                        console.log(group_menu_category_item_resp.data)
                    } else {
                        const group_menu_category = group_menu[group_menu_category_index]
                        const group_menu_category_item_index = _.findIndex(group_menu_category.menu_items, v => v.name.toUpperCase() === item.name.toUpperCase())
                        if (group_menu_category_item_index < 0) {
                            // create menu item
                            const group_menu_category_item_resp = await axios({
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${restaurent.menu_group_id}/menu_items`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "menu_common_id": group_menu_category.common_id,
                                    "signature": false,
                                    "price": item.price,
                                    "image_url": new_image,
                                    "description": item.description,
                                    "active": true,
                                    "name": item.name.toUpperCase(),
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                        } else {
                            // update menu item with price, active, image and descripton
                            const group_menu_category_item = group_menu_category.menu_items[group_menu_category_item_index]
                            const group_menu_category_item_resp = await axios({
                                method: 'patch',
                                maxBodyLength: Infinity,
                                url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${restaurent.menu_group_id}/menu_items/${group_menu_category_item.common_id}`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "description": item.description,
                                    "price": item.price,
                                    "signature": false,
                                    "active": item.active,
                                    "menu_common_id": group_menu_category.common_id,
                                    "image_url": new_image,
                                    "name": item.name.toUpperCase(),
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                        }

                    }
                }

            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

gojek_menu.delete_menu_category_item = async function ({ site_id, access_token }, { category_name, item_name }) {
    if (!access_token) {
        return {}
    }
    // TODO: Currently app not working
}

gojek_menu.delete_menu_category = async function ({ site_id, access_token }, { category_name }) {
    if (!access_token) {
        return {}
    }
    // TODO: Currently app not working
}

// gojek_menu.delete_menu_category({
//     site_id: "10041460",
//     access_token: "B:YOknwDKpYkQAm1cYcXJXcONuFTbWKfPEJiOLbkcLSnXoFbBUFshgn4HPsdOilHSop+2TiX9+SIcHIOSCOOgqT+ygGa+dt/uu2+8yUTyBygI=",
// }, {
//     category_name: "test",
//     item_name: "test23",
// })

gojek_menu.active_menu_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await gojek.get_menu({ site_id, access_token })

        for (const category of group_menu.categories) {
            I: for (const item of category.menu_items) {
                let update_item = _.find(update_items, v => text_slugify(item.name) === text_slugify(v.name))
                if (!update_item) {
                    if (!update_all_items)
                        continue I
                    update_item = {
                        name: item.name,
                        quantity: 0,
                        active: false,
                    }
                }
                const new_status = update_item.active
                if (item.active === new_status)
                    continue

                const group_menu_category_item_resp = await axios({
                    method: 'patch',
                    maxBodyLength: Infinity,
                    url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${group_menu.store.menu_group_id}/menu_items/${item.common_id}`,
                    headers: base_headers({ site_id, access_token }),
                    data: {
                        "menu_common_id": item.menu_common_id,
                        "name": item.name,
                        "signature": item.signature,
                        "description": item.description,
                        "image_url": item.image,
                        "active": new_status,
                        "price": item.price,
                    }

                });
                // Sữa Bột GrowPLUS+ Sữa Non Trên 1 Tuổi Lon 800g
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }

        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// gojek_menu.active_menu_item({
//     access_token: "eyJhbGciOiJkaXIiLCJjdHkiOiJKV1QiLCJlbmMiOiJBMTI4R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0..NvVKBAibziVCQ0dR.tkXh9WEIjfc7LWkSYVstNSVzCif_eUdQQh6PmjqEuvtUuABr7Noe9HpAwZEJkletDlgZOytsXU1kbAGuuTjdPwPXlZ6enrB7n7bJUb3PGJ94bP7lWzgnDh7_gE9OBFLzhTUytsn9x-LEcCxCB4k9pYAh0l7OFUikWkGuU1nubFcNY8wIxENZcvhyBhuPFC-0b6XUtdEj2NVdWqviE5sbXnModBEmX_IDfsoDmdEeDpgzP6v4icwNgr-2jIBvGkx-bM8vGG2pGgkwz3qRADDm1Vg3_iHKyI_soair7YtojhfgSbLbGHxJkyqRYiRQJRWE1sNsmWz_n8rO4-CXBT2kC49ursh8Z54F-eZtQs7fJ30J4aiWB5tpKBWX9x8M-jQYTV0JMjGKDZDvweFhdCJMYh9lt0tNRwN-w468hccbbnmWBCez-dHtB3Sh1ej9YdhB--QwLQekGi4Tc7Nir8UNZc3yiFLmywD8YjzEL5IEZHgGMLiHkQbmTB2ul1Ox-xSf7geAM7s_sWf5DlZ-J8nNuvR_jfqpA5bbQu4mBcVvHpi8jHIgkOoItHUXg7G592GfDJkpKtoWBL4n_tGe323lF9n3zQNcftdlWuPbW2VD2tOGlZLxTq0WCodQYZ7Obhxbeah8JEUKeUSgoNkYWOhQvQD0UGZAzQkVjcGYI1vQnH4KDv-iVSVAoCOmaWVFe48LzrajB3d6MzENwzh3Xgc9-M3qklW6GndjUmel5wF64xYw0Aq5cVj16z7kWJeRALY-os_fsbV7UWECNWe7gkm0SL_3E5xSVHZLGz-TrYEHKUNF8llT9BFnVXVNp578xHtn2ddzuX7HEGkvdLv0cAdQGGTTwhPa3HamSAKeJ5dpnxMsX3lWZvt1ZZUpbfGwAUUYBFDVjC8ejqyFj3DOQzVN24mQ2YHacXDJD0n8_dmB_7Cp5qLMxb0gLeR1cu7JDqx8Tcy5e4kiDEvfWTwXvxB4e-LLYTkaZH1tx66DpHffi9H0nj1nGlEXYre0EBMlRI8ndaW8msyDespUuKszyjIiv3FwQB046rwQONl5WPBuIBGazbbcmiRsK8BhhBOPnBYcjqeLt54y0mlmsxKvNoUtN_O2wSMCVbtOxM1EhfrfGvVwdYEXTQ1HxpvlT1zXUp5uhpoIhPL8_X4lAag1o2VckC-MWVITKcKzF85rAqR--zEagp-zhH_6_hs7o6ZoRLhpCCssSe433KNf6xEpco405-3joKu5z9P9WAPYYsxboqWq7olcyR06vlES-DIgYCNQVh40vy_mmonjOCr8PE-FBLjPqFA_4P5vNacFt10cTfdnbX079gL4PZXmzF_LhQEBgk-lThnoCrwQHMLc6zLJABn4xMQyUBxN9a9Bx5kY07WtwDUfChZmaTI7sKr1s7wXFMN61fPR-Zxbdnmj3uBl_o0xtPRlk-3YAizqxEri--kxwcvwla5gGQSiyjgA-4dLKm-w6UKOuklICgwmjwAhk5k8gIRZiPyf8G6tMQdeVdSEVMrQmXyYpzbC0hWzEvzU-3uLx1hQ3fgSrYNgGTR_tnbLYUqJ-EoXdIvlw3lrwruA_1m1s-PzUDbig9j3WgpCXUXBGdflNrBTc526Ish3TH5BkvofR3LjZLpx7DS2IEdTaMvnatiGINpmKH_TOyU0mBQDxfB15ar5IDD6mRbMRqCBgDe7YhtvdVcHl2VxsW59Mvp6g2xg9JgKyhRvqphgFDKb-uYnYob-NCY4Cj-oYij9mIwAn2gNl6PlfqHX8o01AK7gn6LseikaLjN3wbPOKK_-nVtf7pw35sqjAyomcb8f-MJFw5FxgA2ODgF1zKxnnTFQkJJ6NXJIBubjViJohccgnfE6fr0eVXDy3HIhir7F2yVUa2CUdMdzMNE54cQDl0Sp7Ea8gb3OuhiaemYUcw.vYQfwuP0lS9NbSzj7AVaJA",
//     site_id: "b7571932-df49-46b9-ba34-e8272a8c5c3d",
// }, [{
//     name: "Combo Dinh Dưỡng Tiện Lợi Học Đường",
//     active: true
// }])


// Deactive dish when option is not available
gojek_menu.active_menu_option_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await gojek.get_menu({ site_id, access_token })
        for (const category of group_menu.option_categories) {
            I: for (const item of category.variants) {
                let update_item = _.find(update_items, v => text_slugify(category.name) === text_slugify(v.category_name) && text_slugify(item.name) === text_slugify(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue I
                    }
                    update_item = {
                        category_name: category.name,
                        name: item.name,
                        quantity: 0,
                        active: false,
                    }
                }
                const new_status = update_item.active
                if (item.active === new_status)
                    continue

                const group_menu_category_item_resp = await axios({
                    method: 'patch',
                    maxBodyLength: Infinity,
                    url: `https://api.gojekapi.com/gofood/merchant/v2/menu_groups/${group_menu.store.menu_group_id}/variants/${item.common_id}`,
                    headers: base_headers({ site_id, access_token }),
                    data: {
                        "name": item.name,
                        "active": new_status,
                        "variant_category_common_id": category.common_id,
                        "price": item.price
                    }
                });
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }
        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}


// gojek_menu.active_menu_option_item({
//     access_token: "eyJhbGciOiJkaXIiLCJjdHkiOiJKV1QiLCJlbmMiOiJBMTI4R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0..NvVKBAibziVCQ0dR.tkXh9WEIjfc7LWkSYVstNSVzCif_eUdQQh6PmjqEuvtUuABr7Noe9HpAwZEJkletDlgZOytsXU1kbAGuuTjdPwPXlZ6enrB7n7bJUb3PGJ94bP7lWzgnDh7_gE9OBFLzhTUytsn9x-LEcCxCB4k9pYAh0l7OFUikWkGuU1nubFcNY8wIxENZcvhyBhuPFC-0b6XUtdEj2NVdWqviE5sbXnModBEmX_IDfsoDmdEeDpgzP6v4icwNgr-2jIBvGkx-bM8vGG2pGgkwz3qRADDm1Vg3_iHKyI_soair7YtojhfgSbLbGHxJkyqRYiRQJRWE1sNsmWz_n8rO4-CXBT2kC49ursh8Z54F-eZtQs7fJ30J4aiWB5tpKBWX9x8M-jQYTV0JMjGKDZDvweFhdCJMYh9lt0tNRwN-w468hccbbnmWBCez-dHtB3Sh1ej9YdhB--QwLQekGi4Tc7Nir8UNZc3yiFLmywD8YjzEL5IEZHgGMLiHkQbmTB2ul1Ox-xSf7geAM7s_sWf5DlZ-J8nNuvR_jfqpA5bbQu4mBcVvHpi8jHIgkOoItHUXg7G592GfDJkpKtoWBL4n_tGe323lF9n3zQNcftdlWuPbW2VD2tOGlZLxTq0WCodQYZ7Obhxbeah8JEUKeUSgoNkYWOhQvQD0UGZAzQkVjcGYI1vQnH4KDv-iVSVAoCOmaWVFe48LzrajB3d6MzENwzh3Xgc9-M3qklW6GndjUmel5wF64xYw0Aq5cVj16z7kWJeRALY-os_fsbV7UWECNWe7gkm0SL_3E5xSVHZLGz-TrYEHKUNF8llT9BFnVXVNp578xHtn2ddzuX7HEGkvdLv0cAdQGGTTwhPa3HamSAKeJ5dpnxMsX3lWZvt1ZZUpbfGwAUUYBFDVjC8ejqyFj3DOQzVN24mQ2YHacXDJD0n8_dmB_7Cp5qLMxb0gLeR1cu7JDqx8Tcy5e4kiDEvfWTwXvxB4e-LLYTkaZH1tx66DpHffi9H0nj1nGlEXYre0EBMlRI8ndaW8msyDespUuKszyjIiv3FwQB046rwQONl5WPBuIBGazbbcmiRsK8BhhBOPnBYcjqeLt54y0mlmsxKvNoUtN_O2wSMCVbtOxM1EhfrfGvVwdYEXTQ1HxpvlT1zXUp5uhpoIhPL8_X4lAag1o2VckC-MWVITKcKzF85rAqR--zEagp-zhH_6_hs7o6ZoRLhpCCssSe433KNf6xEpco405-3joKu5z9P9WAPYYsxboqWq7olcyR06vlES-DIgYCNQVh40vy_mmonjOCr8PE-FBLjPqFA_4P5vNacFt10cTfdnbX079gL4PZXmzF_LhQEBgk-lThnoCrwQHMLc6zLJABn4xMQyUBxN9a9Bx5kY07WtwDUfChZmaTI7sKr1s7wXFMN61fPR-Zxbdnmj3uBl_o0xtPRlk-3YAizqxEri--kxwcvwla5gGQSiyjgA-4dLKm-w6UKOuklICgwmjwAhk5k8gIRZiPyf8G6tMQdeVdSEVMrQmXyYpzbC0hWzEvzU-3uLx1hQ3fgSrYNgGTR_tnbLYUqJ-EoXdIvlw3lrwruA_1m1s-PzUDbig9j3WgpCXUXBGdflNrBTc526Ish3TH5BkvofR3LjZLpx7DS2IEdTaMvnatiGINpmKH_TOyU0mBQDxfB15ar5IDD6mRbMRqCBgDe7YhtvdVcHl2VxsW59Mvp6g2xg9JgKyhRvqphgFDKb-uYnYob-NCY4Cj-oYij9mIwAn2gNl6PlfqHX8o01AK7gn6LseikaLjN3wbPOKK_-nVtf7pw35sqjAyomcb8f-MJFw5FxgA2ODgF1zKxnnTFQkJJ6NXJIBubjViJohccgnfE6fr0eVXDy3HIhir7F2yVUa2CUdMdzMNE54cQDl0Sp7Ea8gb3OuhiaemYUcw.vYQfwuP0lS9NbSzj7AVaJA",
//     site_id: "b7571932-df49-46b9-ba34-e8272a8c5c3d",
// }, [{
//     category_name: "CHỌN 1 BÁNH",
//     name: "Bánh Phô Mai Castella Kido's 40g - 600000192",
//     active: false
// }])


module.exports = gojek_menu