const axios = require('../axios');
const moment = require('moment-timezone');
const _ = require('lodash');

const skip_call_api = () => {
    const vietnamTime = moment().tz('Asia/Ho_Chi_Minh');
    return vietnamTime.hour() >= 1 && vietnamTime.hour() <= 5;
};

const base_headers = ({ access_token, is_mart = false }) => {
    return {
        'user-profile-data': '',
        'device-os': 'Android',
        'user-agent': 'Grab Merchant/4.120.0 (android 9; Build 210)',
        'business-type': is_mart ? 'SHOP' : 'FOOD',
        'x-agent': 'mexapp',
        'authorization': access_token,
        'x-mts-ssid': access_token,
        'mex-country': 'VN',
        'mex-type': 'MEXUSERS',
        'x-platform-type': 'android',
        'accept-language': 'vi',
        'networkkit-disable-gzip': 'true',
        'device-model': 'SM-N960N',
        'X-Forwarded-For': '**************',
        'x-grabkit-clientid': 'GrabMerchant-App'
    }
}

const web_base_headers = ({ access_token, is_mart = false }) => {
    return {
        'authority': 'merchant.grab.com',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        'authorization': access_token,
        'requestsource': 'troyPortal',
        'x-agent': 'mexapp',
        'x-app-platform': 'web',
        'x-app-version': '1.2(v67)',
        'x-client-id': 'GrabMerchant-Portal',
        'x-currency': 'VND',
        // 'x-date': '2024-03-23T10:02:21+07:00', 
        'x-device-id': 'ios',
        'x-grabkit-clientid': 'GrabMerchant-Portal',
        'x-language': 'gb',
        'x-mts-jb': 'false',
        'x-mts-ssid': access_token,
        'x-user-type': 'user-profile'
    }
}

async function get_token(username, password) {
    if (!password) {
        return null
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.grabpay.com/mex-app/troy/user-profile/v1/login',
        headers: {
            'x-platform-type': 'android',
            'user-agent': 'Grab Merchant/4.72.0 (android 10; Build 146)',
            'x-agent': 'mexapp',
            'content-type': 'application/json; charset=utf-8'
        },
        data: { "password": password, "username": username }
    };
    try {
        const resp = await axios(config);
        const access_token = resp.data.data.data.jwt
        const store = await get_store({ access_token })
        return {
            access_token: resp.data.data.data.jwt,
            site_name: store.merchant.name,
            site_id: store.merchant.ID,
        }
    } catch (error) {
        console.log(error.message)
        return null
    }
}



async function get_order_list_v2({ access_token }) {
    const result = {
        success: true,
        data: {}
    }


    if (!access_token || skip_call_api()) {
        return result
    }


    // DOING
    {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v3/orders-pagination?pageType=Preparing&autoAcceptGroup=1&timestamp=`,
            headers: base_headers({ access_token }),
        };

        try {
            const resp = await axios(config);
            if (resp?.data?.orders) {
                result.data['DOING'] = resp.data.orders || []
            }
        } catch (err) {
            console.log(err)
            result.success = false
        }

    }

    {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v1/scheduled-orders`,
            headers: base_headers({ access_token }),
        };

        try {
            const resp = await axios(config);
            if (resp?.data?.orders) {
                result.data['PRE_ORDER'] = resp.data.orders || []
            }
        } catch (err) {
            console.log(err)
            result.success = false
        }

    }


    // FINISH, CANCEL
    {
        const start_time = moment().startOf('day').toISOString()
        const end_time = moment().endOf('day').toISOString()
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=0&pageSize=100&startTime=${start_time}&endTime=${end_time}`,
            headers: base_headers({ access_token }),
        };

        try {
            const resp = await axios(config);
            result.data['FINISH'] = resp.data.statements.filter(v => ["ORDER_EXECUTING", "COMPLETED"].includes(v.deliveryStatus))
            result.data['CANCEL'] = resp.data.statements.filter(v => ["FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"].includes(v.deliveryStatus))
            result.data['FINISH'] = _.uniqBy(result.data['FINISH'], 'ID')
            result.data['CANCEL'] = _.uniqBy(result.data['CANCEL'], 'ID')
        } catch (err) {
            console.log(err)
            result.success = false
        }
    }

    return result
}
// get_order_list_v2({ access_token: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" })

async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: [],
        }
    }

    if (!access_token) {
        return result
    }

    if (moment().diff(from, 'days') >= 30) {
        const finance_list = await get_finance_list({ access_token, site_id }, { from, to })
        for (const transaction of finance_list.filter(v => v.transaction_category === 'payment' && v.transaction_status === 'settled')) {
            result.data.FINISH.push({
                orderID: transaction.long_order_id,
            })
        }
        return result
    }


    try {
        let page_index = 0
        while (true) {
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=${page_index}&pageSize=100&startTime=${from.toISOString()}&endTime=${to.toISOString()}`,
                headers: base_headers({ site_id, access_token }),
            };

            const resp = await axios(config);
            result.data.FINISH.push(...resp.data.statements.filter(v => ["COMPLETED"].includes(v.deliveryStatus)))
            result.data.CANCEL.push(...resp.data.statements.filter(v => ["FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"].includes(v.deliveryStatus)))
            page_index++
            if (!resp.data.hasMore)
                break
        }
    } catch (error) {
        console.log(error)
    }
    return result
}


async function confirm_order({ access_token }, order_id) {
    if (!access_token) {
        return {}
    }

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/orders/mark`,
        headers: base_headers({ access_token }),
        data: {
            "markStatus": 1,
            "orderIDs": [
                order_id
            ]
        }
    };


    try {
        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
        return {}
    }
}

async function cancel_order({ access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    const result = {
        success: true,
        message: "",
    }

    if (!access_token) {
        return result
    }

    const config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/cancel-order/${order_id}`,
        headers: base_headers({ access_token }),
        data: {
            "orderID": order_id,
            "cancelReason": cancel_type = 'out_stock' ? "Quán hết món" : "Chúng tôi quá bận",
            "cancelCode": 1002 // TODO
        }
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (err) {
        console.log(err)
        return {}
    }
}


async function get_order_detail({ source, access_token }, order_id) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v3/orders/` + order_id,
        headers: base_headers({ access_token }),
    };
    console.log(config)

    try {
        const resp = await axios(config);
        let order = resp.data.order

        // Fill menu_item to order.itemInfo.items
        try {
            if (source === 'grab_mart') {
                config.headers = base_headers({ access_token, is_mart: true })
                const menu = await get_mart_menu({ access_token })
                for (const item of order.itemInfo.items) {
                    item.menu_item = menu.categories.flatMap(v => v.sub_categories).flatMap(v => v?.items || []).find(v => v.itemID === item.itemID)
                }
            } else {
                const menu = await get_menu({ access_token })
                for (const item of order.itemInfo.items) {
                    item.menu_item = menu.categories.flatMap(v => v.items).find(v => v.itemID === item.itemID)
                }
            }
        } catch (err) { console.log(err) }
        return order
    } catch (error) {
        console.log(error)
        return null
    }

}

// get_order_detail({ access_token: '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' }, '270322505-C6T1EEMDHAMDGN')

async function update_store_status({ access_token }, { status, duration }) {
    if (!access_token) {
        return {}
    }

    let data = status == "close" ? {
        "busyModeRequest": {},
        "fromState": "NORMAL",
        "tempPauseRequest": {
            "isUnpause": false,
            "tempPauseEnd": duration ? moment().add(duration, 'minute').toISOString() : moment().endOf('date').toISOString()
        },
        "toState": "TEMPPAUSED"
    } : {
        "busyModeRequest": {
            "busyModeFoodPrepareTime": 0,
            "option": 0
        },
        "fromState": "BUSY",
        "tempPauseRequest": {},
        "toState": "NORMAL"
    }

    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v1/merchant/status',
        headers: base_headers({ access_token }),
        data
    };

    let config2 = {
        method: 'put',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v1/un-pause',
        headers: base_headers({ access_token }),
        data: {}
    };

    const resp = await axios(config);
    if (status === 'open') {
        config.data.fromState = 'TEMPPAUSED' // BUSY
        await axios(config).catch(err => console.error(err));
        await axios(config2).catch(err => console.error(err));
    }
    console.log(resp.data)
    return resp.data
}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v3/open-status',
        headers: base_headers({ access_token }),
    };

    try {
        const resp = await axios(config);
        return resp.data?.isOpen === true && resp.data?.isMexInBusyMode === false
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ access_token }) {
    if (!access_token) {
        return []
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v2/merchants',
        headers: base_headers({ access_token }),
    };
    try {
        const resp = await axios(config);
        return resp.data.merchant.openingHours
    } catch (error) {
        return []
    }

}

async function get_special_hour({ access_token }) {
    if (!access_token) {
        return null
    }

    try {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://api.grab.com/food/merchant/v2/merchants',
            headers: base_headers({ access_token }),
        };

        const resp = await axios(config);
        return resp.data.merchant.specialOpeningHours
    } catch (error) {
        return []
    }
}


async function update_opening_hour({ access_token }, working_hours) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/opening-hours`,
        headers: base_headers({ access_token }),
        data: { openingHours: working_hours, force: false },
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_menu({ access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v2/menu`,
        headers: base_headers({ access_token }),
    };

    try {
        const resp = await axios(config);
        return {
            categories: resp.data.categories,
            option_categories: resp.data.modifierGroups,
        }
    } catch (error) {
        console.log(error)
        return {
            categories: [],
            option_categories: [],
        }
    }
}

async function get_mart_menu({ access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/mart-menu`,
        headers: base_headers({ access_token, is_mart: true }),
    };

    try {
        const resp = await axios(config);
        return {
            categories: resp.data.sections.flatMap(v => v.departments),
            option_categories: resp.data.modifierGroups,
        }

    } catch (error) {
        console.log(error)
        return {
            categories: [],
            option_categories: [],
        }
    }

}
// get_mart_menu({ access_token: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" })

async function update_menu_item({ access_token }, data) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/items/available-status`,
        headers: base_headers({ access_token }),
        data,
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_store({ access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v2/merchants`,
        headers: base_headers({ access_token }),
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}
// get_store({ access_token: '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' })

// status: open or close
async function set_special_open_status({ access_token }, { name, from, to, status }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v2/special-opening-hour`,
        headers: base_headers({ access_token }),
        data: {
            "specialOpeningHours": [
                {
                    "metadata": {
                        "description": name
                    },
                    "startDate": moment(from).format('YYYY-MM-DD'),
                    "endDate": moment(to).format('YYYY-MM-DD'),
                    "openingHours": {
                        "openPeriodType": "ClosedAllDay"
                    },
                }
            ],
            "shouldCheckPendingOrder": true
        }
    };

    try {
        const resp = await axios(config);
        console.log(resp.data)
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function delete_special_open_status({ access_token }, { name }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v2/merchants',
        headers: base_headers({ access_token }),
    };

    const resp = await axios(config);

    const new_special_times = resp.data.merchant.specialOpeningHours.filter(v => v.metadata.description !== name);
    let config2 = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v2/special-opening-hour`,
        headers: base_headers({ access_token }),
        data: {
            "shouldCheckPendingOrder": true,
            "specialOpeningHours": new_special_times
        }
    };

    try {
        const resp2 = await axios(config2);
        console.log(resp2.data)
        return resp2.data
    } catch (error) {
        console.log(error)
        return null
    }

}

async function delete_all_special_open_status({ access_token }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.grab.com/food/merchant/v2/merchants',
        headers: base_headers({ access_token }),
    };

    const resp = await axios(config);

    let config2 = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v2/special-opening-hour`,
        headers: base_headers({ access_token }),
        data: {
            "shouldCheckPendingOrder": true,
            "specialOpeningHours": []
        }
    };

    try {
        const resp2 = await axios(config2);
        console.log(resp2.data)
        return resp2.data
    } catch (error) {
        console.log(error)
        return null
    }

}


async function get_finance_list({ access_token }, { from, to }) {
    const result = []

    if (!access_token) {
        return result
    }

    const from_format = moment(from).format('YYYY-MM-DD')
    const to_format = moment(to).format('YYYY-MM-DD')

    try {
        let limit = 50
        let offset = 0
        while (true) {
            const resp = await axios({
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://merchant.grab.com/mex/finances/v2/transactions?from=${from_format}&to=${to_format}&currency=VND&limit=${limit}&offset=${offset}`,
                headers: web_base_headers({ access_token }),
            });
            if (resp.data?.data?.results) {
                result.push(...resp.data.data.results)
                offset += limit
                continue
            }
            break
        }

    } catch (error) {
        console.log(error)
    }
    return result
}

// get_finance_list({
//     access_token: '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
// }, {
//     from: '2024-07-27T00:00:00Z',
//     to: '2024-07-27T00:00:00Z'
// })

async function get_finance_detail({ access_token }, { store_id, transaction_id }) {
    if (!access_token) {
        return null
    }

    if (!store_id || !transaction_id) {
        return null
    }

    // Get transaction detail
    try {
        const resp3 = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://merchant.grab.com/mex/finances/v2/stores/${store_id}/transactions/${transaction_id}`,
            headers: web_base_headers({ access_token }),
        });
        return resp3.data.data
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_order_finance({ access_token }, { order_id }) {
    if (!access_token) {
        return null
    }

    // Get transaction detail
    try {
        const resp = await axios({
            method: 'get',
            maxBodyLength: Infinity,
            url: 'https://merchant.grab.com/mex/finances/v2/transactions',
            headers: web_base_headers({ access_token }),
            params: {
                from: moment().format('YYYY-MM-DD'),
                to: moment().format('YYYY-MM-DD'),
                query_search: order_id,
                query_column: 'food_order_id',
                currency: 'VND'
            }
        });
        const transactions = resp.data.data.results
        if (transactions?.length > 0) {
            const resp2 = await axios({
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://merchant.grab.com/mex/finances/v2/stores/${transactions[0].store_id}/transactions/orders/${order_id}`,
                headers: web_base_headers({ access_token }),
                params: {
                    transaction_category: 'payment',
                    currency: 'VND'
                }
            });
            return resp2.data.data
        }
        return null
    } catch (error) {
        console.log(error)
        return null
    }
}

async function get_order_feedbacks({ access_token }, limit) {
    if (!access_token) {
        return null
    }

    let result = []
    let nextToken = ""
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/feedback/reviews`,
        headers: base_headers({ access_token }),
        params: {
            serviceType: 'DELIVERY',
            endDate: moment().toISOString(),
            nextToken: nextToken,
            rating: '',
            status: ''
        }
    };
    try {
        while (true) {
            const resp = await axios(config);
            result.push(...(resp.data?.reviews ?? []))
            if (resp.data.nextToken === "" || result.length > limit)
                break
            config.params.nextToken = resp.data.nextToken
        }
    } catch (error) {
        console.log(error)
    }
    return result
}

async function get_site_feedback_summary({ access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/feedback/overview`,
        headers: base_headers({ access_token }),
        params: {
            serviceType: 'DELIVERY',
        }
    };
    try {
        const resp = await axios(config);
        return resp.data.feedbackOverview
    } catch (error) {
        console.log(error)
    }
    return null
}


async function get_order_incident_list({ access_token }) {
    if (!access_token) {
        return null
    }

    let result = []
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.grab.com/food/merchant/v1/merchant/incidentlist?metric=300&page=1`,
        headers: base_headers({ access_token }),
        params: {
            metric: 300,
            page: 1,
        }
    };
    try {
        const resp = await axios(config);
        return resp.data.incidents
    } catch (error) {
        console.log(error)
    }
    return result
}

// get_finance_data({ access_token: '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' },
//     { from: '2024-02-01T00:00:00Z', to: '2024-03-20T00:00:00Z' })

module.exports = {
    base_headers,
    get_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    cancel_order,
    get_menu,
    get_mart_menu,
    update_store_status,
    get_open_status,
    get_opening_hour,
    get_special_hour,
    update_opening_hour,
    update_menu_item,
    get_store,
    set_special_open_status,
    delete_special_open_status,
    delete_all_special_open_status,
    get_order_finance,
    get_finance_list,
    get_finance_detail,
    get_order_feedbacks,
    get_site_feedback_summary,
    get_order_incident_list,
}