const xlsx = require('xlsx');
const _ = require('lodash');
const axios = require('../axios');
const moment = require('moment');
const crypto = require('crypto');
const { map_order } = require('./mapping');

async function get_mcn_list() {
    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://banhang.shopee.vn/api/v3/affiliateplatform/gql?q=GetMcnList',
        headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'content-type': 'application/json; charset=UTF-8',
            'cookie': 'SPC_F=506Zr0D4Qj3QivFXAlcB0mVFocFDREbC; REC_T_ID=e648bf76-f755-11ee-8ffc-1ef133d506f1; SPC_CLIENTID=NTA2WnIwRDRRajNRiigcwnzuyolhjeqg; _med=refer; _QPWSDCXHZQA=b8b5db78-fbac-4c3f-c7c2-dd5a0858aa17; REC7iLP4Q=0abd2a42-b722-4230-9279-d0263cec6a19; SPC_B_EC=T2szcDVCY2pwRklESzBpUOtupsUomBtw5q72QTSbTBL6YmVL2IOsIJdHuHtBxqgpgabauHHnkXIJuS13cjREZuIxMK/DSts4KRuAf5Sg2beVD56MoqvbnalbHjRPkXC5Rz9Y99Xxs2k6XRrnZNGIuYhr4b8dTqS/Oi0rtuxQRd5z2LGAZQcfE6UYl41rGNbqenrtyRJ/0GFewS0Pd8PVxQ==; SPC_B_SI=XY+XZgAAAABQSHdPNzk3d6FiBAAAAAAAYzRxdnJHb2M=; SPC_SI=XY+XZgAAAABQSHdPNzk3d6BiBAAAAAAAVDV6YUlvNGE=; _gcl_au=1.1.1154319951.1721272599; _fbp=fb.1.1721272601153.867102719191298663; AMP_TOKEN=%24NOT_FOUND; _gid=GA1.2.1728058779.1721272604; SPC_EC=.cEZEdUpaWGlId2NNcUE0N/4J6z3p9WkHXC5USKzmKWKu8f2t8YaZJwg2bap8p1h154c/Ug4BnNXEqMgQipSpaDE0WyH4sJD0Xi4rPTY4jQmjwm+hO1Iq1WVscAKbiiHwZr7W7DER8zAAke4Bz7gwWwDJL0AGvy0uPR18JbEpSdI3wi9AB5Stj77jF4Gb7+20e3avH+by8j2jnGVBAfKG0g==; SPC_ST=.cEZEdUpaWGlId2NNcUE0N/4J6z3p9WkHXC5USKzmKWKu8f2t8YaZJwg2bap8p1h154c/Ug4BnNXEqMgQipSpaDE0WyH4sJD0Xi4rPTY4jQmjwm+hO1Iq1WVscAKbiiHwZr7W7DER8zAAke4Bz7gwWwDJL0AGvy0uPR18JbEpSdI3wi9AB5Stj77jF4Gb7+20e3avH+by8j2jnGVBAfKG0g==; SPC_U=1230576997; SPC_R_T_ID=6Q3OmYhlVRC7D1iepAa/QqNNYDrLLRK8wS95GFErm37d3Ngnn58ESajoEHSjqC6lCh0viAHUVjKRyjdLie6K9YRJLT+fj1kK4TeLBrctG4PGxXIZHBWlZMSeIBt/QsMIVsFTo8wMSvFUyZYRvXLGzIAyPLclAzrExA/RQvdW1uo=; SPC_R_T_IV=RGZSelpsSTRUUGFjaG1oSw==; SPC_T_ID=6Q3OmYhlVRC7D1iepAa/QqNNYDrLLRK8wS95GFErm37d3Ngnn58ESajoEHSjqC6lCh0viAHUVjKRyjdLie6K9YRJLT+fj1kK4TeLBrctG4PGxXIZHBWlZMSeIBt/QsMIVsFTo8wMSvFUyZYRvXLGzIAyPLclAzrExA/RQvdW1uo=; SPC_T_IV=RGZSelpsSTRUUGFjaG1oSw==; SPC_CDS_CHAT=008d02f1-6c0c-4997-bac5-524b2982eb00; _hjSession_868286=eyJpZCI6IjIwOTMzY2M1LWFhNGMtNDMxZS1hMThkLTMzYzFlNjA0YzVmZSIsImMiOjE3MjEyNzI2MjEwMjEsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MX0=; _ga=GA1.1.1287047072.1721272604; _hjSessionUser_868286=eyJpZCI6ImJiYmQ2MGM1LTEwOTktNWNmMC1iNDEwLTY0NjFlNjQwN2FmOCIsImNyZWF0ZWQiOjE3MjEyNzI2MjEwMTUsImV4aXN0aW5nIjp0cnVlfQ==; SPC_SC_TK=53f0a56603d4b1ce933b43e3035ec741; SPC_SC_UD=1230576997; SPC_SC_SESSION=cbb4624d94356424c0f6632f38879305_1_1230576997; SPC_STK=L/o5UYgD7XAK/l9iUXfjpYjvEs+mnzLZqSkmoz62HQmVnBj4hJGx8w0RU5cS0wz6TiCkE081mJ5I5eYzaug9zINPlh5MB2TfbTd+t/KOe6o4iRFDb43bb7hNPlI5CwTD8KZ8gkhwiFxd/2IJyVsO+auwWqR/FIaQUxaGmOmFgYMq/OqbElqfoRFnOyBdnLgd/4aJyB1woNcySfZzaXG2GA==; SC_DFP=tOvelpnRIWaAzDnbZvVQfmrOgSWeSaNl; SPC_CDS=d526a4cf-65e0-482d-81e5-d6823078a92f; _ga_4GPP1ZXG63=GS1.1.1721272603.1.1.1721272647.16.0.0; CTOKEN=RtNsHES0Ee%2BWbhohxOLpeg%3D%3D; web-seller-affiliate-region=vn; MOCK_TYPE=; language=vi; web-seller-affiliate_language=vi; shopee_webUnique_ccd=aqb%2BuwVl9%2ByigS%2BftGcWgw%3D%3D%7Cv8xFn5zrxYmhLSI9%2FORKeNBpuCRi70rSTxQMaYpVsMTquXQn6gE9joSwYAwu4InkwlNTIIGAR2Y%3D%7CXgzU8AvVnfO7AnXd%7C08%7C3; ds=3b59f1f028a93ee9ebd9b144d93a9277',
            'origin': 'https://banhang.shopee.vn',
            'referer': 'https://banhang.shopee.vn/portal/web-seller-affiliate/mcn',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
        },
        data: {
            "operationName": "GetMcnList",
            "query": "\n      query GetMcnList(\n        $order: Int\n        $mcnName: String\n        $offset: Long\n        $limit: Long\n      ) {\n        GetMcnList (\n          order: $order\n          mcnName: $mcnName\n          offset: $offset\n          limit: $limit\n        ) {\n          mcnPortrait {\n            mcnId\n            mcnName\n            categoryIds\n            affiliateCount\n            clickCount\n            orderCount\n            gmv\n            # clickCountGrowth\n            # orderCountGrowth\n            # gmvGrowth\n            mcnLogoImg\n            # companyProfileDocPath\n            # companyDescription\n            phoneNumber\n            email\n            lastUpdateTime\n            topAffiliateMetric {\n              affiliateId\n              creatorProfile {\n                # affiliateId\n                username\n                portrait\n                # country\n                # city\n                # audienceGender {\n                #   genderType\n                #   ratio\n                # }\n                # audienceAge {\n                #   ageType\n                #   ratio\n                # }\n                # registrationTime\n                # top20PopularContents {\n                #   platformType\n                #   accountId\n                #   postId\n                #   postTime\n                #   coverUrl\n                #   mediaUrl\n                #   commentCount\n                #   likeCount\n                #   viewCount\n                #   shareCount\n                #   caption\n                # }\n                # socialMediaDetails {\n                #   platformType\n                #   username\n                #   portrait\n                #   followerCount\n                #   accountLink\n                #   accountId\n                #   socialMediaUserName\n                # }\n                # popularSocialMedia {\n                #   platformType\n                #   followerCount\n                # }\n                # top3PurchaseCategoryIds\n                isSpecificKolFlag\n                # totalOrder\n                # totalClick\n                totalFollowerCount\n                isFreeSampleFlag\n                # lastUpdateTime\n                # topSellingProducts {\n                #   affiliateId\n                #   userId\n                #   itemId\n                #   itemName\n                #   shopId\n                #   shopName\n                #   categoryId\n                #   categoryName\n                #   clicks\n                #   soldQuantity\n                #   minCommRate\n                #   maxCommRate\n                #   minAmsCommRate\n                #   maxAmsCommRate\n                #   minSoldPrice\n                #   maxSoldPrice\n                #   gmv\n                #   dateScope\n                #   rank\n                #   imageUrl\n                #   pdpUrl\n                # }\n                shopeeNickname\n                isPppKolFlag\n                affiliateUserId\n                isCreatorMarketplaceFlag\n                # mcnId\n              }\n              # salesMetrics {\n              #   clickCount\n              #   clickCountGrowth\n              #   soldCount\n              #   soldCountGrowth\n              #   orderCount\n              #   orderCountGrowth\n              #   gmv\n              #   gmvGrowth\n              #   roi\n              #   roiGrowth\n              #   orderCountRange\n              # }\n            }\n          }\n          hasMore\n        }\n      }\n    ",
            "variables": {
                "order": 3,
                "mcnName": "",
                "offset": "0",
                "limit": "100"
            }
        }
    };

    try {
        const resp = await axios(config);
        const mcn_list = resp.data.data.GetMcnList.mcnPortrait
        return mcn_list
    } catch (error) {
        console.log(error.message)
        return null
    }

}

// get_mcn_list()


module.exports = {
    get_mcn_list,
}