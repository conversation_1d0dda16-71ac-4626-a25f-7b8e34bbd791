const xlsx = require('xlsx');
const _ = require('lodash');
const axios = require('../axios');
const moment = require('moment');
const crypto = require('crypto');
const { map_order } = require('./mapping');
const { Brand, Site } = require('../database');
const { text_compare } = require('../helper');

const skip_call_api = () => moment().hour() < 8 || moment().hour() > 21;


function generate_signature_base_string(method, url, body = '') {
    const base_string = `${method.toUpperCase()}|${url}|${body}`;
    console.log('Base String: ', base_string)
    return base_string
}

function calculate_signature(key, baseString) {
    const decodedKey = Buffer.from(key, 'hex');
    const hash = crypto.createHmac('sha256', decodedKey).update(baseString, 'utf-8').digest('hex');
    console.log('Hash String: ', hash)
    return hash;
}

const API_CONFIG = process.env.NODE_ENV === 'prod' ? {
    app_id: 10041,
    api_signature: '188c7c33cfeb12210cff1baa6a8ebfc041c00a21b781a1b9c99f776d8fdd7f2a',
    api_url: 'https://spf-api.nexpos.io',
    hostname: 'gexternalapi.deliverynow.vn',

} : {
    app_id: 10041,
    api_signature: '188c7c33cfeb12210cff1baa6a8ebfc041c00a21b781a1b9c99f776d8fdd7f2a',
    api_url: 'https://spf-api-dev.nexpos.io',
    hostname: 'gqaexternalapi.test.now.vn'
}

const base_headers = (config, { app_id, signature }) => {
    const headers = {
        'Content-Type': 'application/json',
        "X-Foody-App-Id": app_id,
        "X-Foody-Api-Version": 1,
        "X-Foody-Request-Id": 1,
        "X-Foody-Language": "vi",
        "X-Foody-Country": "VN",
    }

    const url_obj = new URL(config.url);
    headers['Authorization'] = `Signature ${calculate_signature(signature,
        generate_signature_base_string(
            config.method,
            config.url.replace(url_obj.hostname, API_CONFIG.hostname),
            JSON.stringify(config.data)
        )
    )}`
    return headers
}

async function get_store({ nexpos_site_id }) {
    const config = {
        method: 'POST',
        url: `${API_CONFIG.api_url}/s2s/restaurant/get_restaurant_info`,
        data: {}
    }
    if (nexpos_site_id) {
        config.data.partner_restaurant_id = nexpos_site_id
    } else {
        config.data.restaurant_id = site_id
    }

    config.headers = base_headers(config, {
        app_id: API_CONFIG.app_id,
        signature: API_CONFIG.api_signature,
    })

    try {
        const resp = await axios(config);
        return resp.data.reply
    } catch (error) {
        console.log(error.message)
        return null
    }
}

async function get_order_list_v2({ site_id, nexpos_site_id }) {
    const result = {
        success: true,
        data: {
            PRE_ORDER: [],
            PENDING: [],
            DOING: [],
            FINISH: [],
            CANCEL: [],
        }
    }

    if (skip_call_api()) {
        return result
    }
    try {
        const current_date = moment().format('YYYY-MM-DD');
        const filter_data = {
            PENDING: {
                status: 1,
            },
            DOING: {
                status: 4,
            },
            FINISH: {
                status: 2,
            },
            CANCEL: {
                status: 3,
            }
        }
        for (const [status, filter] of Object.entries(filter_data)) {
            const config = {
                method: 'POST',
                url: `${API_CONFIG.api_url}/s2s/order/get_list`,
                data: {
                    from_date: current_date,
                    status: filter.status,
                    limit: 100,
                    offset: 0,
                }
            }
            if (nexpos_site_id) {
                config.data.partner_restaurant_id = nexpos_site_id
            } else {
                config.data.restaurant_id = site_id
            }
            config.headers = base_headers(config, {
                app_id: API_CONFIG.app_id,
                signature: API_CONFIG.api_signature,
            })
            const resp = await axios(config)
            result.data[status] = resp.data.reply.orders
        }
        return result
    } catch (err) {
        console.log(err.message)
        result.success = false
        return result
    }
}

// get_order_list_v2({ site_id: 'NNDD_SA309' })


async function get_order_list_by_duration({ access_token, site_id, nexpos_site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: [],
        }
    }

    if (!access_token) {
        return result
    }

    let next_item_id = ""

    try {
        while (true) {
            const data = {
                order_filter_type: 40,
                next_item_id: next_item_id,
                request_count: 50,
                from_time: from.unix(),
                to_time: to.unix(),
                sort_type: 12
            }

            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://gmerchant.deliverynow.vn/api/v5/order/get_list',
                headers: base_headers({ site_id, access_token }),
                data: data
            };

            const resp = await axios(config);

            result.data.FINISH.push(...resp.data.data.orders.filter(v => v.order_status !== 8))
            result.data.CANCEL.push(...resp.data.data.orders.filter(v => v.order_status === 8))
            next_item_id = resp.data.data.next_item_id
            if (!resp.data.data.has_more)
                break
        }

    } catch (error) {
        result.success = false
        console.log(error.message)
    }
    return result
}

async function get_order_detail({ site_id, nexpos_site_id }, order_id) {
    const config = {
        method: 'POST',
        url: `${API_CONFIG.api_url}/s2s/order/get_details`,
        data: {
            order_code: order_id,
        }
    }
    if (nexpos_site_id) {
        config.data.partner_restaurant_id = nexpos_site_id
    } else {
        config.data.restaurant_id = site_id
    }
    config.headers = base_headers(config, {
        app_id: API_CONFIG.app_id,
        signature: API_CONFIG.api_signature,
    })

    try {
        const resp = await axios(config);
        return resp.data.reply
    } catch (error) {
        console.log(error.message)
        return null
    }

}

// get_order_detail({ nexpos_site_id: 'GROWPLUS_SA302' }, '17064-334438734')


async function sync_menu({ site_id, nexpos_site_id }) {
    const config = {
        method: 'POST',
        url: `${API_CONFIG.api_url}/s2s/menu/sync`,
        data: {}
    }
    if (nexpos_site_id) {
        config.data.partner_restaurant_id = nexpos_site_id
    } else {
        config.data.restaurant_id = site_id
    }
    config.headers = base_headers(config, {
        app_id: API_CONFIG.app_id,
        signature: API_CONFIG.api_signature,
    })

    try {
        const resp = await axios(config);
        console.log(resp.config.curlCommand)
        console.log(resp.data)
        return resp.data
    } catch (error) {
        console.log(error.message)
        return null
    }
}

// sync_menu({ nexpos_site_id: 'NNDD_SA008' })

async function sync_promotion({ site_id, nexpos_site_id }, all_menu_items, promotions) {
    const config = {
        method: 'POST',
        url: `${API_CONFIG.api_url}/api/price_slash/set_discount`,
        data: {
            "status": 1,  // CommonStatus.ACTIVE
            "start_time": moment(promotions[0].start_time, 'YYYY/MM/DD').format('DD/MM/YYYY') + ' 08:00',
            "end_time": moment(promotions[0].end_time, 'YYYY/MM/DD').format('DD/MM/YYYY') + ' 21:00',
            "apply_for": 1,  // PriceSlashDiscountApplyFor CUSTOM = 1 ALL_MENU = 2
            "slash_discount_type": 1,  // PriceSlashDiscountType.CUSTOM = 1 FIX_PERCENT = 2
            "min_merchant_discount_value": 5000,
            "discount_items": promotions.map(v => {
                const menu_item = all_menu_items.find(s => text_compare(s.name, v.name))
                if (!menu_item) { return null }
                return {
                    partner_dish_id: menu_item.id,
                    discount_price: Number(v.sell_price),
                    status: 1,
                }
            }).filter(v => v?.discount_price > 5000).slice(0, 20),
            "daily_limit_per_item": 10, // đây là số lượng mỗi món/ngày (VD: set daily_limit_per_item = 100, tức là với mỗi món trong chương trình mỗi ngày chỉ mua được số lượng = 100, có thể mua SL = 100 trên 1 đơn hàng hoặc nhiều đơn hàng)
            "daily_limit_per_user": 1, // số lượng mỗi món 1 user có thể mua / ngày
            "limit_unique_item_per_order": 10, // số lượng khác nhau món được áp dụng giá giảm / đơn hàng
            "limit_per_order": 1, // số lượng mỗi món mà user có thể mua / đơn hàng
        }
    }
    if (nexpos_site_id) {
        config.data.partner_restaurant_id = nexpos_site_id
    } else {
        config.data.restaurant_id = site_id
    }
    config.headers = base_headers(config, {
        app_id: API_CONFIG.app_id,
        signature: API_CONFIG.api_signature,
    })

    try {
        const resp = await axios(config);
        // console.log(resp.config.curlCommand)
        console.log(resp.data)
        return resp.data
    } catch (error) {
        console.log(error.message)
        return null
    }
}

module.exports = {
    get_store,
    base_headers,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    sync_menu,
    sync_promotion,
}