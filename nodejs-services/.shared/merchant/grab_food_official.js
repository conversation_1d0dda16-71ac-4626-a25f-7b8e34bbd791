const axios = require('../axios');
const redis = require('../redis')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');

const base_headers = ({ access_token }) => {
    return {
        'Authorization': 'Bearer ' + access_token,
        'Content-Type': 'application/json',
    }
}

async function get_token(user_name, password) {
    try {
        const master_token_resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://api.grab.com/grabid/v1/oauth2/token',
            headers: {
                'Content-Type': 'application/json'
            },
            data: {
                "client_id": user_name,
                "client_secret": password,
                "grant_type": "client_credentials",
                "scope": "food.partner_api"
            }
        })
        return {
            access_token: master_token_resp.data.access_token
        }
    } catch (err) {
        console.log(err)
    }
    return null
}



module.exports = {
    get_token,
}