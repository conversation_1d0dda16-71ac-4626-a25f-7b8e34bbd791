const axios = require('../axios');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const qs = require('qs');
const _ = require('lodash');

const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;


const base_headers = () => {
    return {
        'content-type': 'application/json',
        'host': 'merchant-api.villship.com',
        'user-agent': 'Dart/3.3 (dart:io)',
        // 'x-province': '2',
        // 'x-sub-province': '2'
    }
}

async function get_token(username, password) {
    if (!password) {
        return null
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://merchant-api.villship.com/api/auth/login',
            headers: base_headers(),
            data: {
                "phone": username,
                "password": password,
                "device_id": "dBw-8RZcSd6uUnNXUQcu1E:APA91bH__O1K8YoXujlnoH4Ss8OybLQSuhbpWEIZ2JX_tbMgt0gF3pLUPkiFnn_W3iJSgtitTQH8EPWESIOnWVpZM7Epu7c-hi9kUP-Fi5B0nv0hsNNOdBD1leJVWooG8FB9Bdvpovot",
                "message": null
            }
        }

        const resp = await axios(config);
        const access_token = resp.data.api_key

        let config2 = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://merchant-api.villship.com/api/global-restaurants/restaurants`,
            headers: base_headers(),
            params: {
                "api_token": access_token,
            }
        };
        const store_resp = await axios(config2)
        const site_id = store_resp.data.data[0].restaurants[0].id
        return {
            access_token,
            site_id: site_id
        }

    } catch (error) {
        console.log(error.message)
        return null
    }
}


async function get_order_list_v2({ site_id, access_token }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: []
        }
    }

    if (!access_token || skip_call_api()) {
        return result
    }

    try {
        const filter_data = {
            "DOING": "3",
            "PICK": "4",
            "FINISH": "5",
            "CANCEL": "6",
        }

        for (const [status, filter] of Object.entries(filter_data)) {
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://merchant-api.villship.com/api/v2/restaurants/${site_id}/orders`,
                headers: base_headers(),
                params: {
                    "api_token": access_token,
                    "with": "user;foodOrders;foodOrders.food;orderStatus;foodOrders.food.extras",
                    "statusId": filter,
                    "limit": "20",
                    "page": "1",
                    "orderBy": "id",
                    "from_date": moment().format('YYYY-MM-DD'),
                    "to_date": moment().format('YYYY-MM-DD')
                }
            };

            let orders = []
            try {
                const resp = await axios(config);
                orders = resp.data.order_info.filter(v => moment(v.created_at).isSame(moment(), 'day'))
                result.data[status] = orders
            } catch (error) {
                console.log(error)
                result.success = false
                return result
            }
        }

        return result

    } catch (error) {
        console.log(error)
        result.success = false
        return result
    }
}


async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: []
        }
    }

    if (!access_token || skip_call_api()) {
        return result
    }

    try {
        const filter_data = {
            "DOING": "3",
            "PICK": "4",
            "FINISH": "5",
            "CANCEL": "6",
        }

        for (const [status, filter] of Object.entries(filter_data)) {
            let config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://merchant-api.villship.com/api/v2/restaurants/${site_id}/orders`,
                headers: base_headers(),
                params: {
                    "api_token": access_token,
                    "with": "user;foodOrders;foodOrders.food;orderStatus;foodOrders.food.extras",
                    "statusId": filter,
                    "limit": "200",
                    "page": "1",
                    "orderBy": "id",
                    "from_date": moment(from).format('YYYY-MM-DD'),
                    "to_date": moment(to).format('YYYY-MM-DD')
                }
            };

            let orders = []
            try {
                const resp = await axios(config);
                orders = resp.data.order_info.filter(v => moment(v.created_at).isSame(moment(), 'day'))
                result.data[status] = orders
            } catch (error) {
                console.log(error)
                result.success = false
                return result
            }
        }

        return result

    } catch (error) {
        console.log(error)
        result.success = false
        return result
    }

}


async function confirm_order({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }
    const result = {
        success: true,
        message: "",
    }
    return result
}

async function cancel_order({ site_id, access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    if (!access_token) {
        return null
    }
    const result = {
        success: true,
        message: "",
    }
    return result
}

async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://merchant-api.villship.com/api/v2/restaurants/${site_id}/orders/${order_id}`,
        headers: base_headers({ access_token }),
        params: {
            "api_token": access_token,
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.data
    } catch (err) {
        console.log(err)
    }
    return null
}

async function update_store_status({ site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return {}
    }
    let data = {
        operating_status: status === "close" ? "tempt_close" : "open"
    }

    if (duration) {
        data.reopen_time = moment().add(duration, 'minutes').format('YYYY-MM-DD HH:mm:ss')
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://merchant-api.villship.com/api/v2/restaurants/${site_id}/operating-status`,
        headers: base_headers({ access_token }),
        params: {
            "api_token": access_token,
        },
        data: { "operating_status": "open" }
    };

    const resp = await axios(config);
    return resp.data
}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    try {
        const store = await get_store({ site_id, access_token })
        return store.is_open
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ site_id, access_token }) {
    if (!access_token) {
        return []
    }

    try {
        const store = await get_store({ site_id, access_token })
        return store.businessHours
    } catch (err) {
        console.log(err)
    }
    return {}
}

async function update_opening_hour({ site_id, access_token }, working_hours) {
    if (!access_token) {
        return null
    }

    try {
        // TODO
    } catch (err) {
        console.log(err)
    }
    return []
}

async function get_menu({ site_id, access_token }, data) {
    const result = {
        categories: [],
        option_categories: [],
    }

    if (!access_token) {
        return result
    }
    // TODO:
    return result
}

async function update_menu_item({ site_id, access_token }, data) {
    if (!access_token) {
        return null
    }

    return null
}

async function get_store({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://merchant-api.villship.com/api/v2/restaurants/${site_id}`,
        headers: base_headers({ access_token }),
        params: {
            "api_token": access_token,
        },
    };

    try {
        const resp = await axios(config);
        return resp.data.data
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_order_feedbacks({ site_id, access_token }, limit) {
    let result = []

    try {
        const resp = await axios.get('https://merchant-api.villship.com/api/restaurants/8006/reviews', {
            params: {
                api_token: 'iitpGqkZFsKqlijJmXzjBxhiCaogNQl7fT9R7WWhtW3di0gCP7CSUxm6wG8q',
                limit: 1000,
                page: 1,
                has_image: 1,
                orderBy: 'id',
                sortedBy: 'desc'
            }
        });
        result = resp.data.ratings
    } catch (err) {
        console.log(err)
    }
    return result
}


module.exports = {
    base_headers,
    get_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    cancel_order,
    update_store_status,
    get_open_status,
    get_opening_hour,
    update_opening_hour,
    get_menu,
    update_menu_item,
    get_store,
    get_order_feedbacks,
}