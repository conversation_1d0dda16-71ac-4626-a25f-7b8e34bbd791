const axios = require('../axios');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;

console.log(moment().hour())

const base_headers = ({ site_id, access_token }) => {
    return {
        'content-type': 'application/json',
        'accept': 'application/json',
        'accept-language': 'vi',
        'x-client-version': '0.64.4',
        'x-client-platform': 'Android',
        // 'if-modified-since': '<PERSON><PERSON>, 14 Mar 2023 03:07:35 GMT',
        'x-access-token': access_token || null,
        'user-agent': 'okhttp/4.9.1'
    }
}

async function get_token(username, password) {
    if (!password) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.baemin.vn/v3/auth/managers/login',
        headers: base_headers({}),
        data: { "email": username, "password": password }
    };
    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error.message)
        return null
    }

}

async function get_order_list({ site_id, access_token }, status) {
    if (!access_token || skip_call_api()) {
        return []
    }

    if (status === 'PRE_ORDER') {
        return []
    }

    try {
        const current_date = moment().format('YYYY-MM-DD');
        var config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}/orders?date=${current_date}&page=1&status=COMPLETED%2CCANCELED%2CDELIVERED%2CPICKED%2CREADY_TO_PICK`,
            headers: base_headers({ access_token })
        };
        const resp = await axios(config);
        const docs = resp.data.docs
        if (status === 'PENDING') {
            return docs.filter(v => v.status === 'READY_TO_PICK')
        }
        if (status === 'DOING') {
            config.url = `https://api.baemin.vn/v3/stores/${site_id}/new-orders`
            const resp_2 = await axios(config);
            const docs_2 = resp_2.data.docs
            // Will approve in our service
            // return docs.filter(v => v.status === 'READY_TO_PICK') 
            return docs_2
        }
        if (status === 'FINISH')
            return docs.filter(v => ["PICKED", "DELIVERED", "COMPLETED"].includes(v.status))
        if (status === 'CANCEL')
            return docs.filter(v => ["CANCELED"].includes(v.status))
    } catch (error) {
        console.log(error.message)
    }
    return []
}

async function get_order_list_by_duration({ site_id, access_token }, { from, to }) {
    return {
        success: true,
        data: {}
    }
    if (!access_token) {
        return []
    }
    let result = []
    try {
        for (let date = from.clone(); date.isSameOrBefore(to); date.add(1, 'day')) {
            const current_date = date.format("YYYY-MM-DD");
            var config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `https://api.baemin.vn/v3/stores/${site_id}/orders?date=${current_date}&page=1&status=COMPLETED,CANCELED,DELIVERED,PICKED,READY_TO_PICK&limit=100`,
                headers: base_headers({ access_token })
            };

            try {
                const resp = await axios(config)
                const docs = resp.data.docs.filter(v => ["PICKED", "DELIVERED", "COMPLETED", "CANCELED"].includes(v.status))
                result.push(...docs)

            } catch (error) {
                console.log(error)
            }
        }
    } catch (error) {
        console.log(error.message)
    }
    return result
}

async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }

    var config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.baemin.vn/v3/stores/${site_id}/orders/${order_id}`,
        headers: base_headers({ access_token })
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error.message)
    }
    return null

}

async function confirm_order({ site_id, access_token }, order_id) {
    // if (!access_token) {
    //     return null
    // }

    // // Baemin don't need confirm
    // if (status === "confirm") {
    //     return {}
    // }

    // if (status === "reject") {
    //     let config = {
    //         method: 'put',
    //         maxBodyLength: Infinity,
    //         url: `https://api.baemin.vn/v3/stores/${site_id}/orders/${order_id}/cancel`,
    //         headers: base_headers({ access_token }),
    //         data: { "cancelReason": "Quán đóng cửa" }
    //     };
    //     const resp = await axios(config);
    //     return resp.data
    // }
    return {}
}

async function update_store_status({ site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return null
    }

    duration = duration ? moment().add(duration, 'minute').valueOf() : moment().endOf('day').valueOf()
    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: 'https://api.baemin.vn/v3/stores/' + site_id,
        headers: base_headers({ access_token }),
        data: { pauseCanOrderTime: status === 'close' ? duration : null }
    };

    const resp = await axios(config);
    return resp.data
}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.baemin.vn/v3/stores/${site_id}`,
        headers: base_headers({ access_token })
    };

    try {
        const resp = await axios(config);
        return resp.data?.canOrder === true && resp.data?.isOpeningHours === true
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ site_id, access_token }) {
    if (!access_token) {
        return []
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.baemin.vn/v3/stores/${site_id}`,
        headers: base_headers({ access_token })
    };

    const resp = await axios(config);
    return resp.data.openingHours
}

async function update_opening_hour({ site_id, access_token }, data) {
    if (!access_token) {
        return null
    }

    console.log(JSON.stringify(data, null, 2))
    let config = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `https://api.baemin.vn/v3/stores/${site_id}`,
        headers: base_headers({ access_token }),
        data
    };

    const resp = await axios(config);
    return resp.data

}

async function get_menu({ site_id, access_token }) {
    if (!access_token) {
        return {
            categories: [],
            option_categories: [],
        }
    }

    try {
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.baemin.vn/v3/stores/${site_id}`,
            headers: base_headers({ access_token })
        };
        const resp = await axios(config);
        return {
            categories: resp.data.sections,
            option_categories: resp.data.dishes,
        }
    } catch (error) {
        console.log(error.message)
        return {
            categories: [],
            option_categories: [],
        }
    }

}

async function get_store({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }


    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.baemin.vn/v3/stores/${site_id}`,
        headers: base_headers({ access_token })
    };

    try {
        const resp = await axios(config);
        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

module.exports = {
    base_headers,
    get_token,
    get_order_list,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    update_store_status,
    get_open_status,
    get_opening_hour,
    update_opening_hour,
    get_menu,
    get_store,
}