const axios = require('../axios');
const moment = require('moment');
const _ = require('lodash');
const { Site, Hub, Order, BrandMenu, HubStock, HubStockHistory } = require('../database')
const { send_slack_message } = require('../slack')
const helper = require('../helper')

let nexpos = {}


nexpos.get_menu_items = async ({ shop, items }) => {
    // Group items by item_code and sum their quantities
    const unique_items = _.chain(all_items)
        .groupBy('item_code')
        .map((items, item_code) => ({
            item_code,
            item_name: items[0].item_name,
            unit: items[0].unit,
            quantity: _.sumBy(items, 'quantity'),
            shop_code: items[0].shop_code,
            shop_name: items[0].shop_name,
        }))
        .value();

    for (const item of items) {
        if (unique_items.some((v) => v.item_code === item)) {
            continue;
        }
        unique_items.push({
            item_code: item,
            item_name: '<PERSON>hông có trong kho',
            unit: '',
            quantity: 0,
            shop_code: shop,
            shop_name: '',
        })
    }
    return unique_items;

};


nexpos.sync_an_order = async (order) => {
    const site = await Site.findById(order.site_id)
    const hub = await Hub.findById(site.hub_id)

    const brand_menu = await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, {
        $setOnInsert: {
            categories: [],
            option_categories: [],
        }
    }, { upsert: true, new: true }).lean()
    const data_mapping = order.data_mapping

    const dishes_resp = helper.add_stock_to_dishes(data_mapping.dishes, brand_menu)
    if (!dishes_resp.success) {
        const message = `[Thất bại]mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, Lỗi: ${dishes_resp.error_messages.join(', ')}`
        return {
            success: false,
            message: message,
            data: {
                request: data_mapping,
                response: {},
            },
        }
    }

    let dishes = dishes_resp.dishes.flatMap(v => v.stocks).filter(v => v.quantity > 0).map(v => ({
        "item_code": v.code,
        "price": v.unit_price,
        "qty": v.quantity,
    }))

    if (dishes.length === 0) {
        const message = `[Thất bại] Mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} sai thông tin món`
        return {
            success: false,
            message: message,
            data: {
                request: {},
                response: {},
            },
        }
    }

    for (const dish of dishes) {
        const stock = await HubStock.findOne({ hub_id: site.hub_id, code: dish.item_code })
        if (!stock) {
            const message = `[Thất bại] Item code: ${dish.item_code} không tồn tại trong kho`
            return {
                success: false,
                message: message,
                data: {
                    request: {},
                    response: {},
                },
            }
        }
    }


    try {
        const hub_stocks = await HubStock.find({ hub_id: site.hub_id, code: dishes.map(v => v.item_code) })
        for (const hub_stock of hub_stocks) {
            const from_quantity = hub_stock.quantity
            const dish = dishes.find(v => v.item_code === hub_stock.code)
            hub_stock.quantity -= dish.qty
            await hub_stock.save()

            await HubStockHistory.create({
                hub_id: site.hub_id,
                code: hub_stock.code,
                from_quantity: from_quantity,
                to_quantity: hub_stock.quantity,
                updated_type: 'order',
                updated_order_id: order.order_id,
            })
        }

        const message = `[Thành công] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, đồng bô thành công.`
        console.log(message)

        return {
            success: true,
            data: {
                request: { hub: _.pick(hub, ['_id', 'name']), dishes: dishes_resp.dishes },
                response: { success: true },
                stock_request: { shops: [hub.code], items: dishes.map(v => v.item_code) },
                stock_response: []
            },
            message: message
        }
    } catch (error) {
        if (error.response) {
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: error.response.data,  // This contains the response body
                },
            };
        } else if (error.request) {
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: No response received`,
                data: {
                    request: order_sync_req_data,
                    response: {},
                },
            };
        } else {
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: {},
                },
            };
        }
    }
}

// (async () => {
//     const order = await Order.findOne({ order_id: '302149395-C6A2GNACDFV2CE' })
//     nexpos.sync_an_order(order)
// })();

module.exports = nexpos