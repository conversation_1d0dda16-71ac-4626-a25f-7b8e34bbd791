const axios = require('../axios');
const moment = require('moment-timezone');
const _ = require('lodash');
const csv = require('csvtojson');


const base_headers = () => {
    return {
        'Origin': 'https://pos.pancake.vn',
        'Referer': 'https://pos.pancake.vn/',
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
    }
}

const get_phone_number_report = async ({ access_token }, phone_number) => {
    if (phone_number?.length < 10 || phone_number?.length > 11) {
        return {
            order_fail: 0,
            order_success: 0,
            warnings: [],
        }
    }

    const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://pos.pages.fm/api/v1/shops/385684/orders/bad_report_info',
        params: {
            phone_number: phone_number,
            access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiTmd1eWVuICIsImV4cCI6MTc1MzIzMzMzMSwiYXBwbGljYXRpb24iOjEsInVpZCI6IjhhN2EyZTU3LTdiYjgtNGE2OS04YzFmLTE3MmNlNGM0NDMxYSIsInNlc3Npb25faWQiOiJVaHZqejBBdVJOQXlPQmpHWGpGVFdIYjUzU0RZRlJOMlhabXNTWHZNeEpNIiwiaWF0IjoxNzQ1NDU3MzMxLCJmYl9pZCI6IjE3OTU2NzE3ODA2NzU2NjciLCJsb2dpbl9zZXNzaW9uIjpudWxsLCJmYl9uYW1lIjoiTmd1eWVuICJ9.b-EU0DByxNLeG8UoZjK8lvpwgCbQx4hOQ4ktLXJVImM',
        },
        headers: base_headers()
    }

    const resp = await axios(config)
    if (Object.keys(resp.data?.data?.reports_by_phone || {}).length === 0) {
        return {
            order_fail: 0,
            order_success: 0,
            warnings: [],
        }
    }
    const report = resp.data.data.reports_by_phone[Object.keys(resp.data.data.reports_by_phone)[0]]
    return {
        order_fail: report.order_fail,
        order_success: report.order_success,
        warnings: resp.data.data.warning_phone_number?.map(v => v.reason) ?? [],
    }
}

module.exports = {
    get_phone_number_report
}