const axios = require('../axios');
const moment = require('moment-timezone');
const _ = require('lodash');
const { base_headers } = require('./grab')
const { text_slugify } = require('../helper');


const upload_image = async function ({ site_id, access_token }, image_url) {
    try {
        if (!image_url)
            return ""

        const response = await axios.get(image_url, { responseType: 'arraybuffer' });
        const imageBuffer = Buffer.from(response.data, 'binary');
        const base64String = imageBuffer.toString('base64');

        const resp_upload = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/upload-file`,
            headers: base_headers({ site_id, access_token }),
            data: { "file": { "data": base64String, "type": "jpg" }, "category": "menu_item_img" }
        });
        return resp_upload.data.url
    } catch (error) {
        console.error('Error uploading and downloading the image:', error.message);
        return ""
    }
}
let grab_menu = {}
grab_menu.sync_menu = async function ({ site_id, access_token }, { site_menu, item_id }) {
    if (!access_token) {
        return {}
    }

    try {

        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.categories


        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            for (let i = 0; i < site_menu.categories[c].items.length; i++) {
                const item = site_menu.categories[c].items[i];
                if (String(item._id) === item_id) {
                    const new_image = await upload_image({ site_id, access_token }, item.image)

                    const group_menu_category_index = _.findIndex(group_menu, v => v.categoryName.toUpperCase() === category.name.toUpperCase())
                    // Create or update category
                    if (group_menu_category_index < 0) {
                        const group_menu_category_resp = await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.grab.com/food/merchant/v2/categories`,
                            headers: base_headers({ site_id, access_token }),
                            data: {
                                "nameTranslation": {
                                    "translation": {}
                                }, "sellingTimeID": "AlwaysAvailable", "name": category.name.toUpperCase(), "sectionID": ""
                            }
                        });

                        const group_menu_category_item_resp = await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.grab.com/food/merchant/v2/items`,
                            headers: base_headers({ site_id, access_token }),
                            data: {
                                "item": {
                                    "itemName": item.name.toUpperCase(),
                                    "nameTranslation": {
                                        "translation": {}
                                    },
                                    "priceInMin": item.price,
                                    "imageURLs": [
                                        new_image,
                                    ],
                                    "sellingTimeID": "AlwaysAvailable",
                                    "descriptionTranslation": {
                                        "translation": {}
                                    },
                                    "description": item.description,
                                    "skuID": ""
                                },
                                "categoryID": group_menu_category_resp.data.categoryID
                            }
                        });
                        console.log(group_menu_category_item_resp.data)
                        await axios({
                            method: 'post',
                            maxBodyLength: Infinity,
                            url: `https://api.grab.com/food/merchant/v2/items/${group_menu_category_item_resp.data.itemID}/proof-item`,
                            headers: base_headers({ site_id, access_token }),
                            data: { "proofImageURL": "" }
                        });
                    } else {
                        const group_menu_category = group_menu[group_menu_category_index]
                        const group_menu_category_item_index = _.findIndex(group_menu_category.items, v => v.itemName.toUpperCase() === item.name.toUpperCase())
                        if (group_menu_category_item_index < 0) {
                            // create menu item
                            const group_menu_category_item_resp = await axios({
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://api.grab.com/food/merchant/v2/items`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "item": {
                                        "itemName": item.name.toUpperCase(),
                                        "nameTranslation": {
                                            "translation": {}
                                        },
                                        "priceInMin": item.price,
                                        "imageURLs": [
                                            new_image,
                                        ],
                                        "sellingTimeID": "AlwaysAvailable",
                                        "descriptionTranslation": {
                                            "translation": {}
                                        },
                                        "description": item.description,
                                        "skuID": ""
                                    },
                                    "categoryID": group_menu_category.categoryID
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                            await axios({
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://api.grab.com/food/merchant/v2/items/${group_menu_category_item_resp.data.itemID}/proof-item`,
                                headers: base_headers({ site_id, access_token }),
                                data: { "proofImageURL": "" }
                            });
                        } else {
                            // update menu item with price, active, image and descripton
                            const group_menu_category_item = group_menu_category.items[group_menu_category_item_index]
                            const group_menu_category_item_resp = await axios({
                                method: 'put',
                                maxBodyLength: Infinity,
                                url: `https://api.grab.com/food/merchant/v2/items/${group_menu_category_item.itemID}`,
                                headers: base_headers({ site_id, access_token }),
                                data: {
                                    "itemID": group_menu_category_item.itemID,
                                    "item": {
                                        "specialItemType": "",
                                        "itemName": item.name.toUpperCase(),
                                        "availableStatus": 1,
                                        "nameTranslation": {
                                            "translation": {}
                                        },
                                        "priceInMin": item.price,
                                        "imageURLs": [
                                            new_image,
                                        ],
                                        "sellingTimeID": "AlwaysAvailable",
                                        "descriptionTranslation": {
                                            "translation": {}
                                        },
                                        "description": item.description,
                                        "skuID": ""
                                    },
                                    "categoryID": group_menu_category.categoryID
                                }
                            });
                            console.log(group_menu_category_item_resp.data)
                            await axios({
                                method: 'post',
                                maxBodyLength: Infinity,
                                url: `https://api.grab.com/food/merchant/v2/items/${group_menu_category_item.itemID}/proof-item`,
                                headers: base_headers({ site_id, access_token }),
                                data: { "proofImageURL": "" }
                            });

                        }

                    }
                }

            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

// [{  item_name, quantity, active = false }]
grab_menu.active_menu_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data

        for (const category of group_menu.categories) {
            I: for (const item of category.items) {
                let update_item = _.find(update_items, v => text_slugify(item.itemName) === text_slugify(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue I
                    }
                    update_item = {
                        name: item.itemName,
                        quantity: 0,
                        active: false,
                    }
                }
                const old_status = item.availableStatus === 1 // 1:true, 3:false
                const new_status = update_item.active
                if (old_status === new_status)
                    continue

                const group_menu_category_item_resp = await axios({
                    method: 'put',
                    maxBodyLength: Infinity,
                    url: `https://api.grab.com/food/merchant/v1/items/available-status`,
                    headers: base_headers({ access_token }),
                    data: {
                        availableStatus: new_status ? 1 : 3,
                        itemIDs: [
                            item.itemID
                        ]
                    },
                });

                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }
        }

    } catch (err) {
        console.log(err)
    }

    try {
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/mart-menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data

        for (const category of group_menu.departments) {
            for (const sub_department of category.subDepartments) {
                I: for (const item of sub_department.items) {
                    let update_item = _.find(update_items, v => text_slugify(item.itemName) === text_slugify(v.name))
                    if (!update_item) {
                        if (!update_all_items) {
                            continue I
                        }
                        update_item = {
                            name: item.itemName,
                            quantity: 0,
                            active: false,
                        }
                    }
                    const old_status = item.availableStatus === 1 // 1:true, 3:false
                    const new_status = update_item.active
                    if (old_status === new_status)
                        continue

                    const group_menu_category_item_resp = await axios({
                        method: 'put',
                        maxBodyLength: Infinity,
                        url: `https://api.grab.com/food/merchant/v1/items/available-status`,
                        headers: base_headers({ access_token }),
                        data: {
                            availableStatus: new_status ? 1 : 3,
                            itemIDs: [
                                item.itemID
                            ]
                        },
                    });

                    console.log(group_menu_category_item_resp.data)
                    updated_items.push(update_item)
                }
            }
        }
    } catch (err) {
        console.log(err)
    }
    return updated_items
}

grab_menu.delete_menu_category_item = async function ({ site_id, access_token }, { category_name, item_name }) {
    if (!access_token) {
        return {}
    }

    try {
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.categories
        for (let c = 0; c < group_menu.length; c++) {
            const category = group_menu[c];
            if (text_slugify(category_name) === text_slugify(category.categoryName)) {
                for (let i = 0; i < category.items.length; i++) {
                    const item = category.items[i];
                    if (text_slugify(item_name) === text_slugify(item.itemName)) {
                        const delete_category_resp = await axios({
                            method: 'delete',
                            maxBodyLength: Infinity,
                            url: `https://api.grab.com/food/merchant/v2/items/${item.itemID}`,
                            headers: base_headers({ access_token }),
                        })
                        console.log(delete_category_resp.data)
                    }
                }
            }

        }
    } catch (err) {
        console.log(err)
        return null
    }
}

grab_menu.delete_menu_category = async function ({ access_token }, { category_name }) {
    if (!access_token) {
        return {}
    }

    try {
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/menu`,
            headers: base_headers({ access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data?.categories
        for (let c = 0; c < group_menu.length; c++) {
            const category = group_menu[c];
            if (text_slugify(category_name) === text_slugify(category.categoryName)) {
                const delete_category_resp = await axios({
                    method: 'delete',
                    maxBodyLength: Infinity,
                    url: `https://api.grab.com/food/merchant/v2/categories/${category.categoryID}`,
                    headers: base_headers({ access_token }),
                })
                console.log(delete_category_resp.data)
            }
        }
    } catch (err) {
        console.log(err)
        return null
    }
}

// grab_menu.delete_menu_category_item({
//     access_token: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
// }, { item_name: 'test', category_name: "test" })


// [{ category_name, name, active = false }]
grab_menu.active_menu_option_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/mart-menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const group_menu = group_menu_resp.data

        for (const category of group_menu?.modifierGroups) {
            I: for (const item of category.modifiers) {
                let update_item = _.find(update_items, v => text_slugify(category.modifierGroupName) === text_slugify(v.category_name) && text_slugify(item.modifierName) === text_slugify(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue I
                    }
                    update_item = {
                        category_name: category.modifierGroupName,
                        name: item.modifierName,
                        quantity: 0,
                        active: false,
                    }
                }
                const new_status = update_item.active ? 1 : 3
                if (item.availableStatus === new_status)
                    continue

                const group_menu_category_item_resp = await axios({
                    method: 'put',
                    maxBodyLength: Infinity,
                    url: `https://api.grab.com/food/merchant/v2/modifiers/available`,
                    headers: base_headers({ access_token }),
                    data: {
                        availableStatus: new_status,
                        modifierIDs: [
                            item.modifierID
                        ]
                    },
                });
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }
        }
    } catch (err) {
        console.log(err)
    }
    return updated_items
}



// grab_menu.active_menu_option_item({ access_token: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" }, {
//     category_name: "Combo DD Ngon Miệng (Chọn Sữa)",
//     name: "Hộp Nutifood GrowPLUS+ Biếng Ăn 180ml",
//     active: false
// })

module.exports = grab_menu