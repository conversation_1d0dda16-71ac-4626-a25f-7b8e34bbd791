const axios = require('../axios');
const moment = require('moment');
const { base_headers, base_body, get_menu } = require('./be')
const FormData = require('form-data');
const _ = require("lodash");
const { _textToSlug } = require('./mapping');

const upload_image = async function ({ site_id, access_token }, image_url) {
    try {
        if (!image_url)
            return ""
        return ""
    } catch (error) {
        console.error('Error uploading and downloading the image:', error.message);
        return ""
    }
}

let be_menu = {}
be_menu.sync_menu = async function ({ site_id, access_token }, { site_menu, item_id }) {
    if (!access_token) {
        return {}
    }
    // Not support for now
    try {
        return true
    } catch (err) {
        console.log(err)
        return null
    }
}

be_menu.active_menu_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await get_menu({ site_id, access_token })
        for (const category of group_menu.categories) {
            D: for (const item of category.items) {
                let update_item = _.find(update_items, v => _textToSlug(item.item_name) === _textToSlug(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue D
                    }
                    update_item = {
                        name: item.item_name,
                        quantity: 0,
                        active: false,
                    }
                }
                const old_status = item.is_active === 1
                const new_status = update_item.active
                if (old_status === new_status)
                    continue

                const group_menu_category_item_resp = await axios({
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gw.be.com.vn/api/v1/be-marketplace/vendor/update_menu_item_status`,
                    headers: base_headers(),
                    data: {
                        ...base_body({ site_id, access_token }),
                        is_active: new_status ? 1 : 3,
                        restaurant_item_id: [item.restaurant_item_id],
                    }

                });
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }

        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// be_menu.active_menu_item({
//     access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************.sVfURUQPAynW5U6HH2miDmEviM5cYj3nBatFm68MSSg",
//     site_id: `{"merchant_id":59085,"restaurant_id":77466,"user_id":52091,"vendor_id":78395} `,
// }, [{
//     category_name: "BÁNH MÌ",
//     name: "Bánh mì Trứng Khìa",
//     active: true
// }])

be_menu.active_menu_option_item = async function ({ site_id, access_token }, update_items, update_all_items = false) {
    if (!access_token) {
        return []
    }
    if (update_items.length === 0) {
        return []
    }

    let updated_items = []

    try {
        const group_menu = await get_menu({ site_id, access_token })
        for (const category of group_menu.option_categories) {
            D: for (const item of category.customize_options) {
                let update_item = _.find(update_items, v => _textToSlug(category.name) === _textToSlug(v.category_name) && _textToSlug(item.name) === _textToSlug(v.name))
                if (!update_item) {
                    if (!update_all_items) {
                        continue D
                    }
                    update_item = {
                        category_name: category.name,
                        name: item.name,
                        quantity: 0,
                        active: false,
                    }
                }
                const old_status = item.is_active === 1
                const new_status = update_item.active
                if (old_status === new_status)
                    continue


                const group_menu_category_item_resp = await axios({
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/update_active_customize_option`,
                    headers: base_headers(),
                    data: {
                        ...base_body({ site_id, access_token }),
                        is_active: new_status ? 1 : 3,
                        option_id: item.option_id,
                    }

                });
                console.log(group_menu_category_item_resp.data)
                updated_items.push(update_item)
            }
        }

    } catch (err) {
        console.log(err)
    }
    return updated_items
}

// be_menu.active_menu_option_item({
//     access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************.sVfURUQPAynW5U6HH2miDmEviM5cYj3nBatFm68MSSg",
//     site_id: `{"merchant_id":59085,"restaurant_id":77466,"user_id":52091,"vendor_id":78395} `,
// }, [{
//     category_name: "Tùy chọn",
//     name: "Sống",
//     active: true
// }])


module.exports = be_menu