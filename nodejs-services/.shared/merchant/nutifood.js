const axios = require('../axios');
const moment = require('moment');
const _ = require('lodash');
const { Site, Hub, Brand, Order, BrandMenu, VendorRequest } = require('../database')
const { send_slack_message } = require('../slack')
const helper = require('../helper');
const { send_zalo_message, ZALO_GROUPS } = require('../zalo');

let nutifood = {}


nutifood.get_menu_items = async ({ shop, items }) => {
    const all_items = [];

    let MAX_RETRY = 3;
    try {
        const limit = 100;
        let offset = 0;

        do {
            const config = {
                method: 'get',
                maxBodyLength: Infinity,
                url: `${process.env.NUTIFOOD_URL}/v1/api/partner/stock`,
                params: {
                    shops: JSON.stringify([shop]),
                    items: JSON.stringify(items),
                    offset,
                    limit,
                },
                headers: {
                    api_key: process.env.NUTIFOOD_API_KEY,
                },
            }

            const { data } = await axios(config);

            if (data.data.stock === undefined) {
                MAX_RETRY--;
                if (MAX_RETRY === 0) {
                    throw new Error(data.message)
                }
                continue;
            }

            offset += limit;
            all_items.push(...data.data.stock);

            if (data.data.stock.length === 0) {
                break;
            }
        } while (true);
    } catch (error) {
        console.log(error.message);
        console.log(error?.response?.body);
        return {
            success: false,
            menu_items: [],
        }
    }

    // Group items by item_code and sum their quantities
    const unique_items = _.chain(all_items)
        .groupBy('item_code')
        .map((items, item_code) => ({
            item_code,
            item_name: items[0].item_name,
            unit: items[0].unit,
            quantity: _.sumBy(items, 'quantity'),
            shop_code: items[0].shop_code,
            shop_name: items[0].shop_name,
        }))
        .value();

    for (const item of items) {
        if (unique_items.some((v) => v.item_code === item)) {
            continue;
        }
        unique_items.push({
            item_code: item,
            item_name: 'Không có trong kho',
            unit: '',
            quantity: 0,
            shop_code: shop,
            shop_name: '',
        })
    }
    return {
        success: true,
        menu_items: unique_items,
    }

};

nutifood.get_menu_items_v2 = async ({ shops, items }) => {
    const all_items = [];

    const result = {
        success: true,
        menu_items: [],
        raw_items: [],
    }
    try {
        const limit = 100;
        let offset = 0;
        let total = 0

        do {
            const config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.NUTIFOOD_URL}/v2/api/partner/stock-v2`,
                headers: {
                    api_key: process.env.NUTIFOOD_API_KEY,
                },
                data: {
                    params: {
                        offset,
                        limit,
                        shop: shops,
                        product: items,
                    }
                }
            }

            const { data } = await axios(config);

            if (!data.data) {
                console.log(offset, total)
                break;
            }

            total = data.data.total_count
            for (const shop_code of Object.keys(data.data.data)) {
                for (const product_item of data.data.data[shop_code].products) {
                    all_items.push({
                        item_code: product_item.product_code,
                        item_name: product_item.product_name,
                        unit: '',
                        quantity: product_item.quantity,
                        shop_code,
                        shop_name: data.data.data[shop_code].shop_name,
                    })
                }

            }
            if (offset >= total) {
                console.log(offset, total)
                break;
            }

            offset += limit;

        } while (true);
    } catch (error) {
        console.log(error.message);
        console.log(error?.response?.body);
        result.success = false
        return result
    }

    // Group items by item_code and sum their quantities
    const unique_items = _.chain(all_items)
        .groupBy(item => `${item.shop_code}_${item.item_code}`)
        .map(items => ({
            item_code: items[0].item_code,
            item_name: items[0].item_name,
            unit: items[0].unit,
            quantity: _.sumBy(items, 'quantity'),
            shop_code: items[0].shop_code,
            shop_name: items[0].shop_name,
        }))
        .value();

    for (const shop of shops) {
        const shop_items = unique_items.filter(v => v.shop_code === shop)
        for (const item of items) {
            if (shop_items.some((v) => v.item_code === item)) {
                continue;
            }
            unique_items.push({
                item_code: item,
                item_name: 'Không có trong kho',
                unit: '',
                quantity: 0,
                shop_code: shop,
                shop_name: shop_items.length > 0 ? shop_items[0].shop_name : '',
            })
        }
    }

    result.menu_items = unique_items
    return result
};



nutifood.sync_an_order = async (order) => {
    const site = await Site.findById(order.site_id)
    const brand = await Brand.findById(site.brand_id)
    const hub = await Hub.findById(site.hub_id)
    const brand_menu = await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, {
        $setOnInsert: {
            categories: [],
            option_categories: [],
        }
    }, { upsert: true, new: true }).lean()
    const data_mapping = order.data_mapping

    const dishes_resp = helper.add_stock_to_dishes(data_mapping.dishes, brand_menu)
    if (!dishes_resp.success) {
        const message = `[Thất bại]mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, Lỗi: ${dishes_resp.error_messages.join(', ')}`
        console.log(message)
        return {
            success: false,
            message: message,
            data: {
                request: data_mapping,
                response: {},
            },
            stock_data: dishes_resp.dishes,
        }
    }

    let dishes = dishes_resp.dishes.flatMap(v => v.stocks).filter(v => v.quantity > 0).map(v => ({
        "item_code": v.code,
        "price": v.unit_price,
        "qty": v.quantity,
        "discount": v.discount,
    }))

    if (dishes.length === 0) {
        const message = `[Thất bại] Mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} sai thông tin món`
        console.log(message)
        return {
            success: false,
            message: message,
            data: {
                request: {},
                response: {},
            },
            stock_data: dishes_resp.dishes,
        }
    }

    let sub_total = 0
    for (const dish of dishes) {
        sub_total += dish.price * dish.qty
    }

    let order_discount = data_mapping.total_discount || 0

    if (dishes_resp.total_diff_price !== 0) {
        order_discount += dishes_resp.total_diff_price
    }

    const notes = []
    if (!['he', 'local'].includes(order.source)) {
        notes.push(`Mã đơn: ${data_mapping.order_id} `)
    }

    if (order.source === 'gojek' && order.data.otp) {
        notes.push(`Mã OTP: ${order.data.otp} `)
    }

    if (data_mapping.note)
        notes.push(data_mapping.note)

    // Grab hide info
    if (data_mapping.customer_name === '***') {
        data_mapping.customer_name = 'NEXPOS'
        data_mapping.customer_phone = '0900000000'
    }

    // const total_paid = ['he', 'local'].includes(order.source) ? (sub_total - dish_discount) : data_mapping.total_for_biz
    const total_paid = (sub_total - order_discount)

    let channel_code = 'Q-COMMERCE'
    if (order.source === 'he')
        channel_code = 'NEXDOR_DLCN'

    if (brand.name === 'NutiFood Bliss')
        channel_code = 'BLISS'

    const order_sync_req_data = {
        delivery: {
            delivery_code: data_mapping.id,
            delivery_fee: 0,
            partner_code: 'Q-COMMERCE',
        },
        lines: dishes.map(v => _.pick(v, ['item_code', 'price', 'qty'])),
        channel_code: channel_code,
        customers: {
            ward_name: '',
            address: '214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam',
            name: 'NEXPOS',
            phone: '0900000000',
        },
        date_order: moment.unix(order.data_mapping.order_time_sort).format('YYYY-MM-DD'),
        payment_ids: [
            {
                amount: total_paid,
                payment_type: 'Q-COMMERCE',
            },
        ],
        shop_code: hub.code,
        user_id: 1,
        to_invoice: false,
        note: notes.join(', '),
        discount: order_discount,
        source_code: {
            shopee: 'SHOPEE',
            shopee_fresh: 'SHOPEE',
            gojek: 'GOJEK',
            grab: 'GRAB',
            grab_mart: 'GRAB',
            be: 'BE',
            baemin: 'BAEMIN',
        }[order.source] || 'NEXDOR_DLCN',
        // attr_1: data_mapping.customer_phone,
        // attr_2: data_mapping.customer_name,
        // attr_3: data_mapping.customer_address,
        attr_1: '0900000000',
        attr_2: 'NEXPOS',
        attr_3: '214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam',
    };

    const vendor_request = await VendorRequest.create({
        vendor: 'nutifood',
        type: 'sync_order'
    })
    try {
        const resp_config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.NUTIFOOD_URL}/v1/api/order-delivery/order-channel`,
            headers: {
                'api_key': process.env.NUTIFOOD_API_KEY,
            },
            data: order_sync_req_data
        }

        vendor_request.url = resp_config.url
        vendor_request.method = resp_config.method
        vendor_request.headers = resp_config.headers
        vendor_request.request_data = resp_config.data

        const resp = await axios(resp_config)

        const message = `[Thành công] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, đồng bô thành công.`
        console.log(message)

        const { success, menu_items } = await nutifood.get_menu_items({ shop: hub.code, items: dishes.map(v => v.item_code) })

        vendor_request.success = true
        vendor_request.response_data = resp.data
        await vendor_request.save()
        return {
            success: true,
            data: {
                request: order_sync_req_data,
                response: resp.data,
                stock_request: { shops: [hub.code], items: dishes.map(v => v.item_code) },
                stock_response: menu_items
            },
            stock_data: dishes_resp.dishes,
            message: message
        }
    } catch (error) {
        vendor_request.success = true
        vendor_request.response_data = error?.response?.data || error?.response || { error: error.message }
        await vendor_request.save()

        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: error.response.data,  // This contains the response body
                },
                stock_data: dishes_resp.dishes,
            };
        } else if (error.request) {
            // The request was made but no response was received
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: No response received`,
                data: {
                    request: order_sync_req_data,
                    response: vendor_request.response_data
                },
                stock_data: dishes_resp.dishes,
            };
        } else {
            // Something happened in setting up the request that triggered an Error
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: vendor_request.response_data
                },
                stock_data: dishes_resp.dishes,
            };
        }
    }
}


nutifood.sync_an_order_v2 = async (order) => {
    const site = await Site.findById(order.site_id)
    const brand = await Brand.findById(site.brand_id)
    const hub = await Hub.findById(site.hub_id)
    const brand_menu = await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, {
        $setOnInsert: {
            categories: [],
            option_categories: [],
        }
    }, { upsert: true, new: true }).lean()
    const data_mapping = order.data_mapping
    const dishes_resp = helper.add_stock_to_dishes(data_mapping.dishes, brand_menu)
    if (!dishes_resp.success) {
        const message = `[Thất bại]mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, Lỗi: ${dishes_resp.error_messages.join(', ')}`
        console.log(message)
        return {
            success: false,
            message: message,
            data: {
                request: data_mapping,
                response: {},
            },
            stock_data: dishes_resp.dishes,
        }
    }

    const nutifood_dishes = []
    for (const dish of data_mapping.dishes) {
        const item = helper.find_item_in_menu_by_name(brand_menu.categories, dish)
        if (item && item?.combo?.length > 0) {
            const combo_price_weight = item.combo.reduce((acc, v) => acc + v.price * v.quantity, 0)
            for (const combo of item.combo) {
                const price_weight = combo.price * combo.quantity / combo_price_weight
                if (dish.quantity * combo.quantity > 0) {
                    nutifood_dishes.push({
                        item_code: combo.code,
                        price: Math.ceil(item.stock_price * price_weight / combo.quantity),
                        qty: dish.quantity * combo.quantity
                    })
                }
            }
        }

    }

    let nutifood_sub_total = 0
    for (const dish of nutifood_dishes) {
        nutifood_sub_total += dish.price * dish.qty
    }

    let real_sub_total = data_mapping.dishes.reduce((acc, v) => acc + v.price, 0)
    let real_order_discount = data_mapping.total_discount || 0

    let total_paid = real_sub_total - real_order_discount
    let nutifood_order_discount = nutifood_sub_total - total_paid

    if (nutifood_order_discount < 0) {
        send_zalo_message(
            {
                group_link: ZALO_GROUPS.ORDER_SYSNCING_NOTIFICATION,
                message: [
                    `Đơn hàng ${data_mapping.id} nhận nhiều tiền hơn giá niêm yết, Niêm yết: ${nutifood_sub_total}, Thực nhận: ${total_paid}`,
                    JSON.stringify({
                        lines: nutifood_dishes,
                        discount: nutifood_order_discount,
                        payment_ids: [
                            {
                                amount: total_paid,
                                payment_type: 'Q-COMMERCE',
                            },
                        ],
                    }, null, 2)].join('\n')

            })
        nutifood_order_discount = 0
        total_paid = nutifood_sub_total
    }

    const notes = []
    if (!['he', 'local'].includes(order.source)) {
        notes.push(`Mã đơn: ${data_mapping.order_id} `)
    }

    if (data_mapping.note)
        notes.push(data_mapping.note)



    let channel_code = 'Q-COMMERCE'
    if (order.source === 'he')
        channel_code = 'NEXDOR_DLCN'

    if (brand.name === 'NutiFood Bliss')
        channel_code = 'BLISS'

    const order_sync_req_data = {
        delivery: {
            delivery_code: data_mapping.id,
            delivery_fee: 0,
            partner_code: 'Q-COMMERCE',
        },
        lines: nutifood_dishes,
        channel_code: channel_code,
        customers: {
            ward_name: '',
            address: '214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam',
            name: 'NEXPOS',
            phone: '0900000000',
        },
        date_order: moment.unix(order.data_mapping.order_time_sort).format('YYYY-MM-DD'),
        payment_ids: [
            {
                amount: total_paid,
                payment_type: 'Q-COMMERCE',
            },
        ],
        shop_code: hub.code,
        user_id: 1,
        to_invoice: false,
        note: notes.join(', '),
        discount: nutifood_order_discount,
        source_code: {
            shopee: 'SHOPEE',
            shopee_fresh: 'SHOPEE',
            gojek: 'GOJEK',
            grab: 'GRAB',
            grab_mart: 'GRAB',
            be: 'BE',
            baemin: 'BAEMIN',
        }[order.source] || 'NEXDOR_DLCN',
        // attr_1: data_mapping.customer_phone,
        // attr_2: data_mapping.customer_name,
        // attr_3: data_mapping.customer_address,
        attr_1: '0900000000',
        attr_2: 'NEXPOS',
        attr_3: '214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam',
    };

    const vendor_request = await VendorRequest.create({
        vendor: 'nutifood',
        type: 'sync_order'
    })
    try {
        const resp_config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.NUTIFOOD_URL}/v1/api/order-delivery/order-channel`,
            headers: {
                'api_key': process.env.NUTIFOOD_API_KEY,
            },
            data: order_sync_req_data
        }

        vendor_request.url = resp_config.url
        vendor_request.method = resp_config.method
        vendor_request.headers = resp_config.headers
        vendor_request.request_data = resp_config.data

        const resp = await axios(resp_config)

        const message = `[Thành công] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name}, đồng bô thành công.`
        console.log(message)

        const { success, menu_items } = await nutifood.get_menu_items({ shop: hub.code, items: nutifood_dishes.map(v => v.item_code) })

        vendor_request.success = true
        vendor_request.response_data = resp.data
        await vendor_request.save()
        return {
            success: true,
            data: {
                request: order_sync_req_data,
                response: resp.data,
                stock_request: { shops: [hub.code], items: nutifood_dishes.map(v => v.item_code) },
                stock_response: menu_items
            },
            stock_data: dishes_resp.dishes,
            message: message
        }
    } catch (error) {
        vendor_request.success = false
        vendor_request.response_data = error?.response?.data || error?.response || { error: error.message }
        await vendor_request.save()

        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: error.response.data,  // This contains the response body
                },
                stock_data: dishes_resp.dishes,
            };
        } else if (error.request) {
            // The request was made but no response was received
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: No response received`,
                data: {
                    request: order_sync_req_data,
                    response: vendor_request.response_data
                },
                stock_data: dishes_resp.dishes,
            };
        } else {
            // Something happened in setting up the request that triggered an Error
            return {
                success: false,
                message: `[Thất bại] mã đơn: ${data_mapping.id}, bán hàng trên ${order.source} - ${site.name} lỗi: ${error.message} `,
                data: {
                    request: order_sync_req_data,
                    response: vendor_request.response_data
                },
                stock_data: dishes_resp.dishes,
            };
        }
    }
}

// (async () => {
//     const order = await Order.findOne({ order_id: '24104-305072261' })
//     nutifood.sync_an_order_v2(order)
// })();

module.exports = nutifood