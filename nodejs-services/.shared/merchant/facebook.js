const axios = require('../axios');
const moment = require('moment-timezone');
const _ = require('lodash');

async function get_token(access_token) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://graph.facebook.com/v21.0/oauth/access_token',
        params: {
            grant_type: 'fb_exchange_token',
            client_id: '1341502293499895',
            client_secret: '6690edde8914716072288e193730b27c',
            fb_exchange_token: access_token
        }
    };
    try {
        const resp = await axios(config);

        return {
            access_token: resp.data.access_token,
        }
    } catch (error) {
        console.log(error.message)
        return null
    }
}

module.exports = {
    get_token,
}