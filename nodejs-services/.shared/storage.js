const { Storage } = require('@google-cloud/storage');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode
const storage = new Storage({ credentials });

exports.upload_file = async ({ bucket, key, buff, wait = true }) => {
    try {
        const google_bucket = storage.bucket(bucket)
        const file = google_bucket.file(key);
        if (wait)
            await file.save(buff);
        else
            file.save(buff);
        return `https://storage.googleapis.com/${bucket}/${key}`;
        // return file.publicUrl()
    } catch (err) {
        console.error(err)
        return ""
    }
}

exports.pre_sign_file = async ({ bucket, key }) => {
    try {
        const google_bucket = storage.bucket(bucket)
        const file = google_bucket.file(key);
        return file.publicUrl()
    } catch (err) {
        console.error(err)
        return ""
    }
}

exports.exist_file = async ({ bucket, key }) => {
    try {
        const google_bucket = storage.bucket(bucket)
        const file = google_bucket.file(key);
        const [exists] = await file.exists();

        if (exists) {
            return file.publicUrl()
        }
        return false;
    } catch (err) {
        console.error(err)
        return false;
    }
}
