const moment = require('moment')
const axios = require('axios')
const _ = require('lodash')
const diff = require('diff');
const { Site, Order, Brand, TokenAccount, OrderShipment, OrderPayment } = require('./database')

const baemin = require('./merchant/baemin')
const shopee = require('./merchant/shopee')
const shopee_ecom = require('./merchant/shopee_ecom')
// const shopee_official = require('./merchant/shopee_official')
const gojek = require('./merchant/gojek')
const grab = require('./merchant/grab')
const be = require('./merchant/be')
const tiktok = require('./merchant/tiktok_unofficial')
const lazada = require('./merchant/lazada')
const zalo = require('./merchant/zalo')
const { map_order, map_menu } = require('./merchant/mapping')
const { send_slack_message } = require('./slack')
const laz = require('./merchant/lazada')
const haravan = require('./merchant/haravan')
const redis = require('./redis');
const { json_to_md5 } = require('./crypto');
const helper = require('./helper');
const { get_token_by_site, is_token_working } = require('./token_account')
const { send_zalo_message, ZALO_GROUPS } = require('./zalo');
const { get_queue, set_queue } = require('./redis_queue');

const cron = {}
cron.get_site_tokens = async (site_id) => {
  const site = await Site.findById(site_id)
  for (let i = 0; i < site.tokens.length; i++) {
    const item = site.tokens[i]
    try {
      // if (item.source === 'gojek') {
      //   const data = await gojek.get_token_by_refresh_token(item.username, item.site_id, item.refresh_token)
      //   if (data && data?.access_token) {
      //     site.tokens[i].access_token = data.access_token
      //     site.tokens[i].refresh_token = data.refresh_token
      //     site.tokens[i].last_updated = moment()
      //   }
      // }

      if (item.source === 'grab' || item.source === 'grab_mart') {
        const data = await grab.get_token(item.username, item.password)
        if (data && data?.access_token) {
          site.tokens[i].access_token = data.access_token
          site.tokens[i].refresh_token = data.refresh_token
          site.tokens[i].last_updated = moment()
        }
      }

      if (item.source === 'be') {
        const data = await be.get_token(item.username, item.password)
        if (data && data.access_token) {
          site.tokens[i].access_token = data.access_token
          site.tokens[i].refresh_token = data.access_token
          site.tokens[i].last_updated = moment()
        }
      }
      // tiktok and lazada token need to get from UI
    } catch (error) {
      console.log(error)
    }
  }
  await site.save()
}

cron.get_site_order_by_source = async (site, merchantFunc, source) => {
  const token = await get_token_by_site(site, source)
  if (!token || !token.access_token) {
    return
  }

  // if (token.token_code_official) return // Skip call API if API Official

  const { success, data: raw_orders_by_status } = await merchantFunc.get_order_list_v2(token)
  if (!success && ['grab', 'grab_mart', 'gojek', 'be'].includes(source)) {
    const queue_key = `cron_job:get_site_order_by_source_errors`
    const list = await get_queue(queue_key)
    const item = list.find(v => v._id === token.token_code)
    if (!item) {
      await set_queue(queue_key, [...list, { ...token, _id: token.token_code, count: 1 }].sort((a, b) => b.count - a.count))
    } else {
      item.count += 1
      await set_queue(queue_key, list.sort((a, b) => b.count - a.count))
    }
    return
  }

  for (const [status, raw_orders] of Object.entries(raw_orders_by_status)) {
    O: for (const order of raw_orders) {
      const order_serial = json_to_md5(order)
      const order_id = {
        'grab': order.orderID || order.ID,
        'grab_mart': order.orderID || order.ID,
        'gojek': order.order_number,
        'shopee': order.code,
        'shopee_fresh': order.code,
        'shopee_ecom': order.order_id,
        'be': order.order_id,
        'tiktok': order.main_order_id,
        'lazada': String(order.order_id),
      }[source]


      let db_order = await redis.getObj(`order:${order_id}`)
      if (!db_order) {
        db_order = await Order.findOne({ order_id }).lean()
        if (db_order) {
          await redis.setObj(`order:${order_id}`, db_order, 3600)
        }
      }
      if (!db_order || db_order?.data?.order_serial !== order_serial) {
        let merchant_order = await merchantFunc.get_order_detail(token, order_id)
        if (!merchant_order) { continue O }
        merchant_order = helper.deep_merge_object(db_order?.data || {}, merchant_order)
        merchant_order.order_serial = order_serial
        merchant_order.order_detail_in_list = order
        const data_mapping = map_order(source, merchant_order)

        if (!db_order) {
          await Order.create({
            site_id: site._id,
            hub_id: site.hub_id,
            source,
            order_id,
            status,
            data: merchant_order,
            data_mapping,
            data_history: [merchant_order],
            external_id: helper.gen_external_id(),
          })
        } else {
          const dish_changed = helper.check_dish_changed(db_order.data_mapping, data_mapping)
          data_mapping.dish_changed = dish_changed
          // await send_slack_message({ channel: "C06356A3PM3", text: sync_order_resp.message, block: 'Thất bại' })
          if (dish_changed) {
            await send_zalo_message({
              group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
              message: [
                `Đơn hàng <bc style="color:#db342e">CÓ SỰ THAY ĐỔI MÓN ${db_order.order_id}</bc>,`,
                `Items cũ: `,
                db_order.data_mapping.dishes.map(v => String(v.quantity) + ' x ' + v.name).join('\n'),
                `Items mới: `,
                data_mapping.dishes.map(v => String(v.quantity) + ' x ' + v.name).join('\n'),
                `Vui lòng kiểm tra chi tiết đơn hàng tại: ${process.env.WEB_URL}/?orderId=${db_order.order_id}`
              ].join('\n'),
            })
          }
          if (db_order.status === 'FINISH') {
            continue O
          }
          db_order = await Order.findOneAndUpdate({ _id: db_order._id }, {
            status,
            data: merchant_order,
            data_mapping,
            $push: { data_history: merchant_order },
          }, { new: true }).lean();
        }
        await redis.del(`order:${order_id}`)
      }
    }
  }
}

cron.get_site_orders_by_days = async (site_id, { from, to }) => {
  const site = await Site.findById(site_id)
  const moment_from = moment(from).startOf('day')
  const moment_to = moment(to)

  const app_functions = {
    shopee: shopee,
    shopee_fresh: shopee,
    // gojek: gojek,
    grab: grab,
    grab_mart: grab,
    be: be,
    shopee_ecom: shopee_ecom,
    tiktok: tiktok,
    lazada: lazada,
  }

  const get_order_id = (source, order) => {
    const merchant_order_id = {
      'grab': order.orderID || order.ID,
      'grab_mart': order.orderID || order.ID,
      'gojek': order.order_number,
      'shopee': order.code,
      'shopee_fresh': order.code,
      'be': order.order_id,
      'shopee_ecom': order.order_id,
      'tiktok': order.main_order_id,
      'lazada': order.order_id,
    }[source]
    return String(merchant_order_id)
  }

  for (const [source, merchantFunc] of Object.entries(app_functions)) {
    const token = await get_token_by_site(site, source)
    if (!token || !token.access_token) {
      continue
    }

    const { success, data: raw_orders_by_status } = await merchantFunc.get_order_list_by_duration(token, { from: moment_from, to: moment_to })
    for (const [status, raw_orders] of Object.entries(raw_orders_by_status)) {
      const order_ids = raw_orders.map(v => get_order_id(source, v))
      const db_orders = await Order.find({
        $or: [
          { order_id: order_ids },
          { source: ['shopee', 'shopee_fresh'], 'data_mapping.order_id': order_ids },
        ],
        // created_at: { $gte: moment(moment_from).add(-90, 'days').toDate() },
      }).lean()
      O: for (const order of raw_orders) {
        const order_serial = json_to_md5(order)

        const order_id = get_order_id(source, order)
        let db_order = db_orders.find(v => String(v.order_id) === order_id)

        if (!db_order || db_order?.data?.order_serial !== order_serial) {
          let merchant_order = await merchantFunc.get_order_detail(token, order_id)
          if (!merchant_order) {
            console.log('Error get_order_detail', order_id, source)
            continue O
          }
          merchant_order = helper.deep_merge_object(db_order?.data || {}, merchant_order)
          merchant_order.order_serial = order_serial
          const data_mapping = map_order(source, merchant_order)

          if (!db_order) {
            await Order.create({
              site_id: site._id,
              hub_id: site.hub_id,
              source,
              order_id,
              status,
              data: merchant_order,
              data_mapping,
              data_history: [merchant_order],
              external_id: helper.gen_external_id(),
            })
          } else {
            const dish_changed = helper.check_dish_changed(db_order.data_mapping, data_mapping)
            data_mapping.dish_changed = dish_changed
            // await send_slack_message({ channel: "C06356A3PM3", text: sync_order_resp.message, block: 'Thất bại' })
            if (dish_changed) {
              await send_zalo_message({
                group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
                message: [
                  `Đơn hàng <bc style="color:#db342e">CÓ SỰ THAY ĐỔI MÓN ${db_order.order_id}</bc>,`,
                  `Items cũ: `,
                  db_order.data_mapping.dishes.map(v => String(v.quantity) + ' x ' + v.name).join('\n'),
                  `Items mới: `,
                  data_mapping.dishes.map(v => String(v.quantity) + ' x ' + v.name).join('\n'),
                  `Vui lòng kiểm tra chi tiết đơn hàng tại: ${process.env.WEB_URL}/?orderId=${db_order.order_id}`
                ].join('\n'),
              })
            }
            if (db_order.status === 'FINISH') {
              continue O
            }

            db_order = await Order.findOneAndUpdate({ _id: db_order._id }, {
              status,
              data: merchant_order,
              data_mapping,
              $push: { data_history: merchant_order },
            }, { new: true }).lean();
            await redis.setObj(`order:${order_id}`, db_order, 3600)
          }
        }
      }
    }

  }
}

cron.get_site_orders = async (site_id) => {
  const site = await Site.findById(site_id)
  console.log(`${moment().format('YYYY-MM-DD HH:mm:ss')} Get_site_orders: `, site.name)

  // Do not run async function in parallel avoid rate limit
  await cron.get_site_order_by_source(site, grab, 'grab').catch(console.error)
  await cron.get_site_order_by_source(site, grab, 'grab_mart').catch(console.error)
  // await cron.get_site_order_by_source(site, gojek, 'gojek').catch(console.error)
  await cron.get_site_order_by_source(site, shopee, 'shopee').catch(console.error)
  await cron.get_site_order_by_source(site, shopee, 'shopee_fresh').catch(console.error)
  await cron.get_site_order_by_source(site, be, 'be').catch(console.error)
}

cron.get_site_ecom_orders = async (site_id) => {
  const site = await Site.findById(site_id)

  await cron.get_site_order_by_source(site, shopee_ecom, 'shopee_ecom').catch(console.error)
  await cron.get_site_order_by_source(site, lazada, 'lazada').catch(console.error)
  await cron.get_site_order_by_source(site, tiktok, 'tiktok').catch(console.error)
}

// cron.get_site_ecom_orders('66693268d8a076467d9bd890')

cron.get_token_account_health = async (token_code) => {
  const token_account = await TokenAccount.findOne({ token_code })
  let is_last_working = token_account.working
  const is_working = await is_token_working(token_account.token_code)
  token_account.working = is_working
  token_account.last_updated = new Date()
  if (is_working) {
    token_account.last_working_at = new Date()
  } else {
    // await send_zalo_message({
    //   group_link: "https://zalo.me/g/rhompm863",
    //   message: [
    //     `Token <bc style="color:#db342e">${token_account.token_code}</bc> đã bị lỗi!`,
    //     `Vui lòng liên hệ admin kiểm tra!`
    //   ].join('\n'),
    // })
  }
  await token_account.save()
  // if (!is_last_working && is_working) {
  //   await send_zalo_message({
  //     group_link: "https://zalo.me/g/rhompm863",
  //     message: [
  //       `Token <bc style="color:#db342e">${token_account.token_code}</bc> đã hoạt động trở lại!`,
  //       `Cảm ơn bạn đã kiên nhẫn chờ đợi!`
  //     ].join('\n'),
  //   })

  // }
}

cron.count_site_orders_by_days = async (site_id, { from, to }) => {
  const site = await Site.findById(site_id)
  const moment_from = moment(from)
  const moment_to = moment(to)

  const app_functions = {
    shopee: shopee,
    shopee_fresh: shopee,
    // gojek: gojek,
    grab: grab,
    grab_mart: grab,
    be: be,
  }

  let result = {
    grab: [],
    grab_mart: [],
    // gojek: [],
    shopee: [],
    shopee_fresh: [],
    be: [],
  }

  for (const [source, merchantFunc] of Object.entries(app_functions)) {
    const token = await get_token_by_site(site, source)
    if (!token || !token.access_token) {
      continue
    }

    const { success, data: raw_orders_by_status } = await merchantFunc.get_order_list_by_duration(token, { from: moment_from, to: moment_to })
    for (const [status, raw_orders] of Object.entries(raw_orders_by_status)) {
      if (status !== 'FINISH') {
        continue
      }

      for (const order of raw_orders) {
        const order_id = {
          'grab': order.orderID || order.ID,
          'grab_mart': order.orderID || order.ID,
          'gojek': order.order_number,
          'shopee': order.code,
          'shopee_fresh': order.code,
          'be': String(order.order_id),
        }[source]

        result[source].push(order_id)
      }
    }

  }

  return result
}



module.exports = cron