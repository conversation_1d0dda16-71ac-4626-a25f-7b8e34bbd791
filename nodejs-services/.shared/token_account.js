const moment = require('moment')
const { TokenAccount, Site, OrderShipment } = require('./database')
const grab_express = require('./delivery/grab_express')
const ahamove = require('./delivery/ahamove')
const shopee = require('./merchant/shopee')
const grab = require('./merchant/grab')
const grab_mart_official = require('./merchant/grab_mart_official')
const grab_food_official = require('./merchant/grab_food_official')
const gojek = require('./merchant/gojek')
const be = require('./merchant/be')
const zalo = require('./merchant/zalo')
const redis = require('./redis')
const _ = require('lodash')
const { send_zalo_message, ZALO_GROUPS } = require('./zalo')
const { is_token_expired } = require('./helper')

const token = {}

token.is_token_working = async (token_code) => {
  const token_account = await TokenAccount.findOne({ token_code })

  if (token_account.source === 'shopee') {
    const data = await shopee.get_store_list({ access_token: token_account.access_token })
    if (!data) {
      return false
    }
    return true
  }
  if (token_account.source === 'grab') {
    if (is_token_expired(token_account.access_token)) {
      return false
    }

    const store = await grab.get_store(token_account)
    if (store?.merchant?.name) {
      return true
    }
    return false
  }
  // if (token_account.source === 'gojek') {
  //   const store = await gojek.get_store(token_account)
  //   if (store?.brand_name) {
  //     return true
  //   }
  //   return false
  // }
  if (token_account.source === 'be') {
    if (is_token_expired(token_account.access_token)) {
      return false
    }

    const store = await be.get_store(token_account)
    if (store?.brand_name) {
      return true
    }
    return false
  }
  if (token_account.source === 'grab_express') {
    if (is_token_expired(token_account.access_token)) {
      return false
    }
    return true
  }

  if (token_account.source === 'zalo') {
    return moment(token_account.expired_at).isAfter(moment())
  }

  if (token_account.source === 'ahamove') {
    if (is_token_expired(token_account.access_token)) {
      return false
    }

    const store = await ahamove.get_store(token_account)
    if (store?.name) {
      const cache_key = `ahamove:${store.name}`
      if (store.main_account.VND < 500000) {
        const message = [
          `Tài khoản <bc style="color:#db342e">Ahamove của ${store.name} - ${store.email} chỉ còn ${store.main_account.VND.toLocaleString('en-US')}đ</bc>`,
          `Vui lòng nạp thêm tiền vào tài khoản!`
        ].join('\n')
        const last_message = await redis.get(cache_key)
        if (last_message !== message) {
          await redis.set(cache_key, message, { EX: 0 })
          await send_zalo_message({
            group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
            message,
          })
        }

      } else {
        const message = [
          `Tài khoản <bc style="color:#0000ff">Ahamove của ${store.name} - ${store.email} còn ${store.main_account.VND.toLocaleString('en-US')}đ</bc>`,
          `Cảm ơn bạn đã nạp tiền!`
        ].join('\n')
        const last_message = await redis.get(cache_key)
        if (last_message !== message) {
          await redis.set(cache_key, message, { EX: 0 })
          await send_zalo_message({
            group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
            message,
          })
        }
      }
      return true
    }
    return false
  }
}

// token.is_token_working('NEXDOR_ahamove')

token.refresh_new_token = async (token_code) => {
  const token_account = await TokenAccount.findOneAndUpdate({ token_code }, { updated_at: new Date() }, { new: true })
  let result = {
    success: true,
  }
  if (!token_account) {
    console.log('Token not found')
    return result
  }
  if (token_account.source === 'grab_express') {
    const data = await grab_express.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  if (token_account.source === 'ahamove') {
    const data = await ahamove.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  if (token_account.source === 'grab') {
    const data = await grab.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      token_account.site_id = data.site_id
      token_account.site_name = data.site_name
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  if (token_account.source === 'grab_food_official') {
    const data = await grab_food_official.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      token_account.site_id = ''
      token_account.site_name = ''
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  if (token_account.source === 'grab_mart_official') {
    const data = await grab_mart_official.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      token_account.site_id = ''
      token_account.site_name = ''
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  if (token_account.source === 'be') {
    const data = await be.get_token(token_account.username, token_account.password)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.access_token = data.access_token
      token_account.expired_at = moment().add(1, 'days').toDate()
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }
  // if (token_account.source === 'gojek') {
  //   let data = await gojek.get_token_by_refresh_token(token_account.username, token_account.site_id, token_account.refresh_token)
  //   if (!data && token_account.username && token_account.password) {
  //     data = await gojek.get_token(token_account.username, token_account.password)
  //   }

  //   if (data) {
  //     token_account.working = true
  //     token_account.last_working_at = new Date()
  //     token_account.fail_count = 0
  //     token_account.access_token = data.access_token
  //     token_account.refresh_token = data.refresh_token
  //     await token_account.save()
  //   } else {
  //     token_account.access_token = ''
  //     token_account.working = false
  //     token_account.site_name = ''
  //     await token_account.save()
  //     result.success = false
  //   }
  // }
  if (token_account.source === 'shopee') {
    const data = await shopee.get_store_list(token_account)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.expired_at = moment().add(365, 'days').toDate()
      token_account.site_name = data.map(v => v.store_name + '|' + v.store_id).join('\n')
      await token_account.save()
    } else {
      token_account.access_token = ''
      token_account.working = false
      token_account.site_name = ''
      await token_account.save()
      result.success = false
    }
  }

  if (token_account.source === 'zalo') {
    const data = await zalo.get_token(token_account)
    if (data) {
      token_account.working = true
      token_account.last_working_at = new Date()
      token_account.fail_count = 0
      token_account.expired_at = moment().add(20, 'hours').toDate()
      token_account.access_token = data.access_token
      token_account.refresh_token = data.refresh_token
      await token_account.save()
    } else {
      // token_account.access_token = ''
      // token_account.working = false
      // token_account.site_name = ''
      // await token_account.save()
      // result.success = false
    }
  }
  return result
}

// (async () => {
//   const accounts = await TokenAccount.find({ working: false })
//   for (const account of accounts) {
//     await token.refresh_new_token(account.token_code)
//   }
// })();
// token.refresh_new_token('NEXDOR.TEST_zalo')

token.get_token_by_code = async (token_code) => {
  const token_account = await TokenAccount.findOne({ token_code }).lean()
  return token_account
}

token.get_token_by_site = async (site, source) => {
  const site_token = site.getToken(source)
  if (!site_token?.token_code) {
    if (!site.is_head_site) {
      const head_site = await Site.findOne({ brand_id: site.brand_id, is_head_site: true })
      if (!head_site) return {}
      return token.get_token_by_site(head_site, source)
    }
    return {}
  }
  const token_account = await TokenAccount.findOne({ token_code: site_token.token_code }).lean()
  if (!token_account) return site_token
  return {
    ..._.pick(site_token, ['source', 'site_name', 'token_code', 'token_code_official']),
    site_id: site_token.site_id ? site_token.site_id : token_account.site_id,
    username: token_account.username,
    password: token_account.password,
    access_token: token_account.access_token,
    refresh_token: token_account.refresh_token,
    working: token_account.working ?? true,
    fail_count: token_account.fail_count,
  }
}

token.get_offline_token_by_site = async (site, source) => {
  const site_token = site.getToken(source)
  if (!site.is_head_site) {
    const head_site = await Site.findOne({ brand_id: site.brand_id, is_head_site: true })
    if (!head_site) return site_token
    return token.get_offline_token_by_site(head_site, source)
  }

  return site_token
}

token.get_token_by_brand = async (brand, source) => {
  const brand_token = brand.tokens.find(token => token.source === source)
  if (!brand_token) return {}
  if (!brand_token.token_code) return token
  const token_account = await TokenAccount.findOne({ token_code: brand_token.token_code }).lean()
  if (!token_account) return brand_token
  return {
    ...brand_token,
    site_id: token_account.site_id ? token_account.site_id : brand_token.site_id,
    username: token_account.username,
    password: token_account.password,
    access_token: token_account.access_token,
    refresh_token: token_account.refresh_token,
    working: token_account.working,
    fail_count: token_account.fail_count,
  }
}

module.exports = token