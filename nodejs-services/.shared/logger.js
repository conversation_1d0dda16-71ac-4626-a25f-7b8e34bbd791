const { createLogger, format, transports } = require('winston');

const options = {
    host: 'http-intake.logs.us5.datadoghq.com',
    path: '/api/v2/logs?dd-api-key=********************************&ddsource=nodejs&service=nexpos_service',
    ssl: true
};

const logger = createLogger({
    level: 'info',
    exitOnError: false,
    format: format.json(),
    transports: [
        new transports.Http(options),
    ],
});

module.exports = logger