const { initializeApp, cert } = require('firebase-admin/app');
const { getMessaging } = require('firebase-admin/messaging');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY);
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n'); // Fix bug when encode

const app = initializeApp({
    credential: cert(credentials),
});
const messaging = getMessaging(app);

module.exports.subscribe_a_topic = async (token, topic) => {
    try {
        const resp = await messaging.subscribeToTopic(token, topic);
        return resp;
    } catch (error) {
        console.log(error.message);
    }
};

module.exports.unsubscribe_a_topic = async (token, topic) => {
    try {
        const resp = await messaging.unsubscribeFromTopic(token, topic);
        return resp;
    } catch (error) {
        console.log(error.message);
    }
}

module.exports.send_message_to_topic = async (topic, message, data) => {
    try {
        const resp = await messaging.send({
            topic: topic,
            data: data || {},
            notification: {
                title: 'Thông báo',
                body: message,
            },
            android: {
                notification: {
                    title: 'Thông báo',
                    body: message,
                    sound: 'default',
                },
            },
            apns: {
                payload: {
                    aps: {
                        sound: 'default',
                    },
                },
            },
        });
        console.log(resp);
        return resp;
    } catch (error) {
        console.log(error.message);
    }
};