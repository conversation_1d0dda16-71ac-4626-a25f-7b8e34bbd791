const redis = require("./redis");
const moment = require("moment");

const client = {};
client.get_queue = async (name) => {
    const queue = await redis.getObj(name);
    if (queue) {
        return queue;
    }
    return [];
}

client.lswap_queue_by_id = async (name, id) => {
    const queue = await client.get_queue(name)

    const index = queue.findIndex(v => v._id === id);
    if (index === -1) {
        return queue;
    }
    const item = queue.splice(index, 1)[0];
    queue.unshift(item);
    await redis.setObj(name, queue, 60 * 60 * 24);
    return queue;
}


client.set_queue = async (name, queue_list) => {
    await redis.setObj(name, queue_list.map(v => ({ ...v, updated_at_unix: moment().unix() })), 60 * 60 * 24);
}

client.find_and_set_queue_count = async (name, query, item) => {
    const queue_list = await client.get_queue(name);
    const found_item = queue_list.find(query);
    item.updated_at_unix = moment().unix()
    if (found_item) {
        found_item.count += 1
        Object.assign(found_item, item);
        client.set_queue(name, queue_list);
    } else {
        item.count = 1
        queue_list.push(item);
        client.set_queue(name, queue_list);
    }
}

// size: number of queue to pick, default 5
// min_duration (seconds): minimum duration to pick queue again, default 60 seconds
client.pick_queue = async (name, { size = 5, min_duration = 60, delete_queue = false }) => {
    const queue_list = await client.get_queue(name);
    const selected_queues = queue_list.splice(0, size);
    if (selected_queues.length === 0) {
        return [];
    }

    const updated_at_unix = selected_queues[0].updated_at_unix;
    if (moment().unix() - updated_at_unix < min_duration) {
        return [];
    }

    if (!delete_queue) {
        queue_list.push(...selected_queues.map(v => ({ ...v, updated_at_unix: moment().unix() })));
    }
    await redis.setObj(name, queue_list, 60 * 60 * 24)
    return selected_queues;
}

client.delete_queue = async (name, items) => {
    const queue_list = await client.get_queue(name);
    const left_queue_list = queue_list.filter(v => !items.some(m => m._id === v._id));

    await redis.setObj(name, left_queue_list, 60 * 60 * 24)
}
module.exports = client;