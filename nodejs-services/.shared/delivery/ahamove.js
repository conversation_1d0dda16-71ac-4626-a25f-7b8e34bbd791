const axios = require('../axios')
const qs = require('qs')
const moment = require('moment')
const _ = require('lodash')
const PhoneNumber = require('libphonenumber-js');


// token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************.nCpCkF4n_SjUVLWBcNGwqy7GqFHxk4PmW_vXbgcjSdU',

const cfg = process.env.NODE_ENV === 'prod'
    ? {
        base_url: 'https://api.ahamove.com',
        api_key: '286779df9a9a6c52a498f978be0a63aba4fde967',
    }
    : {
        base_url: 'https://apistg.ahamove.com',
        api_key: '286779df9a9a6c52a498f978be0a63aba4fde967',
    }

const AHAMOVE_SERVICE_TYPE = {
    'SGN-BIKE': {
        name: '<PERSON><PERSON><PERSON>',
        description: 'Giao hàng nội thành trong 1 giờ',
    },
    'SGN-EXPRESS': {
        name: 'Siêu Tốc - Đồ Ăn',
        description: 'Giao siêu nhanh trong 1 giờ',
    },
    'SGN-2H': {
        name: '2H',
        description: 'Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%',
    },
    'SGN-2H-PUBLIC': {
        name: '2H',
        description: 'Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%',
    },
    'SGN-SAMEDAY': {
        name: '4H',
        description: 'Giao trong 4 giờ, giá siêu tiết kiệm',
    },
    'SGN-AHAPRO': {
        name: 'Siêu Cấp',
        description: 'Tài xế 5* đền bu lên tới 30 triệu',
    },
    'SGN-POOL': {
        name: 'Siêu rẻ',
        description: 'Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%',
    },
}

const SGN_BULK_OPTIONS = [
    { code: "TIER_1", name: "Tiêu chuẩn (50x40x50:30kg)", dimensions: { length: 50, width: 40, height: 50, weight: 30000 }, price: 0 },
    { code: "TIER_2", name: "Mức 1 (60x50x60:40kg)", dimensions: { length: 60, width: 50, height: 60, weight: 40000 }, price: 10000 },
    { code: "TIER_3", name: "Mức 2 (70x60x70:60kg)", dimensions: { length: 70, width: 60, height: 70, weight: 60000 }, price: 20000 },
    { code: "TIER_4", name: "Mức 3 (90x70x90:80kg)", dimensions: { length: 90, width: 70, height: 90, weight: 80000 }, price: 40000 }
];

exports.get_token = async function (username) {
    if (!username) {
        return null
    }
    const phone_number = PhoneNumber(username, 'VN');
    const phone_number_84 = '84' + phone_number.nationalNumber
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.ahamove.com/v1/partner/register_account',
        headers: {
            'content-type': 'application/json; charset=utf-8'
        },
        params: {
            "mobile": phone_number_84,
            "api_key": cfg.api_key,
            "name": phone_number_84,
        }
    };
    try {
        const resp = await axios(config);
        return {
            username: phone_number_84,
            access_token: resp.data.token,
            refresh_token: resp.data.refresh_token,
        }
    } catch (error) {
        console.log(error.message)
        return null
    }
}

exports.check_promo_code = async ({ access_token }, { promo_code, from, to, services }) => {
    try {
        const response = await axios({
            method: 'post',
            url: cfg.base_url + '/v2/order/estimated_fee',
            data: {
                payment_method: 'CASH',
                promo_code: promo_code,
                order_time: 0,
                path: [
                    {
                        address: from.address,
                        short_address: from.address,
                        name: from.name,
                        mobile: from.phone,
                        cod: 0,
                        item_value: 0,
                    },
                    {
                        address: to.address,
                        short_address: to.address,
                        name: to.name,
                        mobile: to.phone,
                        cod: 0,
                        item_description: 'Thực phẩm,Hàng tiêu dùng',
                        item_descriptions: [
                            {
                                code: '673',
                                keyword: 'Thực phẩm',
                                group: 'food',
                            },
                            {
                                code: '675',
                                keyword: 'Hàng tiêu dùng',
                                group: 'grocery',
                            },
                        ],
                        item_value: 0,
                    },
                ],
                images: [],
                package_detail: [],
                services: services,
                token: access_token,
            },
        })

        return response.data
    } catch (error) {
        console.error(error)
        return null
    }
}

exports.create_order = async ({ settings, access_token }, { dishes, promo_code, from, to, service, note, cod, tracking_number, dimensions, schedule_order_time }) => {
    try {
        const request_services = [] // Shiper ứng tiền COD cho cửa hàng

        // const request_services = [
        //     {
        //         _id: `${service.code}-TRANSFER-COD`, // Ứng tiền COD qua tài khoản, không thu tiền mặt
        //     }
        // ]
        if (service.extra_services) {
            for (const extra_service of service.extra_services) {
                request_services.push({
                    _id: `${service.code}-BULKY`,
                    tier_code: extra_service.code,
                })

            }
        }

        const data = {
            requests: JSON.stringify(request_services),
            service_id: service.code,
            payment_method: 'BALANCE',
            promo_code: promo_code || '',
            remarks: note,
            order_time: 0,
            path: JSON.stringify([{
                address: from.address,
                name: from.name,
                mobile: from.phone,
                cod: 0,
            }, {
                address: to.address,
                name: to.name,
                mobile: to.phone,
                cod: cod,
                tracking_number,
            }]),
            items: dishes.map((v, index) => ({
                _id: "dish-" + (index + 1),
                num: v.quantity,
                name: v.name,
                price: v.discount_price,
            })),
            token: access_token,
        }

        if (schedule_order_time) {
            if (schedule_order_time <= moment().add(1, 'minute').unix()) {
                schedule_order_time = moment().add(1, 'minute').unix()
            }
            data.order_time = schedule_order_time
            data.idle_until = schedule_order_time
        }

        console.log(JSON.stringify(data, null, 2))

        const response = await axios({
            method: 'post',
            url: cfg.base_url + '/v1/order/create',
            data: qs.stringify(data),
            headers: {
                'Cache-Control': 'no-cache',
                accept: 'application/json',
                'content-type': 'application/x-www-form-urlencoded',
            },
        })

        if (!response) {
            throw new Error('Error from ahamove')
        }
        console.log(response.data)
        return {
            shipment_id: response.data.order_id,
            tracking_url: response.data.shared_link,
            price: response.data.order.total_price,
            raw_request: data,
            raw_response: response.data,
        }
    } catch (error) {
        throw new Error('Ahamove: ' + JSON.stringify(error.response?.data?.description || error.response?.data || {}))
    }
}

exports.cancel_order = async ({ access_token }, { shipment_id }) => {
    try {
        const order_detail = await this.get_order_detail({ access_token }, { shipment_id })
        if (order_detail.status === 'CANCELLED') {
            return {
                success: true,
                message: 'Đơn hàng đã bị hủy trước đó',
            }
        }
        const response = await axios({
            method: 'get',
            url: cfg.base_url + '/v1/order/cancel',
            params: {
                token: access_token,
                order_id: shipment_id,
                comment: 'User changes pick up',
                cancel_code: 'user_incorrect_pickup',
            },
        })

        if (!response) {
            return {
                success: false,
                message: 'Error from ahamove: no response',
            }
        }
        console.log(response.data)
        return {
            success: true,
            message: '',
        }
    } catch (error) {
        return {
            success: false,
            message: JSON.stringify(error.response?.data?.description || error.response?.data || {}),
        }
    }
}

exports.get_order_detail = async ({ access_token }, { shipment_id }) => {
    try {
        const response = await axios({
            method: 'get',
            url: cfg.base_url + '/v1/order/detail',
            params: {
                token: access_token,
                order_id: shipment_id,
            },
        })

        if (!response) {
            return null
        }
        console.log(response.data)
        return response.data
    } catch (error) {
        console.error(error)
        return null
    }
}

exports.get_shipments = async ({ access_token, site_data }, { from, to, dimensions = {} }) => {
    if (!access_token) {
        return []
    }

    // let service_types = site_data?.service_types || ["SGN-BIKE", "SGN-EXPRESS", "SGN-2H-PUBLIC", "SGN-SAMEDAY", "SGN-AHAPRO", "SGN-POOL"]
    let service_types = ["SGN-BIKE", "SGN-2H", "SGN-EXPRESS"]
    if (site_data?.service_types?.length > 0) {
        service_types = site_data.service_types.split('|').map(v => v.trim())
    }
    let order_time = 0
    console.log(moment().hours())
    if (moment().hours() >= 20) {
        order_time = moment().add(1, 'day').startOf('day').add(9, 'hours').unix();
    }


    try {
        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: cfg.base_url + '/v2/order/estimated_fee?lang=vi',
            data: {
                token: access_token,
                services: service_types.map(v => ({
                    _id: v,
                    requests: [],
                })),
                order_time: order_time,
                idle_until: order_time,
                payment_method: 'cash',
                path: [
                    {
                        address: from.address,
                        short_address: from.address,
                        name: from.name,
                        mobile: from.phone,
                        cod: 0,
                        item_value: 0,
                    },
                    {
                        address: to.address,
                        short_address: to.address,
                        name: to.name,
                        mobile: to.phone,
                        cod: 0,
                        item_description: 'Thực phẩm,Hàng tiêu dùng',
                        item_descriptions: [
                            {
                                code: '673',
                                keyword: 'Thực phẩm',
                                group: 'food',
                            },
                            {
                                code: '675',
                                keyword: 'Hàng tiêu dùng',
                                group: 'grocery',
                            },
                        ],
                        item_value: 0,
                    },
                ],
                order_time: 0,
            },
        })
        return resp.data.map(v => this.mapping_shipment(v, dimensions))
    } catch (error) {
        console.log(error)
        return []
    }
}

exports.get_store = async ({ access_token }) => {
    if (!access_token) {
        return {}
    }

    try {
        const resp = await axios.get(cfg.base_url + '/api/v3/private/user/self', {
            params: {
                token: access_token,
            },
        })

        return resp.data
    } catch (error) {
        console.log(error)
        return null
    }
}

exports.webhook = async (data) => {
    try {
        const { _id, status, supplier_id, supplier_name, shared_link, cancel_code, cancel_comment, rebroadcast_comment } = data;

        const STATUS_MAP = {
            'ASSIGNING': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_ASSIGNING' },
            'ACCEPTED': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_PICKING_UP' },
            'IN PROCESS': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            'COMPLETED': { orderStatus: 'FINISH', shipmentStatus: 'COMPLETED' },
            'CANCELLED': { orderStatus: '', shipmentStatus: 'CANCELLED' },
        };
        const CANCEL_MAP = {
            supplier_driver_not_available: { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Không tìm được tài xế' },
            supplier_not_enough_advance_money: { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Tài xế không đủ tiền ứng trước   ' },
            supplier_sender_cancel: { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Tài xế hủy cuốc' },
            supplier_unable_to_contact_sender: { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Tài xế không liên hệ được với người gửi' },
            supplier_wait_for_pickup_too_long: { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Tài xế đợi lấy hàng quá lâu' },
            user_incorrect_pickup: { cancel_by: 'merchant', cancel_type: '', cancel_reason: 'Quán hủy đơn' },
        }

        const { orderStatus, shipmentStatus } = STATUS_MAP[status];

        const result = {
            shipment_id: _id,
            order_id: null,
            shipment_status: shipmentStatus,
            order_status: orderStatus,
            driver_name: supplier_name || '',
            driver_phone: supplier_id || '',
            tracking_url: shared_link,
        }
        if (shipmentStatus === 'CANCELLED') {
            const cancel = CANCEL_MAP[cancel_code]
            if (cancel) {
                result.cancel = cancel
                result.cancel.cancel_reason = `${cancel_code} (${cancel.cancel_reason}), Chi tiết: ${cancel_comment} ${rebroadcast_comment}`
            } else {
                result.cancel = { cancel_by: 'system', cancel_type: '', cancel_reason: `${cancel_code},  Chi tiết: ${cancel_comment} ${rebroadcast_comment}` }
            }
        }
        return result
    } catch (error) {
        console.error(error)
        return {}
    }
}

exports.mapping_shipment = (item, dimensions) => {
    let extra_services = []
    if (item._id === 'SGN-BIKE') {
        if (dimensions.width > 0 && dimensions.length > 0 && dimensions.height > 0 && dimensions.weight > 0) {
            const available_tiers = SGN_BULK_OPTIONS.filter(v =>
                v.dimensions.width * v.dimensions.length * v.dimensions.height >= dimensions.width * dimensions.length * dimensions.height &&
                v.dimensions.weight >= dimensions.weight)
            available_tiers.sort((a, b) => a.price - b.price)
            if (available_tiers.length > 0 && available_tiers[0].price > 0) {
                extra_services.push({
                    name: available_tiers[0].name,
                    code: available_tiers[0].code,
                    price: available_tiers[0].price,
                })
            }
        }
    }


    return {
        vendor: 'ahamove',
        code: item._id,
        group_code: {
            'SGN-BIKE': 'INSTANT',
            'SGN-EXPRESS': 'INSTANT',
            'SGN-2H': 'INSTANT',
            'SGN-2H-PUBLIC': 'INSTANT',
            'SGN-SAMEDAY': 'SAME_DAY',
            'SGN-AHAPRO': 'INSTANT',
            'SGN-POOL': 'INSTANT',
        }[item._id],
        price: item.total_price + _.sumBy(extra_services, 'price'),
        name: AHAMOVE_SERVICE_TYPE[item._id].name,
        description: AHAMOVE_SERVICE_TYPE[item._id].description + (extra_services.length > 0 ? ` + ${extra_services.map(v => v.name).join(', ')}` : ''),
        extra_services,
        raw: {
            ...item,
            schedule: {
                from_time: moment().add(1, 'minutes').format('HH:mm'),
                to_time: moment().add(1, 'minutes').add(item.duration, 'seconds').format('HH:mm'),
                from_date_time: moment().add(1, 'minutes').toISOString(),
                to_date_time: moment().add(1, 'minutes').add(item.duration, 'seconds').toISOString(),
            }
        }
    }
}
