const axios = require('axios');
exports.get_location = async (address) => {
    const resp = await axios({
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://maps.googleapis.com/maps/api/geocode/json',
        headers: {},
        params: {
            address,
            key: process.env.GOOGLE_MAP_API_KEY,
        }
    })
    return resp.data.results[0]
}

exports.get_suggestion_addresses = async (address) => {
    const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
        headers: {},
        params: {
            input: address,
            key: process.env.GOOGLE_MAP_API_KEY,
            length: 10,
            components: 'country:VN',
            language: 'vi'
        }
    }
    try {
        const resp = await axios(config)
        if (resp.data.status === 'OK') {
            let result = []
            for (let i = 0; i < resp.data.predictions.length; i++) {
                const raw_address = resp.data.predictions[i]
                const term_length = raw_address.terms.length
                if (term_length < 5) {
                    continue
                }
                result.push({
                    formatted_address: raw_address.description,
                    location: {
                        lat: 0,
                        lng: 0,
                    },
                    province_name: raw_address.terms[term_length - 2]?.value,
                    district_name: raw_address.terms[term_length - 3]?.value,
                    ward_name: raw_address.terms[term_length - 4]?.value,
                    route: raw_address.terms[term_length - 5]?.value,
                    raw: raw_address,
                })

            }
            return result
        } else {
            console.error('Error occurred:', resp.data.status);
            return [];
        }
    } catch (error) {
        console.log(error)
        return []
    }
}


exports.get_distance = async (from_address, to_address) => {
    const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://maps.googleapis.com/maps/api/distancematrix/json',
        headers: {},
        params: {
            destinations: to_address,
            key: process.env.GOOGLE_MAP_API_KEY,
            origins: from_address,
        }
    }
    try {
        const resp = await axios(config)
        if (resp.data.status === 'OK') {
            console.log(resp.data.rows[0]?.elements[0])
            return resp.data.rows[0]?.elements[0]?.distance
        } else {
            console.error('Error occurred:', resp.data.status);
            return null;
        }
    } catch (error) {
        console.log(error)
        return []
    }

}