const axios = require('../axios')

const moment = require('moment');
require('moment-duration-format');
const PhoneNumber = require('libphonenumber-js');

const { get_location } = require('./gmap');

const cfg = process.env.NODE_ENV === 'prod'
    ? {
        base_url: 'https://partner-api.grab.com/grab-express',
    }
    : {
        base_url: 'https://partner-api.grab.com/grab-express-sandbox',
    }

exports.get_token = async function (username, password) {
    if (!password) {
        return null
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://partner-api.grab.com/grabid/v1/oauth2/token',
        headers: {
            'content-type': 'application/json; charset=utf-8'
        },
        data: {
            "client_id": username,
            "client_secret": password,
            "grant_type": "client_credentials",
            "scope": "grab_express.partner_deliveries"
        }
    };
    try {
        const resp = await axios(config);
        return {
            access_token: resp.data.access_token
        }
    } catch (error) {
        console.log(error.message)
        return null
    }
}

exports.get_shipments = async ({ site_data, access_token }, { from, to }, vendor = 'grab_express') => {
    if (!access_token) {
        return []
    }
    const from_location = await get_location(from.address)
    const to_location = await get_location(to.address)
    let quotes = []

    let service_types = vendor === 'grab_express' ? ['INSTANT', 'SAME_DAY'] : ['INSTANT']
    if (site_data?.service_types?.length > 0) {
        service_types = site_data.service_types.split('|').map(v => v.trim())
    }

    for (const serviceType of service_types) {
        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: cfg.base_url + '/v1/deliveries/quotes',
            headers: {
                'Authorization': `Bearer ${access_token}`,
                'Content-Type': 'application/json',
            },
            data: {
                "serviceType": serviceType,
                // "vehicleType": "BIKE",
                "codType": "REGULAR",
                "packages": [
                    {
                        "name": "Hàng tiêu dùng",
                        "description": "Hàng tiêu dùng, bánh, sữa",
                        "quantity": 1,
                        "price": 1,
                        "dimensions": {
                            "height": 0,
                            "width": 0,
                            "depth": 0,
                            "weight": 0
                        }
                    },
                ],
                "origin": {
                    "address": from.address,
                    "coordinates": {
                        "latitude": from_location.geometry.location.lat,
                        "longitude": from_location.geometry.location.lng,
                    }
                },
                "destination": {
                    "address": to.address,
                    "coordinates": {
                        "latitude": to_location.geometry.location.lat,
                        "longitude": to_location.geometry.location.lng,
                    }
                }
            }
        }
        try {
            const resp = await axios(config)
            quotes = quotes.concat(resp.data.quotes.map(v => ({
                ...v,
                packages: resp.data.packages,
                origin: resp.data.origin,
                destination: resp.data.destination,
            })))
        } catch (error) {
            console.log(error)
        }

    }
    return quotes.map(v => this.mapping_shipment(vendor, v))
}

exports.create_order = async ({ access_token }, { dishes, from, to, note, tracking_number, cod, service, schedule_order_time }) => {
    const from_phone_number = PhoneNumber(from.phone, 'VN');
    const to_phone_number = PhoneNumber(to.phone, 'VN');

    const from_location = await get_location(from.address)
    const to_location = await get_location(to.address)

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: cfg.base_url + '/v1/deliveries',
        headers: {
            Authorization: `Bearer ${access_token}`,
            'Content-Type': 'application/json',
        },
        data: {
            merchantOrderID: tracking_number + '_' + moment().unix(),
            serviceType: service?.code || 'INSTANT',
            vehicleType: 'BIKE',
            codType: 'REGULAR',
            paymentMethod: 'CASHLESS', // Delivery fee (paid to driver) payment method. Valid values are CASH, CASHLESS.
            highValue: false,
            packages: dishes.map(v => ({
                name: v.name,
                description: v.description || '',
                quantity: v.quantity,
                price: v.discount_price,
                dimensions: {
                    "height": 0,
                    "width": 0,
                    "depth": 0,
                    "weight": 0
                }
            })),
            origin: {
                address: from.address,
                coordinates: {
                    latitude: from_location.geometry.location.lat,
                    longitude: from_location.geometry.location.lng,
                }
            },
            destination: {
                address: to.address,
                coordinates: {
                    latitude: to_location.geometry.location.lat,
                    longitude: to_location.geometry.location.lng,
                },
                cashOnDelivery: { amount: cod },
            },
            recipient: {
                firstName: to.name,
                phone: to_phone_number.number?.replace('+', ''),
                smsEnabled: true,
            },
            sender: {
                firstName: from.name,
                phone: from_phone_number.number?.replace('+', ''),
                smsEnabled: true,
                instruction: note
            },
            payer: 'SENDER',
            cashOnDelivery: {
                amount: cod,
            },
        },
    };

    if (schedule_order_time && schedule_order_time > moment().add(1, 'minute').unix()) {
        config.data.schedule = {
            pickupTimeFrom: moment.unix(schedule_order_time).format('YYYY-MM-DDTHH:mm:ssZ'),
            pickupTimeTo: moment.unix(schedule_order_time).format('YYYY-MM-DDTHH:mm:ssZ'),
        };
    } else if (service?.code === 'INSTANT') {
        config.data.schedule = {
            pickupTimeFrom: moment().add(1, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
            pickupTimeTo: moment().add(1, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        };
    } else if (service?.code === 'SAME_DAY') {
        config.data.schedule = {
            pickupTimeFrom: moment().add(5, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
            pickupTimeTo: moment().add(5, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
        };
    }
    try {
        console.log(config)
        const resp = await axios(config);
        console.log(resp.data)
        return {
            shipment_id: resp.data.deliveryID,
            tracking_url: resp.data.trackingURL || '',
            price: resp.data.quote.amount,
            raw_request: config,
            raw_response: resp.data,
        }
    } catch (error) {
        console.log(error.response?.data)
        console.error(error);
    }
};


exports.get_order = async ({ access_token }, shipment_id) => {
    try {
        const response = await axios({
            method: 'GET',
            url: cfg.base_url + '/v1/deliveries/' + shipment_id,
            headers: {
                Authorization: `Bearer ${access_token}`,
                'Content-Type': 'application/json',
            },
        })

        if (!response) {
            return {
                success: false,
                message: 'No response'
            }
        }
        console.log(response.data)
        if (response.status >= 300) {
            return {
                success: false,
                message: JSON.stringify(response.data),
            }
        }
        return {
            success: true,
            data: response.data,
        }
    } catch (error) {
        return {
            success: false,
            message: JSON.stringify(error?.response?.data)
        }
    }
}

exports.cancel_order = async ({ access_token }, { shipment_id, order_id }) => {
    try {
        const response = await axios({
            method: 'DELETE',
            url: cfg.base_url + '/v1/deliveries/' + shipment_id,
            headers: {
                Authorization: `Bearer ${access_token}`,
                'Content-Type': 'application/json',
            },
        })

        if (!response) {
            return {
                success: false,
                message: 'No response'
            }
        }
        console.log(response.data)
        if (response.status >= 300) {
            return {
                success: false,
                message: JSON.stringify(response.data),
            }
        }
        return {
            success: true,
            message: ''
        }
    } catch (error) {
        return {
            success: false,
            message: JSON.stringify(error?.response?.data)
        }
    }
}

exports.webhook = async (data) => {
    try {
        const { merchantOrderID, deliveryID, status, trackURL, driver, failedReason } = data;
        const statusMap = {
            'QUEUEING': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            'ALLOCATING': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_ASSIGNING' },
            'PENDING_PICKUP': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_PICKING_UP' },
            'PICKING_UP': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_PICKING_UP' },
            'IN_DELIVERY': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            'PENDING_DROP_OFF': { orderStatus: 'PICK', shipmentStatus: 'DRIVER_PICKING_UP' },
            'COMPLETED': { orderStatus: 'FINISH', shipmentStatus: 'COMPLETED' },
            'IN_RETURN': { orderStatus: 'RETURNING', shipmentStatus: 'RETURNED' },
            'RETURNED': { orderStatus: 'RETURNED', shipmentStatus: 'RETURNED' },
            'CANCELED': { orderStatus: '', shipmentStatus: 'CANCELLED' },
            'FAILED': { orderStatus: '', shipmentStatus: 'CANCELLED' },
        };

        const { orderStatus, shipmentStatus } = statusMap[status];

        const CANCEL_MAP = {
            '1|Canceled by Sender': { cancel_by: 'merchant', cancel_type: '', cancel_reason: 'Quán hủy đơn' },
            '2|Canceled by Driver: fake sender/order': { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Tài xế hủy đơn' },
            '5|Canceled by Grab Operator': { cancel_by: 'system', cancel_type: '', cancel_reason: 'Hủy bởi Grab (hệ thống)' },
            '6|Could not find driver': { cancel_by: 'driver', cancel_type: '', cancel_reason: 'Không tìm được tài xế' },

        }

        const result = {
            shipment_id: deliveryID,
            order_id: merchantOrderID,
            shipment_status: shipmentStatus,
            order_status: orderStatus,
            driver_name: driver?.name || '',
            driver_phone: driver?.phone || '',
            tracking_url: trackURL,
        }

        if (shipmentStatus === 'CANCELLED') {
            const cancel = CANCEL_MAP[failedReason]
            if (cancel) {
                result.cancel = cancel
                result.cancel.cancel_reason = `${failedReason} (${cancel.cancel_reason})`
            } else {
                result.cancel = { cancel_by: 'system', cancel_type: '', cancel_reason: failedReason }
            }
        }
        return result
    } catch (error) {
        console.error(error)
        return {}
    }
}


exports.mapping_shipment = (vendor, item) => {
    const pickupTime = moment(item.estimatedTimeline.pickup);
    const completedTime = moment(item.estimatedTimeline.dropoff);
    const duration = moment.duration(completedTime.diff(pickupTime)).format('h [giờ] m [phút]', { locale: 'vi' });

    return {
        vendor: vendor,
        code: item.service.type,
        group_code: item.service.type, // INSTANT, SAME_DAY
        price: item.amount,
        name: {
            SAME_DAY: `Trong ngày`,
            INSTANT: `Siêu tốc`,
        }[item.service.type],
        description: `Dự kiến thời gian lấy hàng: ${pickupTime.format('HH:mm')}, giao hàng: ${completedTime.format('HH:mm')}`,
        raw: {
            ...item,
            schedule: {
                from_time: pickupTime.format('HH:mm'),
                to_time: completedTime.format('HH:mm'),
                from_date_time: pickupTime.toISOString(),
                to_date_time: completedTime.toISOString(),
            }
        }
    }
}
