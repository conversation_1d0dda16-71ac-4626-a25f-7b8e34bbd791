const axios = require('../axios')

const moment = require('moment');
require('moment-duration-format');
const PhoneNumber = require('libphonenumber-js');

const { get_location } = require('./gmap');
const _ = require('lodash');
const helper = require('../helper');
const redis = require('../redis');

exports.get_provinces = async () => {
    let all_provice = await redis.getObj('viettel_post:all_provice');
    if (!all_provice) {
        const resp_3 = await axios.get('https://api.viettelpost.vn/api/setting/listallprovince')
        all_provice = resp_3.data
        redis.setObj('viettel_post:all_provice', all_provice, 24 * 60 * 60);
    }
    return all_provice.map(v => ({
        code: v.PROVINCE_ID,
        name: v.PROVINCE_NAME,
        slug: helper.text_slugify(v.PROVINCE_NAME)
    }))
}

exports.get_districts = async (province_code) => {
    let all_district = await redis.getObj('viettel_post:all_district');
    if (!all_district) {
        const resp_4 = await axios.get('https://api.viettelpost.vn/api/setting/listalldistrict')
        all_district = resp_4.data
        redis.setObj('viettel_post:all_district', all_district, 24 * 60 * 60);
    }

    return all_district.filter(v => v.PROVINCE_ID === province_code).map(v => ({
        code: v.DISTRICT_ID,
        name: v.DISTRICT_NAME,
        slug: helper.text_slugify(v.DISTRICT_NAME)
    }))
}

exports.get_token = async function (username, password) {
    if (!password) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://partner.viettelpost.vn/v2/user/Login',
        headers: {
            'content-type': 'application/json; charset=utf-8'
        },
        data: {
            "USERNAME": username,
            "PASSWORD": password
        }
    };
    try {
        const resp = await axios(config);

        let config2 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://partner.viettelpost.vn/v2/user/Login',
            headers: {
                'content-type': 'application/json; charset=utf-8',
                'Token': resp.data.data.token
            },
            data: {
                "USERNAME": username,
                "PASSWORD": password
            }
        };
        const resp2 = await axios(config2);

        return {
            access_token: resp2.data.data.token
        }
    } catch (error) {
        console.log(error.message)
        return {}
    }
}

// exports.google_address_to_viettel_post_province = async (address) => {
//     try {
//         const location = await get_location(address)
//         const resp = await axios.get(`https://location.viettelpost.vn/location/v1.0/autocomplete`, {
//             params: {
//                 q: address,
//                 system: 'VTP'
//             }
//         })

//         const sort_by_distance = (a, b, location) => {
//             const distanceA = Math.sqrt(Math.pow(a.l.lat - location.geometry.location.lat, 2) + Math.pow(a.l.lng - location.geometry.location.lng, 2));
//             const distanceB = Math.sqrt(Math.pow(b.l.lat - location.geometry.location.lat, 2) + Math.pow(b.l.lng - location.geometry.location.lng, 2));
//             return distanceA - distanceB;
//         }

//         resp.data.sort((a, b) => {
//             return sort_by_distance(a, b, location);
//         });
//         const provice_name = _.last(resp.data[0].name.split(' - '));
//         const provinces = await this.get_provinces();
//         const provice = provinces.find(v => helper.text_slugify(v.name) === helper.text_slugify(provice_name));

//         return provice?.code

//     } catch (error) {
//         return ''
//     }
// }

exports.get_shipments = async ({ access_token }, { from, to, dimensions = {} }) => {
    if (!access_token) {
        return []
    }
    try {
        const from_location = await get_location(from.address)
        const to_location = await get_location(to.address)

        const resp_1 = await axios.get(`https://location.viettelpost.vn/location/v1.0/autocomplete`, {
            params: {
                q: from.address,
                system: 'VTP'
            }
        })
        const resp_2 = await axios.get(`https://location.viettelpost.vn/location/v1.0/autocomplete`, {
            params: {
                q: to.address,
                system: 'VTP'
            }
        })

        const sort_by_distance = (a, b, location) => {
            const distanceA = Math.sqrt(Math.pow(a.l.lat - location.geometry.location.lat, 2) + Math.pow(a.l.lng - location.geometry.location.lng, 2));
            const distanceB = Math.sqrt(Math.pow(b.l.lat - location.geometry.location.lat, 2) + Math.pow(b.l.lng - location.geometry.location.lng, 2));
            return distanceA - distanceB;
        }
        resp_1.data.sort((a, b) => {
            return sort_by_distance(a, b, from_location);
        });
        resp_2.data.sort((a, b) => {
            return sort_by_distance(a, b, to_location);
        });

        const from_provice_name = parseLocationName(resp_1.data[0].name).province
        const from_district_name = parseLocationName(resp_1.data[0].name).district

        const to_provice_name = parseLocationName(resp_2.data[0].name).province
        const to_district_name = parseLocationName(resp_2.data[0].name).district

        // Cùng tỉnh, skip
        if (from_provice_name === to_provice_name) {
            return []
        }

        // let all_provice = await redis.getObj('viettel_post:all_provice');
        // let all_district = await redis.getObj('viettel_post:all_district');
        // if (!all_provice) {
        //     const resp_3 = await axios.get('https://api.viettelpost.vn/api/setting/listallprovince')
        //     all_provice = resp_3.data
        //     redis.setObj('viettel_post:all_provice', all_provice, 24 * 60 * 60);
        // }
        // if (!all_district) {
        //     const resp_4 = await axios.get('https://api.viettelpost.vn/api/setting/listalldistrict')
        //     all_district = resp_4.data
        //     redis.setObj('viettel_post:all_district', all_district, 24 * 60 * 60);
        // }

        const all_provice = await this.get_provinces();

        const from_provice = all_provice.find(v => helper.text_slugify(v.name) === helper.text_slugify(from_provice_name));
        const form_districts = await this.get_districts(from_provice.code);
        const from_district = form_districts.find(v => helper.text_slugify(v.name) === helper.text_slugify(from_district_name));

        const to_provice = all_provice.find(v => helper.text_slugify(v.name) === helper.text_slugify(to_provice_name));
        const to_districts = await this.get_districts(to_provice.code);
        const to_district = to_districts.find(v => helper.text_slugify(v.name) === helper.text_slugify(to_district_name));

        const config5 = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://partner.viettelpost.vn/v2/order/getPriceAll',
            headers: {
                'Token': access_token,
                'Content-Type': 'application/json',
            },
            data: {
                "SENDER_DISTRICT": from_district.code,
                "SENDER_PROVINCE": from_provice.code,
                "RECEIVER_DISTRICT": to_district.code,
                "RECEIVER_PROVINCE": to_provice.code,
                "PRODUCT_TYPE": "HH",
                "PRODUCT_WEIGHT": dimensions.weight || 1000,
                "PRODUCT_LENGTH": dimensions.length || 0,
                "PRODUCT_WIDTH": dimensions.width || 0,
                "PRODUCT_HEIGHT": dimensions.height || 0,
                "PRODUCT_PRICE": 0,
                "MONEY_COLLECTION": 0,
                "TYPE": 1
            }
        }
        const resp5 = await axios(config5)

        return resp5.data.map(v => this.mapping_shipment(v))
    } catch (error) {
        return []
    }


}

const parseLocationName = (name) => {
    const nameParts = name.split(' - ');

    if (name.includes('Bà Rịa - Vũng Tàu')) {
        const lastIndex = nameParts.length - 2
        return {
            province: 'T.Bà Rịa - Vũng Tàu',
            district: nameParts[lastIndex - 1]
        };
    }

    // Normal case for other locations
    return {
        province: _.last(nameParts),
        district: _.nth(nameParts, -2)
    };
}


exports.create_order = async ({ access_token }, { dishes, from, to, note, tracking_number, cod, service, schedule_order_time, dimensions = {} }) => {
    const from_phone_number = PhoneNumber(from.phone, 'VN');
    const to_phone_number = PhoneNumber(to.phone, 'VN');

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://partner.viettelpost.vn/v2/order/createOrderNlp',
        headers: {
            Token: access_token,
            'Content-Type': 'application/json',
        },
        data: {
            "ORDER_NUMBER": tracking_number,
            "SENDER_FULLNAME": from.name,
            "SENDER_ADDRESS": from.address,
            "SENDER_PHONE": from.phone,
            "RECEIVER_FULLNAME": to.name,
            "RECEIVER_ADDRESS": to.address,
            "RECEIVER_PHONE": to.phone,
            "PRODUCT_NAME": dishes.map(v => v.name).join(', '),
            "PRODUCT_DESCRIPTION": "Cho khách xem hàng khi nhận hàng",
            "PRODUCT_QUANTITY": _.sumBy(dishes, 'quantity'),
            "PRODUCT_PRICE": _.sumBy(dishes, 'price'),
            "PRODUCT_WEIGHT": dimensions.weight || 1000,
            "PRODUCT_LENGTH": dimensions.length || 0,
            "PRODUCT_WIDTH": dimensions.width || 0,
            "PRODUCT_HEIGHT": dimensions.height || 0,
            "ORDER_PAYMENT": 3, // 1. Không thu hộ, 2. Thu hộ tiền hàng và tiền cước, 3. Thu hộ tiền hàng, 4. Thu hộ tiền cước
            "ORDER_SERVICE": service.code,
            "ORDER_SERVICE_ADD": null,
            "ORDER_NOTE": "Cho khách xem hàng khi nhận hàng",
            "MONEY_COLLECTION": cod,
            "LIST_ITEM": dishes.map(v => ({
                "PRODUCT_NAME": v.name,
                "PRODUCT_QUANTITY": v.quantity,
                "PRODUCT_PRICE": v.price,
                "PRODUCT_WEIGHT": 100,
            }
            ))
        },
    };

    try {
        console.log(config)
        const resp = await axios(config);
        console.log(resp.data)
        return {
            shipment_id: resp.data.data.ORDER_NUMBER,
            tracking_url: '',
            price: resp.data.data.MONEY_TOTAL,
            raw_request: config,
            raw_response: resp.data,
        }
    } catch (error) {
        console.log(error.response?.data)
        console.error(error);
        return null
    }
};

exports.cancel_order = async ({ access_token }, { shipment_id }) => {
    try {
        const resp = await axios({
            method: 'POST',
            url: `https://partner.viettelpost.vn/v2/order/UpdateOrder`,
            headers: {
                Token: access_token,
                'Content-Type': 'application/json',
            },
            data: {
                "TYPE": 4,
                "ORDER_NUMBER": shipment_id,
                "NOTE": "Đặt nhầm đơn hàng",
            }
        })

        return {
            success: resp.data?.status === 200 || resp.data?.status === 203,
            message: resp.data?.message || ''
        }
    } catch (error) {
        return {
            success: false,
            message: JSON.stringify(error?.response?.data)
        }
    }
}

// // {
//   "DATA":{
//     "ORDER_NUMBER":"10345381626",
//     "ORDER_REFERENCE":"TKS1801492",
//     "ORDER_STATUSDATE":"13/12/2018 17:34:05",
//     "ORDER_STATUS":200,
//     "STATUS_NAME":"Nhận từ bưu tá - Bưu cục gốc",
//     "LOCALION_CURRENTLY":"TT Quận 1 - Hồ Chí Minh",
//     "NOTE":"Giao cho bưu cục",
//     "MONEY_COLLECTION":1500000,
//     "MONEY_FEECOD":0,
//     "MONEY_TOTAL":45650,
//     "EXPECTED_DELIVERY":"Khoảng 2 ngày làm việc",
//     "PRODUCT_WEIGHT":245,
//     "ORDER_SERVICE":"SCOD"
//   },
//   "TOKEN":""
// }

exports.webhook = async (data) => {
    try {
        const {
            ORDER_NUMBER,
            ORDER_REFERENCE,
            ORDER_STATUSDATE,
            ORDER_STATUS,
            NOTE,
        } = data.DATA;

        const statusMap = {
            '-100': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATING' },
            '100': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            '102': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            '103': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            '104': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            '105': { orderStatus: 'PICK', shipmentStatus: 'DRIVER_PICKING_UP' },
            '107': { orderStatus: '', shipmentStatus: 'CANCELLED' },
            '200': { orderStatus: 'DOING', shipmentStatus: 'ORDER_CREATED' },
            '201': { orderStatus: '', shipmentStatus: 'CANCELLED' },
            '202': { orderStatus: 'DOING', shipmentStatus: 'IN_DELIVERY' },
            '300': { orderStatus: 'DOING', shipmentStatus: 'IN_DELIVERY' },
            '400': { orderStatus: 'DOING', shipmentStatus: 'IN_DELIVERY' },
            '500': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            '501': { orderStatus: 'FINISH', shipmentStatus: 'COMPLETED' },
            '502': { orderStatus: 'RETURNED', shipmentStatus: 'RETURNED' },
            '503': { orderStatus: '', shipmentStatus: 'CANCELLED' },
            '504': { orderStatus: 'RETURNED', shipmentStatus: 'RETURNED' },
            '505': { orderStatus: 'RETURNED', shipmentStatus: 'RETURNED' },
            '506': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            '507': { orderStatus: '', shipmentStatus: 'CANCELLED' },
            '508': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            '509': { orderStatus: 'PICK', shipmentStatus: 'IN_DELIVERY' },
            '550': { orderStatus: 'DOING', shipmentStatus: 'DRIVER_ASSIGNING' }
        };

        const { orderStatus, shipmentStatus } = statusMap[String(ORDER_STATUS)];

        const CANCEL_MAP = {
            '-100': {
                cancel_by: 'system',
                cancel_type: 'not_approved',
                cancel_reason: 'Đơn hàng mới tạo, chưa được duyệt'
            },
            '-108': {
                cancel_by: 'system',
                cancel_type: 'post_office',
                cancel_reason: 'Đơn hàng đã được gửi tại bưu điện'
            },
            '-109': {
                cancel_by: 'system',
                cancel_type: 'collection_point',
                cancel_reason: 'Đơn hàng đã được gửi tại các điểm thu gom'
            },
            '-110': {
                cancel_by: 'system',
                cancel_type: 'handover',
                cancel_reason: 'Đơn hàng được bàn giao bởi bưu điện'
            },
            '101': {
                cancel_by: 'system',
                cancel_type: 'system_request',
                cancel_reason: 'ViettelPost yêu cầu khách hàng hủy đơn hàng'
            },
            '107': {
                cancel_by: 'merchant',
                cancel_type: 'api_request',
                cancel_reason: 'Đối tác yêu cầu hủy đơn hàng qua API'
            },
            '201': {
                cancel_by: 'system',
                cancel_type: 'delivery_note',
                cancel_reason: 'Hủy sửa ghi chú giao hàng'
            },
            '503': {
                cancel_by: 'customer',
                cancel_type: 'customer_request',
                cancel_reason: 'Khách hàng yêu cầu hủy đơn hàng'
            },
            '510': {
                cancel_by: 'system',
                cancel_type: 'delivery_cancel',
                cancel_reason: 'Hủy giao hàng'
            }
        };

        const result = {
            shipment_id: ORDER_NUMBER,
            order_id: ORDER_REFERENCE,
            shipment_status: shipmentStatus,
            order_status: orderStatus,
            driver_name: '',
            driver_phone: '',
            tracking_url: '',
        }

        if (shipmentStatus === 'CANCELLED') {
            const cancel = CANCEL_MAP[String(ORDER_STATUS)]
            if (cancel) {
                result.cancel = cancel
                result.cancel.cancel_reason = `${NOTE} (${cancel.cancel_reason})`
            } else {
                result.cancel = { cancel_by: 'system', cancel_type: '', cancel_reason: NOTE }
            }
        }
        return result
    } catch (error) {
        console.error(error)
        return {}
    }
}


exports.mapping_shipment = (item) => {
    const duration = item.THOI_GIAN.replace(' ngày', 'd').replace(' giờ', 'h').replace(' phút', 'm');
    return {
        vendor: 'viettel_post',
        code: item.MA_DV_CHINH,
        group_code: item.MA_DV_CHINH,
        price: item.GIA_CUOC,
        name: item.TEN_DICHVU,
        description: `${item.TEN_DICHVU} - ${item.GIA_CUOC}đ - ${item.THOI_GIAN}`,
        raw: {
            ...item,
            schedule: {
                from_time: moment().format('HH:mm'),
                to_time: moment().add(duration).format('HH:mm'),
                from_date_time: moment().toISOString(),
                to_date_time: moment().add(duration).toISOString(),
            }
        }
    }
}
