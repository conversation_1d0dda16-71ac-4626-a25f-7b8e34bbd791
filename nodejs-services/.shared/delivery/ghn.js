const axios = require('../axios')

const PhoneNumber = require('libphonenumber-js');

const cfg = process.env.NODE_ENV === 'prod'
  ? {
    base_url: 'https://online-gateway.ghn.vn',
    tracking_url: 'https://donhang.ghn.vn/',
    token: '34d2b91d-ffe3-11ee-8653-aaa2dde45bcb',
  }
  : {
    base_url: 'https://dev-online-gateway.ghn.vn',
    tracking_url: 'https://tracking.ghn.dev/',
    token: '34d2b91d-ffe3-11ee-8653-aaa2dde45bcb',
  }

const base_headers = () => ({
  'accept-encoding': 'gzip',
  'host': 'online-gateway.ghn.vn',
  'token': '34d2b91d-ffe3-11ee-8653-aaa2dde45bcb',
  'user-agent': 'okhttp/4.9.2',
  'version': '75'
})

exports.get_suggestion_addresses = async (address) => {
  const config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: 'https://online-gateway.ghn.vn/shiip/public-api/master-data/suggest-address?address=' + address,
    headers: base_headers()
  }
  let result = []
  try {
    const resp = await axios(config)
    await Promise.all(resp.data.data.map(async (address) => {
      const config2 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://online-gateway.ghn.vn/shiip/public-api/master-data/suggest-address/detail-by-place-id',
        headers: base_headers(),
        data: {
          place_id: address.place_id
        }
      }
      const resp2 = await axios(config2)
      const raw_address = resp2.data.data
      result.push({
        formatted_address: raw_address.formatted_address,
        location: raw_address.Location,
        ward_name: raw_address.ward_name,
        district_name: raw_address.district_name,
        province_name: raw_address.province_name,
        route: raw_address.route,
        ghn_place: {
          place_id: raw_address.place_id,
          ward_code: raw_address.ward_code,
          district_id: raw_address.district_id,
          province_id: raw_address.province_id
        }
      })
    }))
    return result
  } catch (error) {
    console.log(error)
    return []
  }
}

exports.get_store = async ({ access_token }) => {
  if (!access_token) {
    return {}
  }

  try {
    const res = await axios.post(
      `${cfg.base_url}/shiip/public-api/v2/shop/all`,
      {
        offset: 0,
        limit: 50,
        client_phone: '',
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Token: access_token,
        },
      }
    )
    return res.data?.data?.shops?.[0] || {}
  } catch (e) {
    console.log(e)
    return {}
  }
}

exports.get_shipments = async ({ access_token, username }, { from, to }) => {
  if (!access_token || !username) {
    return []
  }

  try {
    const ghn_from = await address_to_ghn_address(from.address)
    const ghn_to = await address_to_ghn_address(to.address)

    const res = await axios.post(
      `${cfg.base_url}/shiip/public-api/v2/shipping-order/fee`,
      {
        from_district_id: ghn_from.district_id,
        service_type_id: 2, // E-commerce delivery
        // from_ward_code: '21211',
        to_district_id: ghn_to.district_id,
        // to_ward_code: '21012',
        height: 50, // cm
        length: 20, // cm
        weight: 2000, // gram
        width: 20, // cm
        coupon: null,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Token: access_token,
          ShopId: username,
        },
      }
    )

    const price = res.data?.data?.total
    return [
      {
        vendor: 'ghn',
        code: 2, // E-commerce delivery
        price,
        name: 'Giao hàng nhanh',
        description: `Giao hàng trong 1 ngày - ${price}đ`,
        from_info: ghn_from,
        to_info: ghn_to,
      },
    ]
  } catch (e) {
    console.log(e)
    return []
  }
}

exports.check_promo_code = async ({ access_token, username }, { promo_code, service, from, to }) => {
  if (!access_token || !username) {
    return {}
  }

  try {
    const { from_info, to_info } = service || {}

    const res = await axios.post(
      `${cfg.base_url}/shiip/public-api/v2/shipping-order/fee`,
      {
        from_district_id: from_info.district_id,
        service_type_id: 2, // E-commerce delivery
        from_ward_code: from_info?.ward_id,
        to_district_id: to_info.district_id,
        to_ward_code: to_info?.ward_id,
        height: 50, // cm
        length: 20, // cm
        weight: 2000, // gram
        width: 20, // cm
        coupon: promo_code,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Token: access_token,
          ShopId: username,
        },
      }
    )

    return [{
      voucher_discount: res.data?.coupon_value
    }]
  } catch (e) {
    return [{
      promo_error_message: e.response?.data?.code_message_value,
    }]
  }

}

exports.create_order = async ({ access_token, username }, { service, promo_code, from, to, note, cod }) => {
  if (!access_token || !username) {
    throw new Error('GHN: access_token or username is missing')
  }

  try {
    const { from_info, to_info } = service || {}
    // console.log('from_info', from_info, 'to_info', to_info)
    const res = await axios.post(
      `${cfg.base_url}/shiip/public-api/v2/shipping-order/create`,
      {
        payment_type_id: 1, // shop/seller pay shipping fee
        note,
        required_note: 'CHOXEMHANGKHONGTHU',
        from_name: from.name,
        from_phone: from.phone,
        from_address: from.address,
        // from_ward_name: 'Phường 14', // FIXME: hardcode
        // from_district_name: 'Quận 10', // FIXME: hardcode
        // from_province_name: 'HCM', // FIXME: hardcode
        to_name: to.name,
        to_phone: to.phone,
        to_address: to.address,
        to_ward_code: to_info?.ward_id,
        to_district_id: to_info.district_id,
        cod_amount: cod,
        content: 'Hàng tiêu dùng',
        height: 50, // cm
        length: 20, // cm
        weight: 2000, // gram
        width: 20, // cm
        service_type_id: 2,
        coupon: promo_code,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Token: access_token,
          ShopId: username,
        },
      }
    )

    return res.data.data
  } catch (error) {
    console.log(error)
    throw new Error('GHN: ' + JSON.stringify(error.response?.data?.message || error.response?.data || {}))
  }
}

exports.get_tracking_url = (order_code) => {
  return `${cfg.tracking_url}?order_code=${order_code}`
}

exports.webhook = async (data) => {
  try {
    const { OrderCode, Status } = data;
    const statusMap = {
      'ready_to_pick': {
        orderStatus: 'DOING',
        shipmentStatus: 'DRIVER_ASSIGNING',
      },
      'picking': {
        orderStatus: 'DOING',
        shipmentStatus: 'DRIVER_PICKING_UP',
      },
      'picked': {
        orderStatus: 'PICK',
        shipmentStatus: 'IN_DELIVERY',
      },
      'delivered': {
        orderStatus: 'FINISH',
        shipmentStatus: 'COMPLETED',
      },
      'returned': {
        orderStatus: 'PICK',
        shipmentStatus: 'RETURNED',
      },
      'cancel': {
        orderStatus: 'PICK',
        shipmentStatus: 'CANCELLED',
      },
    };

    const { orderStatus, shipmentStatus } = statusMap[Status];

    const result = {
      shipment_id: OrderCode,
      shipment_status: shipmentStatus,
      order_status: orderStatus,
      driver_name: '',
      driver_phone: '',
      tracking_url: `${cfg.tracking_url}?order_code=${OrderCode}`,
    }
    return result
  } catch (error) {
    console.error(error)
    return {}
  }
}

exports.check_phone_number = async (phone) => {
  const phone_number = PhoneNumber(phone, 'VN');
  try {
    const resp = await axios({
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://fe-online-gateway.ghn.vn/shiip/public-api/etl/return-rate-pro-secured',
      headers: {
        'accept': 'application/json, text/plain, */*',
        'host': 'fe-online-gateway.ghn.vn',
        'shop_id': '165959',
        'token': '1aed09f8-50e7-11ef-9ee3-1226e3877d58',
        'user-agent': 'okhttp/4.9.2',
        'version': '81'
      },
      data: {
        "phone": '0' + phone_number.nationalNumber,
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MjI2MTUyNTd9.CaxrYiKrx8BDv8rxjPUfVq9BZFa0yVO39VTjO1SerGE"
      }
    })
    console.log(resp.data)
  } catch (error) {
    console.log(error)
  }
}
// this.check_phone_number('0901234567')