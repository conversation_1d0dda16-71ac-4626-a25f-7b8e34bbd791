const { google } = require('googleapis');
const gsv4 = google.sheets('v4');
const { URL } = require('url');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode


const auth = new google.auth.JWT(
    credentials.client_email,
    null,
    credentials.private_key,
    ['https://www.googleapis.com/auth/drive']
);

const MAX_TRY = 2
const read_file = async (url) => {
    console.log("Reading...." + url)
    for (let i = 0; i < MAX_TRY; i++) {
        try {
            const parsedUrl = new URL(url);
            let fileId = parsedUrl.searchParams.get('id');
            if (!fileId) {
                fileId = url.split('/')[5];
            }
            if (!fileId) {
                throw new Error('File ID not found in the URL query parameters');
            }
            // console.log(fileId)
            let drive = google.drive({ version: 'v3', auth });
            const res = await drive.files.get({
                fileId,
                alt: 'media',
            }, { responseType: 'arraybuffer' });

            return Buffer.from(res.data, 'binary');
        } catch (error) {
            console.error(error.message);
        }
    }
};

// read_file("https://drive.google.com/open?id=1WzdjHM7BHb3DhmsPT05nX2P4c_lw6tjB&usp=drive_copy")


module.exports = {
    read_file,
}