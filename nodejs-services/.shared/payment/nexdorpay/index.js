const crypto = require('crypto');
const uuid = require('uuid');
const axios = require('axios');
const _ = require('lodash');
const moment = require('moment');
// const base64url = require('b64url');
const slugify = require('slugify');
const { upload_file } = require('../../storage');
const { name_to_id } = require('../../helper');

let nexdorpay = {};

const ordinary = {
    createTransaction: ['transaction_id', 'total', 'cake_account', 'message', 'server_callback', 'client_callback'],
}

const sort_keys = (data, options) => {
    const itemList = ordinary[options];
    const resultList = itemList.map(item => {
        let a = `${item}=${data[item]}`;
        return a;
    })
    return resultList.join("&");
}

const get_data_signature = (data, options, secretKey) => {
    let rawSignature = sort_keys(data, options);
    let signature = crypto.createHmac('sha256', secretKey)
        .update(rawSignature)
        .digest('hex');
    return signature
}

nexdorpay.create_payment_link = async (nexdorpay_token, { order_id, total, callback_url }) => {
    const client_callback = `${process.env.API_BASE}/api/nexdorpay/client-callbacks${callback_url ? `?callback_url=${callback_url}` : ''}`
    const server_callback = `${process.env.API_BASE}/api/nexdorpay/callbacks`

    const transaction_id = name_to_id(`${order_id}-${total}`).substring(0, 10);
    const data = {
        "transaction_id": transaction_id,
        "total": Number(total),
        "cake_account": nexdorpay_token.username,
        "message": "THANH TOAN DON HANG " + order_id,
        "server_callback": server_callback,
        "client_callback": client_callback
    }

    const signature = get_data_signature(data, "createTransaction", nexdorpay_token.password);
    data.signature = signature;

    try {
        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.NEXDORPAY_PAYMENT_URL}/api/transactions`,
            headers: {
                'Content-Type': 'application/json'
            },
            data
        })
        console.log(data)
        console.log(resp.data)
        return {
            pay_url: `${process.env.NEXDORPAY_PAYMENT_URL}?tx=${transaction_id}`,
            transaction_id,
            data: resp.data,
        }
    } catch (error) {
        console.log(error)
        return null
    }
}

nexdorpay.create_payment_qrcode = async ({ order_id, total, callback_url }) => {
    const transaction_id = name_to_id(`${order_id}-${total}`).substring(0, 10);
    try {
        const client_callback = `${process.env.API_BASE}/api/nexdorpay/client-callbacks${callback_url ? `?callback_url=${callback_url}` : ''}`
        const server_callback = `${process.env.API_BASE}/api/nexdorpay/callbacks`


        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.NEXDORPAY_PAYMENT_URL}/api/transactions`,
            headers: {
                'Content-Type': 'application/json'
            },
            data: {
                "amount": total,
                "order_id": order_id,
                "transaction_id": transaction_id,
                client_callback,
                server_callback,
                "signature": ""
            }
        })
        console.log(resp.data)
        return {
            transaction_id,
            qrcode: resp.data.data.qrcode,
            pay_url: resp.data.data.pay_url,
        }
    } catch (error) {
        console.log(error)
        return null
    }
}

module.exports = nexdorpay