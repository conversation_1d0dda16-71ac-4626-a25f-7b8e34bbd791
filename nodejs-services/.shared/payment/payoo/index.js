const moment = require('moment')
const axios = require('axios')
moment.tz.setDefault('Asia/Bangkok');
const crypto = require('crypto');

const create_check_sum = data => {
  const hash = crypto.createHash('sha512')
  hash.update(data)
  return hash.digest('hex')
}

const create_payment_link = async (payoo_token, { order_id, total, client_callback }) => {
  const username = payoo_token.username
  const shop_id = payoo_token.shop_id
  const PAYOO_CHECKSUM = payoo_token.checksum
  const shop_title = payoo_token.shop_title
  const domain = 'https://he-dev.nexpos.io'
  const shop_back_url = encodeURIComponent(client_callback)
  const today = moment().format('DD/MM/YYYY')
  const validity_time = moment().add(15, 'minutes').format('YYYYMMDDHHmmss')

  const xml = `
    <shops>
    <shop>
      <username>${username}</username>
      <shop_id>${shop_id}</shop_id>
      <shop_title>${shop_title}</shop_title>
      <shop_domain>${domain}</shop_domain>
      <order_no>${order_id}</order_no>
      <order_cash_amount>${total}</order_cash_amount>
      <order_ship_date>${today}</order_ship_date>
      <shop_back_url>${shop_back_url}</shop_back_url>
      <validity_time>${validity_time}</validity_time>
      <count_down>1</count_down>
      <JsonResponse>true</JsonResponse>
    </shop>
  </shops>
  `

  const data = {
    data: xml,
    checksum: create_check_sum(PAYOO_CHECKSUM + xml),
    refer: domain,
  }

  console.log(xml, create_check_sum(PAYOO_CHECKSUM + xml))

  let config = {
    method: 'post',
    url: 'https://newsandbox.payoo.com.vn/v2/checkout',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  }

  try {
    const resp = await axios(config)
    // console.log(JSON.stringify(resp.data))
    return {
      data: resp.data,
    }
  } catch (error) {
    // console.log(error)
    return {
      error: error,
    }
  }
}

// create_payment_link({
//   username: 'SB_Nexdor',
//   shop_id: '11955',
//   shop_title: 'Nexdor',
//   checksum: 'MTNjZjVjYWNkNGU0MTUzYWE0OGNiNmYwNGUzOWMyODY=',
// }, {
//   order_id: '123456',
//   total: 100000,
//   client_callback: 'https://he-nexpos.io',
//   customer: {
//     name: 'Nguyen Van A',
//     phone: '090xxxxxxx',
//     address: 'số nhà, đường, phường, tỉnh hoặc thành phố',
//   }
// })

module.exports = {
  create_payment_link,
}
