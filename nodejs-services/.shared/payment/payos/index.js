const crypto = require('crypto');
const moment = require('moment');
const _ = require('lodash');
const axios = require('axios');

const payos = {}
function sort_data_key(obj) {
    const sortedKeys = Object.keys(obj).sort();
    const sortedObject = {};

    sortedKeys.forEach((key) => {
        sortedObject[key] = obj[key];
    });
    return sortedObject;
}

function get_data_signature(data, checksum) {
    const sortData = sort_data_key(data);
    const stringifyData = Object.keys(sortData)
        .map((key) => `${key}=${data[key]}`)
        .join('&');

    const hmac = crypto.createHmac('sha256', checksum);
    hmac.update(stringifyData);
    return hmac.digest('hex');
}

function string_to_number(input) {
    const hash = crypto.createHash('md5').update(input).digest('hex');
    const number = parseInt(hash, 16);
    return number % Number.MAX_SAFE_INTEGER;
}

payos.delete_payment_link = async (payos_token, transaction_id) => {
    const PAYOS_CLIENT_ID = payos_token.username
    const PAYOS_API_KEY = payos_token.password
    const PAYOS_CHECKSUM = payos_token.site_id
    const data = {
        "cancellationReason": "Changed my mind"
    }
    // data.signature = get_data_signature(data)
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://api-merchant.payos.vn/v2/payment-requests/${transaction_id}/cancel`,
        headers: {
            'x-client-id': PAYOS_CLIENT_ID,
            'x-api-key': PAYOS_API_KEY,
            'Content-Type': 'application/json'
        },
        data: data
    };
    const resp = await axios(config)
    console.log(JSON.stringify(resp.data))
    return {
        data: resp.data
    }
}


payos.create_payment_link = async (payos_token, { order_id, reference_id, total, client_callback, dishes = [] }) => {
    const PAYOS_CLIENT_ID = payos_token.username
    const PAYOS_API_KEY = payos_token.password
    const PAYOS_CHECKSUM = payos_token.site_id
    let data = {
        "orderCode": reference_id,
        "amount": total,
        "description": `Order ${order_id.slice(-15)}`,
        // "buyerName": "Nguyen Van A",
        // "buyerEmail": "<EMAIL>",
        // "buyerPhone": "090xxxxxxx",
        // "buyerAddress": "số nhà, đường, phường, tỉnh hoặc thành phố",
        // "items": dishes?.map(dish => ({
        //     "name": dish.name,
        //     "quantity": dish.quantity,
        //     "price": dish.price,
        // })) || [],
        "cancelUrl": client_callback,
        "returnUrl": client_callback,
        "expiredAt": moment().add(1, 'hour').unix(),
    }
    data.signature = get_data_signature(_.pick(data, ['amount', 'cancelUrl', 'description', 'orderCode', 'returnUrl']), PAYOS_CHECKSUM)
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api-merchant.payos.vn/v2/payment-requests',
        headers: {
            'x-client-id': PAYOS_CLIENT_ID,
            'x-api-key': PAYOS_API_KEY,
            'Content-Type': 'application/json'
        },
        data: data
    };
    const resp = await axios(config)
    console.log(JSON.stringify(resp.data))
    return {
        transaction_id: resp.data.data?.paymentLinkId,
        pay_url: resp.data.data?.checkoutUrl,
        qr_code: resp.data.data?.qrCode,
        data: resp.data
    }
}


const create_webhook = async () => {
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api-merchant.payos.vn/confirm-webhook',
        headers: {
            'x-client-id': PAYOS_CLIENT_ID,
            'x-api-key': PAYOS_API_KEY,
            'Content-Type': 'application/json'
        },
        data: {
            webhookUrl: 'https://api.nexpos.io/api/payos/callbacks'
        }
    };
    const resp = await axios(config)
    console.log(JSON.stringify(resp.data))
}

// create_webhook()

// create_payment_link({ order_id: "1237", total: 1000, dishes: [] })

// {"code":"00","desc":"success","data":{"bin":"970422","accountNumber":"VQRQ00011snhc","accountName":"DAO TRONG NGUYEN","amount":1000,"description":"TTH8NR3X  Thanh toan order 123","orderCode":123,"paymentLinkId":"212b4e30e3da41b8aa7b9b1ec21ef8c5","status":"PENDING","expiredAt":**********,"checkoutUrl":"https://pay.payos.vn/web/212b4e30e3da41b8aa7b9b1ec21ef8c5","qrCode":"00020101021238570010A000000727012700069704220113VQRQ00011snhc0208QRIBFTTA5303704540410005802VN62340830TTH8NR3X  Thanh toan order ***********"},"signature":"af3f2de28183d6cb7546c9dc29d5efe762ac0a0ef738ca2f68b09408a3384031"}
// https://nexpos.io/?code=00&id=212b4e30e3da41b8aa7b9b1ec21ef8c5&cancel=false&status=PAID&orderCode=123

module.exports = payos