const redis = require('./redis');
const uuid = require('uuid');

const send_message_to_topic = async ({ topic = 'notifications', message, data }) => {
    try {
        const new_message = Buffer.from(message).toString('utf8');
        data.topic = topic;
        data.message_id = uuid.v4();
        await redis.publish('notifications', JSON.stringify({
            message: new_message,
            data
        }));
    } catch (error) {
        console.log(error.message);
    }
};

module.exports = {
    send_message_to_topic
};

