const slugify = require('slugify');
const _ = require('lodash');
const crypto = require('crypto');
const { customAlphabet } = require('nanoid');

let helper = {}

helper.text_slugify = (text) => slugify(text?.toLowerCase(), { remove: /[^0-9a-zA-Z]/g, replacement: '_', locale: 'vi', trim: true })
helper.text_compare = (text1, text2) => helper.text_slugify(text1) === helper.text_slugify(text2)

helper.name_to_id = (name, version = '') => crypto.createHash('md5').update(helper.text_slugify(name + String(version))).digest('hex')

helper.find_item_in_menu_by_name = (categories, dish) => {
    // Find by code
    if (dish.code) {
        for (const category of categories || []) {
            for (const item of category.items || []) {
                if (item.code === dish.code) {
                    return item;
                }
            }

            for (const sub_category of category.sub_categories || []) {
                for (const item of sub_category.items || []) {
                    if (item.code === dish.code) {
                        return item;
                    }
                }
            }
        }
    }
    // Find by name
    for (const category of categories || []) {
        for (const item of category.items || []) {
            if (helper.text_slugify(item.name) === helper.text_slugify(dish.name)) {
                return item;
            }
        }

        for (const sub_category of category.sub_categories || []) {
            for (const item of sub_category.items || []) {
                if (helper.text_slugify(item.name) === helper.text_slugify(dish.name)) {
                    return item;
                }
            }
        }
    }

    return null;
}



helper.find_option_item_in_menu_by_name = (option_categories, option_name, option_item) => {
    if (!option_name || !option_item) {
        return null
    }
    for (let c = 0; c < option_categories?.length; c++) {
        if (helper.text_slugify(option_categories[c].name) === helper.text_slugify(option_name)) {
            for (let i = 0; i < option_categories[c].options.length; i++) {
                const item = option_categories[c].options[i]
                if (helper.text_slugify(item.name) === helper.text_slugify(option_item)) {
                    return item
                }
            }
        }
    }
    return null
}

// Chuyển đổi giá item theo cấu hình về giá item theo ứng dụng
// Ví dụ: combo có giá cấu hình 100k (20k 2xsp1, 20k 1xsp2), bán trên ứng dụng dish gía 120k => sp1: 48k 2xsp1, sp2: 24k 1xsp2
helper.get_price_from_combo = (combo, dish) => {
    const dish_unit_price = dish.quantity > 0 ? dish.price / dish.quantity : 0
    const dish_unit_discount = dish.quantity > 0 ? (dish.discount || 0) / dish.quantity : 0
    let total_in_combo = _.sumBy(combo, (c) => c.quantity * c.price)

    let dish_items = []

    for (const c of combo) {
        const new_unit_price = total_in_combo === 0 ? 0 : Math.ceil(dish_unit_price * c.price / total_in_combo)
        const new_unit_discount = total_in_combo === 0 ? 0 : Math.ceil(dish_unit_discount * c.price / total_in_combo)
        dish_items.push({
            "name": c.name,
            "code": c.code,
            "unit": c.unit,
            "unit_price": new_unit_price,
            "discount": new_unit_discount,
            "quantity": c.quantity * dish.quantity,
        })
    }
    let diff_price = _.sumBy(dish_items, (i) => i.unit_price * i.quantity) - dish.price
    let diff_discount_price = (_.sumBy(dish_items, (i) => i.discount * i.quantity) - (dish.discount || 0))
    return { dish_items, diff_price, diff_discount_price }
}

helper.add_stock_to_dishes = (dishes, brand_menu) => {
    let result = {
        success: true,
        error_messages: [],
        dishes: dishes, // add stocks, error_message and has_stock_config fields
        total_diff_price: 0,
        total_diff_discount_price: 0,
    }

    D: for (let i = 0; i < result.dishes.length; i++) {
        const dish = result.dishes[i];
        result.dishes[i].has_stock_config = false
        result.dishes[i].stocks = []

        const item = helper.find_item_in_menu_by_name(brand_menu.categories, dish)

        let combo_options = []
        if (item?.combo?.length > 0) {
            combo_options.push(...item?.combo)
        }

        if (dish?.options?.length > 0) {
            for (const option_items of dish.options) {
                for (const o of option_items) {
                    const option = helper.find_option_item_in_menu_by_name(brand_menu.option_categories, o.option_name, o.option_item)
                    if (option && option?.combo?.length > 0) {
                        combo_options.push(...option?.combo)
                    } else {
                        result.success = false
                        result.dishes[i].error_message = `Sản phẩm: ${dish.name}, chưa cấu hình tên tùy chọn: ${o.option_name} - ${o.option_item}`
                        result.error_messages.push(result.dishes[i].error_message)
                        continue D
                    }
                }
            }
        }

        const { dish_items, diff_price, diff_discount_price } = helper.get_price_from_combo(combo_options, dish)

        if (dish_items.length > 0) {
            result.dishes[i].has_stock_config = true
            result.dishes[i].stocks = dish_items
            result.dishes[i].error_message = ''
            result.total_diff_price += diff_price
            result.total_diff_discount_price += diff_discount_price
        }

        if (!result.dishes[i].has_stock_config) {
            result.success = false
            result.dishes[i].error_message = `Sản phẩm: ${dish.name} chưa cấu hình mã sản phẩm (SKU)`
            result.error_messages.push(result.dishes[i].error_message)
            continue D
        }

    }
    return result
}

helper.check_dish_changed = (old_data, new_data) => {
    if (old_data?.dishes?.length !== new_data?.dishes?.length) {
        return true
    }

    const old_dish_names = old_data?.dishes?.map(v => helper.text_slugify(v.name + ' ' + v.quantity)).sort().join(',')
    const new_dish_names = new_data?.dishes?.map(v => helper.text_slugify(v.name + ' ' + v.quantity)).sort().join(',')
    if (old_dish_names !== new_dish_names) {
        return true
    }
    return false
}

helper.find_paths = (obj, value, currentPath = [], paths = []) => {
    _.transform(obj, (result, val, key) => {
        const newPath = currentPath.concat(key);

        if (_.isObject(val)) {
            helper.find_paths(val, value, newPath, paths);
        } else if (val === value) {
            paths.push(newPath.join('.'));
        }
    });

    return paths;
}

helper.deep_merge_object = (source, desc, priorityPaths = []) => {
    const skipPaths = ["itemInfo", ...priorityPaths]

    const shouldSkipPath = (path) => {
        return skipPaths.some(skipPath =>
            path === skipPath || path.startsWith(skipPath + '.'));
    };

    // Custom merge with path tracking
    const customMerge = (objValue, srcValue, key, object) => {
        // Build the current path
        let currentPath = '';
        if (object.__currentPath !== '') {
            currentPath = object.__currentPath + '.' + key;
        } else {
            currentPath = key;
        }

        // Check if current path should be skipped
        if (shouldSkipPath(currentPath)) {
            return objValue;
        }

        // Set path for nested objects to track hierarchy
        if (_.isObject(srcValue) && !_.isArray(srcValue)) {
            srcValue.__currentPath = currentPath;
        }
        if (_.isObject(objValue) && !_.isArray(objValue)) {
            objValue.__currentPath = currentPath;
        }

        // Original logic
        if (_.isEmpty(srcValue) && !_.isEmpty(objValue)) {
            return objValue;
        } else if (_.isEmpty(objValue) && !_.isEmpty(srcValue)) {
            return srcValue;
        } else if (_.isString(srcValue) && srcValue.includes('**')) {
            return objValue;
        } else if (_.isArray(objValue) && _.isArray(srcValue)) {
            return srcValue;
        }
    };

    // Clone objects to avoid modifying originals
    const sourceClone = _.cloneDeep(source);
    const descClone = _.cloneDeep(desc);

    // Initialize root paths
    sourceClone.__currentPath = '';
    descClone.__currentPath = '';

    // Perform merge
    const merged = _.mergeWith(descClone, sourceClone, customMerge);

    // Clean up temporary path properties
    const cleanObject = (obj) => {
        if (obj && typeof obj === 'object') {
            delete obj.__currentPath;
            Object.keys(obj).forEach(key => {
                if (obj[key] && typeof obj[key] === 'object') {
                    cleanObject(obj[key]);
                }
            });
        }
        return obj;
    };

    const result = cleanObject(merged);
    return result
}

// Test cases for deep_merge_object
// Basic merge: helper.deep_merge_object({ a: [1, 2, 3], c: "ABC" }, { b: [1, 3], a: [1, 3], c: "ABC&***" })
// helper.deep_merge_object({ a: [1, 2, 3], dishes: [{ id: 1 }], c: null }, { a: [4, 5], dishes: null, c: "ABC" }, [])

helper.calculate_nutifood_item_quantity = (item, menu_items) => {
    let min_quantity = null
    for (let j = 0; j < item.combo.length; j++) {
        const combo_item_index = menu_items.findIndex(x => x.item_code === item.combo[j].code)
        if (combo_item_index >= 0) {
            const combo_item_available = menu_items[combo_item_index].quantity
            const item_qty = Math.floor(combo_item_available / item.combo[j].quantity * 100) / 100 // floor 2 decimal
            if (min_quantity === null || item_qty < min_quantity) {
                min_quantity = item_qty
            }
        }
    }
    return min_quantity
}

helper.calculate_item_min_quantity = (item, stock_items) => {
    const quantities = item.combo.map(combo => {
        const stock_item = _.find(stock_items, { code: combo.code });
        if (!stock_item) return null;

        return _.floor(stock_item.quantity / combo.quantity, 2);
    }).filter(v => v !== null);

    return _.min(quantities);
};

helper.format_curency = (amount, locale = 'vi-VN', currency = 'VND') => {
    return new Intl.NumberFormat(locale, { style: 'currency', currency }).format(amount);
}

helper.is_token_expired = (access_token) => {
    try {
        if (access_token?.length < 10) {
            return false
        }
        const { exp } = JSON.parse(Buffer.from(access_token.split('.')[1], 'base64').toString());
        return exp < Math.floor(Date.now() / 1000);
    } catch {
        return true;
    }
};

helper.compare_arrays = (left, right, compareFunc = _.isEqual) => {
    const onlyInLeft = _.differenceWith(left, right, compareFunc);
    const onlyInRight = _.differenceWith(right, left, compareFunc);
    const inBoth = _.intersectionWith(left, right, compareFunc);

    return {
        onlyInLeft,
        onlyInRight,
        inBoth
    };
}

const RETRY_DELAY = 2000;

helper.retry_operation = async (operation, max_retry, errorMessage) => {
    for (let attempt = 0; attempt <= max_retry; attempt++) {
        try {
            return await operation();
        } catch (error) {
            if (attempt === max_retry) {
                console.error(`${errorMessage}: ${error.message}`);
                throw error;
            }
            console.warn(`Attempt ${attempt + 1} failed. Retrying...`);
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
    }
};

helper.get_file_extension_from_url = (url) => {
    const regex = /\.([^.\/\?&#]+)($|\?|#)/i;
    const match = url.match(regex);

    return match ? match[1].toLowerCase() : null;
}


helper.gen_external_id = () => {
    return customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', 10)()
}

module.exports = helper
