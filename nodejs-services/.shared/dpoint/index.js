const axios = require('axios')
const moment = require('moment')
const axiosCurl = require('axios-curlirize')

const base_url = process.env.NODE_ENV === 'prod' ? 'https://api.dgvdigital.net/third-party/api/v1' : 'https://alpha.dgvdigital.net/third-party/api/v1'

// const mockToken = {
//   username: 'cyber-kitchen',
//   password: '9uAwG89bvn2Igmfe_GFQRF5sq9vcXHAfvU8Y7qYZp7A',
//   access_key: 'T4OuRO3wf7B3ZoOPgXvmwWHjoEKuzev2',
// }

axiosCurl(axios)
const _to_response = (response) => {
  if (response.data?.data) {
    return {
      data: response.data,
    }
  }

  return {
    error: response.data?.messages?.[0].content || 'Dpoint error',
  }
}

const get_customer_info = async (phone, dpoint_token) => {
  const request = {
    url: `${base_url}/${dpoint_token.username}/enduser/get-info/${phone}`,
    method: 'GET',
    headers: {
      Authorization: `Basic ${Buffer.from(`${dpoint_token.username}:${dpoint_token.password}`).toString('base64')}`,
      AccessKey: dpoint_token.access_token,
    },
  }
  try {
    const response = await axios(request)
    return _to_response(response)
  } catch (error) {
    const errMsg = error.response.data?.messages?.[0].content || 'Dpoint error'
    throw new Error(errMsg)
  }
}

const request_otp = async (phone, token) => {
  const request = {
    url: `${base_url}/${token.username}/enduser/save-membership-info/request-otp`,
    method: 'POST',
    headers: {
      AccessKey: token.access_token,
      Authorization: `Basic ${Buffer.from(`${token.username}:${token.password}`).toString('base64')}`,
      'Content-Type': 'application/json',
    },
    data: {
      phoneNumber: phone,
    },
  }
  try {
    const response = await axios(request)
    return _to_response(response)
  } catch (error) {
    const errMsg = error.response.data?.messages?.[0].content || 'Dpoint error'
    throw new Error(errMsg)
  }
}

const create_customer = async (payload, token) => {
  const request = {
    url: `${base_url}/${token.username}/enduser/save-membership-info`,
    method: 'POST',
    headers: {
      AccessKey: token.access_token,
      Authorization: `Basic ${Buffer.from(`${token.username}:${token.password}`).toString('base64')}`,
      'Content-Type': 'application/json',
    },
    data: {
      phoneNumber: payload.phone_number,
      fullName: payload.full_name,
      gender: payload.gender,
      dateOfBirth: payload.date_of_birth,
      fullAddress: payload.full_address,
      email: payload.email,
      otp: payload.otp,
    },
  }
  try {
    const response = await axios(request)
    return _to_response(response)
  } catch (error) {
    // console.log(error)
    const errMsg = error.response.data?.messages?.[0].content || 'Dpoint error'
    throw new Error(errMsg)
  }
}

const create_transaction = async (payload, token) => {
  const { order_id, phone_number, full_name, total, discount, voucher_code, site_code, order_type, delivery_on, return_order_code } = payload

  const request = {
    url: `${base_url}/${token.username}/transaction/retail-transaction/create`,
    method: 'POST',
    headers: {
      AccessKey: token.access_token,
      Authorization: `Basic ${Buffer.from(`${token.username}:${token.password}`).toString('base64')}`,
      'Content-Type': 'application/json',
    },
    data: {
      fullName: full_name || phone_number,
      phoneNumber: phone_number,
      orderDateTime: moment(delivery_on).format('DD/MM/YYYY HH:mm:ss'),
      orderCode: order_id,
      totalBeforeDiscount: total,
      totalAfterDiscount: total - discount,
      // orderDiscountPercent: 0,
      orderDiscountAmount: discount,
      paymentAmount: total - discount,
      voucherCode: voucher_code,
      StoreCode: site_code,
      orderType: order_type,
      productCyberkitchenInfos: [],
      ReturnOrderCode: return_order_code,
    },
  }

  try {
    const response = await axios(request)
    return _to_response(response)
  } catch (error) {
    // console.log(error)
    const errMsg = error.response.data?.messages?.[0].content || 'Dpoint error'
    throw new Error(errMsg)
  }
}

module.exports = {
  get_customer_info,
  create_customer,
  create_transaction,
  request_otp,
}
