const moment = require('moment')
const { Site, Order, TokenAccount, SiteFinance, OrderFeedback, SiteFeedback, OrderIncident } = require('./database')

const shopee = require('./merchant/shopee')
const shopee_ecom = require('./merchant/shopee_ecom')
// const shopee_official = require('./merchant/shopee_official')
const gojek = require('./merchant/gojek')
const grab = require('./merchant/grab')
const be = require('./merchant/be')
const tiktok = require('./merchant/tiktok_unofficial')
const lazada = require('./merchant/lazada')
const { map_order, map_site_finance, map_order_feedback, map_site_feedback, map_order_incident } = require('./merchant/mapping')
const { send_slack_message } = require('./slack')
const redis = require('./redis');
const { json_to_md5 } = require('./crypto');
const helper = require('./helper');
const { get_token_by_site } = require('./token_account')
const { send_zalo_message, ZALO_GROUPS, send_file_to_zalo } = require('./zalo');
const _ = require('lodash');

const cron = {}
cron.get_site_finance_by_source = async (order_id) => {
  const order = await Order.findOne({ order_id })
  const site = await Site.findById(order.site_id)
  const token = await get_token_by_site(site, order.source)
  if (!token || !token.access_token || token.fail_count > 100) {
    return
  }

  const merchant_function = {
    grab: grab,
    grab_mart: grab,
  }[order.source]

  if (!merchant_function) {
    return
  }


  let merchant_order_finance = await merchant_function.get_order_finance(token, { order_id })
  if (!merchant_order_finance) { return }


  const data_mapping = map_site_finance(order.source, merchant_order_finance)


  if (data_mapping.order_id && data_mapping.type === 'payment') {
    const db_order = await Order.findOne({ order_id: data_mapping.order_id })
    db_order.data.transaction = merchant_order_finance
    db_order.data_mapping = map_order(order.source, db_order.data)
    db_order.markModified('data')
    db_order.markModified('data_mapping')
    await db_order.save()
  }
}

cron.get_order_feedbacks_by_source = async (site, source, limit) => {
  const token = await get_token_by_site(site, source)
  if (!token || !token.access_token || token.fail_count > 100) {
    return
  }

  const merchant_function = {
    grab: grab,
    grab_mart: grab,
    shopee: shopee,
    shopee_fresh: shopee,
    be,
    // gojek,
  }[source]

  if (!merchant_function) {
    return
  }

  const feedback_list = await merchant_function.get_order_feedbacks(token, limit)
  if (feedback_list?.length === 0) { return }
  const feedback_list_map = feedback_list.map((feedback) => map_order_feedback(source, feedback))

  const db_feedbacks = await OrderFeedback.find({ ref_id: feedback_list_map.map(v => v.ref_id) })

  if (source === 'grab' || source === 'grab_mart') {
    const orders = await Order.find({ "data.bookingCode": feedback_list_map.map(v => v.ref_id) }, { order_id: 1, "data.bookingCode": 1 })
    for (const order of orders) {
      const feedback = feedback_list_map.find(v => v.ref_id === order.data.bookingCode)
      if (feedback) {
        feedback.order_id = order.order_id
      }
    }
  }
  for (const feedback of feedback_list_map) {
    const db_feedback = db_feedbacks.find(v => v.ref_id === feedback.ref_id)
    if (!db_feedback) {
      await OrderFeedback.create({
        ref_id: feedback.ref_id,
        site_id: site._id,
        source,
        order_id: feedback.order_id,
        rating: feedback.rating,
        comment: feedback.comment,
        customer_name: feedback.customer_name,
        created_at_unix: feedback.created_at_unix,
        data: feedback.data,
      })
    }
  }
}

cron.get_site_order_feedbacks = async (site_id, limit) => {
  const site = await Site.findById(site_id)
  const sources = ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be', 'gojek']
  for (const source of sources) {
    await cron.get_order_feedbacks_by_source(site, source, limit)
  }
}

cron.get_site_feedback_summary_by_source = async (site, source) => {
  const token = await get_token_by_site(site, source)
  if (!token || !token.access_token || token.fail_count > 100) {
    return
  }

  const merchant_function = {
    grab: grab,
    grab_mart: grab,
    shopee: shopee,
    shopee_fresh: shopee,
    // be,
    // gojek,
  }[source]

  if (!merchant_function) {
    return
  }

  const site_feedback = await merchant_function.get_site_feedback_summary(token)
  if (!site_feedback) { return }
  const site_feedback_map = map_site_feedback(source, site_feedback)

  await SiteFeedback.findOneAndUpdate({
    site_id: site._id,
    source,
  }, {
    ...site_feedback_map,
    data: site_feedback,
  }, { upsert: true })
}

cron.get_site_order_feedback_summary = async (site_id) => {
  const site = await Site.findById(site_id)
  const sources = ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be', 'gojek']
  for (const source of sources) {
    await cron.get_site_feedback_summary_by_source(site, source)
  }
}
// cron.get_site_feedbacks('66474efaf2cca156a06ddcc6', 500)

cron.get_order_incident_list_by_source = async (site, source, limit) => {
  const token = await get_token_by_site(site, source)
  if (!token || !token.access_token || token.fail_count > 100) {
    return
  }

  const merchant_function = {
    grab: grab,
    grab_mart: grab,
    // shopee: shopee,
    // shopee_fresh: shopee,
    // be,
    // gojek,
  }[source]

  if (!merchant_function) {
    return
  }

  const incident_list = await merchant_function.get_order_incident_list(token, limit)
  if (incident_list?.length === 0) { return }
  const incident_list_map = incident_list.map((item) => map_order_incident(source, item))

  const db_items = await OrderIncident.find({ order_id: incident_list_map.map(v => v.order_id) })
  for (const item of incident_list_map) {
    const db_item = db_items.find(v => v.order_id === item.order_id)
    if (!db_item) {
      await send_zalo_message({
        group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
        message: [
          `<bc style="color:#db342e">KHÁCH HÀNG REPORT ĐƠN HÀNG</bc> lúc ${moment.unix(item.created_at_unix).format('DD/MM/yyyy HH:mm')},`,
          `Lý do: ${item.description}`,
          `Trạng thái: ${item.status_detail}`,
          `Số tiền hoàn: ${helper.format_curency(item.refunded_amount)}`,
          `Chi tiết đơn hàng tại: ${process.env.WEB_URL}/?orderId=${item.order_id}`,
        ].join('\n'),
        urgency: 2,
      })
      for (const image of item.images) {
        await send_file_to_zalo({
          group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
          url: image,
        })
      }

      await OrderIncident.create({
        site_id: site._id,
        source,
        ...item,
      })
    } else {
      const old_serial = json_to_md5(db_item.data)
      const new_serial = json_to_md5(item.data)
      if (old_serial !== new_serial) {
        _.assign(db_item, _.pick(item, ['description', 'status_detail', 'refunded_amount', 'data']))
        await db_item.save()
      }

    }
  }
}
cron.get_order_incident_list = async (site_id) => {
  const site = await Site.findById(site_id)
  const sources = ['grab', 'grab_mart']
  for (const source of sources) {
    await cron.get_order_incident_list_by_source(site, source)
  }
}


module.exports = cron