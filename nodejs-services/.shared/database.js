const mongoose = require('mongoose')
const mongoosePaginate = require('mongoose-paginate-v2');
const AutoIncrement = require('mongoose-sequence')(mongoose);
const { customAlphabet } = require('nanoid');

const uri = process.env.MONGODB_URI
// console.log(JSON.stringify(process.env, null, 2))
console.log(uri)
// const uri = 'mongodb://localhost:27017/foodapp'
mongoose.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true })
mongoose.set('debug', process.env.DEBUG === 'true')

const db = mongoose.connection
db.on('error', console.error.bind(console, 'MongoDB connection error:'))
db.once('open', function () {
  console.log('MongoDB connected successfully!')
})

process.on('SIGINT', async function () {
  console.log('Close MongoDB connection')
  await db.close(true)
  process.exit(0)
})

const database = {}

const base_collection = (collection_name) => {
  return {
    collection: collection_name,
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
  }
}

const AddressObjSchema = {
  formatted_address: String,
  location: {
    lat: Number,
    lng: Number,
  },
  ward_name: String,
  district_name: String,
  province_name: String,
  route: String,
}

const BrandSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    address: { type: String, required: true },
    address_obj: AddressObjSchema,
    description: String,
    logo: String,
    menu_sheet_file_id: String,
    app_commission_rate: JSON,
    hotline: String,
    banners: [{
      url: { type: String, required: true },
      title: String,
      description: String,
    }],
    tokens: [
      {
        token_code: String,
        token_code_official: String,
        source: String,
        site_id: String,
        site_data: Object,
        username: String,
        password: String,
        access_token: String,
        refresh_token: String,
        last_updated: Date,
        working: Boolean,
      },
    ],
    last_cron_token: { type: Date, default: new Date() },
    last_cron_order_checking: { type: Date, default: new Date() },
    use_cdp: { type: Boolean, default: false },
    status: { type: String, enum: ['active', 'inactive'], default: 'active' },
  },
  {
    methods: {
      getToken(source) {
        const tokens = this.tokens.filter((v) => v.source === source)
        return tokens.length > 0 ? tokens[0] : {}
      },
      getTokensForUser() {
        const tokens = this.tokens
        for (let i = 0; i < tokens.length; i++) {
          if (tokens[i].working) {
            tokens[i].username = ''
            tokens[i].password = ''
            tokens[i].access_token = ''
            tokens[i].refresh_token = ''
            tokens[i].site_id = ''
          }
        }
        this.tokens = tokens
        return tokens
      },
    },
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
BrandSchema.plugin(mongoosePaginate)
BrandSchema.index({ name: 'text' })
database.Brand = mongoose.model('Brand', BrandSchema)

database.BrandRequest = mongoose.model('BrandRequest', new mongoose.Schema({
  request_types: [{ type: String, enum: ['menu', 'promotion'] }],
  brand_id: String,
  site_ids: [String],
  source: { type: String, enum: ['grab_food', 'grab_mart', 'shopee', 'be_food', 'vill_food'], default: 'grab_food' },
  file: String,
  created_by: String,
  description: String,
  promise_completed_at: { type: Number, default: 0 },
  processing_at_unix: Number,
  completed_at_unix: Number,
  error_messages: [String],
  status: { type: String, enum: ['PENDING', 'APPROVED', 'PROCESSING', 'SUCCESS', 'FAILED'], default: 'PENDING' },
}, base_collection('brand_requests')).plugin(mongoosePaginate))

const HubSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    code: { type: String, unique: true },
    phone: String,
    address: { type: String, required: true },
    address_obj: AddressObjSchema,
    description: String,
    inventory_source: { type: String, enum: ['manual', 'nexpos', 'odoo', 'nutifood'], default: 'manual' },
    last_cron_stock: { type: Date, default: new Date() },
    printer_ip: { type: String, default: '*************:9100' },
    zalo_group: String,
    status: { type: String, enum: ['active', 'inactive'], default: 'active' },
    enable_working_shift: { type: Boolean, required: false },
    brand_ids: [String],
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

HubSchema.plugin(mongoosePaginate)
HubSchema.index({ name: 'text', code: 'text' })
database.Hub = mongoose.model('Hub', HubSchema)


const SiteSchema = new mongoose.Schema(
  {
    brand_id: { type: String, required: true },
    hub_id: { type: String, required: true },
    hub_ids: [String],
    type: {
      type: String,
      required: true,
      default: 'food',
      enum: ['food', 'mart', 'partner'],
    },
    he_id: String, // HE id for retailer, partner site
    apply_commission: { type: Boolean, required: false },
    apply_gift: { type: Boolean, required: false },
    name: {
      type: String,
      required: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
    },
    address: { type: String, required: true },
    address_obj: AddressObjSchema,
    description: String,
    tokens: [
      {
        token_code: String,
        token_code_official: String,
        source: String,
        site_id: String,
        site_name: String,
        username: String,
        password: String,
        access_token: String,
        expired_at: Date,
        refresh_token: String,
        last_updated: Date,
        working: Boolean,
        blocked: Boolean,
        client_id: String,
        client_secret: String,
        merchant_id: String,
        nexdor_commission: Number,
        settings: JSON, // custom settings for each source
      },
    ],
    working_hours: JSON,
    special_working_hours: [mongoose.Schema({
      source: String,
      name: String,
      from_date: Date, // YYYY-MM-DD
      to_date: Date,// YYYY-MM-DD
      closed: Boolean,
      open_hours: [{
        from: String, // HH:mm
        to: String, // HH:mm
      }] // If empty close/open all day
    })],
    pause_apps: {
      shopee: Boolean,
      shopee_fresh: Boolean,
      grab: Boolean,
      grab_mart: Boolean,
      gojek: Boolean,
      baemin: Boolean,
      be: Boolean,
    },
    opening_status: {
      source: String,
      is_open: Boolean,
      next_opening_time: Date,
    },
    active: Boolean,
    auto_confirm: Boolean,
    auto_print: Boolean,
    auto_print_label: Boolean,
    printer_ip: { type: String, default: '*************:9100' },
    slack_channel: String,
    zalo_group: String,
    error_message: String,
    last_cron_order: { type: Date, default: new Date() },
    last_cron_print: { type: Date, default: new Date() },
    last_cron_ecom_order: { type: Date, default: new Date() },
    last_cron_order_finance: { type: Date, default: new Date() },
    last_cron_order_x_days: { type: Date, default: new Date() },
    last_cron_token: { type: Date, default: new Date() },
    last_cron_health: { type: Date, default: new Date() },
    momo_sync_at: Date,
    apply_ph_commission: { type: Boolean, required: false }, // whether apply commission for partner hub site, obsolete
    use_core_product: { type: Boolean, default: false },
    partner_hub_tier: { type: String, enum: ['basic', 'growth'], default: 'basic' },
    enable_local_order_shipping: { type: Boolean, default: false },
    is_head_site: { type: Boolean, default: false },
    preparation_time_per_order: { type: Number, default: 15 },
  },
  {
    methods: {
      getToken(source) {
        const tokens = this.tokens.filter((v) => v.source === source)
        return tokens.length > 0 ? tokens[0] : {}
      },
      getTokensForUser() {
        const tokens = this.tokens
        for (let i = 0; i < tokens.length; i++) {
          if (tokens[i].access_token) {
            tokens[i].password = '********'
            tokens[i].access_token = '********'
            tokens[i].refresh_token = '********'
          }
        }
        this.tokens = tokens
        return tokens
      },
    },
    timestamps: {
      deletedAt: 'deleted_at',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
SiteSchema.index({ name: 'text', code: 'text' })
SiteSchema.plugin(mongoosePaginate)
database.Site = mongoose.model('Site', SiteSchema)


const UserSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      require: true,
      unique: true,
    },
    email: {
      require: true,
      type: String,
      unique: true,
    },
    phone: {
      type: String,
      unique: true,
      sparse: true,
    },
    address: String,
    address_obj: AddressObjSchema,
    password: {
      type: String,
      required: false
    },
    name: String,
    role_id: String,
    is_active: { type: Boolean, default: true },
    is_guest: { type: Boolean, default: false },
    approval_status: { type: String, enum: ['pending', 'approved', 'rejected'] },
    // is_verified: Boolean, // otp verified?
    expired_at: Date,
    permissions: [String],
    hubs: [String],
    brands: [String],
    sites: [String],
    last_login_device: String,
    last_login_devices: { type: [String], default: [] },
    login_fail_count: Number,
    created_by: String,
    he_ids: [String],
    he_info: {
      status: { type: String, enum: ['pending', 'active'] },
      referrer_id: String,
      approved_at: Date,
      saved_accounts: [{
        bank_name: String,
        account_number: String,
        account_name: String,
      }]
    }
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

UserSchema.plugin(mongoosePaginate)
database.User = mongoose.model('User', UserSchema)

database.UserOTP = mongoose.model(
  'UserOTP',
  new mongoose.Schema(
    {
      receive_method: { type: String, enum: ['email', 'zalo', 'sms'], default: 'email' },
      verify_type: { type: String, enum: ['verify_phone', 'register', 'forgot_password'] },
      user_uid: String, // email or phone
      otp: String,
      message: String,
      expired_at: Date,
      verified_hash: String,
    },
    {
      collection: 'user_otps',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.PartnerAPIKey = mongoose.model(
  'PartnerAPIKey',
  new mongoose.Schema(
    {
      type: { type: String, enum: ['partner'] },
      api_key: String,
      metadata: JSON,
      name: String,
      permissions: [String],
      expired_at: Date,
    },
    {
      collection: 'partner_api_keys',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.VendorCallback = mongoose.model(
  'VendorCallback',
  new mongoose.Schema(
    {
      vendor: { type: String, enum: ['zalo', 'momo', 'grab', 'grab_food', 'be', 'payos', 'nexdorpay', 'ghn', 'ahamove', 'grab_express', 'viettel_post', 'shopee', 'dpoint', 'momo_mini', 'dpoint', 'lazada', 'tiktok', 'ocb'] },
      type: {
        type: String, enum: [
          'order',
          'menu',
        ]
      },
      url: String,
      method: String,
      headers: JSON,
      success: { type: Boolean, default: false },
      request_data: JSON,
      response_data: JSON,
    },
    {
      collection: 'vendor_callbacks',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.VendorRequest = mongoose.model(
  'VendorRequest',
  new mongoose.Schema(
    {
      vendor: { type: String, enum: ['nutifood', 'payos', 'zalo'] },
      type: {
        type: String, enum: [
          'get_hub_stock',
          'get_order_stock',
          'sync_order',
          'send_zns',
        ]
      },
      url: String,
      method: String,
      headers: JSON,
      success: { type: Boolean, default: false },
      request_data: JSON,
      response_data: JSON,
    },
    {
      collection: 'vendor_requests',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.OrderPayment = mongoose.model(
  'OrderPayment',
  new mongoose.Schema(
    {
      vendor: { type: String, enum: ['momo', 'payos', 'nexdorpay'] },
      order_id: String,
      transaction_id: String, // Transaction của NexDor
      partner_transaction_id: String, // Transaction id bên đối tác vd mã giao dịch Ngân Hàng / MOMO
      amount: Number,
      description: String,
      currency: { type: String, default: 'VND' },
      status: { type: String, enum: ['PENDING', 'COMPLETED', 'CANCELLED'], default: 'PENDING' },
      payment_data: JSON,
      callback_data: JSON,
    },
    {
      collection: 'order_payments',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).plugin(mongoosePaginate)
)


const UserAddressSchema = new mongoose.Schema(
  {
    user_id: { type: String, required: true },
    address: String,
    address_obj: AddressObjSchema,
    phone: String,
    name: String,
    note: { type: String, require: false },
    is_default: { type: Boolean, default: false },
  },
  {
    collection: 'user_address',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
database.UserAddress = mongoose.model('UserAddress', UserAddressSchema)

const UserCartSchema = new mongoose.Schema(
  {
    user_id: { type: String, required: true },
    site_id: { type: String, required: true },
    dishes: { type: Array, default: [] },
    note: String,
    total_items: { type: Number, default: 0 },
    sub_total: { type: Number, default: 0 },
    shipping_fee: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    gifts: { type: Array, default: [] },
    discount: { type: Array, default: [] },
    ship_discount: { type: Array, default: [] },
    shipping_promo: {
      error: String,
      discount: Number,
      code: String,
    },
    vouchers: { type: Array, default: [] },
    shipment: {
      service: Object,
      from: {
        address: String,
        address_obj: AddressObjSchema,
        phone: String,
        name: String,
      },
      to: {
        user_id: String,
        address: String,
        address_obj: AddressObjSchema,
        phone: String,
        name: String,
      },
      schedule: {
        type: {
          from_time: String,
          to_time: String,
          from_date_time: Date,
          to_date_time: Date
        },
        default: null,
      },
      price: { type: Number, default: 0 },
    },
    shipments: Array,
    payment_method: { type: String, enum: ['CASH', 'MOMO'], default: 'CASH' },
    status: { type: String, enum: ['created', 'completed'] },
  },
  {
    collection: 'user_carts',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
database.UserCart = mongoose.model('UserCart', UserCartSchema)

database.Role = mongoose.model(
  'Role',
  new mongoose.Schema(
    {
      name: {
        type: String,
        required: true,
        unique: true,
      },
      selectors: [{ type: String, enum: ['brand', 'hub', 'site'] }],
      permissions: [String],
    },
    {
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

const shipmentSchema = new mongoose.Schema({
  error: String,
  service: Object,
  vendor: String, //ahamove, grab_express, ghn
  cod: Number,
  payment_method: String,
  shipment_id: String,
  status: {
    type: String,
    enum: ['ORDER_CREATING', 'ORDER_CREATED', 'DRIVER_ASSIGNING', 'DRIVER_PICKING_UP', 'IN_DELIVERY', 'COMPLETED', 'CANCELLED', 'RETURNED'],
    default: 'ORDER_CREATING'
  },
  cancel_comment: String,
  tracking_url: String,
  service: Object,
  from: {
    address: String,
    phone: String,
    name: String,
  },
  to: {
    address: String,
    phone: String,
    name: String,
  },
  price: { type: Number, default: 0 },
  note: String,
  driver: {
    name: String,
    phone: String,
  },
  promo: {
    code: String,
    discount: Number,
    error: String,
  },
  schedule: {
    type: {
      from_time: String,
      to_time: String,
      from_date_time: Date,
      to_date_time: Date
    },
    default: null,
  },
  completed_at: Date,
})

const OrderSchema = new mongoose.Schema(
  {
    source: String,
    site_id: String,
    hub_id: String,
    status: {
      type: String,
      enum: ['DRAFT', 'PRE_ORDER', 'WAITING_PAYMENT', 'PENDING', 'DOING', 'PICK', 'FINISH', 'CANCEL', 'RETURNING', 'RETURNED'],
    },
    order_id: { type: String, unique: true },
    external_id: { type: String, unique: true, index: true },
    user_id: String, // customer user id
    is_first_order: Boolean,
    shipment: shipmentSchema,
    data_mapping: JSON,
    // data_mapping: {
    //   id: String,
    //   order_id: String,
    //   source: String,
    //   order_time: String,
    //   pick_time: String,
    //   delivery_time: String,
    //   delivery_time_unix: Number,
    //   order_time_sort: Number,
    //   driver_name: String,
    //   driver_phone: String,
    //   customer_name: String,
    //   customer_address: String,
    //   customer_phone: String,
    //   dishes: [{
    //     name: String,
    //     description: String,
    //     options: [{
    //       name: String,
    //       quantity: Number,
    //       option_name: String,
    //       option_item: String,
    //     }],
    //     quantity: Number,
    //     price: Number, // Giá gốc đã nhân số lượng
    //     discount: Number,  // Giảm giá đã nhân số lương
    //     discount_price: Number, // Giá bán đã nhân số lượng
    //     note: String,
    //   }],
    //   dish_changed: Boolean,
    //   total: Number,
    //   commission: Number,
    //   total_discount: Number,
    //   total_for_biz: Number,
    //   total_shipment: Number, // Giá ship sau khuyến mãi
    //   shipment_discount: Number, // Giảm giá ship
    //   shipment_fee: Number, // Giá ship trước khuyến mãi
    //   transaction_fee: Number, // Phí giao dịch
    //   note: String,
    //   cancel_type: { type: String, enum: ['out_stock', 'merchant_busy', 'incorrect_order'] },
    //   cancel_reason: String,
    //   cancel_by: { type: String, enum: ['merchant', 'system', 'user', 'driver'] },
    //   coupons: [{
    //     name: String,
    //     code: String,
    //     total: Number,
    //   }],
    //   payments: [{
    //     method: String,
    //     total: Number,
    //     status: String,
    //     note: String,
    //   }],
    //   total_customer_paid: Number, // Tiền khách đã trả
    //   stock_dishes: [], // Món theo tồn kho
    //   finance_data: {
    //     original_price: Number, // Giá gốc
    //     sell_price: Number, // Giá bán (sau gạch giá)
    //     co_fund_promotion_price: Number, // Các khuyến mãi đơn hàng khác
    //     other_promotion_price: Number, // Khuyến mãi khác
    //     total_promotion_price: Number, // Tổng khuyến mãi
    //     gross_received: Number,// Doanh thu sau khuyến mãi
    //     commission: Number, // Commission
    //     transaction_fee: Number, // Phí giao dịch
    //     other_fee: Number, //  Phí khác
    //     total_shipment: Number, // Tiền ship ban đầu
    //     shipping_fee: Number, // Tiền ship sau giảm
    //     shipping_discount: Number, // Giảm giảm
    //     net_received: Number, // Thực nhận sau commission
    //     additional_income: Number, // Phụ thu từ khách or shiper or bo
    //     real_received: Number, // Thu nhập thực tế từ merchant và khách
    //   },
    // },
    he_id: String, // HE user id or retailer id
    data: JSON,
    data_history: JSON,
    rejected_hubs: [{
      hub_id: String,
      reason: String,
      user_id: String,
      time: String
    }],
    auto_printed: { type: Boolean, default: false },
    auto_label_printed: { type: Boolean, default: false },
    auto_cancel_printed: { type: Boolean, default: false },
    auto_confirmed: { type: Boolean, default: false },
    is_new: { type: Boolean, default: true },
    odoo_sync_at: Date,
    odoo_sync_retry: { type: Number, default: 0 },
    odoo_sync_callback: JSON,
    vendor_sync: {
      type: { at: Date, success: Boolean, message: String, callback: JSON, stock_callback: JSON },
      default: null
    },
    vendor_sync_retry: { type: Number, default: 0 },
    pushed_notification: { type: Boolean, default: false },
    notification_count_order_confirm: { type: Number, default: 0 },
    notification_count_order_confirm_at: Date,
    notification_count_order_cancel: { type: Number, default: 0 },
    tracardi_sync_at: Date,
    profile_sync_at: Date,
    dpoint_sync: {
      success: Boolean,
      detail: JSON,
      at: Date,
    },
    finance_data_sync_retry: { type: Number, default: 0 },
    last_cron_transaction: Date,
    stock_sync: {
      status: { type: String, enum: ['PROCESSING', 'SUCCESS', 'FAILED'] },
      detail: JSON
    },
    bill_url: String,
    bill_for_payment_url: String,
    bill_for_complete_url: String,
    bill_for_complete_app_url: String,
    bill_for_cancel_url: String,
    label_urls: [String],
    sent_bill_notification: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
OrderSchema.index({ status: 1, site_id: 1, 'data_mapping.order_time_sort': 1 }) // compound index for query order and report
OrderSchema.index({ status: 1, site_id: 1, 'data_mapping.delivery_time_unix': 1 }) // compound index for query order and report
OrderSchema.index({ site_id: 1 }) // Group by site_id for sharding later
OrderSchema.index({ hub_id: 1 })
OrderSchema.index({ site_id: 1, user_id: 1 })
OrderSchema.index({ site_id: 1, hub_id: 1, status: 1 })
OrderSchema.index({ 'shipment.shipment_id': 1 })
OrderSchema.index({ 'vendor_sync': 1 })
OrderSchema.index({ 'vendor_sync.success': 1 })
// OrderSchema.pre('save', function (next) {
//   if (this.isNew && !this.external_id) {
//     this.external_id = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', 10)()
//   }
//   next()
// })

OrderSchema.plugin(mongoosePaginate)
database.Order = mongoose.model('Order', OrderSchema)

database.OrderReport = mongoose.model('OrderReport', new mongoose.Schema({
  filter_type: { type: String, enum: ['hub', 'brand', 'site'] },
  hub_ids: [String],
  brand_ids: [String],
  site_ids: [String],
  start_date: Date,
  end_date: Date,
  order_filter: JSON,
  file_key: String,
  file_url: String,
  report_sub_list: [String],
  status: { type: String, enum: ['PENDING', 'PROCESSING', 'SUCCESS', 'FAILED'], default: 'PENDING' },
  fail_reason: String,
  created_by: String,
  created_at_unix: Number,
  processing_at_unix: Number,
  completed_at_unix: Number,
}, base_collection('order_reports'))
  .plugin(mongoosePaginate)
)

const OrderShipmentSchema = new mongoose.Schema({
  vendor: String,
  shipment_id: { type: String, unique: true },
  is_sub_shipment: { type: Boolean, default: false },
  is_manual: { type: Boolean, default: false },
  cod: Number,
  price_for_user: Number,
  order_id: String,
  from_address: String,
  from_name: String,
  from_phone: String,
  to_address: String,
  to_name: String,
  to_phone: String,
  driver_name: String,
  driver_phone: String,
  tracking_url: String,
  price: Number,
  status: {
    type: String,
    enum: ['ORDER_CREATING', 'ORDER_CREATED', 'DRIVER_ASSIGNING', 'DRIVER_PICKING_UP', 'IN_DELIVERY', 'COMPLETED', 'CANCELLED', 'RETURNED'],
    default: 'ORDER_CREATING'
  },
  cancel_by: String,
  cancel_reason: String,
  webhooks: Array,

}, {
  collection: 'order_shipments',
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
})

OrderShipmentSchema.plugin(mongoosePaginate)
database.OrderShipment = mongoose.model('OrderShipment', OrderShipmentSchema)

const OrderFeedbackSchema = new mongoose.Schema({
  ref_id: String,
  site_id: String,
  order_id: String,
  source: String,
  customer_name: String,
  rating: { type: Number, min: 1, max: 5 },
  comment: String,
  created_at_unix: Number,
  data: JSON,
}, {
  collection: 'order_feedbacks',
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
})

OrderFeedbackSchema.plugin(mongoosePaginate)
database.OrderFeedback = mongoose.model('OrderFeedback', OrderFeedbackSchema)

const OrderIncidentSchema = new mongoose.Schema({
  site_id: String,
  order_id: String,
  source: String,
  description: String,
  images: [String],
  refunded_amount: Number,
  created_at_unix: Number,
  data: JSON,
}, {
  collection: 'order_incidents',
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
})

OrderIncidentSchema.plugin(mongoosePaginate)
database.OrderIncident = mongoose.model('OrderIncident', OrderIncidentSchema)

const SiteFeedbackSchema = new mongoose.Schema({
  site_id: String,
  source: String,
  avg_rating: Number,
  total_rating: Number,
  total_star1: Number,
  total_star2: Number,
  total_star3: Number,
  total_star4: Number,
  total_star5: Number,
  data: JSON,
}, {
  collection: 'site_feedbacks',
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
})

SiteFeedbackSchema.plugin(mongoosePaginate)
database.SiteFeedback = mongoose.model('SiteFeedback', SiteFeedbackSchema)

const SiteFinanceSchema = new mongoose.Schema({
  source: { type: String, enum: ['shopee', 'shopee_fresh', 'grab', 'grab_mart', 'gojek', 'tiktok', 'lazada'] },
  transaction_id: String,
  site_id: String,
  data: JSON,
  data_mapping: {
    id: String,
    order_id: String,
    type: { type: String, enum: ['payment', 'adjustment', 'marketing', 'voucher', 'loan', 'other'] },
    sub_total: Number,
    discount: Number,
    gross_total: Number,
    commission: Number,
    net_total: Number,
    description: String,
    created_at_unix: Number,
  }
}, {
  collection: 'site_finances',
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
})

SiteFinanceSchema.plugin(mongoosePaginate)
database.SiteFinance = mongoose.model('SiteFinance', SiteFinanceSchema)

const itemSchema = new mongoose.Schema({
  id: String,
  name: String,
  code: String,
  unit: String,
  sources: [String],
  description: String,
  image: String,
  image_preview: String,
  price: Number,
  stock_price: Number,
  floor_price: Number,
  ceiling_price: Number,
  quantity_unlimited: { type: Boolean, default: true },
  quantity_minimum: Number,
  quantity: Number,
  active: Boolean,
  force_update: Boolean,
  force_update_by: String,
  combo: [{
    name: String,
    code: String,
    price: Number,
    quantity: Number,
    unit: String,
    category: String,
    weight: Number,
    images: [String],
    cost_price: Number, // Giá vốn
  }],
  from_brand_id: String,
  brand_name: String,
})

const optionItemSchema = new mongoose.Schema({
  id: String,
  name: { type: String, required: true },
  sources: [String],
  options: [
    {
      id: String,
      name: { type: String, required: true },
      code: String, // as SKU
      sources: [String],
      price: Number,
      active: Boolean,
      quantity_unlimited: { type: Boolean, default: true },
      quantity_minimum: Number,
      combo: [{
        name: String,
        code: String,
        price: Number,
        quantity: Number,
        unit: String,
      }],
    },
  ],
  rule: {
    type: {
      type: String,
      enum: ['SELECT_ONE', 'SELECT_MANY'],
      default: 'SELECT_ONE',
    },
    required: Boolean,
    max_quantity: { type: Number, default: 1 },
    min_quantity: { type: Number, default: 1 },
  },
  category_ids: [String],
})



database.HubMenuGroup = mongoose.model(
  'HubMenuGroup',
  new mongoose.Schema(
    {
      hub_id: String,
      categories: {
        type: [
          {
            id: String,
            name: { type: String, required: true },
            sources: [String],
            sub_categories: [
              {
                id: String,
                name: { type: String, required: true },
                sources: [String],
                items: [itemSchema],
              },
            ],
            items: [itemSchema],
          },
        ],
        default: [],
      },
      option_categories: {
        type: [optionItemSchema],
        default: [],
      },
    },
    {
      collection: 'hub_menu_groups',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.HubStock = mongoose.model(
  'HubStock',
  new mongoose.Schema(
    {
      hub_id: { type: String, required: true },
      code: { type: String, required: true },
      name: { type: String, required: true },
      unit: { type: String, default: 'unit' },
      quantity: { type: Number, default: 0 },
      locked_status: { type: String, required: true, enum: ['use_stock', 'alway_active', 'alway_inactive'], default: 'use_stock' },
    },
    {
      collection: 'hub_stocks',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
    .index({ code: 'text', name: 'text' })
    .plugin(mongoosePaginate)
)

database.VendorHubStock = mongoose.model(
  'VendorHubStock',
  new mongoose.Schema(
    {
      hub_id: { type: String, required: true },
      vendor: { type: String, enum: ['nutifood'] },
      request_codes: [String],
      stocks: [{
        code: String,
        name: String,
        quantity: { type: Number, default: 0 },
        quantity_history: [{
          quantity: { type: Number, default: 0 },
          updated_at: Date,
        }],

      }]
    },
    {
      collection: 'vendor_hub_stocks',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).plugin(mongoosePaginate)
)

database.GoogleSheetFile = mongoose.model(
  'GoogleSheetFile',
  new mongoose.Schema(
    {
      file_id: { type: String, required: true },
      sheet_data: JSON,
    },
    {
      collection: 'google_sheet_files',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
    .plugin(mongoosePaginate)
)

database.HubStockFile = mongoose.model(
  'HubStockFile',
  new mongoose.Schema(
    {
      hub_id: { type: String, required: true },
      file: String,
      note: String,
    },
    {
      collection: 'hub_stock_files',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).plugin(mongoosePaginate)
)

database.HubStockHistory = mongoose.model(
  'HubStockHistory',
  new mongoose.Schema(
    {
      hub_id: String,
      code: { type: String, required: true },
      from_quantity: { type: Number, default: 0 },
      to_quantity: { type: Number, default: 0 },
      updated_type: { type: String, required: true, enum: ['edit', 'order', 'order_cancel', 'import', 'server', 'ticket'] },
      updated_by: String,
      updated_order_id: String,
      updated_import_file: String,
      updated_server: String,
      updated_ticket_number: String,
    },
    {
      collection: 'hub_stock_histories',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).plugin(mongoosePaginate)
)

database.HubStockTicket = mongoose.model(
  'HubStockTicket',
  new mongoose.Schema(
    {
      ticket_number: { type: String, unique: true },
      ticket_type: { type: String, enum: ['export', 'import', 'trash'], default: 'export' },
      hub_id: String,
      target_hub_id: String,
      target_hub_date: Date,
      items: [{
        code: { type: String, required: true },
        name: { type: String, required: true },
        unit: String,
        stock_quantity: { type: Number, default: 0 },
        exchange_quantity: { type: Number, default: 0 },
        exchanged_quantity: { type: Number, default: 0 },
        stock_report_types: [{ type: String, required: true, enum: ['stock_incorrect', 'stock_outdated', 'stock_missed', 'other'] }],
        stock_report_note: String,
      }],
      status: { type: String, enum: ['created', 'submitted', 'approved', 'rejected'], default: 'created' },
      ticket_report_types: [{ type: String, enum: ['ticket_incorrect', 'stock_outdated', 'stock_missed', 'other'] }],
      ticket_report_note: String,
      created_by: String,
      approved_by: String,
      rejected_by: String,
      submitted_by: String,
    },
    {
      collection: 'hub_stock_tickets',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).plugin(mongoosePaginate)
)


database.BrandMenu = mongoose.model(
  'BrandMenu',
  new mongoose.Schema(
    {
      brand_id: { type: String, unique: true },
      categories: {
        type: [
          {
            id: String,
            name: { type: String, required: true },
            code: String,
            sources: [String],
            sub_categories: [
              {
                id: String,
                name: { type: String, required: true },
                code: String,
                sources: [String],
                items: [itemSchema],
              },
            ],
            items: [itemSchema],
          },
        ],
        default: [],
      },
      option_categories: {
        type: [optionItemSchema],
        default: [],
      },
    },
    {
      collection: 'brand_menus',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.SiteMenuGroup = mongoose.model(
  'SiteMenuGroup',
  new mongoose.Schema(
    {
      site_id: { type: String, unique: true },
      categories: {
        type: [
          {
            id: String,
            name: { type: String, required: true },
            code: String,
            sources: [String],
            sub_categories: [
              {
                id: String,
                name: { type: String, required: true },
                code: String,
                sources: [String],
                items: [itemSchema],
              },
            ],
            items: [itemSchema],
          },
        ],
        default: [],
      },
      option_categories: {
        type: [optionItemSchema],
        default: [],
      },
    },
    {
      methods: {
        getItemByID(_id) {
          return this.items.filter((item) => String(item._id) === _id)[0]
        },
        getItemByName(name) {
          return this.items.find((item) => item.name === name)
        },
      },
      collection: 'site_menu_groups',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).index({ site_id: 1 })
)



database.SiteMenuGroupHistory = mongoose.model(
  'SiteMenuGroupHistory',
  new mongoose.Schema(
    {
      site_id: { type: String, required: true },
      source: String,
      category_name: String,
      sub_category_name: String,
      item_name: String,
      action_type: {
        type: String, required: true, enum: [
          'category_deleted',
          'item_status_updated',
          'option_item_status_updated',
          'item_deleted',
          'item_synced',
        ]
      },
      action_message: String,
      action_data: Object,
      action_by: String,
    },
    {
      collection: 'site_menu_group_histories',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).index({ site_id: 1 })
)

database.BrandMasterData = mongoose.model(
  'BrandMasterData',
  new mongoose.Schema(
    {
      brand_id: String,
      brand_name: String,
      key: { type: String, enum: ['shopee_menu', 'grab_menu'] },
      value: JSON,
    },
    {
      collection: 'brand_master_data',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  ).index({ brand_id: 1, key: 1 }, { unique: true })
)

database.PrintQueue = mongoose.model(
  'PrintQueue',
  new mongoose.Schema(
    {
      site_id: String,
      hub_id: String,
      order_id: String,
      order_print_count: { type: Number, default: 1 },
      source: String,
      print_type: String,
      status: {
        type: String,
        enum: ['created', 'printing', 'printed', 'error'],
      },
      send_to_slack: Boolean,
      error_message: String,
      data: Object,
      file_url: String,
    },
    {
      collection: 'print_queues',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.PrintQueueV2 = mongoose.model(
  'PrintQueueV2',
  new mongoose.Schema(
    {
      site_id: String,
      hub_id: String,
      order_id: String,
      status: {
        type: String,
        enum: ['created', 'printing', 'printed', 'error'],
        default: 'created'
      },
      file_url: String,
      error_message: String,
    },
    {
      collection: 'print_queues_v2',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

const TokenAccountSchema = new mongoose.Schema(
  {
    token_code: { type: String, unique: true }, // NNDD_SA001_shopee
    source: {
      type: String,
      enum: [
        'shopee', 'shopee_ecom', 'grab', 'grab_express', 'gojek', 'be', 'zalo', 'momo', 'payos', 'ghn', 'ahamove',
        'viettel_post', 'lazada', 'tiktok', 'shopee_official', 'grab_mart_official', 'grab_food_official',
        'facebook'
      ]
    },
    site_id: String,
    site_name: String,
    site_data: JSON,
    username: String,
    password: String,
    access_token: String,
    expired_at: Date,
    refresh_token: String,
    last_updated: Date,
    working: Boolean,
    last_working_at: Date,
    expired_at: Date,
    fail_count: Number,
    description: String,
  },
  {
    collection: 'token_accounts',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
TokenAccountSchema.plugin(mongoosePaginate)
database.TokenAccount = mongoose.model('TokenAccount', TokenAccountSchema)

database.SiteOrderIndex = mongoose.model(
  'SiteOrderIndex',
  new mongoose.Schema(
    {
      site_id: String,
      group: String,
      current_index: { type: Number, default: 1 },
    },
    {
      collection: 'site_order_index',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

const AddressSchema = new mongoose.Schema(
  {
    id: { type: String, required: true },
    name: { type: String, required: true },
    slug: { type: String, required: true },
    type: { type: String, required: true },
    name_with_type: { type: String, required: true },
    children: [
      {
        id: { type: String, required: true },
        parent_id: { type: String, required: true },
        slug: { type: String, required: true },
        type: { type: String, required: true },
        name: { type: String, required: true },
        name_with_type: { type: String, required: true },
        name_with_path: { type: String, required: true },
        children: [
          {
            id: { type: String, required: true },
            parent_id: { type: String, required: true },
            slug: { type: String, required: true },
            type: { type: String, required: true },
            name: { type: String, required: true },
            name_with_type: { type: String, required: true },
            name_with_path: { type: String, required: true },
          },
        ],
      },
    ],
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)
database.Address = mongoose.model('Address', AddressSchema)

const EmailTemplateSchema = new mongoose.Schema(
  {
    name: String,
    subject: String,
    body: String,
    language: { type: String, default: 'vi' },
  },
  {
    collection: 'email_templates',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

database.EmailTemplate = mongoose.model('EmailTemplate', EmailTemplateSchema)

database.FCMToken = mongoose.model(
  'FCMToken',
  new mongoose.Schema(
    {
      fcm_token: String,
      user_id: String,
      useragent: String,
    },
    {
      collection: 'fcm_tokens',
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

const ItTicketSchema = new mongoose.Schema(
  {
    code: { type: String, unique: true },
    title: { type: String, require: false },
    description: { type: String, require: false },
    images: [String],
    status: { type: String, enum: ['pending', 'doing', 'fixed', 'completed'], default: 'pending' },
    priority: { type: Number, enum: [1, 2, 3, 4], default: 3 },
    reporter_id: String,
    assignee_id: String,
    category: { type: String },
    fixed_at: Date,
    slack_thread_ts: String,
    jira_ticket: String,
    comments: [
      {
        user_id: String,
        content: String,
        attachments: [String],
        created_at: Date,
      },
    ],
    histories: {
      type: [{
        user_id: { type: String, default: null },
        action: { type: String, enum: ['create', 'assign', 'change_status', 'change_priority'] },
        assigned_to: String,
        new_status: String,
        new_priority: Number,
        note: String,
        created_at: { type: Date, default: Date.now },
      }],
      default: [],
    },
    promise_completed_at: Date,
  },
  {
    collection: 'it_tickets',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

ItTicketSchema.plugin(mongoosePaginate)
database.ItTicket = mongoose.model('ItTicket', ItTicketSchema)

database.ItTicketCounter = mongoose.model(
  'ItTicketCounter', new mongoose.Schema(
    {
      seq: { type: Number, default: 0 },
    },
    {
      collection: 'it_ticket_counters',
    }
  ))

const PartnerBankAccount = new mongoose.Schema({
  bank_name: String,
  account_number: String,
  account_name: String,
})

const PartnerSchema = new mongoose.Schema(
  {
    user_id: { type: String, required: false },
    email: { type: String, required: true },
    member_of: { type: String, required: false }, // userId
    type: { type: String, enum: ['leader', 'member', 'affiliate'], required: true },
    status: { type: String, enum: ['pending', 'active'] },
    level: Number, // member level: 1, 2, 3
    saved_accounts: [PartnerBankAccount],
  },
  {
    collection: 'partners',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

PartnerSchema.index({ userId: 1 }, { sparse: true, unique: true })
PartnerSchema.index({ email: 'text' })
PartnerSchema.plugin(mongoosePaginate)
database.Partner = mongoose.model('Partner', PartnerSchema)

const PartnerWithdraw = new mongoose.Schema({
  user_id: { type: String, required: true },
  amount: { type: Number, required: true },
  brand_id: { type: String, required: true },
  status: { type: String, enum: ['pending', 'completed', 'cancelled'], default: 'pending' },
  note: String,
  bank_account: PartnerBankAccount,
  attachments: [String],
  cancelled_reason: String,
}, {
  collection: 'partner_withdraws',
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
})
PartnerWithdraw.plugin(mongoosePaginate)
database.PartnerWithdraw = mongoose.model('PartnerWithdraw', PartnerWithdraw)

const CommissionSummarySchema = new mongoose.Schema({
  brand_id: { type: String, required: true },
  user_id: { type: String, required: true },
  balance: { type: Number, required: true },
  month: { type: Number, required: true },
  year: { type: Number, required: true },
  details: JSON,
}, {
  collection: 'commission_summaries',
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
})
database.CommissionSummary = mongoose.model('CommissionSummary', CommissionSummarySchema)

const ReferralCommissionSchema = new mongoose.Schema({
  brand_id: { type: String, required: true },
  referrer_id: { type: String, required: true },
  referee_id: { type: String, required: true },
  commissions: [{
    type: { type: String, enum: ['first_order', 'first_month', 'next_n_months'], required: true },
    amount: { type: Number, required: true },
    config: JSON,
    order_ids: [String],
    status: { type: String, enum: ['pending', 'completed'], default: 'pending' },
    redeem_on_month: Number,
    redeem_on_year: Number,
  }]
}, {
  collection: 'referral_commissions',
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
})
ReferralCommissionSchema.index({ brand_id: 1, referrer_id: 1, referee_id: 1 }, { unique: true })
database.ReferralCommission = mongoose.model('ReferralCommission', ReferralCommissionSchema)

const itemCommissionSchema = new mongoose.Schema({
  id: { type: String, required: false },
  category_name: { type: String, required: true },
  item_name: { type: String, required: true },
  commission: {
    type: [
      {
        sale_above: { type: Number, required: true },
        percentage: { type: Number, required: true, min: 0, max: 100 },
      },
    ],
    required: true,
  },
})

const CampaignSchema = new mongoose.Schema(
  {
    brand_id: { type: String, required: true },
    name: { type: String, required: true },
    description: { type: String, required: false },
    start_date: { type: Date, required: true },
    end_date: { type: Date, required: true },
    items: { type: [itemCommissionSchema] },
    created_by: { type: String, required: true },
    partners: [{
      user_id: { type: String, required: true },
      joined_date: { type: Date, required: true },
    }]

  },
  {
    collection: 'campaigns',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

CampaignSchema.index({ name: 'text' })
CampaignSchema.plugin(mongoosePaginate)
database.Campaign = mongoose.model('Campaign', CampaignSchema)

const participantsSchema = new mongoose.Schema({
  phone: { type: String, required: true },
  name: { type: String, required: true },
  address: String,
})

const CampaignUserSchema = new mongoose.Schema(
  {
    brand_id: { type: String, required: true },
    title: { type: String, required: true },
    description: { type: String, required: true },
    status: { type: String, enum: ['pending', 'active'], required: true, default: 'pending' },
    participants: { type: [participantsSchema] }
  },
  {
    collection: 'campaign_users',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

CampaignUserSchema.plugin(mongoosePaginate)
database.CampaignUser = mongoose.model('CampaignUser', CampaignUserSchema)

const PartnerHubCommissionSchema = new mongoose.Schema({
  online_order: {
    packaging: { type: Number, required: true, default: 0 },
    commission: { type: Number, required: true, default: 0 },
  },
  offline_order: {
    packaging: { type: Number, required: true, default: 0 },
    commission: { type: Number, required: true, default: 0 },
  },
})

const BrandCommissionSchema = new mongoose.Schema(
  {
    brand_id: { type: String, required: true },
    name: { type: String, required: true },
    status: { type: String, enum: ['active', 'inactive'], required: true },
    type: { type: String, enum: ['personal', 'team', 'new_customer', 'referral', 'partner_hub'] },
    new_customer: {
      required: false,
      type: {
        bonus: { type: Number, required: true }, // VND
      },
    },
    personal: {
      required: false,
      type: [
        {
          min_sale: { type: Number, required: true }, // minimum sale in a month, vnd
          bonus: { type: Number, required: true }, // percentage over overachievement sale
        },
      ],
    },
    team: {
      required: false,
      type: {
        min_sale_personal: { type: Number, required: true },
        min_team_size: { type: Number, required: true },
        scales: {
          type: [
            {
              min_sale_team: { type: Number, required: true },
              bonus: { type: Number, required: true }, // percentage of total team sale
            },
          ],
          required: true,
        },
      },
    },
    referral: {
      required: false,
      type: {
        first_order: {
          type: {
            bonus: { type: Number, required: true },
            min_order_value: { type: Number, required: true },
          },
          required: false,
        },
        first_month: {
          type: {
            bonus: { type: Number, required: true },
            min_no_of_orders: { type: Number, required: true },
            min_order_value: { type: Number, required: true },
          },
          required: false,
        },
        next_n_months: {
          type: {
            bonus: { type: Number, required: true },
            n: { type: Number, required: true },
            min_no_of_orders: { type: Number, required: true },
            min_order_value: { type: Number, required: true },
          },
          required: false,
        }
      },
    },
    partner_hub: {
      required: false,
      type: {
        basic: PartnerHubCommissionSchema,
        growth: PartnerHubCommissionSchema,
      }
    }
  },
  {
    collection: 'brand_commissions',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
)

BrandCommissionSchema.plugin(mongoosePaginate)
BrandCommissionSchema.index({ brand_id: 1, type: 1 }, { unique: true })
database.BrandCommission = mongoose.model('BrandCommission', BrandCommissionSchema)

database.AppVersion = mongoose.model(
  'AppVersion',
  new mongoose.Schema(
    {
      environment: String,
      name: String,
      bundle_identifier: { type: String, unique: true, required: true },
      version_name: String,
      version_code: String,
      android_version_code: String,
    },
    {
      timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at',
      },
    }
  )
)

database.RetailerSaleConfig = mongoose.model(
  'RetailerSaleConfig',
  new mongoose.Schema({
    brand_id: { type: String, required: true },
    type: {
      type: String,
      enum: ['order_bonus', 'buy_x_get_y', 'discount', 'percent_discount', 'fixed_discount', 'order_discount', 'ship_discount', 'buy_x_discount_y'],
      required: true,
    },
    name: { type: String, required: true },
    description: String,
    order: { type: Number, required: false, default: 0 },
    active: { type: Boolean, required: true, default: true },
    start_date: { type: Date, required: false },
    end_date: { type: Date, required: false },
    voucher_config: {
      type: { type: String, enum: ['one-many'], required: true, default: 'one-many' },
      apply_with_other: { type: Boolean, required: false, default: false },
      voucher_code: { type: String, required: false },
      prefix: { type: String, required: false },
      quantity: { type: Number, required: false },
      usage_count: { type: Number, required: false },
    },
    config: JSON,
  }, {
    collection: 'retailer_sale_configs',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  })
)

database.GhnProvinces = mongoose.model('GhnProvinces', new mongoose.Schema({
  ProvinceID: Number,
  ProvinceName: String,
  NameExtension: [String],
  IsEnable: Boolean,
  RegionID: Number,
  RegionCPN: String,
}, { collection: 'ghn_provinces' }))

database.GhnDistricts = mongoose.model('GhnDistricts', new mongoose.Schema({
  DistrictID: Number,
  ProvinceID: Number,
  DistrictName: String,
  Type: Number,
  SupportType: Number,
  CanUpdateCOD: Boolean,
  Status: Number,
  PickType: Number,
  DeliverType: Number,
}, { collection: 'ghn_districts' }))

database.GhnWards = mongoose.model('GhnWards', new mongoose.Schema({
  WardCode: String,
  WardName: String,
  DistrictID: Number,
  ProvinceID: Number,
  SupportType: Number,
  Status: Number,
  DeliverType: Number,
}, { collection: 'ghn_wards' }))

database.FileQueue = mongoose.model('FileQueue', new mongoose.Schema({
  request_id: String,
  bucket: String,
  key: String,
  file: String,
}, base_collection('file_queues')))

database.UserNotification = mongoose.model('UserNotification', new mongoose.Schema({
  user_id: String,
  type: { type: String, enum: ['success', 'error', 'info'] },
  title: String,
  content: String,
  content_data: Object,
  attachments: [String],
  images: [String],
  status: { type: String, enum: ['unread', 'read'], default: 'unread' },
  topic: { type: String, required: false, enum: ['report', 'it_ticket', 'menu'] },
}, base_collection('user_notifications'))
  .plugin(mongoosePaginate)
)


const BrandProfileSchema = new mongoose.Schema({
  total_orders: { type: Number, index: true },
  total_order_amount: { type: Number, index: true },
  last_order_at: { type: Date, index: true },
  segments: { type: [String], index: true },
  brand_id: { type: String, required: true, index: true },
})

database.CustomerProfile = mongoose.model('CustomerProfile', new mongoose.Schema({
  phone: { type: String, required: true, index: true },
  names: [String],
  addresses: [String],
  total_orders: Number,
  brands: [BrandProfileSchema],
  // last_order_at: Date,
  total_order_amount: Number,
  // segments: [String],
  segment_synced: Boolean,
}, base_collection('customer_profiles'))
  .plugin(mongoosePaginate)
)

database.CustomerSegment = mongoose.model('CustomerSegment', new mongoose.Schema({
  name: { type: String, required: true },
  description: String,
  filters: {
    type: [{
      field: String,
      operator: {
        type: String,
        enum: ['bw', 'gte', 'lte', 'lt', 'gt'],
        required: true,
      },
      value: mongoose.Schema.Types.Mixed,
    }],
    required: true
  },
  synced: Boolean,
  brand_id: { type: String, required: true },
}, base_collection('customer_segments')).index({ brand_id: 1, name: 1 }, { unique: true })
)

database.Voucher = mongoose.model('Voucher', new mongoose.Schema({
  code: { type: String, required: true, index: true },
  dpoint_username: { type: String, required: false }, // cyber-kitchen, daumamix, ... 
  discount: { type: Number, required: false },
  vendor: { type: String, enum: ['dpoint', 'nexdor'], required: true },
  is_used: { type: Boolean, default: false },
  owner_phone: String,
  used_at: Date,
  trade_at: Date,
  expired_at: Date,
  note: String,
  brand_id: { type: String, required: false }, // for nexdor
}, base_collection('vouchers')).index({ dpoint_username: 1, code: 1 }, { unique: true })
)

database.WatchDogEvent = mongoose.model('WatchDogEvent', new mongoose.Schema({
  event_name: { type: String, enum: ['order_need_confirm_notification'] },
  event_id: String,
  data: Object,
  count: { type: Number, default: 0 },
  active: { type: Boolean, default: true },
}, base_collection('watchdog_events')))

database.FileDownload = mongoose.model('FileDownload', new mongoose.Schema({
  file: { type: String, required: true },
  file_src: { type: String, required: true },
  file_name: String,
  md5: String,
}, base_collection('file_downloads')))

database.SupportTool = mongoose.model('SupportTool', new mongoose.Schema({
  request_number: { type: Number },
  request_type: { type: String, enum: ['site_menu_promotion', 'site_open_time', 'site_open_status', 'site_special_time'] },
  request_data: new mongoose.Schema({
    site_menu: new mongoose.Schema({
      brand_id: String,
      sources: [{ type: String, enum: ['shopee', 'grab'] }],
      site_ids: [String], // If empty, apply for all sites
      file_url: String,
      set_promotion: { type: Boolean, default: false },
    }),
  }),
  description: String,
  status: { type: String, enum: ['PENDING', 'PROCESSING', 'SUCCESS', 'FAILED'], default: 'PENDING' },
  fail_reason: String,
  created_by: String,
  created_at_unix: Number,
  processing_at_unix: Number,
  completed_at_unix: Number,
}, base_collection('support_tools'))
  .plugin(mongoosePaginate)
  .plugin(AutoIncrement, { inc_field: 'request_number' })
)

database.Material = mongoose.model('Material', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
  code: { type: String, required: true },
  category: { type: String, required: false },
  description: String,
  price: { type: Number, required: false },
  images: [String],
  unit: { type: String, required: false },
  source: { type: String, required: false },
  weight: { type: Number, required: false }, // kg
  sale_price: { type: Number, required: false },
}, base_collection('materials')).plugin(mongoosePaginate).index({ brand_id: 1, code: 1 }, { unique: true }))

database.CoreProduct = mongoose.model('CoreProduct', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
  code: { type: String, required: true },
  bar_code: { type: String, required: false },
  category: { type: String, required: false },
  source: { type: String, required: false },
  type: { type: String, require: true, enum: ['nguyen_lieu', 'ban_thanh_pham', 'thanh_pham', 'hang_hoa'], default: 'nguyen_lieu' },
  description: String,
  sale_price: { type: Number, required: false },
  price: { type: Number, required: false },
  images: [String],
  unit: { type: String, required: false },
  weight: { type: Number, required: false }, // kg
  length: { type: Number, required: false }, // cm
  height: { type: Number, required: false }, // cm
  ingredients: {
    type: [{
      code: { type: String, required: true },
      amount: { type: Number, required: true },
      display_name: { type: String, required: false },
      unit: { type: String, required: false },
    }],
    required: false,
    default: []
  },
  available_for_sale: { type: Boolean, default: true },
  status: { type: String, enum: ['active', 'draft', 'inactive'], default: 'active' },
  quantity_unlimited: { type: Boolean, default: false },
}, base_collection('core_products')).plugin(mongoosePaginate).index({ brand_id: 1, code: 1 }, { unique: true }))

database.CoreProductCategory = mongoose.model('CoreProductCategory', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
}, base_collection('core_product_categories')).plugin(mongoosePaginate).index({ brand_id: 1, name: 1 }, { unique: true }))

database.CoreProductSource = mongoose.model('CoreProductSource', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
}, base_collection('core_product_sources')).plugin(mongoosePaginate).index({ brand_id: 1, name: 1 }, { unique: true }))

database.CoreProductUnit = mongoose.model('CoreProductUnit', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
}, base_collection('core_product_unit')).plugin(mongoosePaginate).index({ brand_id: 1, name: 1 }, { unique: true }))

// menu cp
database.CpBrandMenuTemplate = mongoose.model('CpBrandMenuTemplate', new mongoose.Schema({
  brand_id: { type: String, required: true },
  name: { type: String, required: true },
  description: String,
}, base_collection('brand_menu_templates')))

const siteMenuSchema = new mongoose.Schema({
  site_id: { type: String, required: true },
  modified_on: { type: Date, required: false },
  template_id: { type: String, required: false },
  channels: {
    type: [{
      channel: { type: String, required: true, enum: ['grab_food', 'he', 'local'] },
      modified_on: { type: Date, required: false },
      synced: { type: Boolean, default: false },
    }],
  },
  histories: [JSON],
}, base_collection('site_menus'))
database.SiteMenu = mongoose.model('SiteMenu', siteMenuSchema);

const BaseCPMenuCategorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  order: { type: Number, required: true },
  active: { type: Boolean, default: true },
  parent_id: { type: String, required: false },
})

database.BrandMenuCategoryTemplate = mongoose.model('BrandMenuCategoryTemplate', new mongoose.Schema({
  template_id: { type: String, required: true },
  ...BaseCPMenuCategorySchema.obj,
}, base_collection('brand_menu_category_templates')).plugin(mongoosePaginate).index({ template_id: 1, name: 1 }, { unique: true }))

database.SiteMenuCategory = mongoose.model('SiteMenuCategory', new mongoose.Schema({
  site_id: { type: String, required: true },
  ...BaseCPMenuCategorySchema.obj,
}, base_collection('site_menu_categories')).plugin(mongoosePaginate).index({ site_id: 1, name: 1 }, { unique: true }))

const BaseCPMenuItemSchema = new mongoose.Schema({
  name: { type: String, required: true },
  code: { type: String, required: true },
  category_id: { type: String, required: false },
  option_id: { type: String, required: false },
  order: { type: Number, required: false, default: 0 },
  unit: String,
  sources: [String],
  description: String,
  images: [String],
  image_preview: String,
  price: Number,
  active: { type: Boolean, default: true },
  quantity: { type: Number },
  quantity_unlimited: { type: Boolean, default: false },
})

database.BrandMenuItemTemplate = mongoose.model('BrandMenuItemTemplate', new mongoose.Schema({
  template_id: { type: String, required: true },
  ...BaseCPMenuItemSchema.obj,
}, base_collection('brand_menu_item_templates')).plugin(mongoosePaginate))

database.SiteMenuItem = mongoose.model('SiteMenuItem', new mongoose.Schema({
  site_id: { type: String, required: true },
  ref_id: { type: String, required: false },
  channels: {
    type: [{
      channel: { type: String, required: true, enum: ['grab_food', 'he', 'local', 'shopee_food'] },
      active: { type: Boolean, default: true },
      price: { type: Number, required: false },
      name: { type: String, required: false },
      categories: [String],
      additional: { type: Object, required: false, default: {} },
    }]
  },
  ...BaseCPMenuItemSchema.obj,
}, base_collection('site_menu_items')).plugin(mongoosePaginate))

const BaseCPMenuOptionSchema = new mongoose.Schema({
  name: { type: String, required: true },
  order: { type: Number, required: false, default: 0 },
  active: { type: Boolean, default: true },
  rule: {
    type: {
      type: String,
      enum: ['SELECT_ONE', 'SELECT_MANY'],
      default: 'SELECT_ONE',
    },
    required: Boolean,
    max_quantity: { type: Number, default: 1 },
    min_quantity: { type: Number, default: 1 },
  },
  item_ids: {
    type: [String],
    required: false,
    default: []
  },
})

database.BrandMenuOptionTemplate = mongoose.model('BrandMenuOptionTemplate', new mongoose.Schema({
  template_id: { type: String, required: true },
  ...BaseCPMenuOptionSchema.obj,
}, base_collection('brand_menu_option_templates')).plugin(mongoosePaginate))

database.SiteMenuOption = mongoose.model('SiteMenuOption', new mongoose.Schema({
  site_id: { type: String, required: true },
  ...BaseCPMenuOptionSchema.obj,
}, base_collection('site_menu_options')).plugin(mongoosePaginate))

const BaseCPMenuOptionItemSchema = new mongoose.Schema({
  name: { type: String, required: true },
  code: { type: String, required: true },
  option_id: { type: String, required: true },
  price: { type: Number, required: false },
  order: { type: Number, required: false, default: 0 },
  active: { type: Boolean, default: true },
  quantity: { type: Number },
  quantity_unlimited: { type: Boolean, default: false },
})

database.BrandMenuOptionItemTemplate = mongoose.model('BrandMenuOptionItemTemplate', new mongoose.Schema({
  template_id: { type: String, required: true },
  ...BaseCPMenuOptionItemSchema.obj,
}, base_collection('brand_menu_option_item_templates')).plugin(mongoosePaginate))

database.SiteMenuOptionItem = mongoose.model('SiteMenuOptionItem', new mongoose.Schema({
  site_id: { type: String, required: true },
  ...BaseCPMenuOptionItemSchema.obj,
}, base_collection('site_menu_option_items')).plugin(mongoosePaginate))

database.Provinces = mongoose.model('Provinces', new mongoose.Schema({
  code: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  google_names: [String],
}, base_collection('provinces')))

const siteCMSSchema = new mongoose.Schema(
  {
    site_id: { type: mongoose.Schema.Types.ObjectId, required: true, unique: true, ref: 'Site' },
    pages: { type: mongoose.Schema.Types.Mixed, required: true },
  },
  {
    collection: 'site_cms',
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  }
);
database.SiteCMS = mongoose.model('SiteCMS', siteCMSSchema);

database.BrandBillConfig = mongoose.model('BrandBillConfig', new mongoose.Schema({
  name: { type: String, required: true },
  brand_id: { type: String, required: true },
  bill_type: { type: String, required: true },
  bill_size: { type: Number, default: 80 },
  show_bank_payment: { type: Boolean, default: false },
  content_html: { type: String, default: '' },
  active: { type: Boolean, default: true },
}, base_collection('brand_bill_configs')).plugin(mongoosePaginate))

database.PageConversation = mongoose.model('PageConversation', new mongoose.Schema({
  channel: { type: String, required: true, enum: ['facebook', 'zalo'], default: 'facebook' },
  page_id: { type: String, required: true },
  uid: { type: String, required: true },
  status: { type: String, enum: ['open', 'closed'], default: 'open' },
  messages: [{
    role: { type: String, required: true, enum: ['user', 'bot'] },
    message: { type: String, required: true },
    attachments: [String],
    created_at: { type: Date, required: true },
  }]
}, base_collection('page_conversations')))

database.WorkingShift = mongoose.model('WorkingShift', new mongoose.Schema({
  hub_id: { type: String, required: true },
  open_by_user_id: { type: String, required: true },
  closed_by_user_id: String,
  start_time: Date,
  end_time: Date,
  initial_amount: Number,
  income: [{
    order_id: String,
    payment_method: { type: String, required: true, default: 'CASH' },
    amount: Number,
    note: String
  }],
  outcome: [{
    amount: Number,
    payment_method: { type: String, required: true, default: 'CASH' },
    note: String
  }],
  total_amount: Number,
  actual_amount: Number,
  status: { type: String, enum: ['open', 'closed'], default: 'open' },
  note: String,
  report: JSON,
  report_url: String
}, base_collection('working_shifts')).plugin(mongoosePaginate))

database.ShorternCode = mongoose.model('ShorternCode', new mongoose.Schema({
  code: { type: String, required: true },
  data: { type: JSON, required: true },
  status: { type: String, enum: ['new', 'used'], default: 'new' },
}, base_collection('shortern_codes')).plugin(mongoosePaginate))


database.ZaloChatMessage = mongoose.model('ZaloChatMessage', new mongoose.Schema({
  target: { type: String, enum: ['user', 'group'], default: 'user' },
  to_id: String,
  to_name: String,
  msg_id: { type: String, required: true },
  msg_type: { type: String, enum: ['text', 'file'], default: 'text' },
  from_id: { type: String, required: true },
  from_name: { type: String, required: true },
  message: String,
  files: [String],
  created_at_unix: { type: Number, required: true },
  raw: JSON,
}, base_collection('zalo_chat_messages')).plugin(mongoosePaginate))

module.exports = database
