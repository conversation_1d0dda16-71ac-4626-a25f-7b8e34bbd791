const searchReadService = async (cookie, params) => {
    var response = await fetch(process.env.ODOO_URL + '/web/dataset/search_read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Cookie': cookie },
        body: JSON.stringify({
            jsonrpc: '2.0',
            method: "call",
            params: params
        })
    });

    var responseJson = await response.json();
    return responseJson;
}

module.exports = { searchReadService }