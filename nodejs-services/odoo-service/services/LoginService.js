const axios = require('axios');

const loginService = async () => {
    try {
        const response = await axios({
            method: 'POST',
            url: process.env.ODOO_URL + '/web/session/authenticate',
            headers: { 'Content-Type': 'application/json' },
            data: {
                jsonrpc: '2.0',
                params: {
                    "db": process.env.ODOO_DATABASE,
                    "login": process.env.ODOO_USER,
                    "password": process.env.ODOO_PASSWORD
                }
            }
        })


        const cookie = response.headers.get("Set-Cookie");

        return cookie;
    } catch (error) {
        console.error(error)
        return null
    }

}

module.exports = { loginService }