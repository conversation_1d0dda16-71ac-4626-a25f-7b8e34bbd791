const axios = require('axios');
axios.interceptors.request.use((config) => {
    config.timeout = 15000
    return config
})

const callKwService = async (cookie, params) => {
    try {
        const resp = await axios({
            method: 'POST',
            url: process.env.ODOO_URL + '/web/dataset/call_kw',
            headers: { 'Content-Type': 'application/json', 'Cookie': cookie },
            data: {
                jsonrpc: '2.0',
                method: 'call',
                params: params
            }
        })

        return resp.data;
    } catch (error) {
        console.error(error)
        return null
    }

}

module.exports = { callKwService }