require('dotenv').config({ path: '.env' })
const express = require('express');

const { sync_all_products } = require('./route/product');
const odoo = require('./route/order');
const tracardi = require('./route/tracardi');
const { sync_customer_profiles } = require('./route/customer_profile');

// (async () => {
//     while (true) {
//         try {
//             await tracardi.sync_all_orders()
//             await new Promise((resolve) => setTimeout(resolve, 1000));
//         } catch (error) {
//             console.log(error.message)
//         }
//     }
// })();

(async () => {
    while (true) {
        try {
            await odoo.sync_all_orders()
            await new Promise(r => setTimeout(r, 5000));
        } catch (error) {
            console.log(error.message)
            await new Promise(r => setTimeout(r, 5000));
        }
    }
})();

(async () => {
    // await sync_all_products()
})();

(async () => {
    while (true) {
        try {
            await sync_customer_profiles()
            await new Promise((resolve) => setTimeout(resolve, 10000));
        } catch (error) {
            console.log(error.message)
        }
    }
})();

const app = express();
const port = 3000;

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});
