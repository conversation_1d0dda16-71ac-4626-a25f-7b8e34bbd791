# -*- coding: utf-8 -*-

from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    group_stock_multi_locations = fields.Boolean(
        'Storage Locations',
        implied_group='stock.group_stock_multi_locations',
        default=True,
        help="Store products in specific locations of your warehouse (e.g. bins, racks) and to track inventory accordingly."
    )
