from odoo import models


class ReportInventoryXlsx(models.AbstractModel):
    _name = "report.report_inventory_xlsx"
    _inherit = "report.report_xlsx.abstract"
    _description = "Report Inventory XLSX"

    def generate_xlsx_report(self, workbook, data, records):
        stock_location = self.env['stock.location'].browse(data['stock_location_id'])
        date_from = data['date_from']
        date_to = data['date_to']

        sheet = workbook.add_worksheet("Xuất Nhập Tồn")

        # STYLE
        bold_format = workbook.add_format({"bold": True})
        align_center_format = workbook.add_format({'align': 'center'})
        align_right_format = workbook.add_format({'align': 'right'})

        center_format_wrap = workbook.add_format({'align': 'center', 'text_wrap': True})

        center_bold_format = workbook.add_format({'align': 'center', 'bold': True})
        center_bold_18_format = workbook.add_format({'align': 'center', 'bold': True, 'font_size': 18})
        center_bold_italic_format = workbook.add_format({'align': 'center', 'bold': True, 'italic': True})

        merge_format_left = workbook.add_format({'align': 'left'})
        merge_format_left_bold = workbook.add_format({'align': 'left', 'bold': True})

        date_format_bold = workbook.add_format({'num_format': 'dd/mm/yyyy', 'bold': True})

        currency_format = workbook.add_format({'num_format': '#,##0'})
        currency_bold_format = workbook.add_format({'num_format': '#,##0', 'bold': True})

        sheet.merge_range('A1:M1', 'TỔNG HỢP XUẤT-NHẬP-TỒN KHO', center_bold_18_format)
        sheet.merge_range('A2:M2', 'Từ ngày %s đến ngày %s' % (date_from, date_to), center_bold_italic_format)
        sheet.merge_range('A3:M3', 'Kho: %s' % stock_location.name, center_bold_format)

        sheet.merge_range('A5:A6', 'STT', center_bold_format)
        sheet.merge_range('B5:B6', 'Mã sản phẩm', center_bold_format)
        sheet.merge_range('C5:C6', 'Tên sản phẩm', center_bold_format)
        sheet.merge_range('D5:D6', 'Nhóm sản phẩm', center_bold_format)
        sheet.merge_range('E5:E6', 'ĐVT', center_bold_format)

        sheet.merge_range('F5:G5', 'Tồn đầu kỳ', center_bold_format)
        sheet.write('F6', 'Số lượng', center_bold_format)
        sheet.write('G6', 'Giá trị', center_bold_format)

        sheet.merge_range('H5:I5', 'Nhập trong kỳ', center_bold_format)
        sheet.write('H6', 'Số lượng', center_bold_format)
        sheet.write('I6', 'Giá trị', center_bold_format)

        sheet.merge_range('J5:K5', 'Xuất trong kỳ', center_bold_format)
        sheet.write('J6', 'Số lượng', center_bold_format)
        sheet.write('K6', 'Giá trị', center_bold_format)

        sheet.merge_range('L5:M5', 'Tồn cuối kỳ', center_bold_format)
        sheet.write('L6', 'Số lượng', center_bold_format)
        sheet.write('M6', 'Giá trị', center_bold_format)

        stock_move_lines = self.env['stock.move.line'].search([
            "&",
            ('state', '=', 'done'),
            "&",
            "&",
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            "|",
            ("location_id", "=", stock_location.id),
            ("location_dest_id", "=", stock_location.id),
        ])

        stock_move_lines_by_product = stock_move_lines.read_group([], ['product_id'], ['product_id'])

        stt = 1
        for item in stock_move_lines_by_product:
            product_id = self.env['product.product'].browse(item['product_id'][0])

            sheet.write('A%s' % str(stt + 6), stt)
            sheet.write('B%s' % str(stt + 6), product_id.default_code if product_id.default_code else '')
            sheet.write('C%s' % str(stt + 6), product_id.display_name)
            sheet.write('D%s' % str(stt + 6), product_id.categ_id.name)
            sheet.write('E%s' % str(stt + 6), product_id.uom_id.name)

            qty_start = product_id.with_context({'to_date': date_from}).qty_available
            sheet.write('F%s' % str(stt + 6), int(qty_start))
            sheet.write('G%s' % str(stt + 6), int(qty_start) * product_id.standard_price)

            incoming_moves = stock_move_lines.search([
                ('product_id', '=', product_id.id),
                ('picking_code', '=', 'incoming'),
            ])
            qty_incoming_moves = sum(incoming_moves.mapped('qty_done'))
            sheet.write('H%s' % str(stt + 6), int(qty_incoming_moves))
            sheet.write('I%s' % str(stt + 6), int(qty_incoming_moves) * product_id.standard_price)

            outgoing_moves = stock_move_lines.search([
                ('product_id', '=', product_id.id),
                ('picking_code', '=', 'outgoing'),
            ])
            qty_outgoing_moves = sum(outgoing_moves.mapped('qty_done'))
            sheet.write('J%s' % str(stt + 6), int(qty_outgoing_moves))
            sheet.write('K%s' % str(stt + 6), int(qty_outgoing_moves) * product_id.standard_price)

            qty_end = product_id.with_context({'to_date': date_to}).qty_available
            sheet.write('L%s' % str(stt + 6), int(qty_end))
            sheet.write('M%s' % str(stt + 6), int(qty_end) * product_id.standard_price)

            stt += 1

