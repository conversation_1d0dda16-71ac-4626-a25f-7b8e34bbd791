# -*- coding: utf-8 -*-
from odoo import models, fields


class ReportInventoryWizard(models.TransientModel):
    _name = "report.inventory.wizard"
    _description = "Report Inventory"

    stock_location = fields.Many2one('stock.location', string='Stock', required=True)
    date_from = fields.Date(string='From Date', required=True)
    date_to = fields.Date(string='To Date', required=True)

    def export_report(self):
        data = {
            'stock_location_id': self.stock_location.id,
            'date_from': self.date_from,
            'date_to': self.date_to
        }

        report_action = self.env.ref('nexpos_stock.report_inventory_xlsx').report_action(self, data=data)
        report_action['close_on_report_download'] = True
        return report_action
