# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_budget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 18:16+0000\n"
"PO-Revision-Date: 2022-04-15 18:16+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "يجب تضمين \"تاريخ الانتهاء\" لبند الميزانية في فترة الميزانية\n"

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "يجب تضمين \"تاريخ البدء\" لبند الميزانية في فترة الميزانية\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"فترة\n"
"\"/>"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
msgid "Accounts"
msgstr "الحسابات"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "موهلات"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_plan_id
msgid "Analytic Group"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "الدخل\n"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "بنود الميزانية\n"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "حد الميزانية\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "خطوط الميزانية\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__name
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Budget Name"
msgstr "اسم الميزانية\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "دولة الميزانية\n"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "موقف الميزانية\n"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.open_budget_post_form
#: model:ir.ui.menu,name:om_account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "وظائف الميزانية\n"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "الميزانيات\n"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "تحليل الميزانيات\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "إلغاء الميزانية\n"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "انقر لإنشاء ميزانية جديدة.\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "الشركة"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""
"مقارنة بين المقدار العملي والنظري. يخبرك هذا الإجراء إذا كنت أقل من "
"الميزانية أو تزيد عنها.\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Done"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "مشروع الميزانيات\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "فوق الميزانية\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "تاريخ المدفوعة\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "فترة\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "المبلغ المخطط\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "المبلغ المخطط\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "المبلغ العملي\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "المبلغ العملي\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__state
msgid "Status"
msgstr ""

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "يجب أن تحتوي الميزانية على حساب واحد على الأقل.\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Theoretical Amount"
msgstr "المبلغ النظري\n"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoritical Amount"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Theoritical amount"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "لاعتماد الميزانيات\n"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"استخدم الميزانيات لمقارنة الإيرادات الفعلية مع الإيرادات والتكاليف "
"المتوقعة\n"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr ""
"يجب عليك إدخال موقف ميزانية أو حساب تحليلي على الأقل في بند الميزانية.\n"
