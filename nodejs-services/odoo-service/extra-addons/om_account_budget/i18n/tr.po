# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_budget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 06:43+0000\n"
"PO-Revision-Date: 2022-04-15 06:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "Bütçe satırının \"bitiş tarihi\", bütçe dönemi içinde olmalıdır."

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "Bütçe satırının \"başlangıç tarihi\", bütçe dönemi içinde olmalıdır."

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Dönem\" title=\"Dönem\"/>"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
msgid "Accounts"
msgstr "Hesaplar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "Kazanım"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr "Eylem Gerekli"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr "Gerçekten kazanılan/harcanan tutar."

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "Bu tarihe dek kazanmanız/harcamanız gereken tutar"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""
"Kazanmayı/harcamayı planladığınız tutar. Bu bir gelir ise pozitif, gider ise"
" negatif değer giriniz."

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_plan_id
msgid "Analytic Group"
msgstr "Analitik Grup"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "Onayla"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "Bütçe"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "Bütçe Kalemleri"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "Bütçe Satırı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "Bütçe Satırları"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__name
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Budget Name"
msgstr "Bütçe Adı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "Bütçe Durumu"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "Bütçe Pozisyonu"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.open_budget_post_form
#: model:ir.ui.menu,name:om_account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "Bütçe Pozisyonları"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "Bütçeler"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "Bütçe Analizi"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "Bütçeyi İptal Et"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "Yeni bir bütçe oluşturmak için tıklayınız"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "Şirket"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""
"Pratik ve teorik tutarların karşılaştırması. Bu kriter size bütçenin altında"
" mı üstünde mi olduğunuzu söyler."

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "Onayla"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr "Teyit Edildi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr "Oluşturulma Tarihi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr "Para Birimi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr "Görüntülenen Ad"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "Tamamlandı"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr "Taslak"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "Taslak Bütçe"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr "Kayıtlar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş Ortakları)"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr "Gruplama"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr "Mesajı Olan"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr ""

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşaretlenirse, yeni mesajlarla ilgilenmeniz gerekir."

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretlenirse, bazı iletilerde teslim hatası olur."

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "Bütçenin Üzerinde"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi?"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr "Son Değişiklik Tarihi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr "Son Güncellemeyi Yapan"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme Tarihi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr "Ana Ek"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr "Teslim hatası mesajı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr "İsim"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr "İptal Edilmedi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "Eylem Sayısı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr "Hata Sayısı"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Eylem gerektiren mesaj sayısı"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslim hatası içeren mesaj hatası"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr "Okunmamış mesaj sayısı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "Ödeme Tarihi"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "Dönem"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "Planlanan Tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "Planlanan Tutar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "Pratikteki Tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "Pratikteki tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "Taslak durumuna sıfırla"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "Sorumlu"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Teslim Hatası"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "Başlangıç Tarihi"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__state
msgid "Status"
msgstr "Durum"

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "Bütçe en az bir hesap içermelidir."

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
msgid "Theoretical Amount"
msgstr "Teorik Tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoritical Amount"
msgstr "Teorik Tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
msgid "Theoritical amount"
msgstr "Teorik Tutar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr "Onaylamak"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "Onay Bekleyen Bütçe"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr "Okunmamış Mesajlar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Okunmamış Mesaj Sayacı"

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"Beklenen gelir ve harcamalar ile gerçekte değerleri karşılaştırmak için "
"bütçeleri kullanabilirsiniz"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr "Doğrulandı"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: om_account_budget
#: code:addons/om_account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr ""
"Bütçe satırına en az bir bütçe pozisyonu ya da analitik hesap girmelisiniz."
