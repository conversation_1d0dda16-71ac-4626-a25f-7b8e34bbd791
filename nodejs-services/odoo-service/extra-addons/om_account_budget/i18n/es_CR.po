# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_budget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-08 20:50+0000\n"
"PO-Revision-Date: 2023-07-08 14:51-0600\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_CR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.3.2\n"

#. module: om_account_budget
#. odoo-python
#: code:addons/om_account_budget/models/account_budget.py:0
#, fuzzy, python-format
msgid "\"End Date\" of the budget line should be included in the Period of the budget"
msgstr ""
"La \"Fecha de finalización\" de la línea presupuestaria debe incluirse en el período "
"del presupuesto"

#. module: om_account_budget
#. odoo-python
#: code:addons/om_account_budget/models/account_budget.py:0
#, fuzzy, python-format
msgid "\"Start Date\" of the budget line should be included in the Period of the budget"
msgstr ""
"La \"Fecha de inicio\" de la línea presupuestaria debe incluirse en el período del "
"presupuesto"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_kanban
#, fuzzy
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
#, fuzzy
msgid "Accounts"
msgstr "Cuentas"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__percentage
#, fuzzy
msgid "Achievement"
msgstr "Logro"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction
#, fuzzy
msgid "Action Needed"
msgstr "Acción necesaria"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__practical_amount
#, fuzzy
msgid "Amount really earned/spent."
msgstr "Cantidad realmente ganada/gastada."

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__theoritical_amount
#, fuzzy
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "Cantidad que se supone que debe haber ganado / gastado en esta fecha."

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__planned_amount
#, fuzzy
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue and a "
"negative amount if it is a cost."
msgstr ""
"Cantidad que planea ganar/gastar. Registre una cantidad positiva si es un ingreso y "
"una cantidad negativa si es un costo."

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__analytic_plan_id
#, fuzzy
msgid "Analytic Plan"
msgstr "Plan analítico"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Approve"
msgstr "Aprobar"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_attachment_count
#, fuzzy
msgid "Attachment Count"
msgstr "Recuento de archivos adjuntos"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
#, fuzzy
msgid "Budget"
msgstr "Presupuesto"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
#, fuzzy
msgid "Budget Items"
msgstr "Partidas presupuestarias"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_crossovered_budget_lines
#, fuzzy
msgid "Budget Line"
msgstr "Línea de presupuesto"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_tree
#, fuzzy
msgid "Budget Lines"
msgstr "Líneas presupuestarias"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__name
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Budget Name"
msgstr "Nombre del presupuesto"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__crossovered_budget_state
#, fuzzy
msgid "Budget State"
msgstr "Estado del presupuesto"

#. module: om_account_budget
#: model:ir.model,name:om_account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_budget_post_tree
#, fuzzy
msgid "Budgetary Position"
msgstr "Situación Presupuestaria"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.open_budget_post_form
#: model:ir.ui.menu,name:om_account_budget.menu_budget_post_form
#, fuzzy
msgid "Budgetary Positions"
msgstr "Situaciones presupuestarias"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#, fuzzy
msgid "Budgets"
msgstr "Presupuestos"

#. module: om_account_budget
#: model:ir.actions.act_window,name:om_account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:om_account_budget.menu_act_crossovered_budget_lines_view
#, fuzzy
msgid "Budgets Analysis"
msgstr "Análisis de Presupuestos"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Cancel Budget"
msgstr "Cancelar presupuesto"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__cancel
#, fuzzy
msgid "Cancelled"
msgstr "Cancelado"

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
#, fuzzy
msgid "Click to create a new budget."
msgstr "Haga clic para crear un nuevo presupuesto."

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__company_id
#, fuzzy
msgid "Company"
msgstr "Compañía"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget_lines__percentage
#, fuzzy
msgid ""
"Comparison between practical and theoretical amount. This measure tells you if you "
"are below or over budget."
msgstr ""
"Comparación entre cantidad práctica y teórica. Esta medida le indica si está por "
"debajo o por encima del presupuesto."

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Confirm"
msgstr "Confirmar"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__confirm
#, fuzzy
msgid "Confirmed"
msgstr "Confirmado"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_uid
#, fuzzy
msgid "Created by"
msgstr "Creado por"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__create_date
#, fuzzy
msgid "Created on"
msgstr "Creado en"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__currency_id
#, fuzzy
msgid "Currency"
msgstr "Moneda"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__display_name
#, fuzzy
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Done"
msgstr "Realizado"

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
#, fuzzy
msgid "Draft"
msgstr "Borrador"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
#, fuzzy
msgid "Draft Budgets"
msgstr "Proyectos de presupuesto"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_to
#, fuzzy
msgid "End Date"
msgstr "Fecha final"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Entries..."
msgstr "Asientos"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_follower_ids
#, fuzzy
msgid "Followers"
msgstr "Seguidores"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_partner_ids
#, fuzzy
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#, fuzzy
msgid "Group By"
msgstr "Agrupar por"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__has_message
#, fuzzy
msgid "Has Message"
msgstr "Tiene mensaje"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__id
#, fuzzy
msgid "ID"
msgstr "ID (identificación)"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction
#, fuzzy
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, los nuevos mensajes requieren su atención."

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_sms_error
#, fuzzy
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen un error de entrega."

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__is_above_budget
#, fuzzy
msgid "Is Above Budget"
msgstr "Está por encima del presupuesto"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_is_follower
#, fuzzy
msgid "Is Follower"
msgstr "Es seguidor"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines____last_update
#, fuzzy
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_uid
#, fuzzy
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__write_date
#, fuzzy
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_main_attachment_id
#, fuzzy
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error
#, fuzzy
msgid "Message Delivery error"
msgstr "Error de entrega de mensajes"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_ids
#, fuzzy
msgid "Messages"
msgstr "Mensajes"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__name
#, fuzzy
msgid "Name"
msgstr "Nombre"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_search
#, fuzzy
msgid "Not Cancelled"
msgstr "Cancelado"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_needaction_counter
#, fuzzy
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_error_counter
#, fuzzy
msgid "Number of errors"
msgstr "Número de errores"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_needaction_counter
#, fuzzy
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__message_has_error_counter
#, fuzzy
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__paid_date
#, fuzzy
msgid "Paid Date"
msgstr "Fecha de pago"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Period"
msgstr "Periodo"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Planned Amount"
msgstr "Cantidad planificada"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#, fuzzy
msgid "Planned amount"
msgstr "Importe previsto"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
#, fuzzy
msgid "Practical Amount"
msgstr "Cantidad práctica"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#, fuzzy
msgid "Practical amount"
msgstr "Cantidad práctica"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Reset to Draft"
msgstr "Restablecer a borrador"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__user_id
#, fuzzy
msgid "Responsible"
msgstr "Responsable"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__message_has_sms_error
#, fuzzy
msgid "SMS Delivery error"
msgstr "Error de entrega de SMS"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__date_from
#, fuzzy
msgid "Start Date"
msgstr "Fecha inicial"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__state
#, fuzzy
msgid "Status"
msgstr "Estado"

#. module: om_account_budget
#. odoo-python
#: code:addons/om_account_budget/models/account_budget.py:0
#, fuzzy, python-format
msgid "The budget must have at least one account."
msgstr "El presupuesto debe tener al menos una cuenta."

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:om_account_budget.crossovered_budget_view_form
#, fuzzy
msgid "Theoretical Amount"
msgstr "Importe teórico"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_account_analytic_account_form_inherit_budget
#, fuzzy
msgid "Theoritical Amount"
msgstr "Cantidad teórica"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_line_pivot
#, fuzzy
msgid "Theoritical amount"
msgstr "Cantidad teórica"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
#, fuzzy
msgid "To Approve"
msgstr "Por aprobar"

#. module: om_account_budget
#: model_terms:ir.ui.view,arch_db:om_account_budget.view_crossovered_budget_search
#, fuzzy
msgid "To Approve Budgets"
msgstr "Para aprobar presupuestos"

#. module: om_account_budget
#: model_terms:ir.actions.act_window,help:om_account_budget.act_crossovered_budget_view
#, fuzzy
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"Utiliza los presupuestos para comparar los ingresos y costos reales con los esperados."

#. module: om_account_budget
#: model:ir.model.fields.selection,name:om_account_budget.selection__crossovered_budget__state__validate
#, fuzzy
msgid "Validated"
msgstr "Validado"

#. module: om_account_budget
#: model:ir.model.fields,field_description:om_account_budget.field_crossovered_budget__website_message_ids
#, fuzzy
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: om_account_budget
#: model:ir.model.fields,help:om_account_budget.field_crossovered_budget__website_message_ids
#, fuzzy
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: om_account_budget
#. odoo-python
#: code:addons/om_account_budget/models/account_budget.py:0
#, fuzzy, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a budget line."
msgstr ""
"Debe introducir al menos una situación presupuestaria o una cuenta analítica en una "
"línea presupuestaria."
