<?xml version="1.0" encoding="UTF-8"?>
	<templates id="pos_product_bundle_pack_template" inherit_id="point_of_sale.template" xml:space="preserve">

	<t t-extend="Product" t-inherit="point_of_sale.ProductItem" t-inherit-mode="extension" owl="1">
		
		<xpath expr="//div[hasclass('product-img')]" position="inside">
			<t t-if="props.product.is_pack">
				<div class="product-bundle-pack" style="position: absolute; box-sizing: border-box; bottom: 0;top: 65px; line-height: 15px; width: 100%; height: 30px; overflow: hidden; text-overflow: ellipsis; background: #FD142A; color: #fff; padding: 3px; padding-top: 5px;">
				Combo Product
				</div>
			</t>
		</xpath>
	</t>

	<t t-name="SelectComboProductPopupWidget" owl="1">
		<div class="modal-dialog">
			<div class="popup popup-textinput" style="height: 550px !important;" t-on-click="renderElement">
				<p class="title" style="background-color: #dbdbdc; color: #4e4c4c;">POS Combo Products</p>
				<div style="display: grid;">
					<span class="required-products">Requrired Products</span>
					<div class="product-div">
						<t t-if="props.required_products">
							<t t-foreach="req_product" t-as="product" t-key="product.id">
								<div class='required-product' t-att-data-product-id="product.id" tabindex="0" t-attf-aria-labelledby="article_product_#{product.id}">
									<div class="product-img">
										<img t-att-src='product.product_image_url' alt="Product image"/>
									</div>
									<div class="product-name" t-attf-id="article_product_#{product.id}">
										<t t-esc="product.display_name"/>
									</div>
								</div>
							</t>
						</t>
					</div>
					<span class="optional-products">Optional Products</span>
					<div class="optional-div">
						<t t-if="props.optional_products">
							<t t-foreach="optional_product" t-as="product" t-key="product.id">
								<div class='optional-product' t-att-data-product-id="product.id" tabindex="0" t-attf-aria-labelledby="article_product_#{product.id}" t-on-click="renderElement">
									<div class="optional-product-img">
										<img t-att-src='product.product_image_url' alt="Product image"/>
										<span class="remove-product" t-att-data-product-id="product.id">
					                       <i class="fa fa-times"/>
					                    </span>
									</div>
									<div class="product-name" t-attf-id="article_product_#{product.id}">
										<t t-esc="product.display_name"/>
									</div>
								</div>
							</t>
						</t>
					</div>
				</div>
				<div class="footer" style="background-color: #dbdbdc; color: #4e4c4c;bottom:0;position:fixed;">
					<div class="button cancel" style="background-color: #ececec; color: #040404;" t-on-click="go_back_screen">
						Cancel 
					</div>
					<div class="button confirm-add" id="change_date" t-on-click="add_confirm" style="background-color: #ececec; color: #040404;" >
						Apply 
					</div>
				</div>
			</div>
		</div>
	</t>

   
	 
   	<t t-extend="Orderline" t-inherit="point_of_sale.Orderline" t-inherit-mode="extension" owl="1">
        <xpath expr="//span[hasclass('product-name')]" position="inside">
   			<t t-if="props.line.product.is_pack">
				<t t-if="props.line.get_combo_products()">
					<span class="fa fa-edit edit-combo" style="font-size: 19px;color: green; margin-left: 10px;" t-on-click="on_click"/>
				</t>
			</t>
   		</xpath>
		<xpath expr="//li[hasclass('info')]" position="after">
			<t t-if="props.line.product.is_pack">
				<t t-if="props.line.get_combo_products()">
					<ul class="info-list">
						<t t-foreach="props.line.get_combo_products()" t-as="products"  t-key="products.id">
							<t t-if="products != null">
								<span class="product-name" style="font-size: 15px;font-weight: 500;">
									<t t-esc='products.display_name' /> <t t-esc="props.line.quantity"/>
								</span>
							</t>
							
				  		</t>
					</ul>
				</t>
			</t>
	</xpath>
	</t>

	<t t-extend="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension" owl="1">
			<xpath expr="//div[hasclass('orderlines')]" position="replace">
				<div class="orderlines">
					<t t-foreach="receipt.orderlines" t-as="line" t-key="line.id">
	                    <t t-if="isSimple(line)">
	                        <div>
	                            <t t-esc="line.product_name_wrapped[0]" />
	                            <span t-esc="env.pos.format_currency_no_symbol(line.price_display)" class="price_display pos-receipt-right-align"/>

	                            <t t-if="line.is_pack">
									<t t-if="line.combo_products">
										<ul class="info-list" style="margin-left: 10px;">
											<t t-foreach="line.combo_products" t-as="products" t-key="products.id">
												<t t-if="products != null">
													<t t-esc='products.display_name' /> 1 qty<br/>
												</t>
									  		</t>
										</ul>
									</t>
								</t>
	                        </div>
	                        <WrappedProductNameLines line="line" />
	                    </t>
	                    <t t-else="">
	                        <div t-esc="line.product_name_wrapped[0]" />
	                        <t t-if="line.is_pack">
								<t t-if="line.combo_products">
									<ul class="info-list" style="margin-left: 10px;">
										<t t-foreach="line.combo_products" t-as="products" t-key="products.id">
											<t t-if="products != null">
												<t t-esc='products.display_name' /> <t t-esc="line.quantity"/> x 1 qty<br/>
											</t>
								  		</t>
									</ul>
								</t>
							</t>
	                        <WrappedProductNameLines line="line" />
	                        <t t-if="line.display_discount_policy == 'without_discount' and line.price != line.price_lst">
	                            <div class="pos-receipt-left-padding">
	                                <t t-esc="env.pos.format_currency_no_symbol(line.price_lst)" />
	                                ->
	                                <t t-esc="env.pos.format_currency_no_symbol(line.price)" />
	                            </div>
	                        </t>
	                        <t t-elif="line.discount !== 0">
	                            <div class="pos-receipt-left-padding">
	                                <t t-if="env.pos.config.iface_tax_included === 'total'">
	                                    <t t-esc="env.pos.format_currency_no_symbol(line.price_with_tax_before_discount)"/>
	                                </t>
	                                <t t-else="">
	                                    <t t-esc="env.pos.format_currency_no_symbol(line.price)"/>
	                                </t>
	                            </div>
	                        </t>
	                        <t t-if="line.discount !== 0">
	                            <div class="pos-receipt-left-padding">
	                                Discount: <t t-esc="line.discount" />%
	                            </div>
	                        </t>
	                        <div class="pos-receipt-left-padding">
	                            <t t-esc="Math.round(line.quantity * Math.pow(10, env.pos.dp['Product Unit of Measure'])) / Math.pow(10, env.pos.dp['Product Unit of Measure'])"/>
	                            <t t-if="line.unit_name !== 'Units'" t-esc="line.unit_name" />
	                            x
	                            <t t-esc="env.pos.format_currency_no_symbol(line.price_display_one)" />
	                            <span class="price_display pos-receipt-right-align">
	                                <t t-esc="env.pos.format_currency_no_symbol(line.price_display)" />
	                            </span>
	                        </div>
	                    </t>
						<t t-if="line.customer_note">
							<div class="pos-receipt-left-padding pos-receipt-customer-note">
								<t t-esc="line.customer_note"/>
							</div>
						</t>
						<t t-if="line.pack_lot_lines">
							<div class="pos-receipt-left-padding">
								<ul>
									<t t-foreach="line.pack_lot_lines" t-as="lot" t-key="lot.cid">
										<li>
											SN <t t-esc="lot.lot_name"/>
										</li>
									</t>
								</ul>
							</div>
						</t>
	                </t>
				</div>
			</xpath>
	   
		</t> 
	
	</templates>
	
	
