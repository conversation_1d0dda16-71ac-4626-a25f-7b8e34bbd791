# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* report_xlsx
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2022-04-22 13:05+0000\n"
"Last-Translator: pablontura <<EMAIL>>\n"
"Language-Team: none\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: report_xlsx
#: code:addons/report_xlsx/models/ir_report.py:0
#, python-format
msgid "%s model was not found"
msgstr "No s'ha trobat el model %s"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_abstract
msgid "Abstract XLSX Report"
msgstr "Informe XLSX abstracte"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_partner_xlsx
msgid "Partner XLSX Report"
msgstr "Informe XLSX del soci"

#. module: report_xlsx
#: model:ir.actions.report,name:report_xlsx.partner_xlsx
msgid "Print to XLSX"
msgstr "Imprimeix a XLSX"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_ir_actions_report
msgid "Report Action"
msgstr "Informe d'acció"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_ir_actions_report__report_type
msgid "Report Type"
msgstr "Tipus d'informe"

#. module: report_xlsx
#: model:ir.model.fields,help:report_xlsx.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""
"El tipus d'informe que es renderà, cadascun amb el seu propi mètode de "
"representació. HTML significa que l'informe s'obrirà directament al vostre "
"navegador, PDF significa que l'informe es representarà amb Wkhtmltopdf i "
"l'usuari el baixarà."

#. module: report_xlsx
#: model:ir.model.fields.selection,name:report_xlsx.selection__ir_actions_report__report_type__xlsx
msgid "XLSX"
msgstr "XLSX"
