from odoo import api, models, _


class PosOrder(models.Model):
    _inherit = "pos.order"

    @api.model
    def create_from_nexpos(self, order):
        try:
            data_mapping = order.get('data_mapping', None)
            if not data_mapping:
                return {
                    "status": False,
                    "message": "Not Data Mapping"
                }

            pos_order = self.env['pos.order'].search([
                ('pos_reference', '=', data_mapping.get('id'))
            ], limit=1)
            if pos_order:
                return {
                    "status": True,
                    "message": "Order Id already exists"
                }

            if not order.get('site_id', None):
                return {
                    "status": True,
                    "message": "Not Site Id"
                }

            pos_config = self.env['pos.config'].search([
                ('nexpos_site_id', '=', order.get('site').get("_id"))
            ], limit=1)

            if not pos_config:
                # Create Hub/Warehouse
                stock_warehouse = self.env['stock.warehouse'].search([
                    ('code', '=', order.get('hub').get("code")[-4:])
                ], limit=1)
                if not stock_warehouse:
                    stock_warehouse = self.env['stock.warehouse'].create({
                        'name': order.get('hub').get("name"),
                        'code': order.get('hub').get("code")[-4:],
                    })

                stock_picking_type = self.env['stock.picking.type'].search([
                    ('sequence_code', '=', 'POS'),
                    ('warehouse_id', '=', stock_warehouse.id)
                ], limit=1)
                if not stock_picking_type:
                    stock_location = self.env['stock.location'].search([
                        ('warehouse_id', '=', stock_warehouse.id)
                    ], limit=1, order='id ASC')
                    stock_picking_type = self.env['stock.picking.type'].create({
                        'name': _('PoS Orders'),
                        'code': 'outgoing',
                        'default_location_src_id': stock_location.id,
                        'default_location_dest_id': self.env.ref('stock.stock_location_customers').id,
                        'sequence': 1,
                        'sequence_code': 'POS',
                        'warehouse_id': stock_warehouse.id,
                        # 'company_id': self.company_id.id, # Not need now
                        'show_operations': False,
                    })

                discount_product = self.env['product.product'].search([
                    ('name', '=', "Discount Product"),  # Fixed now
                    ('type', '=', "service"),
                    ('available_in_pos', '=', True)
                ], limit=1)
                if not discount_product:
                    discount_product = self.env["product.product"].create(
                        {
                            "name": "Discount Product",
                            "type": "service",
                            "list_price": 0,
                            "available_in_pos": True,
                            "taxes_id": [],
                        }
                    )

                # Create POS
                pos_config = self.env['pos.config'].create({
                    'name': order.get('site').get("name"),
                    'nexpos_site_id': order.get('site').get("_id"),
                    'picking_type_id': stock_picking_type.id,
                    'module_pos_discount': True,
                    'discount_product_id': discount_product.id,
                    'discount_pc': 10,
                })

            if pos_config:
                payments = []
                new_payments = []
                if len(data_mapping.get('payments', [])) == 0:
                    return {
                        "status": False,
                        "message": "Not Payment"
                    }

                for payment in data_mapping.get('payments', []):
                    payment_method = self.env['pos.payment.method'].search([
                        ('name', '=', payment.get('method'))
                    ], limit=1)
                    if not payment_method:
                        account_journal = self.env['account.journal'].create({
                            'name': payment.get('method'),
                            'type': "cash",  # Fixed now
                            'code': payment.get('method', "")[:4]
                        })
                        payment_method = self.env['pos.payment.method'].create({
                            'name': payment.get('method'),
                            'journal_id': account_journal.id
                        })

                    if payment_method not in pos_config.payment_method_ids:
                        new_payments.append(payment_method.id)

                    payments.append([0, 0, {
                        "name": order.get('created_at').replace('T', ' ')[:19],
                        "payment_method_id": payment_method.id,
                        "amount": payment.get('total'),
                    }])

                current_session = pos_config.current_session_id
                if len(new_payments) > 0:
                    if current_session:
                        total_paid = sum(
                            current_session.mapped('order_ids.payment_ids').mapped('amount')
                        )
                        action = current_session.action_pos_session_closing_control(amount_to_balance=total_paid)
                        if isinstance(action, dict) and action.get('res_id', 0):
                            wizard = self.env['pos.close.session.wizard'].browse(action['res_id'])
                            wizard.with_context(action['context']).close_session()
                    for new_payment in new_payments:
                        pos_config.sudo().write({
                            'payment_method_ids': [(4, new_payment)],
                        })

                if not pos_config.current_session_id:
                    # Open session again
                    current_session = self.env['pos.session'].create({
                        'user_id': self.env.uid,
                        'config_id': pos_config.id
                    })
                    current_session.set_cashbox_pos(0, "")

                order_lines = []
                for item in data_mapping.get('dishes', []):
                    # Check exits product
                    product_template = self.env['product.template'].search([
                        ('name', '=', item.get('name'))
                    ], limit=1)
                    if not product_template:
                        product_brand = self.env['product.brand'].search([
                            ('name', '=', order.get('brand').get('name'))
                        ], limit=1)
                        if not product_brand:
                            product_brand = self.env['product.brand'].create({
                                'name': order.get('brand').get('name'),
                                'description': order.get('brand').get('description')
                            })

                        product_template = self.env['product.template'].create({
                            'name': item.get('name'),
                            "list_price": item.get('price'),
                            "detailed_type": "product",
                            "available_in_pos": True,
                            "product_brand_id": product_brand.id
                        })

                    price_unit = item.get('price', 0) / item.get('quantity', 0)

                    if item.get('options'):
                        for option_items in item.get('options'):
                            for option in option_items:
                                product_attribute = self.env['product.attribute'].search([
                                    ('name', '=', option.get('option_name'))
                                ], limit=1)
                                if not product_attribute:
                                    product_attribute = self.env['product.attribute'].create({
                                        'name': option.get('option_name')
                                    })

                                product_attribute_value = self.env['product.attribute.value'].search([
                                    ('name', '=', option.get('option_item')),
                                    ('attribute_id', '=', product_attribute.id)
                                ], limit=1)
                                if not product_attribute_value:
                                    product_attribute_value = self.env['product.attribute.value'].create({
                                        'name': option.get('option_item'),
                                        'attribute_id': product_attribute.id,
                                        'sequence': 1
                                    })

                                product_attribute_line = self.env['product.template.attribute.line'].search([
                                    ('product_tmpl_id', '=', product_template.id),
                                    ('attribute_id', '=', product_attribute.id)
                                ], limit=1)
                                if product_attribute_line:
                                    product_template.write({
                                        'attribute_line_ids': [(1, product_attribute_line.id, {
                                            'attribute_id': product_attribute.id,
                                            'value_ids': [(4, product_attribute_value.id)],
                                        })]
                                    })
                                else:
                                    product_attribute_line = self.env['product.template.attribute.line'].create({
                                        'product_tmpl_id': product_template.id,
                                        'attribute_id': product_attribute.id,
                                        'value_ids': [(6, 0, [product_attribute_value.id])],
                                    })

                                product_template_attribute_value = self.env['product.template.attribute.value'].search([
                                    ('attribute_line_id', '=', product_attribute_line.id),
                                    ('product_attribute_value_id', '=', product_attribute_value.id)
                                ], limit=1)
                                product_product = product_template.product_variant_ids.filtered(
                                    lambda p: p.product_template_attribute_value_ids == product_template_attribute_value
                                )

                                if product_product:
                                    order_lines.append([0, 0, {
                                        "product_id": product_product.id,
                                        "full_product_name": product_product.name + " - " + product_template_attribute_value.name,
                                        "qty": item.get('quantity', 0),
                                        "price_unit": item.get('price', 0),
                                        "price_subtotal": item.get('quantity', 0) * item.get('price', 0),
                                        "price_subtotal_incl": item.get('quantity', 0) * item.get('price', 0),
                                        "discount": 0,
                                        "note": item.get('note', '')
                                    }])
                    else:
                        product_product = product_template.product_variant_ids
                        order_lines.append([0, 0, {
                            "product_id": product_product.id,
                            "full_product_name": product_product.name,
                            "qty": item.get('quantity', 0),
                            "price_unit": price_unit,
                            "price_subtotal": item.get('price', 0),
                            "price_subtotal_incl": item.get('price', 0),
                            "discount": 0,
                            "note": item.get('note', '')
                        }])

                    # Update sale price
                    if product_template.list_price != price_unit:
                        product_template.write({
                            "list_price": price_unit
                        })

                # Add discount line
                if data_mapping.get('total_discount', 0) and pos_config.module_pos_discount:
                    order_lines.append([0, 0, {
                        "product_id": pos_config.discount_product_id.id,
                        "full_product_name": pos_config.discount_product_id.name,
                        "qty": 1,
                        "price_unit": -data_mapping.get('total_discount'),
                        "price_subtotal": -data_mapping.get('total_discount'),
                        "price_subtotal_incl": -data_mapping.get('total_discount'),
                        "discount": 0,
                        "note": ""
                    }])

                # Add shipment line
                if data_mapping.get('total_shipment', 0):
                    shipment_product = self.env['product.product'].search([
                        ('name', '=', "Shipment Product"),  # Fixed now
                        ('type', '=', "service"),
                        ('available_in_pos', '=', True)
                    ], limit=1)
                    if not shipment_product:
                        shipment_product = self.env["product.product"].create(
                            {
                                "name": "Shipment Product",
                                "type": "service",
                                "list_price": 0,
                                "available_in_pos": True,
                                "taxes_id": [],
                            }
                        )

                    order_lines.append([0, 0, {
                        "product_id": shipment_product.id,
                        "full_product_name": shipment_product.name,
                        "qty": 1,
                        "price_unit": data_mapping.get('total_shipment'),
                        "price_subtotal": data_mapping.get('total_shipment'),
                        "price_subtotal_incl": data_mapping.get('total_shipment'),
                        "discount": 0,
                        "note": ""
                    }])

                # Add commission line
                if data_mapping.get('commission', 0):
                    commission_product = self.env['product.product'].search([
                        ('name', '=', "Commission Product"),  # Fixed now
                        ('type', '=', "service"),
                        ('available_in_pos', '=', True)
                    ], limit=1)
                    if not commission_product:
                        commission_product = self.env["product.product"].create(
                            {
                                "name": "Commission Product",
                                "type": "service",
                                "list_price": 0,
                                "available_in_pos": True,
                                "taxes_id": [],
                            }
                        )

                    order_lines.append([0, 0, {
                        "product_id": commission_product.id,
                        "full_product_name": commission_product.name,
                        "qty": 1,
                        "price_unit": -data_mapping.get('commission'),
                        "price_subtotal": -data_mapping.get('commission'),
                        "price_subtotal_incl": -data_mapping.get('commission'),
                        "discount": 0,
                        "note": ""
                    }])

                partner = self.env['res.partner'].search([
                    ('name', '=', data_mapping.get('customer_name')),
                    ('phone', '=', data_mapping.get('customer_phone')),
                ], limit=1)
                if not partner:
                    partner = self.env['res.partner'].create({
                        "name": data_mapping.get('customer_name'),
                        "phone": data_mapping.get('customer_phone')
                    })


                order_data = {
                    "data": {
                        "name": data_mapping.get('id'),
                        "amount_paid": data_mapping.get('total_for_biz', 0),
                        "amount_total": data_mapping.get('total', 0) - data_mapping.get('total_discount', 0) + data_mapping.get('total_shipment', 0) - data_mapping.get('commission', 0),
                        "lines": order_lines,
                        "statement_ids": payments,
                        "pos_session_id": current_session.id,
                        "partner_id": partner.id,
                        "user_id": self.env.uid,
                        "amount_tax": 0,
                        "amount_return": 0,
                        "sequence_number": 1,
                        "pricelist_id": 1,
                        "fiscal_position_id": False,
                        "note": data_mapping.get("note", ""),
                        "creation_date": data_mapping.get('delivery_time')
                    },
                    "to_invoice": False
                }

                res = self.create_from_ui([order_data])

                if len(res) > 0:
                    return {
                        "status": True,
                        "message": "Sync OK",
                        "data": {
                            "order_id": res[0]['id']
                        }
                    }
                else:
                    return {
                        "status": False,
                        "message": "Sync Fail"
                    }

        except Exception as ex:
            return {
                "status": False,
                "message": str(ex)
            }
