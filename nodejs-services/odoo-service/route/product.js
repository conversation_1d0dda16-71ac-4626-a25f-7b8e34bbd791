const { SiteMenuGroup } = require('../../.shared/database');
const { loginService } = require('../services/LoginService');
const { callKwService } = require('../services/CallKwService');

const sync_all_products = async () => {
    const cookie = await loginService();
    if (!cookie) {
        console.error('login fail')
        return null
    }

    const products = await SiteMenuGroup.find().lean();

    const product_args = [];
    for (const product of products) {
        product.categories.forEach(c => {
            c.items.forEach(i => {
                product_args.push({
                    name: i.name,
                    list_price: i.price,
                    available_in_pos: true
                });

            })
        })
    }
    const params = {
        model: "product.product",
        method: "create",
        args: [product_args],
        kwargs: {
            context: {}
        }
    }
    const resp = await callKwService(cookie, params);
    if (resp) {
        console.log("Sync successfully!");
    } else {
        console.error("Sync error!");
    }
}

module.exports = { sync_all_products }
