const { Order, CustomerProfile, Site, Brand } = require('../../.shared/database')
const _ = require('lodash')
const redis = require('../../.shared/redis')

const LIMIT = 30
// const brand_ids = [
//   '64d7c520513d86579aa7713b',
//   '651d261539a81c8274ac503d',
//   '65640ddd461989b7f202c07c',
//   '656db0f1f37d827a133b963c',
//   '65a764a831822fab3e5c30ff',
//   '65a8b2564f6dc44181856751',
//   '642d61a744b2361680a5837a', // o o o
// ]

const format_date = (date) => {
  const d = new Date(date * 1000)
  return !isNaN(d.getTime()) ? d : new Date()
}

const get_site_ids = async () => {
  const key = 'cdp_site_ids'
  const cached = await redis.getObj(key)
  if (cached) {
    return cached
  }

  const brand_ids = await Brand.find({ use_cdp: true }, { _id: 1 }).then((b) => b.map((b) => String(b._id)))
  console.log(brand_ids)

  const sites = await Site.find({ brand_id: { $in: brand_ids } }, { _id: 1, brand_id: 1 })
  const site_map = sites.reduce((acc, s) => {
    acc[s._id] = s
    return acc
  }, {})

  await redis.setObj(key, site_map, 60 * 60)
  return site_map
}

const sync_customer_profiles = async () => {
  const site_map = await get_site_ids()
  const sites = Object.values(site_map).map((s) => s._id)
  try {
    const orders = await Order.find(
      {
        status: 'FINISH',
        profile_sync_at: null,
        'data_mapping.customer_phone': { $exists: true, $ne: '' },
        site_id: { $in: sites },
      },
      {
        'data_mapping.raw': 0,
      }
    )
      .sort({ profile_sync_at: 1, created_at: 1 })
      .limit(LIMIT)

    // If no orders found, wait and exit
    if (orders.length === 0) await new Promise((r) => setTimeout(r, 60 * 1000))
    
    for (const o of orders) {
      const brand_id = site_map[o.site_id].brand_id
      if (!brand_id) return

      const profile = await CustomerProfile.findOne({ phone: o.data_mapping.customer_phone, 'brands.brand_id': brand_id })

      if (profile) {
        await CustomerProfile.updateOne(
          { phone: o.data_mapping.customer_phone, 'brands.brand_id': brand_id },
          {
            $addToSet: {
              names: o.data_mapping.customer_name,
              addresses: o.data_mapping.customer_address,
            },
            $inc: {
              total_orders: 1,
              total_order_amount: o.data_mapping.total,
              'brands.$.total_orders': 1,
              'brands.$.total_order_amount': o.data_mapping.total,
            },
            $set: {
              'brands.$.last_order_at': format_date(o.data_mapping.delivery_time_unix),
              // segment_synced: false,
            },
          }
        )
      } else {
        await CustomerProfile.findOneAndUpdate(
          { phone: o.data_mapping.customer_phone },
          {
            $addToSet: {
              names: o.data_mapping.customer_name,
              addresses: o.data_mapping.customer_address,
            },
            $push: {
              brands: {
                brand_id: brand_id,
                total_orders: 1,
                total_order_amount: o.data_mapping.total,
                last_order_at: format_date(o.data_mapping.delivery_time_unix),
              },
            },
            $inc: {
              total_orders: 1,
              total_order_amount: o.data_mapping.total,
            },
            // $set: {
            //   segment_synced: false,
            // },
          },
          { upsert: true }
        )
      }

      await Order.updateOne({ _id: o._id }, { profile_sync_at: new Date() })
    }

    console.log(`Synced ${orders.length} orders`)
  } catch (error) {
    console.log(error)
  }
}

exports.sync_customer_profiles = sync_customer_profiles
