const axios = require('axios')
const moment = require('moment-timezone')
const _ = require('lodash')
moment.tz.setDefault('Asia/Bangkok')

const { Order, Site } = require('../../.shared/database')
const MAX_SYNC = 1

const _textToSlug = (text) => slugify(text.toLowerCase(), { replacement: '_', locale: 'vi', trim: true, strict: true })

// Function to synchronize all orders to TraCardi
const sync_all_orders = async () => {
    try {
        // Step 2: Get orders to be synced
        const orders = await Order.find({
            status: 'FINISH',
            tracardi_sync_at: null,
            'data_mapping.customer_phone': { $exists: true, $ne: '' },
            'data_mapping.delivery_time_unix': { $gte: moment('2023-03-31T17:00:00Z').unix() },
        })
            .sort({ tracardi_sync_at: 1, created_at: 1 })
            .limit(MAX_SYNC)

        // If no orders found, wait and exit
        if (orders.length === 0) await new Promise((r) => setTimeout(r, 5000))

        // Step 3: Sync each order to TraCardi
        await Promise.all(
            orders.map(async (order) => {
                const order_json = order.toJSON()

                console.log('Sync order:', order_json.order_id)
                // Retrieve site information
                const site = await Site.findById(order_json.site_id, { tokens: 0 }).lean()
                if (!site) {
                    order.tracardi_sync_at = new Date()
                    await order.save()
                    return
                }

                // TODO: Move to config json when has multi cluster, hard code for now
                const tracardi_conf = process.env.NODE_ENV === 'prod' ? {
                    url: "http://66.29.131.103:8686",
                    event_source_id: "77d70638-6e6c-4e07-9e8b-7cf152d27569",
                    headers: {
                        'x-context': 'production'
                    }
                } : {
                    url: "http://66.29.131.103:8686",
                    event_source_id: "77d70638-6e6c-4e07-9e8b-7cf152d27569",
                    headers: {}
                }

                try {
                    delete order_json.data_mapping.raw
                    delete order_json.data
                    const resp = await axios({
                        method: 'post',
                        maxBodyLength: Infinity,
                        url: `${tracardi_conf.url}/track`,
                        headers: {
                            'Content-Type': 'application/json',
                            ...tracardi_conf.headers
                        },
                        data: {
                            context: {},
                            source: {
                                id: tracardi_conf.event_source_id,
                                name: 'Nexpos Order (rest)',
                            },
                            events: [
                                {
                                    type: 'nexpos_completed_order',
                                    properties: order_json,
                                },
                            ],
                        },
                    })

                    order.tracardi_sync_at = new Date()
                    await order.save()

                    console.log('Sync Status:', resp.status)
                } catch (error) {
                    console.log(error)
                }
            })
        )
    } catch (error) {
        console.log(error)
    }
}

module.exports = { sync_all_orders }
