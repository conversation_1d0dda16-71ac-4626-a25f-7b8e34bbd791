const moment = require('moment-timezone');
const _ = require('lodash');
moment.tz.setDefault('Asia/Bangkok');

const { Order, Site, Brand, Hub, BrandMenu } = require('../../.shared/database');
const { loginService } = require('../services/LoginService');
const { callKwService } = require('../services/CallKwService');
const { _textToSlug } = require('../../.shared/merchant/mapping');


const MAX_SYNC = 1;



// Function to synchronize all orders to Odoo
const sync_all_orders = async () => {
    try {
        // Step 1: Login
        const cookie = await loginService();
        if (!cookie) {
            console.error('Login fail');
            return null;
        }

        // Step 2: Get orders to be synced
        const orders = await Order.find({
            status: 'FINISH',
            odoo_sync_at: null,
            "data_mapping.delivery_time_unix": { $gte: moment('2023-05-31T17:00:00Z').unix() },
        }).sort({ odoo_sync_retry: 1, created_at: 1 }).limit(MAX_SYNC);

        // If no orders found, wait and exit
        if (orders.length === 0)
            await new Promise(r => setTimeout(r, 5000));

        // Step 3: Sync each order to Odoo
        await Promise.all(orders.map(async order => {
            const order_json = order.toJSON();

            // Handle non-local source orders
            if (order_json.source !== 'local') {
                order_json.data_mapping.payments = [{ method: order_json.source, total: order_json.data_mapping.total_for_biz }];
            } else {
                if (!order_json.data_mapping.payments)
                    order_json.data_mapping.payments = []
                const paid = _.sumBy(order_json.data_mapping.payments, 'total') || 0
                if (paid !== order_json.data_mapping.total_for_biz) {
                    order_json.data_mapping.payments = [{ method: "CASH", total: order_json.data_mapping.total_for_biz }];
                }
            }
            console.log("Sync order:", order_json.order_id)
            // Retrieve site information
            const site = await Site.findById(order_json.site_id, { tokens: 0 }).lean();
            if (!site) {
                order.odoo_sync_at = new Date();
                order.odoo_sync_callback = {
                    message: "site not found"
                };
                await order.save();
                return
            }

            // Remap menu and quantity
            const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean();
            const menu_items = [...brand_menu?.categories.flatMap(v => v.items), ...brand_menu?.categories.flatMap(c => c.sub_categories).flatMap(v => v.items)];

            const get_option_item = (category_name, option_name) => {
                const category = brand_menu?.option_categories.find(v => _textToSlug(v.name) === _textToSlug(category_name));
                if (!category) return null
                const option = category?.options?.find(v => _textToSlug(v.name) === _textToSlug(option_name));
                return option || null;
            }
            // Prepare the new_dishes array to store remapped dishes and expanded options
            const new_dishes = [];

            // Loop through each dish in the order to remap and expand options
            for (let i = 0; i < order_json.data_mapping.dishes.length; i++) {
                const dish = order_json.data_mapping.dishes[i];

                // Check if the current dish exists in the menu and has a positive reference quantity
                const items_in_brand_menu = menu_items?.filter(v => _textToSlug(v.name) === _textToSlug(dish.name) && v.combo?.length > 0);

                if (dish.quantity > 0) {
                    let has_combo_in_dish = dish.combo?.length > 0;
                    if (has_combo_in_dish) {
                        for (const item of dish.combo || []) {
                            new_dishes.push({
                                name: item.name,
                                quantity: dish.quantity * Number(item?.quantity),
                                price: dish.quantity * Number(item?.price),
                                options: [],
                                note: ""
                            });
                        }
                    } else if (items_in_brand_menu?.length > 0) {
                        for (const item of items_in_brand_menu[0].combo || []) {
                            new_dishes.push({
                                name: item.name,
                                quantity: dish.quantity,
                                price: dish.quantity * Number(item?.price),
                                options: [],
                                note: ""
                            });
                        }
                    } else {
                        new_dishes.push(dish);
                    }

                    // Extract and clear the options for the current dish
                    const options = dish.options;
                    order_json.data_mapping.dishes[i].options = [];
                    // Expand and add individual option items to the new_dishes array
                    options.map(v => v.map(m => {
                        const option_item_in_brand_menu = get_option_item(m.option_name, m.option_item);
                        if (option_item_in_brand_menu) {
                            for (const item of option_item_in_brand_menu.combo || []) {
                                new_dishes.push({
                                    name: item.name,
                                    quantity: dish.quantity,
                                    price: dish.quantity * Number(item?.price),
                                    options: [],
                                    note: ""
                                });
                            }
                        } else {
                            new_dishes.push({
                                name: m.name,
                                quantity: m.quantity,
                                price: 0,
                                options: [],
                                note: ""
                            });
                        }

                    }));
                }
            }

            order_json.data_mapping.dishes = new_dishes;

            // Add site, brand, and hub information to order_json
            if (site) {
                order_json.site = site;
                order_json.brand = await Brand.findById(site.brand_id);
                order_json.hub = await Hub.findById(site.hub_id);
            }

            console.log("Start sync order:", order_json.order_id);

            // Prepare parameters for Odoo API call
            const params = {
                model: "pos.order",
                method: "create_from_nexpos",
                args: [order_json],
                kwargs: {
                    context: {}
                }
            };

            try {
                // Call Odoo API to sync order
                const resp = await callKwService(cookie, params);

                if (resp?.result?.status === true) {
                    order.odoo_sync_at = new Date();
                } else {
                    order.odoo_sync_retry++;
                }
                order.odoo_sync_callback = resp;
                await order.save();

                console.log("Sync Status:", resp);
            } catch (error) {
                console.log(error);
            }
        }));
    } catch (error) {
        console.log(error);
    }
}

module.exports = { sync_all_orders };


