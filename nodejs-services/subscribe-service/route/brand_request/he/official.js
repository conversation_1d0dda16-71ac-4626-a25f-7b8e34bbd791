const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok');
const { Site, Brand, BrandMenu, GoogleSheetFile } = require('../../../../.shared/database')
const { base_headers, base_body } = require('../../../../.shared/merchant/be');
const { get_token_by_site, get_token_by_code } = require('../../../../.shared/token_account');
const { text_slugify, text_compare, retry_operation, compare_arrays, get_file_extension_from_url } = require('../../../../.shared/helper');
const { get_brand_menu_from_google_sheet } = require('../../../../.shared/googlesheet/template');

const sync_brand_menu = async (brand_id) => {
    const brand_menu = await get_brand_menu_from_google_sheet(brand_id)
    await BrandMenu.findOneAndUpdate({ brand_id }, brand_menu, { upsert: true })
    const sites = await Site.find({ brand_id })
    await SiteMenu.updateMany({ site_id: sites.map(v => String(v._id)) }, brand_menu, { upsert: true })
}

const sync_brand_promotion = async (brand_id) => {
    const brand = await Brand.findById(brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    // TODO:
}


module.exports = {
    sync_brand_menu,
    sync_brand_promotion,
}