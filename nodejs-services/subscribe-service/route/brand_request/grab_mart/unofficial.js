const moment = require('moment-timezone')
const _ = require('lodash');
const axios = require('axios');
const sharp = require('sharp');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport, GoogleSheetFile } = require('../../../../.shared/database')
const { base_headers } = require('../../../../.shared/merchant/grab');
const { get_token_by_site } = require('../../../../.shared/token_account');
const { text_slugify, text_compare, compare_arrays, retry_operation, get_file_extension_from_url } = require('../../../../.shared/helper');
const { GRAB_CATEGORIES } = require('../../../../.shared/merchant/grab_mart_official');

const sync_site_menu = async (code) => {
    const site = await Site.findOne({ code });
    const brand = await Brand.findById(site.brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const token = await get_token_by_site(site, 'grab_mart')
    const updated_items = sheet_data.MD_menu.filter(v => v.brand === brand.name).map(v => ({
        category_name: v.category,
        grab_category: v.grab_category,
        grab_sub_category: v.grab_sub_category,
        name: v.name,
        description: v.description,
        images: [v.image, v.image_1, v.image_2].filter(v => v),
        price: Number(v.price),
    }))
    await sync_menu_items(code, { updated_items })
}

const sync_menu_items = async function (code, { updated_items }) {
    const site = await Site.findOne({ code });
    const { site_id, access_token } = await get_token_by_site(site, 'grab_mart')

    if (!access_token) {
        return {};
    }

    try {
        await axios.post(`https://api.grab.com/food/merchant/passcode/skip`, {}, { headers: base_headers({ site_id, access_token }) }).catch(err => console.log(err));
        await delete_site_promotions(site.code)

        const group_menu_resp = await axios.get(`https://api.grab.com/food/merchant/v2/mart-menu`, { headers: base_headers({ site_id, access_token }) });
        const grab_categories = group_menu_resp.data?.departments || [];

        const grab_items = []
        for (const grab_category of grab_categories) {
            for (const grab_sub_category of grab_category.subDepartments) {
                for (const item of grab_sub_category.items) {
                    grab_items.push({
                        id: item.itemID,
                        name: item.itemName,
                        description: item.description,
                        images: item.imageURLs,
                        price: item.priceInMin,
                        grab_category: grab_category.itemClassName,
                        grab_sub_category: grab_sub_category.itemClassName,
                    })
                }
            }
        }

        const { onlyInLeft: itemsToCreate, onlyInRight: itemsToDelete, inBoth: itemsToUpdate } =
            compare_arrays(updated_items, grab_items, (a, b) => text_compare(a.name, b.name) && text_compare(a.grab_sub_category, b.grab_sub_category));

        // Create new items
        await Promise.all(_.chunk(itemsToCreate, 10).map(async (chunk) => {
            for (const item of chunk) {
                await create_item(item, { site_id, access_token });
            }
        }));

        // Update existing items
        await Promise.all(_.chunk(itemsToUpdate, 10).map(async (chunk) => {
            for (const item of chunk) {
                const grabItem = grab_items.find(gi => text_compare(gi.name, item.name) && text_compare(gi.grab_sub_category, item.grab_sub_category));
                await update_item({ ...item, id: grabItem.id }, { site_id, access_token });
            }
        }));

        // Delete items
        for (const item of itemsToDelete) {
            await delete_item(item, { site_id, access_token });
        }

        return {
            created: itemsToCreate.length,
            updated: itemsToUpdate.length,
            deleted: itemsToDelete.length
        };

    } catch (err) {
        console.log(err);
        return null;
    }
};

const get_grab_category_id = (category_name) => {
    return GRAB_CATEGORIES.find(v => text_compare(v.name, category_name)).id
}

const get_grab_sub_category_id = (category_name, sub_category_name) => {
    return GRAB_CATEGORIES.find(v => text_compare(v.name, category_name)).subCategories?.find(v => text_compare(v.name, sub_category_name))?.id
}


const create_item = async (item, { site_id, access_token }) => {
    try {
        const uploadedImages = await uploadImage({ site_id, access_token }, item.images);
        const category_id = get_grab_category_id(item.grab_category)
        const sub_category_id = get_grab_sub_category_id(item.grab_category, item.grab_sub_category)
        const resp = await axios.post(`https://api.grab.com/food/merchant/v2/items`, {
            "item": {
                "soldByWeight": false,
                "specialItemType": "",
                "itemCode": "",
                "description": item.description,
                "aiGeneratedFields": [],
                "itemClassID": sub_category_id,
                "itemName": item.name,
                "priceInMin": item.price,
                "imageURLs": uploadedImages,
                "sellingTimeID": "AlwaysAvailable",
                "prediction": {
                    "isAlcohol": false
                },
                "skuID": ""
            },
            "categoryID": category_id
        }, { headers: base_headers({ site_id, access_token }) });

        await axios.post(`https://api.grab.com/food/merchant/v2/items/${resp.data.itemID}/proof-item`, { "proofImageURL": "" }, { headers: base_headers({ site_id, access_token }) });
        console.log('created', item.name);
        return resp.data.itemID;
    } catch (error) {
        console.error('Error creating item:', error);
    }
}

const update_item = async (item, { site_id, access_token }) => {
    try {
        const uploadedImages = await uploadImage({ site_id, access_token }, item.images);

        const resp = await axios.put(`https://api.grab.com/food/merchant/v2/items/${item.id}`, {
            "itemID": item.id,
            "item": {
                "soldByWeight": false,
                "specialItemType": "",
                "itemCode": "",
                "description": item.description,
                "aiGeneratedFields": [],
                "itemClassID": get_grab_sub_category_id(item.grab_category, item.grab_sub_category),
                "itemName": item.name,
                "availableStatus": 1,
                "priceInMin": item.price,
                "imageURLs": uploadedImages,
                "sellingTimeID": "AlwaysAvailable",
                "prediction": {
                    "isAlcohol": false
                },
                "skuID": ""
            },
            "categoryID": get_grab_category_id(item.grab_category)
        }, { headers: base_headers({ site_id, access_token }) });

        // await axios.post(`https://api.grab.com/food/merchant/v2/items/${resp.data.itemID}/proof-item`, { "proofImageURL": "" }, { headers: base_headers({ site_id, access_token }) });
        console.log('updated', item.name);
        return resp.data.itemID;
    } catch (error) {
        console.error('Error updating item:', error);
    }
}

const delete_item = async (item, { site_id, access_token }) => {
    try {
        await axios.delete(`https://api.grab.com/food/merchant/v2/items/${item.id}`, { headers: base_headers({ site_id, access_token }) });
        console.log('deleted', item.name);
        return true;
    } catch (error) {
        console.error('Error deleting item:', error);
    }
}
const uploadImage = async function ({ site_id, access_token }, image_urls) {
    const result = [];

    for (const image_url of image_urls) {
        try {
            const imageBuffer = await retry_operation(async () => {
                const response = await axios.get(image_url, { responseType: 'arraybuffer' });
                return Buffer.from(response.data, 'binary');
            }, 2, 'Error downloading the image');

            const newImageBuff = await sharp(imageBuffer).resize(500, 500).jpeg().toBuffer();
            const base64String = newImageBuff.toString('base64');
            const resp_upload = await retry_operation(async () => {
                return await axios({
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://api.grab.com/food/merchant/v2/upload-file`,
                    headers: base_headers({ site_id, access_token }),
                    data: { "file": { "data": base64String, "type": "jpeg" }, "category": "menu_item_img" }
                });
            }, 2, 'Error uploading the image'
            );

            result.push(resp_upload.data.url);
        } catch (error) {
            console.log(image_url);
            console.error('Failed to process image after all retry attempts:', error.message);
        }
    }
    return result;
};


const delete_site_promotions = async function (code) {
    const site = await Site.findOne({ code });
    const { site_id, access_token } = await get_token_by_site(site, 'grab_mart')

    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // Fetch existing campaigns
        const campaigns_resp = await axios.get(`https://api.grab.com/food/merchant/v2/campaigns`, {
            headers: base_headers({ site_id, access_token })
        });
        const campaigns = _.concat(campaigns_resp.data?.campaigns.ongoing || [], campaigns_resp.data?.campaigns.upcoming || []);

        // Delete existing campaigns
        for (const campaign of campaigns) {
            await axios.delete(`https://api.grab.com/food/merchant/v2/campaigns/${campaign.campaignID}`, {
                headers: base_headers({ site_id, access_token }),
            });
        }
    } catch (error) { }
}

const sync_site_promotion = async (code) => {
    const site = await Site.findOne({ code });
    const brand = await Brand.findById(site.brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const token = await get_token_by_site(site, 'grab_mart')
    const min_start_time = moment().add(6, 'minutes')
    const updated_items = sheet_data.MD_promotion.filter(v => v.brand === brand.name && v.grab_mart === 'TRUE').map(v => ({
        name: v.name,
        price: Number(v.price),
        sell_price: Number(v.sell_price),
        start_time: moment(v.start_time, 'YYYY/MM/DD').isBefore(min_start_time) ? min_start_time.toISOString() : moment(v.start_time, 'YYYY/MM/DD').toISOString(),
        end_time: moment(v.end_time, 'YYYY/MM/DD').toISOString(),
    }))
    await sync_promotion_items(token, { updated_items })
}


const sync_promotion_items = async function ({ site_id, access_token }, { updated_items }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {

        const group_menu_resp = await axios.get(`https://api.grab.com/food/merchant/v2/mart-menu`, { headers: base_headers({ site_id, access_token }) });
        const grab_categories = group_menu_resp.data?.departments || [];

        const grab_items = []
        for (const grab_category of grab_categories) {
            for (const grab_sub_category of grab_category.subDepartments) {
                for (const item of grab_sub_category.items) {
                    grab_items.push({
                        id: item.itemID,
                        name: item.itemName,
                        description: item.description,
                        images: item.imageURLs,
                        price: item.priceInMin,
                        grab_category: grab_category.itemClassName,
                        grab_sub_category: grab_sub_category.itemClassName,
                    })
                }
            }
        }


        // Create new campaigns for each updated item
        for (const item of updated_items) {
            const itemId = grab_items.find(v => text_compare(v.name, item.name))?.id
            if (!itemId) {
                console.warn(`Item "${item.name}" not found in the Grab menu. Skipping campaign creation.`);
                continue;
            }

            await axios.post(`https://api.grab.com/food/merchant/v2/campaigns`, {
                preCheck: false,
                config: {
                    campaignObjective: "DRIVE_SALES_ALL_CUSTOMER",
                    condition: {},
                    countryCode: "VN",
                    scope: {
                        type: "items",
                        items: [
                            {
                                itemID: itemId,
                                itemName: item.name
                            }
                        ],
                        objects: [
                            {
                                objectID: itemId,
                                objectName: item.name
                            }
                        ],
                    },
                    discount: {
                        type: "net",
                        discountValue: item.price - item.sell_price
                    },
                    startTime: item.start_time,
                    endTime: item.end_time,
                    cityID: 9,
                    countryID: 5
                }
            }, {
                headers: base_headers({ site_id, access_token }),
            });
            console.log(`Created campaign for item "${item.name}"`);
        }

        console.log(`Successfully synced ${updated_items.length} menu promotions`);
        return { success: true, itemCount: updated_items.length };
    } catch (error) {
        console.error('Error syncing menu items:', error);
        return null;
    }
};


module.exports = {
    delete_site_promotions,
    sync_site_menu,
    sync_site_promotion,
}