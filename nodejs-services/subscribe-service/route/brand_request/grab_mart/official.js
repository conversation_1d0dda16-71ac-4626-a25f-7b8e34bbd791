const moment = require('moment-timezone');
const _ = require('lodash');
const axios = require('axios');
moment.tz.setDefault('Asia/Bangkok');
const { Site, Brand, GoogleSheetFile, TokenAccount } = require('../../../../.shared/database');
const { get_token_by_site } = require('../../../../.shared/token_account');
const { get_brand_menu_from_google_sheet, get_site_promotion_from_google_sheet } = require('../../../../.shared/googlesheet/template');
const { name_to_id } = require('../../../../.shared/helper');

const get_store = async (code) => {
    try {
        const site = await Site.findOne({ code });
        const token = await get_token_by_site(site, 'grab_mart');
        if (!token.site_id) {
            return null
        }

        const grab_mart_official_token = await TokenAccount.findOne({ token_code: 'NEXDOR_grab_mart_official' }).lean();
        const resp = await axios.get(`https://partner-api.grab.com/grabmart/partner/v1/merchants/${token.site_id}/store/status`, {
            headers: {
                Authorization: "Bearer " + grab_mart_official_token.access_token,
                'Content-Type': 'application/json'
            }
        })
        return resp.data
    } catch (error) {
        console.log(error.message)
        console.log(error.response?.data)
    }
    return null
}

const sync_site_menu = async (code) => {
    const site = await Site.findOne({ code });

    try {
        const token = await get_token_by_site(site, 'grab_mart');
        if (!token.site_id) {
            return;
        }

        const grab_mart_official_token = await TokenAccount.findOne({ token_code: 'NEXDOR_grab_mart_official' }).lean();

        const resp = await axios.post(`https://partner-api.grab.com/grabmart/partner/v1/merchant/menu/notification`, {
            "merchantID": token.site_id
        }, {
            headers: {
                Authorization: "Bearer " + grab_mart_official_token.access_token,
                'Content-Type': 'application/json'
            }
        });
        console.log(resp.data);
    } catch (error) {
        console.log(error?.response?.data?.message);
    }
};

const sync_site_promotion = async (code) => {
    try {
        const site = await Site.findOne({ code: code });
        const site_token = await get_token_by_site(site, 'grab_mart');
        if (!site_token.site_id) {
            return;
        }
        const brand = await Brand.findById(site.brand_id);

        const menu_mapping = await get_brand_menu_from_google_sheet(brand._id);
        const promotions = await get_site_promotion_from_google_sheet(site._id, 'grab_mart');

        const all_menu_items = menu_mapping.categories.flatMap(s => s.items);

        const master_official_access_token = await TokenAccount.findOne({ token_code: 'NEXDOR_grab_mart_official' }).lean();

        // Delete existing campaigns
        await delete_site_promotions(code);

        // Create new campaigns
        for (const promotion of promotions) {
            await create_campaign(promotion, all_menu_items, site_token.site_id, master_official_access_token.access_token);
        }
    } catch (error) {
        console.log(error.message)
        console.log(JSON.stringify(error?.response?.data || {}, null, 2));
    }
};

const delete_site_promotions = async (code) => {
    const site = await Site.findOne({ code });
    const site_token = await get_token_by_site(site, 'grab_mart');
    if (!site_token.site_id) {
        return;
    }

    const master_official_access_token = await TokenAccount.findOne({ token_code: 'NEXDOR_grab_mart_official' }).lean();
    const access_token = master_official_access_token.access_token;
    const merchant = site_token.site_id;

    console.log(`Delete all campaigns of merchant: ${merchant}`);
    try {
        const config = {
            method: 'get',
            url: `https://partner-api.grab.com/grabmart/partner/v1/campaigns`,
            headers: {
                Authorization: `Bearer ${access_token}`,
                'Content-Type': 'application/json'
            },
            data: { merchantID: merchant }
        };
        const response = await axios(config);
        const campaigns = [...(response.data.ongoing || []), ...(response.data.upcoming || [])];

        for (const campaign of campaigns) {
            await axios.delete(`https://partner-api.grab.com/grabmart/partner/v1/campaigns/${campaign.id}`, {
                headers: { Authorization: `Bearer ${access_token}` }
            });
        }
    } catch (error) {
        console.log(error.message)
        console.log(JSON.stringify(error?.response?.data || {}, null, 2));
    }
};

const create_campaign = async (promotion, all_menu_items, merchant, access_token) => {
    if (_.toNumber(promotion.price) <= _.toNumber(promotion.sell_price)) {
        return;
    }

    let start_time = moment(promotion.start_time, 'YYYY/MM/DD').startOf('day');
    if (start_time.isBefore(moment().add(6, 'minutes'))) {
        start_time = moment().add(6, 'minutes');
    }

    const menu_item = all_menu_items.find(v => v.name === promotion.name);
    if (!menu_item) {
        return;
    }

    const formatted = {
        merchantID: merchant,
        name: promotion.name,
        quotas: null,
        conditions: {
            startTime: start_time.toISOString(),
            endTime: moment(promotion.end_time, 'YYYY/MM/DD').endOf('day').toISOString(),
            eaterType: "all",
            minBasketAmount: 0,
            bundleQuantity: 0,
            workingHour: {
                "sun": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "mon": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "tue": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "wed": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "thu": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "fri": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] },
                "sat": { "periods": [{ "startTime": "00:00", "endTime": "23:59" }] }
            },
        },
        discount: {
            type: "net",
            cap: 0,
            value: _.toNumber(promotion.price) - _.toNumber(promotion.sell_price),
            scope: {
                type: "items",
                objectIDs: [menu_item.id]
            }
        },
        customTag: ""
    };

    // Set quotas limit
    if (Number(promotion.total_count ?? 0) > 0) {
        if (formatted.quotas === null) {
            formatted.quotas = {};
        }
        formatted.quotas.totalCount = Number(promotion.total_count);
    }
    if (Number(promotion.total_count_per_user ?? 0) > 0) {
        if (formatted.quotas === null) {
            formatted.quotas = {};
        }
        formatted.quotas.totalCountPerUser = Number(promotion.total_count_per_user);
    }

    try {
        const response = await axios.post(`https://partner-api.grab.com/grabmart/partner/v1/campaigns`, formatted, {
            headers: {
                Authorization: `Bearer ${access_token}`,
                'Content-Type': 'application/json'
            }
        });
        console.log(response.data);
    } catch (error) {
        console.log(error.message)
        console.log(JSON.stringify(error?.response?.data || {}, null, 2));
    }
};

module.exports = {
    get_store,
    sync_site_menu,
    delete_site_promotions,
    sync_site_promotion,
};