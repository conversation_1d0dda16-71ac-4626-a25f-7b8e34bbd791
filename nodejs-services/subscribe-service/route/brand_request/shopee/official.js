
const { Brand, Site } = require('../../../../.shared/database');
const shopee_official = require('../../../../.shared/merchant/shopee_official');
const { get_token_by_site } = require('../../../../.shared/token_account')
const { get_brand_menu_from_google_sheet, get_site_promotion_from_google_sheet } = require("../../../../.shared/googlesheet/template");

const get_store = async (code) => {
    try {
        const store = await shopee_official.get_store({ nexpos_site_id: code })
        return store
    } catch (error) {
        console.log(error.message)
    }
    return null
}

const sync_site_menu = async (code) => {
    await shopee_official.sync_menu({ nexpos_site_id: code })
}

const sync_site_promotion = async (code) => {
    try {
        const site = await Site.findOne({ code: code });

        const brand = await Brand.findById(site.brand_id);

        const menu_mapping = await get_brand_menu_from_google_sheet(brand._id);
        const promotions = await get_site_promotion_from_google_sheet(site._id, 'shopee_fresh');

        const all_menu_items = menu_mapping.categories.flatMap(s => s.items);

        // Create new campaigns
        await shopee_official.sync_promotion({ nexpos_site_id: code }, all_menu_items, promotions)
    } catch (error) {
        console.log(error);
    }
};
// sync_site_promotion('NNDD_SA003')


module.exports = {
    get_store,
    sync_site_menu,
    sync_site_promotion,
}