const moment = require('moment-timezone')
const _ = require('lodash');
const axios = require('axios');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Brand, Hub } = require('../../../../.shared/database')
const { get_token_by_site } = require('../../../../.shared/token_account');
const shopee = require('../../../../.shared/merchant/shopee');
const { get_site_promotion_from_google_sheet } = require('../../../../.shared/googlesheet/template');

const sync_site_promotion = async (code) => {
    const site = await Site.findOne({ code })

    const promotions = await get_site_promotion_from_google_sheet(site._id, 'shopee_fresh')


    console.log(site.code)
    const token = await get_token_by_site(site, 'shopee_fresh')
    await shopee.delete_all_promotions(token).catch(console.log)

    if (promotions.length === 0) {
        return
    }

    let start_time = moment(promotions[0].start_time, 'YYYY/MM/DD')
    if (start_time.isBefore(moment().add(5, 'minutes'))) {
        start_time = moment().add(5, 'minutes')
    }

    await shopee.set_menu_item_promotions(token, {
        start_time: start_time.toISOString(),
        end_time: moment(promotions[0].end_time, 'YYYY/MM/DD').tz('Asia/Ho_Chi_Minh').endOf('day').toISOString(),
        items: promotions.map(v => ({
            name: v.name,
            sell_price: Number(v.sell_price),
        }))
    }).catch(console.log)

    console.log('DONE set_brand_promotion')
}


module.exports = { sync_site_promotion }