const moment = require('moment-timezone')
const _ = require('lodash');
moment.tz.setDefault('Asia/Bangkok');
const { BrandRequest, User, UserNotification, Site, BrandMenu, GoogleSheetFile, Hub, Brand } = require('../../../.shared/database');
const shopee_official = require('./shopee/official');
const shopee_unofficial = require('./shopee/unofficial');
const grab_mart_unofficial = require('./grab_mart/unofficial');
const grab_mart_official = require('./grab_mart/official');
const grab_food_unofficial = require('./grab_food/unofficial');
const be_food_unofficial = require('./be_food/unofficial');
const be_food_official = require('./be_food/official');
const { get_brand_menu_from_google_sheet } = require('../../../.shared/googlesheet/template');
const { read_sheet } = require('../../../.shared/googlesheet');

var is_running = false
const cron_brand_request = async () => {
    if (is_running) return

    const brand_request = await BrandRequest.findOne({
        status: 'APPROVED',
        $or: [{
            promise_completed_at: 0,
            promise_completed_at: { $lt: moment().unix() }
        }],
    })
    if (!brand_request) return

    is_running = true
    await dispatch_brand_request(brand_request._id)
    is_running = false
}

const dispatch_brand_request = async (request_id) => {
    const brand_request = await BrandRequest.findOneAndUpdate({ _id: request_id }, {
        status: 'PROCESSING',
        processing_at_unix: moment().unix(),
    }, { new: true })
    if (!brand_request) return

    const {
        source,
        request_types,
        brand_id,
        site_ids,
        created_by,
    } = brand_request;


    const brand = await Brand.findById(brand_id)
    if (!brand.menu_sheet_file_id) {
        return
    }
    const sheet_data = await read_sheet(brand.menu_sheet_file_id, 1, ['MD_menu', 'MD_option', 'MD_promotion'])
    await GoogleSheetFile.findOneAndUpdate({
        file_id: brand.menu_sheet_file_id,
    }, { sheet_data }, { upsert: true })
    const menu_mapping = await get_brand_menu_from_google_sheet(brand_id)
    await BrandMenu.findOneAndUpdate({ brand_id }, menu_mapping, { upsert: true })


    const error_messages = []
    try {
        const site_filter = { brand_id }
        if (site_ids.length > 0) {
            site_filter._id = site_ids
        }

        const active_hubs = await Hub.find({ status: 'active' }).lean()
        site_filter.hub_id = active_hubs.map(v => String(v._id))

        const sites = await Site.find(site_filter)

        const is_official_brand = [
            'NutiFood GrowPLUS+',
            'Nutifood NNDD',
            'NutiFood NutiMilk',
            'NutiFood Bliss',
            'NutiFood Varna',
            'Sạp Lê La',
            'Sạp Lê La [Partner Hub]',
        ].includes(brand.name)

        if (source === 'shopee') {
            for (const site of sites) {
                // const official_store = await shopee_official.get_store(site.code)
                if (request_types.includes('menu')) {
                    console.log('sync_site_menu', site.code)
                    if (is_official_brand) {
                        await shopee_official.sync_site_menu(site.code)
                    } else {
                        error_messages.push(`Site ${site.code} is not official store`)
                    }
                }
                if (request_types.includes('promotion')) {
                    console.log('sync_site_promotion', site.code)
                    await shopee_unofficial.sync_site_promotion(site.code)
                }
            }
        }
        if (source === 'grab_mart') {
            for (const site of sites) {
                // const official_store = await grab_mart_official.get_store(site.code)
                if (request_types.includes('menu')) {
                    // Delete all promotion
                    if (request_types.includes('promotion')) {
                        if (is_official_brand) {
                            await grab_mart_official.delete_site_promotions(site.code)
                        } else {
                            await grab_mart_unofficial.delete_site_promotions(site.code)
                        }
                    }

                    console.log('sync_site_menu', site.code)
                    if (is_official_brand) {
                        await grab_mart_official.sync_site_menu(site.code)
                    } else {
                        await grab_mart_unofficial.sync_site_menu(site.code)
                    }
                }
            }

            console.log('Start waiting for 10 minutes')
            await new Promise(r => setTimeout(r, 10 * 60 * 1000)) // await 10 minutes
            for (const site of sites) {
                // const official_store = await grab_mart_official.get_store(site.code)
                if (request_types.includes('promotion')) {
                    console.log('sync_site_promotion', site.code)
                    if (is_official_brand) {
                        await grab_mart_official.sync_site_promotion(site.code)
                    } else {
                        await grab_mart_unofficial.sync_site_promotion(site.code)
                    }
                }
            }
        }
        if (source === 'grab_food') {
            for (const site of sites) {
                const official_store = false // TODO
                if (request_types.includes('menu')) {
                    console.log('sync_site_menu', site.code)
                    if (official_store) {
                        // TODO:
                    } else {
                        await grab_food_unofficial.sync_site_menu(site.code)
                    }
                }
                if (request_types.includes('promotion')) {
                    console.log('sync_site_promotion', site.code)
                    await grab_food_unofficial.sync_site_promotion(site.code)
                }
            }
        }
        if (source === 'be_food') {
            if (request_types.includes('menu')) {
                await be_food_unofficial.sync_brand_menu(brand_id)
            }
            if (request_types.includes('promotion')) {
                console.log('sync_site_promotion')
                await be_food_unofficial.sync_brand_promotion(brand_id)
            }


            const official_store = false // TODO
            if (request_types.includes('menu')) {
                if (official_store) {

                } else {
                    await be_food_unofficial.sync_site_menu(sites.map(v => v.code))
                }
            }
        }

        brand_request.file = ''
        brand_request.error_messages = error_messages
        brand_request.status = 'SUCCESS'
    } catch (error) {
        error_messages.push(error.message)
        brand_request.status = 'FAILED'
        brand_request.error_messages = error_messages
        console.error(error)
    }

    brand_request.completed_at_unix = moment().unix()
    await brand_request.save()
}

// dispatch_brand_request('6833c1d097fe06264480f763')
module.exports = {
    dispatch_brand_request,
    cron_brand_request,
}