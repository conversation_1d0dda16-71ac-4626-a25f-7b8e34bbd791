const moment = require('moment-timezone')
const _ = require('lodash');
const axios = require('axios');
const sharp = require('sharp');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport, GoogleSheetFile } = require('../../../../.shared/database')
const { base_headers } = require('../../../../.shared/merchant/grab');
const { get_token_by_site } = require('../../../../.shared/token_account');
const { text_slugify, text_compare, retry_operation, compare_arrays, get_file_extension_from_url } = require('../../../../.shared/helper');
const { GRAB_CATEGORIES } = require('../../../../.shared/merchant/grab_mart_official');

const sync_site_menu = async (code) => {
    const site = await Site.findOne({ code });
    const brand = await Brand.findById(site.brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const token = await get_token_by_site(site, 'grab')
    const updated_items = sheet_data.MD_menu.filter(v => v.brand === brand.name).map(v => ({
        category_name: v.category,
        name: v.name,
        description: v.description,
        images: [v.image, v.image_1, v.image_2].filter(v => v),
        price: Number(v.price),
    }))
    await sync_menu_items(token, { updated_items })
}

/**
 * Synchronizes menu items between the site and Grab platform
 * @param {Object} params - The parameters object
 * @param {string} params.site_id - The site ID
 * @param {string} params.access_token - The access token for authentication
 * @param {Object} data - The data object containing updated items
 * @param {Array} data.updated_items - Array of updated menu items
 * @param {string} data.updated_items[].category_name - The category name of the item
 * @param {string} data.updated_items[].name - The name of the item
 * @param {string} data.updated_items[].description - The description of the item
 * @param {Array} data.updated_items[].images - Array of image URLs for the item
 * @param {number} data.updated_items[].price - The price of the item
 * @returns {Object|null} - Returns an object or null if an error occurs
 */
const sync_menu_items = async function ({ site_id, access_token }, { updated_items }) {
    if (!access_token) {
        return {};
    }

    try {
        // Fetch current Grab menu
        const group_menu_config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://api.grab.com/food/merchant/v2/menu`,
            headers: base_headers({ site_id, access_token }),
        };
        const group_menu_resp = await axios(group_menu_config);
        const grab_categories = group_menu_resp.data?.categories || [];

        // Prepare data for comparison
        const grabCategories = grab_categories.map(cat => ({
            name: cat.categoryName,
            id: cat.categoryID,
            items: cat.items.map(item => ({
                name: item.itemName,
                id: item.itemID,
                price: item.priceInMin,
                description: item.description,
                images: item.imageURLs
            }))
        }));

        const siteCategories = _.chain(updated_items)
            .groupBy('category_name')
            .map((items, category) => ({
                name: category,
                items: items.map(item => ({
                    name: item.name,
                    price: item.price,
                    description: item.description,
                    images: item.images
                }))
            }))
            .value();


        // Compare categories
        const { onlyInLeft: categoriesToCreate, onlyInRight: categoriesToDelete, inBoth: categoriesToUpdate } =
            compare_arrays(siteCategories, grabCategories, (a, b) => text_compare(a.name, b.name));

        for (const category of categoriesToDelete) {
            await deleteCategory(category, { site_id, access_token });
        }

        // Process categories
        for (const category of categoriesToCreate) {
            await createCategory(category, { site_id, access_token });
        }


        for (const category of categoriesToUpdate) {
            const grabCategory = grabCategories.find(c => text_compare(c.name, category.name));
            await updateCategory(category, grabCategory, { site_id, access_token });
        }

    } catch (err) {
        console.log(err);
        return null;
    }
};


const uploadImage = async function ({ site_id, access_token }, image_urls) {
    const result = [];

    for (const image_url of image_urls) {
        try {
            const imageBuffer = await retry_operation(async () => {
                const response = await axios.get(image_url, { responseType: 'arraybuffer' });
                return Buffer.from(response.data, 'binary');
            }, 2, 'Error downloading the image');

            const newImageBuff = await sharp(imageBuffer).resize(500, 500).jpeg().toBuffer();
            const base64String = newImageBuff.toString('base64');

            const resp_upload = await retry_operation(async () => {
                return await axios({
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `https://api.grab.com/food/merchant/v2/upload-file`,
                    headers: base_headers({ site_id, access_token }),
                    data: { "file": { "data": base64String, "type": "jpeg" }, "category": "menu_item_img" }
                });
            }, 2, 'Error uploading the image'
            );

            result.push(resp_upload.data.url);
        } catch (error) {
            console.log(image_url);
            console.error('Failed to process image after all retry attempts:', error.message);
        }
    }
    return result;
};

async function createCategory(category, { site_id, access_token }) {
    const resp = await axios({
        method: 'post',
        url: `https://api.grab.com/food/merchant/v2/categories`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "nameTranslation": { "translation": {} },
            "sellingTimeID": "AlwaysAvailable",
            "name": category.name,
            "sectionID": ""
        }
    });

    for (const item of category.items) {
        await createItem(item, resp.data.categoryID, { site_id, access_token });
    }
}

async function deleteCategory(category, { site_id, access_token }) {
    await axios({
        method: 'delete',
        url: `https://api.grab.com/food/merchant/v2/categories/${category.id}`,
        headers: base_headers({ site_id, access_token })
    }).then(() => console.log('Deleted category', category.name)).catch(err => console.log(err));
}

async function updateCategory(siteCategory, grabCategory, { site_id, access_token }) {
    const { onlyInLeft: itemsToCreate, onlyInRight: itemsToDelete, inBoth: itemsToUpdate } =
        compare_arrays(siteCategory.items, grabCategory.items, (a, b) => text_compare(a.name, b.name));

    for (const item of itemsToCreate) {
        await createItem(item, grabCategory.id, { site_id, access_token });
    }

    for (const item of itemsToDelete) {
        await deleteItem(item, { site_id, access_token });
    }

    for (const item of itemsToUpdate) {
        const grabItem = grabCategory.items.find(i => text_compare(i.name, item.name));
        await updateItem(item, grabItem, grabCategory.id, { site_id, access_token });
    }
}

async function createItem(item, categoryID, { site_id, access_token }) {
    const new_images = await uploadImage({ site_id, access_token }, item.images);
    const resp = await axios({
        method: 'post',
        url: `https://api.grab.com/food/merchant/v2/items`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "item": {
                "itemName": item.name,
                "nameTranslation": { "translation": {} },
                "priceInMin": item.price,
                "imageURLs": new_images,
                "sellingTimeID": "AlwaysAvailable",
                "descriptionTranslation": { "translation": {} },
                "description": item.description,
                "skuID": ""
            },
            "categoryID": categoryID
        }
    });

    await axios({
        method: 'post',
        url: `https://api.grab.com/food/merchant/v2/items/${resp.data.itemID}/proof-item`,
        headers: base_headers({ site_id, access_token }),
        data: { "proofImageURL": "" }
    });
}

async function deleteItem(item, { site_id, access_token }) {
    await axios({
        method: 'delete',
        url: `https://api.grab.com/food/merchant/v2/items/${item.id}`,
        headers: base_headers({ site_id, access_token })
    });
}

async function updateItem(siteItem, grabItem, categoryID, { site_id, access_token }) {
    const new_images = await uploadImage({ site_id, access_token }, siteItem.images);
    await axios({
        method: 'put',
        url: `https://api.grab.com/food/merchant/v2/items/${grabItem.id}`,
        headers: base_headers({ site_id, access_token }),
        data: {
            "itemID": grabItem.id,
            "item": {
                "specialItemType": "",
                "itemName": siteItem.name,
                "availableStatus": 1,
                "nameTranslation": { "translation": {} },
                "priceInMin": siteItem.price,
                "imageURLs": new_images,
                "sellingTimeID": "AlwaysAvailable",
                "descriptionTranslation": { "translation": {} },
                "description": siteItem.description,
                "skuID": ""
            },
            "categoryID": categoryID
        }
    });

    await axios({
        method: 'post',
        url: `https://api.grab.com/food/merchant/v2/items/${grabItem.id}/proof-item`,
        headers: base_headers({ site_id, access_token }),
        data: { "proofImageURL": "" }
    });
}

const sync_site_promotion = async (code) => {
    const site = await Site.findOne({ code });
    const brand = await Brand.findById(site.brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const token = await get_token_by_site(site, 'grab')
    const updated_items = sheet_data.MD_promotion.filter(v => v.brand === brand.name).map(v => ({
        name: v.name,
        price: Number(v.price),
        sell_price: Number(v.sell_price),
        start_time: moment(v.start_time, 'YYYY/MM/DD').isBefore(moment()) ? moment().add(5, 'minutes').toISOString() : moment(v.start_time, 'YYYY/MM/DD').toISOString(),
        end_time: moment(v.end_time, 'YYYY/MM/DD').toISOString(),
    }))
    await sync_promotion_items(token, { updated_items })
}

const delete_all_promotion_items = async function ({ site_id, access_token }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // Fetch existing campaigns
        const campaigns_resp = await axios.get(`https://api.grab.com/food/merchant/v2/campaigns`, {
            headers: base_headers({ site_id, access_token })
        });
        const campaigns = _.concat(campaigns_resp.data?.campaigns.ongoing || [], campaigns_resp.data?.campaigns.upcoming || []);

        // Delete existing campaigns
        for (const campaign of campaigns) {
            await axios.delete(`https://api.grab.com/food/merchant/v2/campaigns/${campaign.campaignID}`, {
                headers: base_headers({ site_id, access_token }),
            });
        }
    } catch (error) { }
}
/**
 * Synchronizes menu items between the site and Grab platform
 * @param {Object} params - The parameters object
 * @param {string} params.site_id - The site ID
 * @param {string} params.access_token - The access token for authentication
 * @param {Object} data - The data object containing updated items
 * @param {Array} data.updated_items - Array of updated menu items
 * @returns {Object|null} - Returns an object or null if an error occurs
 */

const sync_promotion_items = async function ({ site_id, access_token }, { updated_items }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        await delete_all_promotion_items({ site_id, access_token })

        // Fetch the current menu from Grab
        const group_menu_resp = await axios.get(`https://api.grab.com/food/merchant/v2/menu`, {
            headers: base_headers({ site_id, access_token })
        });
        const grab_categories = group_menu_resp.data?.categories || [];
        const grab_items = grab_categories.map(v => v.items).flat();

        const itemNameToIdMap = new Map(grab_items.map(item => [item.itemName, item.itemID]));

        // Create new campaigns for each updated item
        for (const item of updated_items) {
            const itemId = itemNameToIdMap.get(item.name);
            if (!itemId) {
                console.warn(`Item "${item.name}" not found in the Grab menu. Skipping campaign creation.`);
                continue;
            }

            await axios.post(`https://api.grab.com/food/merchant/v2/campaigns`, {
                preCheck: false,
                config: {
                    campaignObjective: "DRIVE_SALES_ALL_CUSTOMER",
                    condition: {},
                    countryCode: "VN",
                    scope: {
                        type: "items",
                        items: [
                            {
                                itemID: itemId,
                                itemName: item.name
                            }
                        ],
                        objects: [
                            {
                                objectID: itemId,
                                objectName: item.name
                            }
                        ],
                    },
                    discount: {
                        type: "net",
                        discountValue: item.price - item.sell_price
                    },
                    startTime: item.start_time,
                    endTime: item.end_time,
                    cityID: 9,
                    countryID: 5
                }
            }, {
                headers: base_headers({ site_id, access_token }),
            });
        }

        console.log(`Successfully synced ${updated_items.length} menu items`);
        return { success: true, itemCount: updated_items.length };
    } catch (error) {
        console.error('Error syncing menu items:', error);
        return null;
    }
};


module.exports = {
    sync_site_menu,
    sync_site_promotion,
}