const moment = require('moment-timezone')
const _ = require('lodash');
const axios = require('axios');
const FormData = require('form-data');
const sharp = require('sharp');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport, GoogleSheetFile } = require('../../../../.shared/database')
const { base_headers, base_body } = require('../../../../.shared/merchant/be');
const be_official = require('../../../../.shared/merchant/be_official');
const { } = require('../../../../.shared/merchant/be_official');
const { get_token_by_site, get_token_by_code } = require('../../../../.shared/token_account');
const { text_slugify, text_compare, retry_operation, compare_arrays, get_file_extension_from_url } = require('../../../../.shared/helper');
const { get_brand_menu_from_google_sheet } = require('../../../../.shared/googlesheet/template');
const { sync_brand_menu_option_items } = require('./unofficial');

const sync_brand_menu = async (brand_id) => {
    const brand = await Brand.findById(brand_id);
    const master_token = await get_token_by_code('NEXDOR_be')
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })

    const option_items = sheet_data.MD_option.filter(v => v.brand === brand.name) || []
    await sync_brand_menu_option_items(master_token, option_items)
    const updated_items = sheet_data.MD_menu.filter(v => v.brand === brand.name).map(v => ({
        category_name: v.category,
        name: v.name,
        description: v.description,
        images: [v.image, v.image_1, v.image_2].filter(v => v),
        price: Number(v.price),
    }))

    await sync_master_menu_items(master_token, { updated_items }, option_items)
}

const sync_site_menu = async (code) => {
    const site = await Site.findOne({ code });
    const brand = await Brand.findById(site.brand_id);

    const { access_token } = await be_official.get_token()
    const menu_mapping = await get_brand_menu_from_google_sheet(brand.id)
    await be_official.sync_site_menu({
        site_id: '6464',
        access_token,
    }, menu_mapping)

}

// (async () => {
//     const sites = await Site.find({ brand_id: '66b5f95374e70c1305b35735' })
//     for (const site of sites) {
//         await sync_site_menu(site.code)
//     }
//     console.log('DONE')
// })();

const sync_brand_promotion = async (brand_id) => {
    const brand = await Brand.findById(brand_id);
    const { sheet_data } = await GoogleSheetFile.findOne({ file_id: brand.menu_sheet_file_id })
    const token = await get_token_by_code('NEXDOR_be')
    const updated_items = sheet_data.MD_promotion.filter(v => v.brand === brand.name).map(v => ({
        name: v.name,
        price: Number(v.price),
        sell_price: Number(v.sell_price),
        start_time: moment(v.start_time, 'YYYY/MM/DD').isBefore(moment().add(5, 'minutes')) ? moment().add(5, 'minutes').toISOString() : moment(v.start_time, 'YYYY/MM/DD').toISOString(),
        end_time: moment(v.end_time, 'YYYY/MM/DD').toISOString(),
    }))
    await sync_promotion_items(brand, token, { updated_items })
}


const delete_all_promotion_items = async function ({ site_id, access_token }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // Fetch existing campaigns
        const campaigns_resp = await axios.get(`https://api.grab.com/food/merchant/v2/campaigns`, {
            headers: base_headers({ site_id, access_token })
        });
        const campaigns = _.concat(campaigns_resp.data?.campaigns.ongoing || [], campaigns_resp.data?.campaigns.upcoming || []);

        // Delete existing campaigns
        for (const campaign of campaigns) {
            await axios.delete(`https://api.grab.com/food/merchant/v2/campaigns/${campaign.campaignID}`, {
                headers: base_headers({ site_id, access_token }),
            });
        }
    } catch (error) { }
}
/**
 * Synchronizes menu items between the site and Grab platform
 * @param {Object} params - The parameters object
 * @param {string} params.site_id - The site ID
 * @param {string} params.access_token - The access token for authentication
 * @param {Object} data - The data object containing updated items
 * @param {Array} data.updated_items - Array of updated menu items
 * @returns {Object|null} - Returns an object or null if an error occurs
 */

const sync_promotion_items = async function (brand, { site_id, access_token }, { updated_items }) {
    if (!access_token) {
        console.error('Access token is missing');
        return null;
    }

    try {
        // await delete_all_promotion_items({ site_id, access_token })

        const group_menu_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_merchant_items`, base_body({ site_id, access_token }), { headers: base_headers() });
        const grab_categories = group_menu_resp.data?.restaurant_items || [];

        const grab_items = []
        for (const cat of grab_categories) {
            for (const item of cat.items) {
                grab_items.push({
                    category_name: cat.category.name,
                    category_id: cat.category.category_id,
                    id: item.restaurant_item_id,
                    name: item.item_name,
                    price: item.price,
                    description: item.item_details,
                    images: [item.item_image]
                })

            }
        }

        const itemNameMap = new Map(grab_items.map(item => [item.name, item]));

        const direct_sale_items = []
        for (const updated_item of updated_items) {
            const grab_item = itemNameMap.get(updated_item.name);
            if (!grab_item) {
                console.warn(`Item "${updated_item.name}" not found in the Grab menu. Skipping campaign creation.`);
                continue;
            }
            direct_sale_items.push({
                "sale_price": updated_item.sell_price,
                "campaign_id": 0,
                "restaurant_item_id": String(grab_item.id),
                "item_image": grab_item.images[0],
                "restaurant_item_name": grab_item.name,
                "old_price": grab_item.price,
                "category_name": grab_item.category_name
            })
        }
        if (direct_sale_items.length === 0) {
            console.log('No items to sync');
            return { success: true }
        }

        const campain_resp = await axios.post(`https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/direct_sale_campaign/create`, {
            ...base_body({ site_id, access_token }),
            "campaign_info": {
                "campaign_name": `Khuyến mãi gạch giá ${brand.name}`,
                "start_date": moment(updated_items[0].start_time).unix(),
                "end_date": moment(updated_items[0].end_time).unix(),
                "timings": "",
                "max_allow_per_day": null,
                "direct_sale_items": direct_sale_items
            },
            "direct_sale_items": direct_sale_items
        }, {
            headers: base_headers(),
        });
        console.log(campain_resp.data)
        console.log(`Successfully synced ${direct_sale_items.length} menu items`);
        return { success: true, itemCount: direct_sale_items.length };
    } catch (error) {
        console.error('Error syncing menu items:', error);
        return null;
    }
};


module.exports = {
    sync_brand_menu,
    sync_site_menu,
    sync_brand_promotion,
}