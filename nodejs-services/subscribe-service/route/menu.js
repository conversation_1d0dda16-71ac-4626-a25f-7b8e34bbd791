const _ = require('lodash')
const xlsx = require('xlsx');
const { v4 } = require('uuid');
const { BrandMenu, Site, Brand, HubMenuGroup, HubStock, UserNotification } = require('../../.shared/database')
const { map_menu, _textToSlug } = require('../../.shared/merchant/mapping')
const shopee = require('../../.shared/merchant/shopee');
const grab = require('../../.shared/merchant/grab');
const be = require('../../.shared/merchant/be');
const { upload_file } = require('../../.shared/storage')
const { text_compare } = require('../../.shared/helper');
const { get_token_by_site } = require('../../.shared/token_account');

const export_brand_menu_of_all_sites = async ({ file_key, brand_id, user_id }) => {
    const rows = []
    const brand = await Brand.findById(brand_id)
    try {
        // const file_key = `brand_menu/${Date.now()}/all_site_menu_items.xlsx`
        const sources = ["grab", "grab_mart", "shopee", "shopee_fresh", "be"]
        const sites = await Site.find({ brand_id })


        const brand_menu = await BrandMenu.findOne({ brand_id }).lean()
        const brand_menu_items = []
        brand_menu_items.push(...brand_menu?.categories?.flatMap(v => v.items))
        brand_menu_items.push(...brand_menu?.categories?.flatMap(v => v.sub_categories.flatMap(s => s.items)))
        const brand_menu_skus = _.uniqBy(brand_menu_items.filter(v => v.combo?.length > 0).flatMap(v => v.combo), 'code')


        const merchant_functions = {
            grab: grab.get_menu,
            grab_mart: grab.get_mart_menu,
            shopee: shopee.get_menu,
            shopee_fresh: shopee.get_menu,
            be: be.get_menu,
        }

        const all_hub_stocks = await HubStock.find({ hub_id: { $in: sites.map(v => v.hub_id) }, code: { $in: brand_menu_skus.map(v => v.code) } }).select({ hub_id: 1, code: 1, quantity: 1, locked_status: 1 }).lean()
        for (const chunk of _.chunk(sites, 50)) {
            await Promise.all(chunk.map(async site => {
                const hub_stocks = all_hub_stocks.filter(v => String(v.hub_id) === String(site.hub_id))
                const hub_stock_map = _.keyBy(hub_stocks, 'code')

                for (const source of sources) {
                    const merchant_function = merchant_functions[source]
                    if (merchant_function) {
                        const token = await get_token_by_site(site, source)
                        const menu = await merchant_function(token)
                        const new_menu = map_menu(source, menu)
                        const all_menu_items = [
                            ...new_menu.categories.map(c => c.items),
                            ...new_menu.categories.map(c => c.sub_categories?.flatMap(s => s.items))
                        ].filter(v => v).flat()
                        for (const item of all_menu_items) {
                            const row = {
                                'Mã site': site.code,
                                'Tên site': site.name,
                                'Sàn': source,
                                'Tên món': item.name,
                                'Giá bán': item.price,
                                'Trạng thái': item.active ? 'active' : 'inactive',
                            }
                            const brand_menu_item = brand_menu_items.find(v => text_compare(v.name, item.name))
                            if (brand_menu_item && brand_menu_item?.combo?.length > 0) {
                                const stock = hub_stock_map[brand_menu_item.combo[0].code]
                                row['Cấu hình tắt mở món'] = { 'use_stock': 'Theo tồn kho', 'alway_active': 'Luôn bật', 'alway_inactive': 'Luôn tắt' }[stock?.locked_status] || 'Theo tồn kho'
                                row['Số lượng theo SKU'] = stock?.quantity || 0
                                row['Số lượng tối thiểu'] = brand_menu_item?.quantity_minimum || 0
                            }

                            rows.push(row)
                        }
                    }
                }
            }))
        }

    } catch (err) {
        rows.push({ error: err.message })
    }
    const buff = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(rows);
    xlsx.utils.book_append_sheet(buff, worksheet, 'All site menu items');
    const file = await upload_file({ bucket: 'nexpos-files', key: file_key, buff: xlsx.write(buff, { type: 'buffer' }) })

    await UserNotification.create({
        user_id,
        type: 'success',
        title: `Menu của brand: ${brand.name} đã tải xong`,
        content: `Vui lòng tải file đính kèm`,
        attachments: [file],
        topic: 'menu',
    })

    console.log(file)
}

module.exports = {
    export_brand_menu_of_all_sites
}