const moment = require('moment-timezone')
const _ = require('lodash');
const fs = require('fs');
const xlsx = require('xlsx');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport, OrderShipment, Role, CoreProduct } = require('../../.shared/database')
const { upload_file, pre_sign_file } = require('../../.shared/storage')
const helper = require('../../.shared/helper');
const { MERCHANT_INFO } = require('../../.shared/const')

const generate_report_file = async (report_id) => {
    const order_report = await OrderReport.findOneAndUpdate({ _id: report_id }, {
        status: 'PROCESSING',
        processing_at_unix: moment().unix(),
    }, { new: true })
    if (!order_report) return
    const {
        created_by,
        file_key,
        order_filter,
    } = order_report;

    const user = await User.findOne({ _id: created_by })
    const { permissions } = await Role.findOne({ _id: user.role_id })
    try {
        const orders = await Order.find(order_filter, {
            order_id: 1,
            source: 1,
            site_id: 1,
            hub_id: 1,
            'data_mapping.id': 1,
            'data_mapping.source': 1,
            'data_mapping.coupons': 1,
            'data_mapping.order_id': 1,
            'data_mapping.dishes': 1,
            'data_mapping.total': 1,
            'data_mapping.total_for_biz': 1,
            'data_mapping.total_discount': 1,
            'data_mapping.commission': 1,
            'data_mapping.payments': 1,
            'data_mapping.order_time_sort': 1,
            'data_mapping.delivery_time_unix': 1,
            'data_mapping.customer_name': 1,
            'data_mapping.customer_phone': 1,
            'data_mapping.customer_address': 1,
            'data_mapping.finance_data': 1,
            'data_mapping.last_transaction': 1,
            'data_mapping.cancel_by': 1,
            'data_mapping.cancel_reason': 1,
            shipment: 1,
        }).lean()

        const sites = await Site.find({ _id: _.uniq(orders.map(v => v.site_id)) }, {
            _id: 1,
            name: 1,
            code: 1,
            hub_id: 1,
            brand_id: 1,
            tokens: 1,
        }).lean()
        const hubs = await Hub.find({ _id: _.uniq(orders.map(v => v.hub_id)) }, {
            _id: 1,
            name: 1,
            code: 1,
        }).lean()
        const brands = await Brand.find({ _id: sites.map(v => v.brand_id) }, {
            _id: 1,
            name: 1,
        }).lean()
        const order_feedbacks = await OrderFeedback.find({ order_id: orders.map(v => v.order_id) }, { order_id: 1, rating: 1, comment: 1 }).lean()
        const order_shipments = await OrderShipment.find({ order_id: orders.map(v => v.order_id) }, { webhooks: 0 }).lean()
        const brand_menus = await BrandMenu.find({ brand_id: brands.map(v => v._id) })
        const siteMap = _.keyBy(sites, "_id")
        const hubMap = _.keyBy(hubs, "_id")
        const brandMap = _.keyBy(brands, "_id")
        const brandMenuMap = _.keyBy(brand_menus, "brand_id")
        const orderFeedbackMap = _.keyBy(order_feedbacks, "order_id")


        let sheet_orders = [];
        let sheet_transactions = [];
        let sheet_feedbacks = [];
        let sheet_shipments = [];
        let sheet_items = [];
        let sheet_compare = [];
        let sheet_stocks = [];
        let sheet_gross_by_day = [];
        let sheet_brand_group_by_day = [];
        let sheet_hub_group_by_day = [];
        let sheet_accountant_orders = [];

        for (let order_idx = 0; order_idx < orders.length; order_idx++) {
            orders[order_idx].STT = order_idx + 1
        }

        for (const chunk_orders of _.chunk(orders, 1000)) {
            await Promise.all(chunk_orders.map(async order => {
                // const order = orders[order_idx];
                const site = siteMap[order.site_id]
                const order_mapping = order.data_mapping
                const order_hub = hubMap[order.hub_id]
                const order_site = siteMap[order.site_id]
                const order_brand = brandMap[order_site?.brand_id]
                const brand_menu = brandMenuMap[order_brand?._id] || {
                    categories: [],
                    option_categories: []
                }

                let record1 = {
                    // 'STT': order_idx + 1,
                    STT: order.STT,
                    'Mã tham chiếu': order_mapping.id,
                    'Mã đơn hàng': order_mapping.order_id,
                    'Nguồn đặt': MERCHANT_INFO[order_mapping.source]?.label || order_mapping.source,
                    'ĐVVC': order.shipment?.vendor,
                    'Mã đơn vận chuyển': order.shipment?.shipment_id,
                    'Hub bán hàng': order_hub?.name,
                    'Mã hub': order_hub?.code,
                    'Brand bán hàng': order_brand?.name,
                    'Site bán hàng': order_site?.name,
                    'Số lượng món ': _.sumBy(order_mapping.dishes, 'quantity'),

                    'GIÁ GỐC': order_mapping.finance_data?.original_price,
                    'GẠCH GIÁ': order_mapping.finance_data?.other_promotion_price,
                    'GIÁ BÁN': order_mapping.finance_data?.sell_price,
                    'TIỀN SHIP': order_mapping.finance_data?.shipment_fee,
                    'GIẢM GIÁ ĐƠN HÀNG': order_mapping.finance_data?.co_fund_promotion_price + (order_mapping.finance_data?.shipment_discount || 0),
                    'TỔNG KM': order_mapping.finance_data?.total_promotion_price + (order_mapping.finance_data?.shipment_discount || 0),
                    'DT SAU KM': order_mapping.finance_data?.gross_received,
                    'THU KHÁC': order_mapping.finance_data?.additional_income,
                    'COMMISSION': order_mapping.finance_data?.commission,
                    'PHÍ GIAO DỊCH': order_mapping.finance_data?.transaction_fee,
                    'PHÍ ĐIỀU CHỈNH': order_mapping.finance_data?.adjustment_fee,
                    'THƯC NHẬN TỪ SÀN': order_mapping.finance_data?.net_received,
                    'THỰC NHẬN TỪ KH': order_mapping.finance_data?.real_received,

                    'Mã KM': order_mapping.coupons?.filter(v => v.code)?.map(v => v.code)?.join('\n'),
                    'Ngày đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY'),
                    'Giờ đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('HH:mm:ss'),
                    'Ngày giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
                    'Giờ giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('HH:mm:ss'),
                }
                if (permissions?.includes('system')) {
                    record1['Tên khách hàng'] = order_mapping.customer_name
                    record1['SĐT khách hàng'] = order_mapping.customer_phone
                    record1['Địa chỉ khách hàng'] = order_mapping.customer_address
                }
                if (order_filter.status === 'CANCEL') {
                    record1['Hủy bởi'] = order_mapping.cancel_by
                    record1['Lý do hủy'] = order_mapping.cancel_reason
                }

                sheet_orders.push(record1);

                let record_accountant_orders = {
                    'Nguồn đặt': record1['Nguồn đặt'],
                    'Ngày giao hàng': record1['Ngày giao hàng'],
                    'Giờ giao hàng': record1['Giờ giao hàng'],
                    'Hub bán hàng': record1['Hub bán hàng'],
                    'Mã hub': record1['Mã hub'],
                    'Brand bán hàng': record1['Brand bán hàng'],
                    'Site bán hàng': record1['Site bán hàng'],
                    'GIÁ GỐC': record1['GIÁ GỐC'],
                    'GẠCH GIÁ': record1['GẠCH GIÁ'],
                    'GIẢM GIÁ ĐƠN HÀNG': record1['GIẢM GIÁ ĐƠN HÀNG'],
                    'DT SAU KM': record1['DT SAU KM'],
                    '% COMMISSION': 0,
                    'COMMISSION': record1['COMMISSION'],
                    'Phí dịch vụ Nexdor': 0,
                    'THỰC NHẬN KH': record1['THỰC NHẬN TỪ KH']
                }
                const merchant_token = siteMap[order.site_id].tokens.find(v => v.name === order_mapping.source)
                const nexdor_commision = merchant_token?.nexdor_commission ? Number(merchant_token?.nexdor_commission) / 100 : 0

                record_accountant_orders['% COMMISSION'] = _.round((Number(record_accountant_orders['COMMISSION']) / Number(record1['DT SAU KM'])) * 100, 1)
                record_accountant_orders['Phí dịch vụ Nexdor'] = _.round(record1['DT SAU KM'] * nexdor_commision, 0)
                record_accountant_orders['THỰC NHẬN KH'] = record1['DT SAU KM'] - record1['COMMISSION'] - record_accountant_orders['Phí dịch vụ Nexdor']

                sheet_accountant_orders.push(record_accountant_orders);

                let payments = order_mapping.payments || []
                payments = payments.filter(v => (['NEXDORPAY', 'MOMO'].includes(v.method) && v.status === 'COMPLETED') || !['NEXDORPAY', 'MOMO'].includes(v.method))
                for (const payment of payments) {
                    let record11 = {
                        'Mã tham chiếu': record1['Mã tham chiếu'],
                        'Mã đơn hàng': record1['Mã đơn hàng'],
                        'Nguồn đặt': record1['Nguồn đặt'],
                        'Hub bán hàng': record1['Hub bán hàng'],
                        'Brand bán hàng': record1['Brand bán hàng'],
                        'Site bán hàng': record1['Site bán hàng'],

                        'Hình thức thanh toán': payment.method,
                        'Mã giao dịch': payment.code ?? payment.voucher_code,
                        'Giá trị giao dịch': payment.total,

                        'Ngày đặt hàng': record1['Ngày đặt hàng'],
                        'Giờ đặt hàng': record1['Giờ đặt hàng'],
                        'Ngày giao hàng': record1['Ngày giao hàng'],
                        'Giờ giao hàng': record1['Giờ giao hàng'],
                    }
                    sheet_transactions.push(record11);
                }

                const order_feedback = orderFeedbackMap[order_mapping.order_id]
                if (order_feedback) {
                    let record12 = {
                        'Mã tham chiếu': record1['Mã tham chiếu'],
                        'Mã đơn hàng': record1['Mã đơn hàng'],
                        'Nguồn đặt': record1['Nguồn đặt'],
                        'Hub bán hàng': record1['Hub bán hàng'],
                        'Brand bán hàng': record1['Brand bán hàng'],
                        'Site bán hàng': record1['Site bán hàng'],
                        'Đánh giá Sao': order_feedback.rating,
                        'Bình luận': order_feedback.comment,
                        'Ngày đặt hàng': record1['Ngày đặt hàng'],
                        'Giờ đặt hà)ng': record1['Giờ đặt hàng'],
                        'Ngày giao hàng': record1['Ngày giao hàng'],
                        'Giờ giao hàng': record1['Giờ giao hàng'],
                    }
                    if (permissions?.includes('get_user_review')) {
                        sheet_feedbacks.push(record12);
                    }
                }

                const sub_total = _.sumBy(order_mapping.dishes, v => v.price)
                const sell_price_total = _.sumBy(order_mapping.dishes, v => v.discount_price)

                for (const dish of order_mapping.dishes) {
                    const item = helper.find_item_in_menu_by_name(brand_menu.categories, dish)
                    const unit_price = dish.quantity > 0 ? Number(dish.price / dish.quantity) : 0
                    const unit_sell_price = dish.quantity > 0 ? Number(dish.discount_price / dish.quantity) : 0
                    const dish_discount = (dish.price - dish.discount_price) + _.round(dish.discount_price / sell_price_total * Number(record1['GIẢM GIÁ ĐƠN HÀNG']) || 0, 2)
                    let record2 = {
                        'Mã tham chiếu': order_mapping.id,
                        'Mã đơn hàng': order_mapping.order_id,
                        'Hub bán hàng': order_hub?.name,
                        'Mã hub': order_hub?.code,
                        'Brand bán hàng': order_brand?.name,
                        'Site bán hàng': order_site?.name,
                        'Brand món': item?.brand_name || order_brand?.name,
                        'Mã món': item?.code,
                        'Phân loại': 'Theo món',
                        'Tên món': dish.name,
                        'Tên tùy chọn': '',
                        'Tên lựa chọn': '',
                        'SL bán': dish.quantity,
                        'Giá gốc': unit_price,
                        'Giá bán': unit_sell_price,
                        'Tiền hàng': dish.price,
                        'Khuyến mãi': dish_discount,
                        'Thành tiền sau KM': dish.price - dish_discount,
                        'Nguồn đặt': MERCHANT_INFO[order.source]?.label || order.source,
                        'Ngày đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY'),
                        'Giờ đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('HH:mm:ss'),
                        'Ngày giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
                        'Giờ giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('HH:mm:ss'),
                        'Kênh thanh toán': ['local', 'he'].includes(order_mapping.source) ? order_mapping.payments?.map(x => x?.method)?.join(', ') : order_mapping.source,
                    }

                    if (permissions?.includes('system')) {
                        record2['Tên khách hàng'] = order_mapping.customer_name
                        record2['SĐT khách hàng'] = order_mapping.customer_phone
                        record2['Địa chỉ khách hàng'] = order_mapping.customer_address
                    }

                    const record2os = []
                    for (const options of dish.options) {
                        for (const option of options) {
                            const option_unit_price = dish.quantity > 0 ? Number(option.option_price / dish.quantity) : 0
                            const option_unit_sell_price = dish.quantity > 0 ? Number(option.option_discount_price / dish.quantity) : 0

                            let record2o = {
                                'Mã tham chiếu': order_mapping.id,
                                'Mã đơn hàng': order_mapping.order_id,
                                'Hub bán hàng': order_hub?.name,
                                'Mã hub': order_hub?.code,
                                'Brand bán hàng': order_brand?.name,
                                'Site bán hàng': order_site?.name,
                                'Brand món': item?.brand_name || order_brand?.name,
                                'Mã món': item?.code,
                                'Phân loại': 'Theo tùy chọn',
                                'Tên món': dish.name,
                                'Tên tùy chọn': option.option_name,
                                'Tên lựa chọn': option.option_item,
                                'SL bán': dish.quantity,
                                'Giá gốc': option_unit_price,
                                'Giá bán': option_unit_sell_price,
                                'Tiền hàng': option.option_price,
                                'Khuyến mãi': option.option_price - option.option_discount_price,
                                'Thành tiền sau KM': option.option_discount_price,
                                'Nguồn đặt': MERCHANT_INFO[order.source]?.label || order.source,
                                'Ngày đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY'),
                                'Giờ đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('HH:mm:ss'),
                                'Ngày giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
                                'Giờ giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('HH:mm:ss'),
                            }
                            record2['Giá gốc'] -= record2o['Giá gốc']
                            record2['Giá bán'] -= record2o['Giá bán']
                            record2['Tiền hàng'] -= record2o['Tiền hàng']
                            record2['Khuyến mãi'] -= record2o['Khuyến mãi']
                            record2['Thành tiền sau KM'] -= record2o['Thành tiền sau KM']
                            record2os.push(record2o)
                        }
                    }
                    sheet_items.push(record2);
                    sheet_items.push(...record2os);


                    let has_config = false
                    if (item?.combo?.length > 0) {
                        has_config = true
                        item?.combo?.map(c => {
                            if (c.quantity > 0) {
                                const stock = order?.vendor_sync?.callback?.stock_response?.find(v => v.item_code === c.code)?.quantity || 0
                                sheet_stocks.push({
                                    'Mã tham chiếu': order_mapping.id,
                                    'Mã đơn hàng': order_mapping.order_id,
                                    'Hub bán hàng': order_hub?.name,
                                    'Mã hub': order_hub?.code,
                                    'Brand bán hàng': order_brand?.name,
                                    'Site bán hàng': order_site?.name,
                                    'Mã NVL': c.code,
                                    'Tên NVL': c.name,
                                    'ĐVT': c.unit,
                                    'Giá món (unit)': c.price,
                                    'SL xuất bán': c.quantity * dish.quantity,
                                    'SL trước xuất bán': stock,
                                })
                            }
                        })
                    }

                    if (dish?.options?.length > 0) {
                        for (const option_items of dish.options) {
                            for (const o of option_items) {
                                const option = helper.find_option_item_in_menu_by_name(brand_menu.option_categories, o.option_name, o.option_item)
                                if (option) {
                                    option?.combo?.map(c => {
                                        if (c.quantity > 0) {
                                            has_config = true
                                            const stock = order?.vendor_sync?.callback?.stock_response?.find(v => v.item_code === c.code)?.quantity || 0
                                            sheet_stocks.push({
                                                'Mã tham chiếu': order_mapping.id,
                                                'Mã đơn hàng': order_mapping.order_id,
                                                'Hub bán hàng': order_hub?.name,
                                                'Mã hub': order_hub?.code,
                                                'Brand bán hàng': order_brand?.name,
                                                'Site bán hàng': order_site?.name,
                                                'Mã NVL': c.code,
                                                'Tên NVL': c.name,
                                                'ĐVT': c.unit,
                                                'Giá món (unit)': c.price,
                                                'SL xuất bán': c.quantity * dish.quantity,
                                                'SL trước xuất bán': stock,
                                            })
                                        }
                                    })
                                }
                            }
                        }
                    }

                    if (!has_config) {
                        sheet_stocks.push({
                            'Mã tham chiếu': order_mapping.id,
                            'Mã đơn hàng': order_mapping.order_id,
                            'Hub bán hàng': order_hub?.name,
                            'Mã hub': order_hub?.code,
                            'Brand bán hàng': order_brand?.name,
                            'Site bán hàng': order_site?.name,
                            'Mã NVL': '',
                            'Tên NVL': '',
                            'ĐVT': '',
                            'Giá món (unit)': '',
                            'SL xuất bán': '',
                            'SL trước xuất bán': '',
                        })
                    }
                }

            })).catch(console.error)
        }

        const sheet3_group_by_dish_and_day = sheet_items.reduce((acc, curr) => {
            const key = `${helper.text_slugify(curr['Tên món'])}|${curr['Ngày đặt hàng']}`;
            if (!acc[key]) {
                acc[key] = {
                    'Mã món': curr['Mã món'],
                    'Tên món': curr['Tên món'],
                    'Ngày đặt hàng': curr['Ngày đặt hàng'],
                    'SL bán': parseInt(curr['SL bán'] || 0)
                };
            } else {
                acc[key]['SL bán'] += parseInt(curr['SL bán'] || 0);
            }
            return acc;
        }, {});

        const unique_dishes = _.uniqBy(Object.values(sheet3_group_by_dish_and_day), 'Tên món');

        const dates = _.uniq(Object.values(sheet3_group_by_dish_and_day).map(item => item['Ngày đặt hàng']));
        dates.sort((a, b) => moment(a, 'DD-MM-YYYY').unix() - moment(b, 'DD-MM-YYYY').unix());
        sheet_compare = unique_dishes.map(dish => {
            const row = {
                'Tên món': dish['Tên món'],
                'Mã món': dish['Mã món'],
            };
            dates.forEach(date => {
                const key = `${helper.text_slugify(dish['Tên món'])}|${date}`;
                row[date] = sheet3_group_by_dish_and_day[key] ? (sheet3_group_by_dish_and_day[key]['SL bán'] > 0 ? sheet3_group_by_dish_and_day[key]['SL bán'] : '-') : '-';
            });
            return row;
        });

        const sheet5_group_by_day = sheet_items.reduce((acc, curr) => {
            const key = curr['Ngày đặt hàng'];
            if (!acc[key]) {
                acc[key] = {
                    'Ngày tháng năm giao hàng': curr['Ngày đặt hàng'],
                    'Doanh thu trước KM': parseInt(curr['Tiền hàng'] || 0),
                    'Tổng khuyến mãi': parseInt(curr['Khuyến mãi'] || 0),
                    'Doanh thu sau KM': parseInt(curr['Thành tiền sau KM'] || 0),
                };
            } else {
                acc[key]['Doanh thu trước KM'] += parseInt(curr['Tiền hàng'] || 0);
                acc[key]['Tổng khuyến mãi'] += parseInt(curr['Khuyến mãi'] || 0);
                acc[key]['Doanh thu sau KM'] += parseInt(curr['Thành tiền sau KM'] || 0);
            }
            return acc;
        }, {});
        sheet_gross_by_day = _.toArray(sheet5_group_by_day).sort((a, b) => moment(a['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix() - moment(b['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix());

        const sheet6_group_by_day = sheet_items.reduce((acc, curr) => {
            const key = curr['Ngày đặt hàng'];
            const total = parseInt(curr['Thành tiền sau KM'] || 0);
            if (!acc[key]) {
                acc[key] = {
                    'Ngày tháng năm giao hàng': curr['Ngày đặt hàng'],
                    'Doanh thu trước KM': parseInt(curr['Tiền hàng'] || 0),
                    'Tổng khuyến mãi': parseInt(curr['Khuyến mãi'] || 0),
                    'Doanh thu sau KM': parseInt(curr['Thành tiền sau KM'] || 0),
                    'Grab': curr['Nguồn đặt'] === MERCHANT_INFO.grab.label ? total : 0,
                    'Grab Mart': curr['Nguồn đặt'] === MERCHANT_INFO.grab_mart.label ? total : 0,
                    'BE': curr['Nguồn đặt'] === MERCHANT_INFO.be.label ? total : 0,
                    'Gojek': curr['Nguồn đặt'] === MERCHANT_INFO.gojek.label ? total : 0,
                    'Shopee': curr['Nguồn đặt'] === MERCHANT_INFO.shopee.label ? total : 0,
                    'Shopee Fresh': curr['Nguồn đặt'] === MERCHANT_INFO.shopee_fresh.label ? total : 0,
                    'Shopee Ecom': curr['Nguồn đặt'] === MERCHANT_INFO.shopee_ecom.label ? total : 0,
                    'Local': curr['Nguồn đặt'] === MERCHANT_INFO.local.label ? total : 0,
                    'H.E': curr['Nguồn đặt'] === MERCHANT_INFO.he.label ? total : 0,
                };
            } else {
                acc[key]['Doanh thu trước KM'] += parseInt(curr['Tiền hàng'] || 0);
                acc[key]['Tổng khuyến mãi'] += parseInt(curr['Khuyến mãi'] || 0);
                acc[key]['Doanh thu sau KM'] += total;
                acc[key]['Grab'] += (curr['Nguồn đặt'] === MERCHANT_INFO.grab.label ? total : 0);
                acc[key]['Grab Mart'] += (curr['Nguồn đặt'] === MERCHANT_INFO.grab_mart.label ? total : 0);
                acc[key]['H.E'] += (curr['Nguồn đặt'] === MERCHANT_INFO.he.label ? total : 0);
                acc[key]['BE'] += (curr['Nguồn đặt'] === MERCHANT_INFO.be.label ? total : 0);
                acc[key]['Gojek'] += (curr['Nguồn đặt'] === MERCHANT_INFO.gojek.label ? total : 0);
                acc[key]['Shopee'] += (curr['Nguồn đặt'] === MERCHANT_INFO.shopee.label ? total : 0);
                acc[key]['Shopee Fresh'] += (curr['Nguồn đặt'] === MERCHANT_INFO.shopee_fresh.label ? total : 0);
                acc[key]['Shopee Ecom'] += (curr['Nguồn đặt'] === MERCHANT_INFO.shopee_ecom.label ? total : 0);
                acc[key]['Local'] += (curr['Nguồn đặt'] === MERCHANT_INFO.local.label ? total : 0);
            }
            return acc;
        }, {});
        sheet6 = _.toArray(sheet6_group_by_day).sort((a, b) => moment(a['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix() - moment(b['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix());

        const sheet7_group_by_day = sheet_items.reduce((acc, curr) => {
            const key = helper.text_slugify(curr['Ngày đặt hàng'] + '_' + curr['Brand bán hàng']);
            const total = parseInt(curr['Thành tiền sau KM'] || 0);
            if (!acc[key]) {
                acc[key] = {
                    'Ngày tháng năm giao hàng': curr['Ngày đặt hàng'],
                    'Brand bán hàng': curr['Brand bán hàng'],
                    'Doanh thu trước KM': parseInt(curr['Tiền hàng'] || 0),
                    'Tổng khuyến mãi': parseInt(curr['Khuyến mãi'] || 0),
                    'Doanh thu sau KM': total,
                };
            } else {
                acc[key]['Doanh thu trước KM'] += parseInt(curr['Tiền hàng'] || 0);
                acc[key]['Tổng khuyến mãi'] += parseInt(curr['Khuyến mãi'] || 0);
                acc[key]['Doanh thu sau KM'] += total;
            }
            return acc;
        }, {});
        sheet_brand_group_by_day = _.toArray(sheet7_group_by_day).sort((a, b) => moment(a['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix() - moment(b['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix());

        const sheet8_group_by_day = sheet_items.reduce((acc, curr) => {
            const key = helper.text_slugify(curr['Ngày đặt hàng'] + '_' + curr['Mã hub']);
            const total = parseInt(curr['Thành tiền sau KM'] || 0);
            if (!acc[key]) {
                acc[key] = {
                    'Ngày tháng năm giao hàng': curr['Ngày đặt hàng'],
                    'Hub bán hàng': curr['Hub bán hàng'],
                    'Mã hub': curr['Mã hub'],
                    'Doanh thu trước KM': parseInt(curr['Tiền hàng'] || 0),
                    'Tổng khuyến mãi': parseInt(curr['Khuyến mãi'] || 0),
                    'Doanh thu sau KM': total,
                };
            } else {
                acc[key]['Doanh thu trước KM'] += parseInt(curr['Tiền hàng'] || 0);
                acc[key]['Tổng khuyến mãi'] += parseInt(curr['Khuyến mãi'] || 0);
                acc[key]['Doanh thu sau KM'] += total;
            }
            return acc;
        }, {});
        sheet_hub_group_by_day = _.toArray(sheet8_group_by_day).sort((a, b) => moment(a['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix() - moment(b['Ngày tháng năm giao hàng'], 'DD-MM-YYYY').unix());
        for (const order_shipment of order_shipments) {
            const order = orders.find(v => v.order_id === order_shipment.order_id)
            let record = {
                'Mã tham chiếu': order.data_mapping.id,
                'Mã đơn hàng': order.data_mapping.order_id,
                'Nguồn đặt': MERCHANT_INFO[order.data_mapping.source]?.label || order.data_mapping.source,
                'ĐVVC': order_shipment.vendor,
                'Vận đơn': order_shipment.shipment_id,
                'Tiền ship ĐH': order.shipment.service?.price ?? 0,
                'Tiền ship thực tế': order_shipment.price,
                'Địa chỉ gửi': order_shipment.from_address,
                'Người gửi': order_shipment.from_name,
                'Địa chỉ nhận': order_shipment.to_address,
                'Người nhận': order_shipment.to_name,
                'Trạng thái đơn': order_shipment.status,
                'Ngày đặt': moment(order_shipment.created_at).format('DD-MM-YYYY'),
            }
            sheet_shipments.push(record);
        }

        console.log(_.sumBy(sheet_orders, v => v['Doanh thu sau khuyến mãi'] || 0))
        console.log(_.sumBy(sheet_items, v => v['Thành tiền sau KM'] || 0))
        const workbook = xlsx.utils.book_new();

        if (!order_report.report_sub_list) {
            order_report.report_sub_list = ['orders', 'items', 'stocks']
        }
        for (const sheet_name of order_report.report_sub_list) {
            switch (sheet_name) {
                case 'orders':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_orders), 'Bảng kê đơn hàng');
                    break;
                case 'items':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_items), 'DT theo đơn hàng và mặt hàng');
                    break;
                case 'stocks':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_stocks), 'Báo cáo xuất kho NVL');
                    break;
                case 'transactions':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_transactions), 'Mã giao dịch');
                    break;
                case 'stocks':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_stocks), 'Báo cáo xuất kho NVL');
                    break;
                case 'feedbacks':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_feedbacks), 'Đánh giá đơn hàng');
                    break;
                case 'shipments':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_shipments), 'Mã vận đơn');
                    break;
                case 'compare':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_compare), 'So sánh SL bán theo thời gian');
                    break;
                case 'gross_by_day':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_gross_by_day), 'TH Doanh thu theo ngày');
                    break;
                case 'gross':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet6), 'TH Doanh thu theo sàn');
                    break;
                case 'brand_group_by_day':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_brand_group_by_day), 'Doanh thu Brand theo ngày');
                    break;
                case 'hub_group_by_day':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_hub_group_by_day), 'Doanh thu CH theo ngày');
                    break;
                case 'accountant_orders':
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet_accountant_orders), 'Danh sách đơn hàng kế toán');
                    break;

                default:
                    break;
            }
        }

        const buff = xlsx.write(workbook, { type: 'buffer' });

        const file = await upload_file({ bucket: 'nexpos-files', key: file_key, buff })
        console.log(file)

        await UserNotification.create({
            user_id: created_by,
            type: 'success',
            title: 'Báo cáo đã tải xong',
            content: `Báo cáo đã tải xong. Vui lòng tải file đính kèm`,
            attachments: [file],
            topic: 'report',
        })
        order_report.file_url = file
        order_report.status = 'SUCCESS'
    } catch (error) {
        order_report.status = 'FAILED'
        order_report.fail_reason = error.message
        console.error(error)
    }
    order_report.completed_at_unix = moment().unix()
    await order_report.save()
}

module.exports = {
    generate_report_file
}