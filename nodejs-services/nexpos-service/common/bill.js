const cheerio = require('cheerio')
const moment = require('moment')
const axios = require('axios')
const ejs = require('ejs');
const fs = require('fs')
const { BrandBillConfig, Order, Site, Hub, Brand, WorkingShift, User } = require("../../.shared/database");
const _ = require('lodash');
const { MERCHANT_INFO } = require('../../.shared/const');

const get_bill_by_template = (name) => {
    if (!['bill_for_kitchen', 'bill_for_payment', 'bill_for_complete', 'bill_for_complete_app', 'label'].includes(name)) {
        return null
    }
    return fs.readFileSync('files/' + name + '.ejs', 'utf8')
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
}

const stringReplacer = (str, obj) => {
    str = str.replace(/\{([^}]+\|[^}]+)\}/g, (match, content) => {
        const [prefix, key] = content.split('|');
        return obj.hasOwnProperty(key) ? (obj[key] ? prefix + obj[key] : '') : match;
    });

    return str.replace(/\{([^}]+)\}/g, (match, key) => {
        return obj.hasOwnProperty(key) ? obj[key] : match;
    });
};

function findClosestMatch(element, name) {
    const children = element.children();
    for (let i = 0; i < children.length; i++) {
        const child = children.eq(i);
        const text = child.text();
        if (text.includes(name)) {
            return findClosestMatch(child, name);
        }
    }
    return element;
}

const render_bill_html = async ({ bill_type = 'bill_for_kitchen', order_id, more_data = {} }) => {
    const order = await Order.findOne({ order_id });
    const site = await Site.findById(order.site_id);
    const brand = await Brand.findById(site.brand_id);
    const hub = await Hub.findById(order.hub_id);
    const bill = await BrandBillConfig.findOne({ brand_id: site.brand_id, bill_type });
    let bill_html = bill?.content_html;
    if (!bill_html || bill?.active === false) {
        // bill_html = fs.readFileSync('files/bill_default.ckeditor.html', 'utf8');
        return null;
    }

    const replacement = {
        'bill_name': bill?.name || 'PHIẾU LÀM MÓN',
        'long_order_id': order.order_id,
        'short_order_id': order.data_mapping.order_id,
        'order_note': order.data_mapping.note || '',
        'site_name': site.name,
        'brand_name': brand.name,
        'order_source': MERCHANT_INFO[order.source]?.label || order.source,
        'hub_name': hub.name,
        'hub_phone': hub.phone || '',
        'order_created_at': moment(order.data_mapping.order_time).format('DD/MM/YYYY HH:mm:ss'),
        'order_pick_at': moment(order.data_mapping.pick_time).format('DD/MM/YYYY HH:mm:ss'),
        'order_delivery_at': order.shipment?.schedule?.from_time ?
            `${moment(order.shipment?.schedule?.from_date_time).format('DD/MM/YYYY HH:mm')}  - ${moment(order.shipment?.schedule?.to_date_time).format('HH:mm')}` :
            moment(order.data_mapping.delivery_time).format('DD/MM/YYYY HH:mm'),
        'customer_name': order.data_mapping.customer_name,
        'customer_address': order.data_mapping.customer_address,
        'order_sub_total': formatCurrency(order.data_mapping.total),
        'order_total_discount': formatCurrency(order.data_mapping.total_discount),
        'order_total_paid': formatCurrency(order.data_mapping.total_for_biz),
        'cancel_reason': order.data_mapping.cancel_reason || '',
        ...more_data
    };

    const $ = cheerio.load(bill_html);
    const dishTemplate = $('tr').filter((i, el) => $(el).text().includes('{dish_name}')).first();

    if (dishTemplate.length && Array.isArray(order.data_mapping.dishes)) {
        const dishesContainer = dishTemplate.parent();

        // Process each dish
        for (const dish of order.data_mapping.dishes) {
            const dishRow = dishTemplate.clone();

            const dishHTML = stringReplacer(dishRow.html(), {
                dish_name: dish.name || '',
                dish_description: dish.description || '',
                dish_quantity: dish.quantity || 0,
                dish_price: dish.discount_price !== dish.price ? formatCurrency(dish.price || 0) : '',
                dish_discount_price: formatCurrency(dish.discount_price || 0),
                dish_note: dish.note || '',
                dish_total: formatCurrency((dish.price || 0) * (dish.quantity || 1)),
                dish_final_price: formatCurrency((dish.discount_price || dish.price || 0) * (dish.quantity || 1))
            })

            dishRow.html(dishHTML);

            const optionsPlaceholder = findClosestMatch(dishRow, '{dish_options}');
            if (dish.options?.length > 0) {
                if (optionsPlaceholder.length) {
                    const optionsHtml = [];

                    const groupedOptions = dish.options.flat().reduce((acc, option) => {
                        if (!acc[option.option_name]) {
                            acc[option.option_name] = [];
                        }
                        acc[option.option_name].push(option);
                        return acc;
                    }, {});

                    for (const [groupName, options] of Object.entries(groupedOptions)) {
                        const groupHtml = `
                            <div class="option-group">
                                <p><span style="font-family:Arial, Helvetica, sans-serif;font-size:26px;"><i>${groupName}</i></span></p>
                                ${options.map(opt => `
                                    <p><span style="font-family:Arial, Helvetica, sans-serif;font-size:26px;"><i>- ${opt.quantity}x ${opt.option_item}${opt.price ? ` - ${formatCurrency(opt.price)}` : ''}</i></span></p>
                                `).join('\n')}
                            </div>
                        `;
                        optionsHtml.push(groupHtml);
                    }

                    optionsPlaceholder.html(optionsHtml.join(''));
                }
            } else {
                optionsPlaceholder.remove();
            }

            dishesContainer.append(dishRow);
        }

        dishTemplate.remove();
    }

    // Apply global replacements
    let updated_bill_html = $.html();
    updated_bill_html = stringReplacer(updated_bill_html, replacement);

    // Add necessary styles and classes
    updated_bill_html = updated_bill_html.replace(
        '<body>',
        `<body class="ck-content" style="width: ${bill?.bill_size ?? 590}px; background-color: white;">`
    );

    const ckeditorStyles = '<link rel="stylesheet" type="text/css" href="https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css">';
    updated_bill_html = updated_bill_html.replace('</head>', `${ckeditorStyles}</head>`);

    return updated_bill_html;
};

const render_label_html = async ({ bill_type = 'bill_for_label', order_id, dish_index, label_index, more_data = {}, size = '590px' }) => {
    const order = await Order.findOne({ order_id })
    const site = await Site.findById(order.site_id);
    const hub = await Hub.findById(order.hub_id);
    const bill = await BrandBillConfig.findOne({ brand_id: site.brand_id, bill_type });
    let bill_html = bill?.content_html;
    if (!bill_html || bill?.active === false) {
        // bill_html = fs.readFileSync('files/label_default.ckeditor.html', 'utf8')
        return null;
    }

    const dish = order.data_mapping.dishes[dish_index];
    const label_index_per_total = _.sumBy(order.data_mapping.dishes.slice(0, dish_index), 'quantity') + label_index + 1;

    const replacement = {
        'bill_name': bill?.name || 'NHÃN ĐƠN HÀNG',
        'long_order_id': order.order_id,
        'short_order_id': order.data_mapping.order_id,
        'order_note': order.data_mapping.note || '',
        'order_source': MERCHANT_INFO[order.source]?.label || order.source,
        'site_name': site.name,
        'hub_name': hub.name,
        'hub_phone': hub.phone || '',
        'order_created_at': moment(order.data_mapping.created_at).format('DD/MM/YYYY HH:mm:ss'),
        'customer_name': order.data_mapping.customer_name,
        'customer_address': order.data_mapping.customer_address,
        'order_sub_total': formatCurrency(order.data_mapping.total),
        'order_total_discount': formatCurrency(order.data_mapping.total_discount),
        'order_total_paid': formatCurrency(order.data_mapping.total_for_biz),
        'dish_name': dish.name,
        'dish_description': dish.description,
        'dish_quantity': dish.quantity,
        'dish_price': formatCurrency(dish.price),
        'dish_discount_price': formatCurrency(dish.discount_price),
        'dish_note': dish.note || '',
        'label_index': `${label_index_per_total}/${_.sumBy(order.data_mapping.dishes, 'quantity')}`,
    };

    for (const [key, value] of Object.entries(more_data)) {
        replacement[`{${key}}`] = value;
    }

    bill_html = Object.entries(replacement).reduce((html, [key, value]) =>
        html.replace(new RegExp(`{${key}}`, 'g'), value),
        bill_html
    );

    const $ = cheerio.load(bill_html);


    const firstMatchOptionItem = $('*').filter(function () {
        return $(this).text().includes('{option_item}');
    }).first();

    const optionTemplate = findClosestMatch(firstMatchOptionItem, '{option_item}')
    if (optionTemplate.length && dish?.options) {
        const parentTd = optionTemplate.parent();
        optionTemplate.remove();

        const groupedOptions = {};
        dish.options.forEach(optionGroup => {
            optionGroup.forEach(topping => {
                if (!groupedOptions[topping.option_name]) {
                    groupedOptions[topping.option_name] = [];
                }
                groupedOptions[topping.option_name].push(topping);
            });
        });

        const optionsHtmls = []
        Object.entries(groupedOptions).forEach(([optionName, toppings]) => {
            const optionsHtml = toppings.map(topping => {
                const newH4 = optionTemplate.clone();
                const h4Html = newH4.html()
                    .replace(/{option_name}/g, optionName)
                    .replace(/{option_item}/g, topping.option_item || '')
                    .replace(/{option_quantity}/g, topping.quantity ? ` x${topping.quantity}` : '')
                    .replace(/{option_price}/g, topping.price ? ` - ${formatCurrency(topping.price)}` : '');
                newH4.html(h4Html);
                return newH4;
            });
            optionsHtmls.push(optionsHtml);
        });
        parentTd.append(optionsHtmls.join('<br>'));
    }

    let updated_bill_html = $.html();
    updated_bill_html = updated_bill_html.replace('<body>', `<body class="ck-content" style="width: ${size}; height: ${240}px; background-color: white;">`);
    const ckeditorStyles = '<link rel="stylesheet" type="text/css" href="https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css">';
    updated_bill_html = updated_bill_html.replace('</head>', `${ckeditorStyles}</head>`);
    return updated_bill_html;
};

const render_shift_html = async ({ bill_type = 'bill_for_shift', shift_id, more_data = {}, size = '590px' }) => {
    const working_shift = await WorkingShift.findById(shift_id);

    const template = fs.readFileSync('files/bill_for_shift.ejs', 'utf8')
    const bill_html = ejs.compile(template)(working_shift.report)
    const file = await html_to_image(working_shift._id, bill_html)
    return file
};

const render_shift_with_payment_html = async ({ bill_type = 'bill_for_shift_with_payment', shift_id, more_data = {}, size = '590px' }) => {
    const working_shift = await WorkingShift.findById(shift_id);

    const template = fs.readFileSync('files/bill_for_shift_with_payment.ejs', 'utf8')
    const bill_html = ejs.compile(template)(working_shift.report)
    const file = await html_to_image(working_shift._id, bill_html)
    return file
};


const html_to_image = async (key, html) => {
    const resp = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://tools.nexpos.io/convert',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
            order_code: key,
            html_string: html
        }
    })
    return resp.data
}

module.exports = {
    formatCurrency,
    get_bill_by_template,
    render_bill_html,
    render_label_html,
    render_shift_html,
    render_shift_with_payment_html,
    html_to_image,
}