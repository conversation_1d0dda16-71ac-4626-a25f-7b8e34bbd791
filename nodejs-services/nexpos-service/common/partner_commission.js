const { Order, Site, BrandCommission, Campaign, Partner, User, BrandMenu, ReferralCommission } = require('../../.shared/database')
const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok')
const _ = require('lodash')

const get_data_for_calculation = async (user, month, year, brand_id) => {
  const site_ids = user.sites
  const sites = await Site.find({
    _id: { $in: site_ids },
    apply_commission: true,
    type: 'partner',
    brand_id,
  })

  if (!sites.length) {
    return { sites: [], brand_configs: [], orders: [], user }
  }

  const start_of_month = moment({ year, month: month - 1 })
    .startOf('month')
    .unix() // moment month from 0-11

  const end_of_month = moment({ year, month: month - 1 })
    .endOf('month')
    .unix()

  const orders = await Order.find(
    {
      site_id: { $in: sites.map((s) => String(s._id)) },
      status: 'FINISH',
      source: 'he',
      'data_mapping.delivery_time_unix': {
        $gte: start_of_month,
        $lte: end_of_month,
      },
    },
    {
      user_id: 1,
      he_id: 1,
      'data_mapping.delivery_time': 1,
      'data_mapping.delivery_time_unix': 1,
      'data': 1,
      order_id: 1,
      site_id: 1,
      _id: 0,
    }
  )

  const brand_menu = await BrandMenu.findOne({ brand_id }).lean()
  // flatten brand item, and sub-category items
  const flatten_brand_menu = brand_menu.categories.reduce(
    (acc, cur) => [
      ...acc,
      ...cur.items.map((i) => ({
        ...i,
        category_name: cur.name,
        ...(i.sub_categories || []).reduce((acc, subCat) => [...acc, ...subCat.items.map((i) => ({ ...i, category_name: cur.name }))], []),
      })),
    ],
    []
  )

  const map_orders = orders.filter(o=>o.data?.dishes).map((order) => {
    const dishes = order.data.dishes.map((dish) => {
      const brand_item = flatten_brand_menu.find((i) => i.name === dish.name && i.category_name === dish.category_name)
      return {
        ...dish,
        brand_price: brand_item?.price || 0,
      }
    })

    return {
      ...order.toObject(),
      data: {
        ...order.data,
        ...order.data_mapping,
        dishes,
      },
    }
  })

  console.log('orders', map_orders)
  return { sites, orders: map_orders }
}

const calculate_total_sale = (orders) => {
  const flatten_items = orders.reduce((acc, cur) => [...acc, ...cur.data.dishes], []).filter((i) => !i.is_gift)
  let total_sale = 0
  flatten_items.forEach((item) => {
    if (item.brand_price) {
      total_sale = total_sale + item.brand_price * item.quantity
    }
  })

  return total_sale
}

const calculate_campaign_commission = async ({ user, brand_id, orders }) => {
  // campaign
  const campaigns = await Campaign.find({ brand_id, partners: { $elemMatch: { user_id: String(user._id) } } }).lean()

  const commission_by_items = {}
  campaigns.forEach((campaign) => {
    // const start_date = moment(campaign.start_date).unix()
    const end_date = moment(campaign.end_date).unix()
    const joined_date = moment(campaign.partners.find((p) => String(p.user_id) === String(user._id)).joined_date).unix()

    const config = campaign.items.reduce((acc, cur) => ({ ...acc, [`${cur.category_name}__${cur.item_name}`]: cur }), {})
    // filter orders finish within campaign durations
    const valid_orders = orders.filter((o) => o.data.delivery_time_unix >= joined_date && o.data.delivery_time_unix < end_date)

    // flatten dishes and get the one being configured only
    const items_to_calculate = valid_orders.reduce((acc, cur) => [...acc, ...cur.data.dishes], []).filter((item) => config[`${item.category_name}__${item.name}`])

    const sale_by_items = items_to_calculate.reduce((acc, cur) => {
      const brand_price = cur.brand_price || 0
      return { ...acc, [`${cur.category_name}__${cur.name}`]: (acc[`${cur.category_name}__${cur.name}`] || 0) + brand_price * cur.quantity }
    }, {})

    campaign.items.forEach((item) => {
      // get the commission rule that require biggest sale
      const applied_commission = item.commission?.sort((a, b) => (a.sale_above > b.sale_above ? -1 : 1))?.[0]

      const key = `${item.category_name}__${item.item_name}`
      if (applied_commission && sale_by_items[key] && sale_by_items[key] >= applied_commission.sale_above) {
        const commission = (sale_by_items[key] * applied_commission.percentage) / 100
        // because item commission can be duplicated, get the biggest commission
        if (!commission_by_items[key] || commission_by_items[key] < commission) {
          commission_by_items[key] = { ...item, commission: Math.floor(commission) }
        }
      }
    })
  })

  const campaign_commission = _.sumBy(Object.values(commission_by_items), 'commission')
  return { campaign_commission, commission_by_items: Object.values(commission_by_items), campaigns }
}

const calculate_personal_commission = async ({ brand_configs, orders }) => {
  const brand_config = brand_configs.find((c) => c.type === 'personal')
  const total_sale = calculate_total_sale(orders)

  // calculate personal commission, a brand has at most 1 personal config and 1 team config active
  const sorted_config = brand_config ? _.orderBy(brand_config.personal, 'min_sale', 'desc') : []

  const applied_config = sorted_config.find((c) => c.min_sale < total_sale)
  const percentage = applied_config?.bonus || 0

  const personal_commission = Math.floor((total_sale * percentage) / 100)

  return { total_sale, personal_commission, applied_config }
}

const calculate_team_sales = (member, member_sales) => {
  const sale = member_sales.find((m) => m.user_id === member.user_id)?.total_sale || 0
  if (member.members.length) {
    return member.members.reduce((acc, cur) => acc + calculate_team_sales(cur, member_sales), sale)
  }
  return sale
}

const calculate_team_commission = async ({ user, month, year, brand_id, brand_configs, total_sale }) => {
  const members = await User.find({ 'he_info.referrer_id': String(user._id), approval_status: 'approved' })

  const team_config = brand_configs.find((c) => c.type === 'team')?.team
  if (!team_config) {
    return { commission: 0, members: members.map(m=>({
      _id: String(m._id),
      total_sale: 0
    })), total_sale: 0 }
  }

  const { min_sale_personal, min_team_size } = team_config
  if (total_sale < min_sale_personal || members.length < min_team_size) {
    return { commission: 0, members: members.map(m => ({
      _id: String(m._id),
      total_sale: 0
    })), total_sale: 0 }
  }

  // calculate sale by each member
  const member_sales = await Promise.all(
    members.map(async (m) => {
      const { orders } = await get_data_for_calculation(m, month, year, brand_id)
      const total_sale = calculate_total_sale(orders)
      return { user_id: String(m._id), total_sale }
    })
  )

  const team_sales = member_sales.reduce((acc, cur) => acc + cur.total_sale, 0)
  const applied_rule = _.maxBy(
    team_config.scales.filter((r) => team_sales >= r.min_sale_team),
    'min_sale_team'
  )
  const team_commission = applied_rule ? Math.floor((team_sales * applied_rule.bonus) / 100) : 0

  const member_with_sales = members.map((m) => {
    const sale = member_sales.find((s) => s.user_id === String(m._id))?.total_sale || 0
    return { _id: m._id, total_sale: sale }
  })
  
  return { commission: team_commission, members: member_with_sales, total_sale: team_sales }
}

const calculate_new_customer_commission = async ({ brand_configs, orders, user, brand_id, month, year }) => {
  const config = brand_configs.find((c) => c.type === 'new_customer' && c.brand_id === brand_id)?.new_customer

  const first_orders = await Order.find(
    {
      he_id: String(user._id),
      'data_mapping.delivery_time_unix': {
        $gte: moment({ year, month: month - 1 })
          .startOf('month')
          .unix(),
        $lte: moment({ year, month: month - 1 })
          .endOf('month')
          .unix(),
      },
      status: 'FINISH',
      is_first_order: true,
    },
    { user_id: 1, _id: 0 }
  )
  const ids = first_orders.map((o) => o.user_id).filter((u) => !!u)
  const registered_users = await User.find({ _id: { $in: ids }, is_guest: false }, { _id: 1 })

  return {
    new_customer: registered_users.length,
    new_customer_ids: registered_users.map((u) => String(u._id)),
    commission: registered_users.length * (config?.bonus || 0),
  }
}

const calculate_diff_earning = async (orders) => {
  const flatten_items = orders.reduce((acc, cur) => [...acc, ...cur.data.dishes], []).filter((i) => !i.is_gift)
  let diff_earning = 0
  flatten_items.forEach((item) => {
    if (item.brand_price && item.price) {
      diff_earning = diff_earning + (item.price - item.brand_price * item.quantity)
    }
  })

  return diff_earning
}

const calculate_referral_commission = async ({ user, brand_configs, brand_id }) => {
  const referral_config = brand_configs.find((c) => c.type === 'referral')?.referral
  const members = await User.find({ 'he_info.referrer_id': String(user._id), 'he_info.status': 'active' })
  if (!referral_config) {
    return { total_commission: 0, pending_commission: 0, members }
  }

  const referral_commissions = await ReferralCommission.find({
    referrer_id: String(user._id),
    brand_id,
    referee_id: { $in: members.map((m) => String(m._id)) },
  }).lean()

  for (const member of members) {
    const { first_order, first_month, next_n_months } = referral_config
    const existing = referral_commissions.find((c) => c.referee_id === String(member._id))

    const new_commission = Object.assign(
      {
        referrer_id: String(user._id),
        referee_id: String(member._id),
        brand_id,
        commissions: [],
      },
      existing
    )

    if (first_order) {
      const need_cal = !existing || !existing.commissions.find((c) => c.type === 'first_order')
      if (need_cal) {
        const { bonus, min_order_value } = first_order
        const order = await Order.findOne({
          he_id: String(member._id),
          status: 'FINISH',
          'data.total': { $gte: min_order_value },
        }, { order_id: 1, 'data_mapping.delivery_time': 1})
        order &&
          new_commission.commissions.push({
            type: 'first_order',
            amount: bonus,
            order_ids: [order.order_id],
            config: first_order,
            status: 'pending',
            redeem_on_month: moment(order.data_mapping.delivery_time).month() + 1,
            redeem_on_year: moment(order.data_mapping.delivery_time).year(),
          })
      }
    }

    if (first_month) {
      const need_cal = !existing || !existing.commissions.find((c) => c.type === 'first_month')
      if (need_cal) {
        const { bonus, min_no_of_orders, min_order_value } = first_month
        const start_time = moment(member.he_info.approved_at).startOf('month').unix()
        const end_time = moment(member.he_info.approved_at).add(30, 'days').unix()

        if (end_time > moment().unix()) {
          const orders = await Order.find({
            he_id: String(member._id),
            status: 'FINISH',
            'data_mapping.total': { $gte: min_order_value },
            'data_mapping.delivery_time_unix': {
              $gte: start_time,
              $lte: end_time,
            },
          }, { order_id: 1, 'data_mapping.delivery_time': 1})

          if (orders.length >= min_no_of_orders) {
            new_commission.commissions.push({
              type: 'first_month',
              amount: bonus,
              orders: orders.map((o) => o.order_id),
              config: first_month,
              status: 'pending',
              redeem_on_month: moment(orders[0].data_mapping.delivery_time).month() + 1,
              redeem_on_year: moment(orders[0].data_mapping.delivery_time).year(),
            })
          }
        }
      }
    }

    if (next_n_months) {
      const need_cal = !existing || !existing.commissions.find((c) => c.type === 'next_n_months')
      if (need_cal) {
        const { bonus, n, min_no_of_orders, min_order_value } = next_n_months

        const start_time = moment(member.he_info.approved_at).startOf('month').unix()
        const end_time = moment(member.he_info.approved_at).add(n, 'months').unix()

        if (end_time > moment().unix()) {
          const orders = await Order.find({
            he_id: String(member._id),
            status: 'FINISH',
            'data_mapping.total': { $gte: min_order_value },
            'data_mapping.delivery_time_unix': {
              $gte: start_time,
              $lte: end_time,
            },
          }, { order_id: 1, 'data_mapping.delivery_time': 1 }, {
            sort: { 'data_mapping.delivery_time': 1 }
          }).lean()

          const orders_by_month = _.groupBy(orders, (o) => moment(o.data_mapping.delivery_time).month())
          
          // user receive bonus if they have min_no_of_orders in every month, the bonus will redeem on the last month
          // valid if they have orders in every month in n months
          const is_valid = Object.keys(orders_by_month).length === n && Object.values(orders_by_month).every((o) => o.length >= min_no_of_orders)
        
          if (is_valid) {
            const last_order = orders.at(-1)
            new_commission.commissions.push({
              type: 'next_n_months',
              amount: bonus,
              orders: orders.map((o) => o.order_id),
              config: next_n_months,
              status: 'pending',
              redeem_on_month: moment(last_order.data_mapping.delivery_time).month() + 1,
              redeem_on_year: moment(last_order.data_mapping.delivery_time).year(),
            })
          }
        }
      }
    }
    await ReferralCommission.findOneAndUpdate({ referrer_id: String(user._id), referee_id: String(member._id) }, new_commission, { upsert: true })
  }

  const latest = await ReferralCommission.find({ referrer_id: String(user._id), brand_id }).lean()

  const total_commission = latest.reduce((acc, cur) => acc + cur.commissions.reduce((acc, cur) => acc + cur.amount, 0), 0) || 0
  const pending_commission = latest.reduce((acc, cur) => acc + cur.commissions.filter((c) => c.status === 'pending').reduce((acc, cur) => acc + cur.amount, 0), 0) || 0

  return {
    total_commission,
    pending_commission,
    members: members.map((m) => ({ 
      _id: String(m._id), 
      name: m.name, 
      email: m.email,
      phone: m.phone,
      commissions: latest.find((l) => l.referee_id === String(m._id))?.commissions || [] 
    })),
  }
}

// only personal
module.exports.calculate_commissions = async (users, month, year, brand_id) => {
  const result = {}

  const brand_configs = await BrandCommission.find({ status: 'active', brand_id })

  for (const user of users) {
    const data = await get_data_for_calculation(user, month, year, brand_id) 
    const personal = await calculate_personal_commission({ brand_configs, orders: data.orders })
    
    result[String(user._id)] = { 
      total_sale: personal.total_sale,
      commission: personal.personal_commission,
      total_order: data.orders.length
    }
  }

  return result
}

module.exports.calculate_partner_commission_by_brand = async (user, month, year, brand_id) => {
  const data = await get_data_for_calculation(user, month, year, brand_id)

  const { orders } = data

  const brand_configs = await BrandCommission.find({ status: 'active', brand_id })

  const personal = await calculate_personal_commission({ orders, user, brand_id, brand_configs })
  const team = await calculate_team_commission({ user, month, year, brand_id, brand_configs, total_sale: personal.total_sale })
  const campaign = await calculate_campaign_commission({ user, month, year, brand_id, orders })
  const new_customer = await calculate_new_customer_commission({ brand_configs, orders, user, brand_id, month, year })
  const referral = await calculate_referral_commission({ user, brand_configs, brand_id })

  const team_commission = team?.commission || 0
  const personal_commission = personal.personal_commission || 0
  const campaign_commission = campaign.campaign_commission || 0
  const diff_earning = (await calculate_diff_earning(orders)) || 0
  // referral commission for this month
  const referral_commission = referral.members.reduce(
    (acc, cur) => acc + cur.commissions.filter((c) => c.redeem_on_month === month && c.redeem_on_year === year).reduce((acc, cur) => acc + cur.bonus, 0),
    0
  )

  return {
    personal: {
      total_sale: personal.total_sale,
      commission: personal_commission,
      total_orders: orders.length,
    },
    new_customer: {
      commission: new_customer.commission,
      new_customer: new_customer.new_customer,
      new_customer_ids: new_customer.new_customer_ids,
    },
    referral,
    team: { commission: team_commission, sales: team.total_sale, members: team.members },
    campaign: { commission: campaign_commission, commission_by_items: campaign.commission_by_items },
    total_income: team_commission + personal_commission + campaign_commission + diff_earning + new_customer.commission + referral_commission || 0,
    diff_earning,
    orders,
    brand_configs,
    campaigns: campaign.campaigns,
  }
}


module.exports.default_commission = async (user, month, year, brand_id) => {
  return {
    personal: {
      total_sale: 0,
      commission: 0,
      total_orders: 0,
    },
    new_customer: {
      commission: 0,
      new_customer: 0,
      new_customer_ids: [],
    },
    referral: {
      total_commission: 0,
      pending_commission: 0,
      members: [],
    },
    team: { commission: 0, sales: 0, members: [] },
    campaign: { commission: 0, commission_by_items: [] },
    total_income: 0,
    diff_earning: 0,
    orders: [],
    brand_configs: [],
    campaigns: [],
  }
}