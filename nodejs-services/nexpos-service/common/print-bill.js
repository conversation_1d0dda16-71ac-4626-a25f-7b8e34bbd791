const axios = require('axios')
const _ = require('lodash')
const fs = require('fs')
const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok')

const bill_templates = {
  bill_for_kitchen: 'bill_for_kitchen',
  bill_for_payment: 'bill_for_payment',
  bill_for_complete: 'bill_for_complete',
  bill_for_complete_app: 'bill_for_complete_app',
  label: 'label',
}

const getBillTemplate = (name) => {
  if (!Object.values(bill_templates).includes(name)) {
    return null
  }
  return fs.readFileSync('files/' + name + '.ejs', 'utf8')
}

const print_bill = async (template, order, site = {}, hub = {}) => {
  const params = {
    site,
    hub,
    order: order.data_mapping,
    data: {
      total_paid: _.sumBy(order.data_mapping?.payments || [], 'total'),
      order_count_message: '',
      status: order.status,
      created_at: moment(order.data_mapping.order_time).format('DD-MM-YYYY hh:mm:ss A'),
      printed_at: moment().format('DD-MM-YYYY hh:mm:ss A'),
      pick_time: order.shipment?.schedule?.from_time ? `${moment(order.shipment?.schedule?.from_date_time).format('DD/MM HH:mm')}  - ${order.shipment?.schedule?.to_time}` : '',
      qrcode: '',
    },
  }

  const html_preview = _.template(getBillTemplate(template))(params)
  try {
    const response = await axios({
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://tools.nexpos.io/convert',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: {
        order_code: order.order_id,
        html_string: html_preview,
      },
    })
    return response.data
  } catch (error) {
    console.error('Error printing bill:', error.message)
    throw error
  }
}

module.exports = { print_bill, bill_templates }
