const { SiteMenu, SiteMenuCategory, SiteMenuItem, SiteMenuOption, SiteMenuOptionItem, CoreProduct, Site } = require('../../.shared/database')

const build_combo_from_core_product = async (brand_id) => {
  const core_products = await CoreProduct.find({
    brand_id,
  })

  const map_by_code = core_products.reduce((acc, cp) => {
    acc[cp.code] = cp
    return acc
  }, {})

  const result = {}
  for (const core_product of core_products) {
    if (core_product.ingredients?.length) {
      const combo = core_product.ingredients
        .map((i) => {
          const item = map_by_code[i.code]
          if (!item) {
            return null
          }

          return {
            code: item.code,
            name: item.name,
            price: item.sale_price || item.price || 0,
            unit: i.unit,
            quantity: i.amount,
            _id: item._id,
          }
        })
        .filter((i) => i)
      result[core_product.code] = combo
    } else {
      result[core_product.code] = [{
        code: core_product.code,
        name: core_product.name,
        price: core_product.sale_price || core_product.price || 0,
        unit: core_product.unit,
        quantity: 1,
        _id: core_product._id,
      }]
    }
  }
  return result
}

const build_menu = async function (site_id, { active } = {}) {
  const active_query = active != null ? { active } : {}
  const site_menu = await SiteMenu.findOneAndUpdate({ site_id }, { site_id }, { upsert: true, new: true })

  const categories = await SiteMenuCategory.find({ site_id, ...active_query }).lean()
  const items = await SiteMenuItem.find({ site_id, ...active_query }).lean()
  const options = await SiteMenuOption.find({ site_id, ...active_query }).lean()
  const option_items = await SiteMenuOptionItem.find({ site_id, ...active_query }).lean()

  const site = await Site.findOne({ _id: site_id }, { brand_id: 1 })
  const core_products_combo = await build_combo_from_core_product(site.brand_id)

  const main_categories = categories.filter((c) => !c.parent_id)
  const itemCategoryMap = items
    .filter((i) => i.category_id)
    .reduce((acc, i) => {
      acc[i.category_id] = acc[i.category_id] || []
      acc[i.category_id].push(i)
      return acc
    }, {})

  const menu = {
    modified_on: site_menu?.modified_on,
    channels: site_menu?.channels,
    categories: main_categories.map((c) => {
      const sub_categories = categories.filter((sc) => sc.parent_id === String(c._id))
      return {
        ...c,
        items:
          itemCategoryMap[c._id]?.map((i) => {
            const combo = core_products_combo[i.code]
            return {
              ...i,
              combo,
            }
          }) || [],
        sub_categories: sub_categories.map((sc) => {
          const items =
            itemCategoryMap[sc._id].map((i) => {
              const combo = core_products_combo[i.code]
              return {
                ...i,
                combo,
              }
            }) || []

          return {
            ...sc,
            items,
          }
        }),
      }
    }),
    option_categories: options.map((o) => {
      const opt_items = option_items.filter((oi) => oi.option_id === String(o._id)).map((oi) => {
        const combo = core_products_combo[oi.code] || []
        return {
          ...oi,
          combo,
          id: oi._id.toString(),
        }
      })

      const linked_items = o.item_ids
        .filter(Boolean)
        .map((item_id) => {
          const item = items.find((i) => i._id.toString() === item_id.toString())
          if (!item) {
            return null
          }
          return {
            _id: item._id,
            images: item.images,
            name: item.name,
            price: item.price,
            code: item.code,
            order: item.order,
            combo: core_products_combo[item.code],
          }
        })
        .filter(Boolean)

      return {
        ...o,
        option_items: opt_items,
        linked_items,
      }
    }),
  }

  return menu
}

const get_stock_for_cp_item = (item, hub_stocks, all_items) => {
  const ingredients = item.ingredients?.length ? item.ingredients : [{ code: item.code, amount: 1 }]
  let min_stock = Infinity
  for (const ingredient of ingredients) {
    const ingredient_item = all_items.find((i) => i.code == ingredient.code)
    if (!ingredient_item) {
      min_stock = 0
      break
    }

    if (ingredient_item.quantity_unlimited) {
      continue
    }

    const hub_stock = hub_stocks.find((h) => h.code == ingredient.code)
    if (!hub_stock) {
      min_stock = 0
      break
    }

    if (hub_stock.locked_status == 'alway_inactive') {
      min_stock = 0
      break
    }

    if (hub_stock.locked_status == 'alway_active') {
      continue
    }

    min_stock = Math.min(min_stock, hub_stock.quantity / ingredient.amount)
  }

  let result = {}
  if (min_stock === Infinity) {
    result.quantity_unlimited = true
  } else {
    result.quantity = min_stock
    result.quantity_unlimited = false
  }

  return result
}

module.exports = {
  build_menu,
  get_stock_for_cp_item,
}
