const { Site, Brand, Hub } = require("../../.shared/database");
const _ = require('lodash');

const GET_ALL = '*';

const get_order_list_filter = async (req) => {
    const { filter_type, hub_ids, brand_ids, site_ids, filter_name } = req.query
    // filter_name is only valid when GET_ALL, which means get all hubs/bands/sites satisfy the filter_name
    // if not GET_ALL, use site_ids, hub_ids, brand_ids

    const selectors = req.role.selectors
    const is_system = req.permissions?.includes('system')
    if (filter_type == 'hub') {
        let selected_hub_ids = []
        if (hub_ids === GET_ALL) {
            selected_hub_ids = req.user.hubs
        } else if (hub_ids && hub_ids !== GET_ALL) {
            selected_hub_ids = hub_ids
        } else if (is_system && !filter_name) {
            selected_hub_ids = []
        } else if (is_system && filter_name) {
            const hubs = await Hub.find({ name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            selected_hub_ids = hubs.map(v => String(v._id))
        } else if (!is_system && !filter_name) {
            selected_hub_ids = []
        } else if (!is_system && filter_name) {
            const hubs = await Hub.find({ _id: req.user.hubs, name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            selected_hub_ids = hubs.map(v => String(v._id))
        }
        selected_hub_ids = selected_hub_ids.filter(v => req.user.hubs.includes(v))

        const filter = { hub_id: selected_hub_ids }
        if (selectors?.includes('brand')) {
            const sites = await Site.find({ brand_id: req.user.brands }, { _id: 1 }).lean()
            filter.site_id = sites.map(v => String(v._id))
        }
        if (selectors?.includes('site')) {
            const sites = await Site.find({ _id: req.user.sites }, { _id: 1 }).lean()
            filter.site_id = sites.map(v => String(v._id))
        }
        return filter
    }

    if (filter_type == 'brand') {
        if (brand_ids !== GET_ALL) {
            const sites = await Site.find({ brand_id: brand_ids }, { _id: 1 })
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }

        // get all, system user
        if (is_system && !filter_name) {
            return {}
        }

        if (is_system && filter_name) {
            const brands = await Brand.find({ name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            const sites = await Site.find({ brand_id: brands.map(b => String(b._id)) }, { _id: 1 }).lean()
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }

        // get all, but not user system
        if (!is_system && !filter_name) {
            const sites = await Site.find({ brand_id: req.user.brands })
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }

        if (!is_system && filter_name) {
            const sites = await Site.find({ brand_id: { $in: req.user.brands }, name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }
    }

    if (filter_type == 'site') {
        if (site_ids && site_ids !== GET_ALL) {
            // return site_ids
            return { site_id: site_ids }
        }

        // get all, system user
        if (is_system && !filter_name) {
            return {}
        }

        if (is_system && filter_name) {
            const sites = await Site.find({ name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }

        // get all, but not user system
        if (!is_system && !filter_name) {
            // return req.user.sites
            return { site_id: req.user.sites }
        }

        if (!is_system && filter_name) {
            const sites = await Site.find({ _id: { $in: req.user.sites }, name: new RegExp(filter_name, 'i') }, { _id: 1 }).lean()
            // return sites.map(v => String(v._id))
            return { site_id: sites.map(v => String(v._id)) }
        }
    }
    return { site_id: [] }
}

const get_order_list_filter_v2 = async (req) => {
    let { filter_type, hub_ids, brand_ids, site_ids } = req.query

    if (hub_ids === '*') { hub_ids = req.user.hubs }
    if (brand_ids === '*') { brand_ids = req.user.brands }
    if (site_ids === '*') { site_ids = req.user.sites }

    const selectors = req.role.selectors
    if (selectors?.includes('hub')) {
        hub_ids = _.intersection(hub_ids, req.user.hubs)
    }
    if (selectors?.includes('brand')) {
        brand_ids = _.intersection(brand_ids, req.user.brands)
    }
    if (selectors?.includes('site')) {
        site_ids = _.intersection(site_ids, req.user.sites)
    }


    if (filter_type === 'hub') {
        return { hub_id: hub_ids }
    }

    if (filter_type === 'brand') {
        let sites = await Site.find({ brand_id: brand_ids }, { _id: 1, hub_id: 1, brand_id: 1 }).lean()
        if (selectors?.includes('hub')) {
            sites = sites.filter(v => req.user.hubs.includes(v.hub_id))
        }
        if (selectors?.includes('site')) {
            sites = sites.filter(v => req.user.sites.includes(v._id))
        }
        return { site_id: sites.map(v => String(v._id)) }
    }

    if (filter_type === 'site') {
        return { site_id: site_ids }
    }

    return { site_id: [] }
}

module.exports = {
    get_order_list_filter,
    get_order_list_filter_v2,
}