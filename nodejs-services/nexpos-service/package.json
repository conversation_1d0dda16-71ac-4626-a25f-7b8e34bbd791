{"name": "api", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"build": "webpack --config webpack.config.js", "start": "node dist/bundle.js", "test": "echo \"Error: no test specified\" && exit 1", "dev": "NODE_ENV=dev node app.js", "local": "NODE_ENV=dev nodemon app.js", "stag": "NODE_ENV=stag node app.js"}, "author": "", "license": "ISC", "dependencies": {"@google-cloud/pubsub": "^3.7.1", "@google-cloud/storage": "^6.11.0", "aws-sdk": "^2.1369.0", "axios": "^1.3.4", "axios-curlirize": "^1.3.7", "bcrypt": "^5.1.0", "cheerio": "^1.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.1.1", "csvtojson": "^2.0.10", "datadog-winston": "^1.6.0", "dotenv": "^16.0.3", "ejs": "^3.1.9", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-useragent": "^1.0.15", "firebase-admin": "^12.5.0", "jimp": "^0.22.8", "js-yaml": "^4.1.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.0", "libphonenumber-js": "^1.10.49", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongodb": "^5.1.0", "mongoose": "^7.0.3", "mongoose-sequence": "^6.0.1", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.6", "slugify": "^1.6.6", "uuid": "^9.0.0", "validator": "^13.9.0", "winston": "^3.13.0", "xlsx": "^0.18.5", "xlsx-template": "^1.4.3"}, "devDependencies": {"@babel/preset-env": "^7.25.4", "babel-loader": "^9.1.3", "eslint": "^8.52.0", "nodemon": "^2.0.22", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}}