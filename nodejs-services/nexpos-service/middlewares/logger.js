const logger = require('../../.shared/logger')

const logger_request = async (req, res, next) => {
    const token = req.headers['x-access-token'];
    let username = ''
    if (token && token.split('.').length > 1) {
        const token_data = Buffer.from(token.split('.')[1], 'base64').toString('utf-8');
        username = JSON.parse(token_data).username
    }
    if (process.env.NODE_ENV === 'prod') {
        logger.info(JSON.stringify({
            method: req.method,
            url: req.url,
            headers: req.headers,
            username,
            body: req.body,
        }), { tag: 'API_REQUEST' })
    }

    next()
}

const logger_response = async (req, res, next) => {
    next()
}

const my_logger = (option) => {
    if (option?.s) {
        return logger_response
    }
    return logger_request
}
module.exports = my_logger 