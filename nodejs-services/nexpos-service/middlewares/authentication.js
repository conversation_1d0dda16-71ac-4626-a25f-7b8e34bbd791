const jwt = require('jsonwebtoken');
const { User, Role, PartnerAP<PERSON><PERSON><PERSON>, AppVersion } = require('../../.shared/database');
const redis = require('../../.shared/redis');
const crypto = require('crypto');

exports.required = (permissions, skip_logger = false) => async (req, res, next) => {
  if (!permissions)
    permissions = [];
  if (!Array.isArray(permissions))
    permissions = [permissions];

  const token = req.cookies?.[process.env.COOKIE_TOKEN_KEY] ?? req.headers?.['x-access-token'];
  if (!token) {
    return res.status(401).send('Access denied. No token provided.');
  }

  if (permissions.includes('internal')) {
    if (req.headers['x-access-token'] === process.env.INTERNAL_API_KEY) {
      next();
    } else {
      res.status(401).send('invalid_token.');
    }
    return;
  }

  try {
    const { username } = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findOne({ username }).lean();
    if (!user) {
      return res.status(401).send('User not found.');
    }

    const role = await Role.findById(user.role_id).lean();
    if (!role) {
      return res.status(401).send('Role not found.');
    }

    // Check if any of the user's permissions match the required permissions
    const hasPermission = permissions.some((p) => role.permissions.includes(p));
    if (permissions.length > 0 && !hasPermission) {
      return res.status(401).send('Invalid permission.');
    }

    req.user = user;
    req.role = role;
    req.permissions = role.permissions;

    const is_different_device = !req.user.last_login_devices.includes(JSON.stringify(req.useragent));
    const is_not_individual_or_guest = role.name?.toLowerCase() !== 'individual' && role.name?.toLowerCase() !== 'guest';
    const is_not_partner = !role.permissions?.includes('partner');

    // if (is_different_device && is_not_individual_or_guest && is_not_partner) {
    //   return res.status(401).send('You are logged in on another device.');
    // }

    // API rate limiting
    try {
      let MAX_REQUEST_PER_X_SECOND = 2;
      if (req.method === 'POST') {
        MAX_REQUEST_PER_X_SECOND = 1;
      }
      const request_data = {
        url: req.originalUrl,
        method: req.method,
        body: req.body && Object.keys(req.body).length > 0 ? req.body : null
      };

      const requestHash = crypto.createHash('md5').update(JSON.stringify(request_data)).digest('hex');
      const rateKey = `rate_limit:${user._id}:${requestHash}`;

      const user_requests = await redis.getObj(rateKey) || [];

      const now = Date.now();
      const recent_requests = user_requests.filter(req => (now - req.timestamp) < 5000);

      // Check if user has made more than MAX_REQUEST_PER_X_SECOND requests in the last 5 seconds
      if (recent_requests.length >= MAX_REQUEST_PER_X_SECOND) {
        return res.status(429).json({
          success: false,
          error_code: 'rate_limit_exceeded',
          error_message: 'Too many requests. Please try again later.'
        });
      }
      recent_requests.push({ timestamp: now });

      await redis.setObj(rateKey, recent_requests, 60);
    } catch (error) {
      console.error('Rate limiting error:', error.message);
    }

    next();
  } catch (ex) {
    res.status(401).send('invalid_token.');
  }
};

exports.auth_by_api_key = (name) => async (req, res, next) => {
  const token = req.headers['x-access-token'];
  if (!token) {
    return res.status(401).send({ success: false, error_code: 'no_token_provided' });
  }

  const partner = await PartnerAPIKey.findOne({ api_key: token, ...(name ? { name } : {}) }).lean()
  if (!partner) {
    return res.status(401).send({ success: false, error_code: 'invalid_token_provided' });
  }

  // API rate limiting implementation for partner API keys
  try {
    // Create a unique key for this partner and request
    const request_data = {
      url: req.originalUrl,
      method: req.method,
      body: req.body && Object.keys(req.body).length > 0 ? req.body : null
    };

    // Create a hash of the request data to use as part of the key
    const requestHash = crypto.createHash('md5').update(JSON.stringify(request_data)).digest('hex');
    const rateKey = `rate_limit:partner:${partner._id}:${requestHash}`;

    // Get existing requests for this partner and endpoint
    const partnerRequests = await redis.getObj(rateKey) || [];

    // Filter requests to only include those from the last 10 seconds
    const now = Date.now();
    const recentRequests = partnerRequests.filter(req => (now - req.timestamp) < 10000);

    // Check if partner has made more than 2 requests in the last 10 seconds
    if (recentRequests.length >= 2) {
      return res.status(429).json({
        success: false,
        error_code: 'rate_limit_exceeded',
        error_message: 'Too many requests. Please try again later.'
      });
    }

    recentRequests.push({ timestamp: now });

    await redis.setObj(rateKey, recentRequests, 60);
  } catch (error) {
    console.error('Rate limiting error:', error.message);
  }

  req.permissions = partner.permissions;
  req.partner = partner;
  next()
};