const multer = require('multer')
const multer_storage = multer.memoryStorage()
const upload = multer({ multer_storage })
const upload_single = upload.single('file')
const util = require('util')
const upload_array = upload.array('files', 10)
const upload_multiples = util.promisify(upload_array);
const { Storage } = require('@google-cloud/storage');


const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode
const storage = new Storage({ credentials });

module.exports = { upload_single, upload_array, upload_multiples, storage }