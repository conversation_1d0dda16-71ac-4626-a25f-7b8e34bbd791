const fs = require('fs');
const _ = require('lodash');
const yaml = require('js-yaml');
const localize_str = fs.readFileSync('localize.yml', 'utf8');
const localize_data = yaml.load(localize_str);

const error_mw = () => (req, res, next) => {
    const language = req.headers['x-nexpos-language'] || 'en';
    res.json = (data) => {
        if (data && data.success === false && data.error) {
            const error_message = localize_data[language][data.error] || data.error;
            let modified_data = { ...data, error_code: data.error, error_message };
            delete modified_data.error;

            if (typeof modified_data.error_message === 'string' && modified_data.error_data) {
                modified_data.error_message = _.template(modified_data.error_message)(modified_data.error_data);
            }

            res.setHeader('Content-Type', 'application/json');
            res.send(JSON.stringify(modified_data));
        } else {
            res.setHeader('Content-Type', 'application/json');
            res.send(JSON.stringify(data));
        }
    };
    next();
};

module.exports = error_mw;