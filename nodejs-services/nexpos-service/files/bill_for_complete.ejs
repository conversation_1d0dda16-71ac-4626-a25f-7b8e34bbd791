<!DOCTYPE html>
<html>

<head>
    <title>Receipt</title>
    <style>
        body {
            font-size: 25px;
            font-family: Arial, sans-serif;
            max-width: 550px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }

        .header,
        .name,
        .order,
        .order-note,
        .address {
            margin-bottom: 5px;
        }

        .header {
            font-size: 30px;
            text-align: center;
            margin-bottom: 20px;
        }

        .bold {
            font-weight: bold;
        }

        .large-font {
            font-size: 30px;
        }

        .extra-large-font {
            font-size: 40px;
        }

        .order-note {
            padding: 8px;
            text-align: left;
        }

        .small-font {
            font-size: 20px;
        }

        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        table,
        th,
        td {
            border: 3px solid black;
        }

        td,
        th {
            padding: 8px;
            text-align: left;
        }

        .right-align {
            text-align: right;
        }

        .total {
            font-weight: bold;
            text-align: right;
        }

        .hotline-text {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            line-height: 1.5;
        }

        #dpoint_qrcode img {
            display: inline !important;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>

</head>

<body>
    <div class="header">
        <p style="text-align: center;"><img src="https://storage.googleapis.com/nexpos-images/nexpos-logo.png"
                style="width: 300px; height: 65px;"></p>
        <div class="name large-font bold">
            <%= site.name %>
        </div>
        <div class="address small-font">
            <%= hub.name %>
        </div>
        <div class="name large-font bold"> PHIẾU THANH TOÁN</div>

        <div class="name large-font bold">
            <%= order.source.toUpperCase() %>
                <%= order.order_id %>
        </div>

        <% if (data.status==='CANCEL' ) { %>
            <div class="name large-font bold">ĐƠN ĐÃ BỊ HỦY</div>
            <div class="address small-font"><b>Lý do: <%= order.cancel_reason %></b></div>
            <% } %>

                <div class="address small-font"><b>Thời gian đặt:</b>
                    <%= data.created_at %>
                </div>
                <% if (data.pick_time) { %>
                    <div class="address small-font"><b>Thời gian lấy hàng:</b>
                        <%= data.pick_time %>
                    </div>
                    <% } %>

                        <div class="address small-font"><b>Khách hàng:</b>
                            <%= order.customer_name %>
                        </div>
                        <div class="address small-font"><b>Địa chỉ:</b>
                            <%= order.customer_address %>
                        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Tên món</th>
                <th>SL</th>
                <th>Giá</th>
            </tr>
        </thead>
        <tbody>
            <% _.forEach(order.dishes, function(dish) { %>
                <tr>
                    <td>
                        <p>
                            <%= dish.name %>
                        </p>
                        <% if (dish.description) { %>
                            <p>
                                Mô tả: <%= dish.description %>
                            </p>
                            <% } %>
                                <% _.forEach(dish.options || [], function(option) { %>
                                    <% if (option.length> 0) { %>
                                        <p>
                                            <%= option[0].option_name %> :
                                                <% _.forEach(option, function(option_item) { %>
                                                    <li>
                                                        <%= option_item.quantity %>x <%= option_item.option_item %>
                                                    </li>
                                                    <% }) %>
                                        </p>
                                        <% } %>
                                            <% }) %>
                                                <% if (dish.note) { %>
                                                    <p>
                                                        <%= 'Ghi chú: ' + dish.note %>
                                                    </p>
                                                    <% } %>
                    </td>
                    <td class="right-align">
                        <%= dish.quantity %>
                    </td>
                    <td class="right-align">
                        <% if (dish.discount_price && dish.discount_price !==dish.price) { %>
                            <del style="opacity: 0.5;">
                                <%= dish.price?.toLocaleString('de-DE') %>đ
                            </del>
                            <%= dish.discount_price?.toLocaleString('de-DE') %>đ
                                <% } else { %>
                                    <%= dish.price?.toLocaleString('de-DE') %>đ
                                        <% } %>
                    </td>
                </tr>
                <% }) %>
        </tbody>
    </table>

    <% if (order.note) { %>
        <div class="order-note">
            <p>
                <%= 'Ghi chú đơn: ' + order.note %>
            </p>
        </div>
        <% } %>

            <table class="summary-table">
                <tbody>
                    <tr>
                        <th>Giá gốc</th>
                        <td class="total">
                            <%= order.total?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                    <tr>
                        <th>Khuyến mãi</th>
                        <td class="total">
                            <%= order.total_discount?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                    <tr>
                        <th>Tổng thanh toán</th>
                        <td class="total">
                            <%= order.total_for_biz?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                </tbody>
            </table>

            <% if (order.source==='local' ) { %>
                <table class="summary-table">
                    <tbody>
                        <% for (var i=0; i < order.payments?.length; i++) { %>
                            <tr>
                                <th>
                                    <%= order.payments[i].method %>
                                </th>
                                <td class="total">
                                    <%= order.payments[i].total?.toLocaleString('de-DE') %>đ
                                </td>
                            </tr>
                            <% } %>
                                <tr>
                                    <th>TỔNG THANH TOÁN:</th>
                                    <td class="total">
                                        <%= data.total_paid?.toLocaleString('de-DE') %>đ
                                    </td>
                                </tr>
                    </tbody>
                </table>
                <% } %>

                    <% if (data.dpoint_qrcode) { %>
                        <div class="header large-font bold"> Quét mã QR để tích điểm và đổi quà</div>
                        <p style="text-align: center;">
                        <div id="dpoint_qrcode" style="text-align: center;"></div>
                        </p>

                        <script>
                            var qrcode = new QRCode(document.getElementById("dpoint_qrcode"), {
                                text: "<%= data.dpoint_qrcode %>",
                                width: 300,
                                height: 300,
                            });
                        </script>

                        <% } %>
                            <p class="hotline-text">Mọi phản ánh/ góp ý mong quý khách liên hệ số hotline để được hỗ trợ
                                kịp thời </br>
                                <%= hub.phone ?? site.phone %></br>Cảm ơn quý khách!
                            </p>
</body>


</html>