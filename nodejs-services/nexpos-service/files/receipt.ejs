<!DOCTYPE html>
<html>

<head>
    <title>Receipt</title>
    <style>
        body {
            font-size: 25px;
            font-family: Arial, sans-serif;
            max-width: 550px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }

        .header,
        .name,
        .order,
        .order-note,
        .address {
            margin-bottom: 5px;
        }

        .header {
            font-size: 30px;
            text-align: center;
            margin-bottom: 20px;
        }

        .bold {
            font-weight: bold;
        }

        .large-font {
            font-size: 30px;
        }

        .extra-large-font {
            font-size: 40px;
        }

        .order-note {
            padding: 8px;
            text-align: left;
        }

        .small-font {
            font-size: 20px;
        }

        table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }

        table,
        th,
        td {
            border: 3px solid black;
        }

        td,
        th {
            padding: 8px;
            text-align: left;
        }

        .right-align {
            text-align: right;
        }

        .total {
            font-weight: bold;
            text-align: right;
        }
    </style>
</head>

<body>
    <div class="header">
        <p style="text-align: center;"><img src="https://storage.googleapis.com/nexpos-images/nexpos-logo.png"
                style="width: 300px; height: 65px;"></p>
        <div class="name large-font bold">
            <%= site.name %>
        </div>
        <div class="address small-font"><b>Lấy hàng tại:</b>
            <%= hub.name %>
        </div>
        <% if (data.pick_time) { %>
            <div class="address small-font"><b>Thời gian lấy hàng dự kiến:</b>
                <%= data.pick_time %>
            </div>
            <% } %>
                <div class="address small-font"><b>Tạo đơn:</b>
                    <%= data.created_at %>
                </div>
                <div class="address small-font"><b>In đơn:</b>
                    <%= data.printed_at %>
                </div>
                <div class="name large-font bold">Nguồn đơn: <%= order.source.toUpperCase() %>
                </div>
                <div class="address small-font">
                    <%= data.order_count_message %>
                </div>
                <div class="name large-font bold">
                    <%= order.order_id %>
                </div>
                <div class="address small-font"><b>Khách hàng:</b>
                    <%= order.customer_name %>
                </div>
                <div class="address small-font"><b>Địa chỉ:</b>
                    <%= order.customer_address %>
                </div>
                <% if (data.status==='CANCEL' ) { %>
                    <div class="name large-font bold">ĐƠN ĐÃ BỊ HỦY</div>
                    <div class="address small-font"><b>Lý do: <%= order.cancel_reason %></b></div>
                    <% } %>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Món</th>
                <th>SL</th>
                <th>Giá</th>
            </tr>
        </thead>
        <tbody>
            <% for (var i=0; i < order.dishes.length; i++) { %>
                <tr>
                    <td>
                        <p>
                            <%= order.dishes[i].name %>
                        </p>
                        <% for (const option of order.dishes[i].options || []) { %>
                            <% if (option.length> 0) { %>
                                <p>
                                    <%= option[0].option_name %> :
                                        <% for (const option_item of option) { %>
                                            <li>
                                                <%= option_item.quantity %>x <%= option_item.option_item %>
                                            </li>
                                            <% } %>
                                </p>
                                <% } %>
                                    <% } %>
                                        <% if (order.dishes[i].note) { %>
                                            <p>
                                                <%= 'Ghi chú: ' + order.dishes[i].note %>
                                            </p>
                                            <% } %>
                    </td>
                    <td class="right-align">
                        <%= order.dishes[i].quantity %>
                    </td>
                    <td class="right-align">
                        <%= order.dishes[i].price?.toLocaleString('de-DE') %>đ
                    </td>
                </tr>
                <% } %>
        </tbody>
    </table>

    <% if (order.note) { %>
        <div class="order-note">
            <p>
                <%= 'Ghi chú đơn: ' + order.note %>
            </p>
        </div>
        <% } %>

            <table class="summary-table">
                <tbody>
                    <tr>
                        <th>Giá gốc</th>
                        <td class="total">
                            <%= order.total?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                    <tr>
                        <th>Khuyến mãi</th>
                        <td class="total">
                            <%= order.total_discount?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                    <tr>
                        <th>Tổng tiền</th>
                        <td class="total">
                            <%= order.total_for_biz?.toLocaleString('de-DE') %>đ
                        </td>
                    </tr>
                </tbody>
            </table>

            <% if (order.source==='local' ) { %>
                <table class="summary-table">
                    <tbody>
                        <% for (var i=0; i < order.payments?.length; i++) { %>
                            <tr>
                                <th>
                                    <%= order.payments[i].method %>
                                </th>
                                <td class="total">
                                    <%= order.payments[i].total?.toLocaleString('de-DE') %>đ
                                </td>
                            </tr>
                            <% } %>
                                <tr>
                                    <th>TỔNG THANH TOÁN:</th>
                                    <td class="total">
                                        <%= data.total_paid?.toLocaleString('de-DE') %>đ
                                    </td>
                                </tr>
                    </tbody>
                </table>
                <% } %>

                    <% if (data.qrcode) { %>
                        <div class="header large-font bold">Thông Tin Thanh Toán</div>
                        <p style="text-align: center;"><img src="<%= data.qrcode %>"
                                style="width: 500px; height: 500px;"></p>
                        <% } %>

</body>

</html>