<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bulma/0.9.4/css/bulma.min.css">
    <style>
        body {
            width: 500px;
            font-weight: bold !important;
            font-size: 18px !important;
        }

        .section {
            padding: 1rem;
        }

        .title {
            font-size: 24px !important;
        }

        .column {
            font-size: 18px !important;
            padding: 0.5rem 1rem !important;
        }

        .data-row {
            border: 2px solid #000;
            margin-bottom: 0.5rem !important;
        }

        .data-row .column:first-child {
            border-right: 2px solid #000;
        }

        @media print {
            .section {
                padding: 0.5rem !important;
            }

            .data-row,
            .data-row .column:first-child {
                border-color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>

<body>
    <section class="section">
        <div class="container">
            <div class="box">
                <h1 class="title has-text-centered p-3 mb-5">
                    HÌNH THỨC THANH TOÁN
                </h1>

                <div class="columns is-mobile mb-2 data-row">
                    <div class="column">Hub:</div>
                    <div class="column has-text-right">
                        <%= hub_name %>
                    </div>
                </div>

                <div class="columns is-mobile mb-2 data-row">
                    <div class="column">Thời gian:</div>
                    <div class="column has-text-right">
                        <%= created_at %>
                    </div>
                </div>

                <div class="columns is-mobile mb-2 data-row">
                    <div class="column">Người xuất:</div>
                    <div class="column has-text-right">
                        <%= close_by %>
                    </div>
                </div>

                <div class="columns is-mobile mb-2 data-row">
                    <div class="column">Tổng đơn:</div>
                    <div class="column has-text-right">
                        <%= total_order %> đơn
                    </div>
                </div>

                <h1 class="title has-text-centered p-3 mb-5">
                    HÌNH THỨC THANH TOÁN
                </h1>

                <% for(let payment_method in payment_methods) { %>
                    <div class="columns is-mobile mb-2 data-row">
                        <div class="column">
                            <%= payment_method %>
                        </div>
                        <div class="column has-text-right">
                            <%= payment_methods[payment_method].total %> đ
                        </div>
                    </div>
                    <% } %>
            </div>
        </div>
    </section>
</body>

</html>