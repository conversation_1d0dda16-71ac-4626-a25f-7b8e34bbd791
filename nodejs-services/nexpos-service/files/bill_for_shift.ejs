<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title><PERSON><PERSON>o C<PERSON></title>
    <style>
        @page {
            margin: 0;
        }

        body {
            width: 500px;
            font-family: "Arial", sans-serif;
            font-weight: bold;
            font-size: 14px;
            margin: 0;
            padding: 10px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 16px;
        }

        .table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            border: 2px solid black;
            padding: 8px;
            font-size: 16px;
            text-align: center;
        }

        .table th {
            font-weight: bold;
        }

        .amount {
            text-align: right;
        }

        .divider {
            border-top: 2px dashed black;
            margin: 15px 0;
        }

        @media print {
            body {
                width: 100%;
            }

            .table th,
            .table td {
                border: 2px solid black !important;
            }
        }
    </style>
</head>

<body>
    <div class="title">BÁO CÁO BÁN HÀNG</div>

    <div class="divider"></div>

    <div class="info-row">
        <div>Hub:</div>
        <div>
            <%= hub_name %>
        </div>
    </div>

    <div class="info-row">
        <div>Thời gian:</div>
        <div>
            <%= created_at %>
        </div>
    </div>

    <% if(typeof close_by==='string' ){ %>
        <div class="info-row">
            <div>Người xuất:</div>
            <div>
                <%= close_by %>
            </div>
        </div>
        <% } %>

            <div class="info-row">
                <div>Tổng đơn:</div>
                <div>
                    <%= total_order %>
                </div>
            </div>

            <div class="info-row">
                <div>DT trước KM:</div>
                <div>
                    <%= new Intl.NumberFormat('vi-VN').format(total_gross_received) %> VNĐ
                </div>
            </div>

            <div class="info-row">
                <div>DT sau KM:</div>
                <div>
                    <%= new Intl.NumberFormat('vi-VN').format(total_net_received) %> VNĐ
                </div>
            </div>

            <div class="divider"></div>

            <table class="table">
                <thead>
                    <tr>
                        <th>KÊNH BÁN</th>
                        <th>SL</th>
                        <th>DT SAU KM</th>
                    </tr>
                </thead>
                <tbody>
                    <% if(Object.keys(merchants).length===0){ %>
                        <tr>
                            <td colspan="3">Không có dữ liệu</td>
                        </tr>
                        <% } else { %>
                            <% for(let merchant in merchants){ %>
                                <tr>
                                    <td>
                                        <%= merchant %>
                                    </td>
                                    <td class="amount">
                                        <%= merchants[merchant].total_orders %>
                                    </td>
                                    <td class="amount">
                                        <%= new
                                            Intl.NumberFormat('vi-VN').format(merchants[merchant].total_net_received) %>
                                            VNĐ
                                    </td>
                                </tr>
                                <% } %>
                                    <% } %>
                </tbody>
            </table>
</body>

</html>