require('dotenv').config({ path: '.env.' + process.env.NODE_ENV })
require('dotenv').config({ path: '.env' })
const http = require('http')
const express = require('express')
const cors = require('cors')
require('express-async-errors')
const axios = require('axios')
const useragent = require('express-useragent')
const cookieParser = require('cookie-parser')
const merchant = require('./route/merchant')
const partner_3rd = require('./route/partner_3rd')
const {
  cron_site_orders,
  cron_refresh_token_accounts,
  fetch_site_orders,
  cron_site_orders_in_x_days,
  cron_token_account_healths,
  cron_order_notifications,
  cron_site_ecom_orders,
  cron_brand_order_checking,
  cron_site_finances,
  cron_site_order_feedbacks,
  cron_site_feedback_summary,
  cron_site_order_incident_list,
  cron_refresh_token_accounts_v2,
} = require('./route/merchant.cron')
const site = require('./route/site')
const user = require('./route/user')
const user_address = require('./route/user_address')
const nutifood = require('./route/nutifood')
const nexpos = require('./route/nexpos')
const zalo = require('./route/zalo')
const payment = require('./route/payment')
const { create_role, update_role, get_role_list, delete_role } = require('./route/role')
const menu_category = require('./route/menu_category')
const menu_site_category = require('./route/menu_site_category')
const menu_hub_category = require('./route/menu_hub_category')
const hub_stock = require('./route/hub_stock')
const hub_stock_ticket = require('./route/hub_stock_ticket')
const { login, register, verify_otp, change_password, forgot_password, change_password_by_code, resend_otp, login_as_guest, verify_token, login_v2, verify_token_v2, logout_v2, user_register } = require('./route/login')
const brand = require('./route/brand')
const brand_request = require('./route/brand_request')
const order_notification = require('./route/order_notification')
const merchant_ecom = require('./route/merchant_ecom')
const banker_callback = require('./route/banker_callback')

const { get_hub_list, create_hub, update_hub, delete_hub, get_hub_printers } = require('./route/hub')
const { required, auth_by_api_key } = require('./middlewares/authentication')
const error_mw = require('./middlewares/error')
const logger = require('./middlewares/logger')
const cart = require('./route/cart')
const file = require('./route/file')
const tickets = require('./route/it_ticket')
const partners = require('./route/partner')
const campaigns = require('./route/campaign')
const campaign_user = require('./route/campaign_user')
const brand_commission = require('./route/brand_commission')
const brand_withdraw = require('./route/brand_partner_management')
const partner_report = require('./route/partner_report')
const orders = require('./route/order')
const partner_withdraw = require('./route/partner_withdraw')
const app_version = require('./route/app_version')
const partner_order = require('./route/partner_order')
const grab_partner = require('./route/grab_partner')
const shopee_partner = require('./route/shopee_partner')
const shopee_seller = require('./route/shopee_seller')
const be_partner = require('./route/be_partner')
const tiktok_partner = require('./route/tiktok_partner')
const delivery = require('./route/delivery')
const report = require('./route/report')
const brand_otp = require('./route/brand_otp')
const brand_retailer = require('./route/brand_retailer')
const merchant_callback = require('./route/merchant_callback')
const merchant_shipment = require('./route/merchant_shipment')
const merchant_payment = require('./route/merchant_payment')
const customer_profiles = require('./route/customer_profile')
const momo_mini_app = require('./route/momo_mini')
const token_account = require('./route/token_account')
const voucher = require('./route/voucher')
const partner_hub = require('./route/partner_hub')
const material = require('./route/material')
const core_product = require('./route/core-product')
const province = require('./route/province')
const site_menu = require('./route/cp_site_menu')
const cp_sync_stock = require('./route/core_product_sync_stock')
const cp_grab = require('./route/cp_grab_partner')
const site_cms = require('./route/site_cms')
const cp_menu_template = require('./route/cp_menu_template')
const google_template = require('./route/google_template')
const { customer_support_create_order } = require('./route/customer_support')
const brand_bill = require('./route/brand_bill')
const working_shift = require('./route/working_shift')
const app_integration = require('./route/app_integration')
const pos_order = require('./route/pos_order')
const summary = require('./route/summary')

const app = express()
const port = process.env.PORT || 3001

app.use(cors({
  origin: (origin, callback) => {
    callback(null, true);
  },
  credentials: true,
}))


app.use(express.json({ limit: '10Mb' }))
app.use(express.urlencoded())
app.use(express.text())
app.use(useragent.express())
app.use(cookieParser())



app.use(express.static('public'))
app.use(error_mw());

app.post('/api/upload/images', file.upload_image)
app.post('/api/upload/multiple-images', file.upload_multiple_images)
app.post('/api/upload/files', file.upload_file)
app.post('/api/file/headers', file.get_file_headers)

app.get('/api/health', async (req, res) => {
  res.json({ success: true })
})

app.get('/api/phone/validate', required(), merchant.get_phone_validate)
app.get('/api/sites/:site_id/orders', logger(), required(), merchant.get_order_list)
app.post('/api/sites/:site_id/accounts', logger(), required(), merchant.check_site_accounts)
app.post('/api/sites/:site_id/fetch_orders', logger(), required(), fetch_site_orders)
app.get('/api/site/orders', required('get_order'), merchant.get_multi_site_order_list)
app.get('/api/site/order/counts', required('get_order'), merchant.get_multi_site_order_list_count)
app.get('/api/site/new_orders', logger(), required('get_order'), merchant.get_multi_site_new_orders)
app.get('/api/site/orders/:order_id', logger(), required('get_detail_order'), merchant.get_order_detail)
app.put('/api/site/orders/:order_id', logger(), required('update_order'), merchant.update_order)
app.delete('/api/site/orders/:order_id', logger(), required('delete_order'), merchant.delete_order)
app.get('/api/site/orders/:order_id/latest', logger('get_order'), required(), merchant.get_order_detail_latest)
app.post('/api/site/orders/:order_id/confirm', logger(), required(), merchant.confirm_order)
app.post('/api/site/orders/:order_id/cancel', logger(), required('cancel_order'), merchant.cancel_order)
app.post('/api/site/orders/:order_id/confirm_payments', logger(), required(), merchant.confirm_payment)
app.post('/api/site/orders/:order_id/delete_payments', logger(), required(), merchant.delete_payment)
app.post('/api/site/orders/:order_id/change_payment_method', logger(), required(), merchant.change_payment_method)
app.get('/api/site/orders/:order_id/prints', logger(), required('print_order'), merchant.print_order)
app.get('/api/site/orders/:order_id/print_to_images_for_ecom', logger(), required(), merchant.print_order_to_image_for_ecom)
app.get('/api/site/orders/:order_id/print_payment_bill', logger(), required(), merchant.print_order_payment_bill)
app.get('/api/site/orders/:order_id/site_has_stocks', logger(), required(), merchant.get_site_has_stock)
app.put('/api/site/orders/:order_id/site_has_stocks/:site_id', logger(), required(), merchant.put_site_has_stock)
app.get('/api/site/orders/:order_id/hub_has_stocks', logger(), required(), merchant.get_hub_has_stock)
app.put('/api/site/orders/:order_id/hub_has_stocks/:hub_id', logger(), required('change_order_hub'), merchant.put_hub_has_stock)
app.get('/api/site/orders/:order_id/vendor/sync', logger(), required(), merchant.vendor_sync_order)
app.get('/api/site/orders/:order_id/shipments', logger(), required(), merchant_shipment.get_order_shipments)
app.post('/api/site/orders/:order_id/shipments', logger(), required('create_shipment_order'), merchant_shipment.create_order_shipments)
app.post('/api/site/orders/:order_id/manual_shipments', logger(), required('create_order_manual'), merchant_shipment.create_order_manual_shipments)
app.post('/api/site/orders/:order_id/shipment/cancel', logger(), required('cancel_shipment_order'), merchant_shipment.cancel_order_shipments)
app.post('/api/sites/:site_id/order/shipments', logger(), required(), merchant_shipment.get_new_order_shipments)
app.get('/api/site/order/shipment/histories', logger('shipment'), required(), merchant_shipment.get_order_shipment_histories)
app.get('/api/site/order/payment/histories', logger('transaction'), required(), merchant_payment.get_order_payment_histories)
// app.get('/api/sites/:site_id/prints', logger(), site.get_site_print_list)
app.get('/api/hubs/:hub_id/prints', site.get_hub_print_list)
app.get('/api/hub/prints', site.get_hub_print_list_v2)
app.put('/api/sites/:site_id/prints/:print_id', logger(), site.update_site_print_queue)
app.get('/api/sites/:site_id/open/status', logger(), required(), site.get_site_open_status)
app.get('/api/sites/:site_id', logger(), required(), site.get_site_by_id)
app.get('/api/site/errors', logger(), required(), site.get_site_errors)

app.post('/api/login', logger(), login)
app.post('/api/login_as_guest', logger(), login_as_guest)
app.post('/api/register', logger(), register)
app.post('/api/user-register', logger(), user_register)
app.post('/api/otp/verify', logger(), verify_otp) // deprecated
// app.post('/api/otp/resend', logger(), resend_otp)  // deprecated

app.post('/api/brands/:brand_id/otp/send', logger(), brand_otp.send_phone_otp)
app.post('/api/brands/:brand_id/otp/verify', logger(), brand_otp.verify_phone_otp)
app.get('/api/brands/:brand_id/token/headers', logger(), site.get_brand_token_headers)

app.get('/api/cron/token_account/refresh', required('internal'), cron_refresh_token_accounts_v2)
app.get('/api/cron/orders', required('internal'), cron_site_orders)
app.get('/api/cron/ecom/orders', required('internal'), cron_site_ecom_orders)
app.get('/api/cron/orders_in_x_days', cron_site_orders_in_x_days)
app.get('/api/cron/site/finances', cron_site_finances)
app.get('/api/cron/site/feedbacks', cron_site_order_feedbacks)
app.get('/api/cron/site/incidents', cron_site_order_incident_list)

app.get('/api/cron/site/feedback_summary', cron_site_feedback_summary)
app.get('/api/cron/order/total_checking', required('internal'), cron_brand_order_checking)
app.get('/api/cron/order/notification/remind', required('internal'), order_notification.cron_order_confirmation)
app.get('/api/cron/order/notification/cancel', required('internal'), order_notification.cron_site_order_cancel)
app.get('/api/cron/notifications', required('internal'), cron_order_notifications)

app.get('/api/cron/healths', required('internal'), cron_token_account_healths)
app.get('/api/cron/auto-confirms', required('internal'), merchant.auto_confirm_order)
app.get('/api/cron/auto-print-bills', required('internal'), merchant.auto_print_order_bill)
// app.get('/api/cron/auto-print-labels', required('internal'), merchant.auto_print_order_label)
// app.get('/api/cron/auto-generate-bills', required('internal'), merchant.auto_generate_bill)
app.get('/api/cron/nutifood/sync_orders', required('internal'), nutifood.sync_orders)
app.get('/api/cron/nutifood/get_hub_stocks', required('internal'), nutifood.get_hub_stocks)
app.get('/api/cron/nutifood/sync_stocks', required('internal'), menu_hub_category.sync_hub_stock_to_apps)
app.get('/api/cron/nexpos/sync_orders', required('internal'), nexpos.sync_orders)
app.get('/api/cron/partner-commission', required('internal'), partner_report.sync_commission_summary)
app.get('/api/cron/update_it_tickets', required('internal'), tickets.cron_update_status)
app.get('/api/cron/core-product/sync-stocks', required('internal'), cp_sync_stock.sync_orders_stock)

// Callback
app.post('/api/zalo/callbacks', logger(), zalo.callback)
app.get('/api/zalo/client-callbacks', logger(), zalo.client_callback)
app.get('/api/momo/client-callbacks', logger(), payment.momo_client_callback)
app.all('/api/momo/callbacks', logger(), payment.momo_server_callback)
app.get('/api/payos/client-callbacks', logger(), payment.payos_client_callback)
app.all('/api/payos/callbacks', logger(), payment.payos_server_callback)
app.get('/api/nexdorpay/client-callbacks', logger(), payment.nexdorpay_client_callback)
app.all('/api/nexdorpay/callbacks', logger(), payment.nexdorpay_server_callback)
app.all('/api/apps/shopee/:callback_type', logger(), merchant_callback.order_callbacks)

app.get('/api/tokens', logger(), required(), token_account.get_token_list)
app.post('/api/tokens', logger(), required(), token_account.create_token)
app.get('/api/tokens/:token_id', logger(), required(), token_account.get_token)
app.put('/api/tokens/:token_id', logger(), required(), token_account.update_token)
app.post('/api/tokens/:token_id/refresh', logger(), required(), token_account.refresh_token)
app.delete('/api/tokens/:token_id', logger(), required(), token_account.delete_token)

app.get('/api/hubs', logger(), required(), get_hub_list)
app.post('/api/hubs', logger(), required(), create_hub)
app.put('/api/hubs/:hub_id', logger(), required(), update_hub)
app.delete('/api/hubs/:hub_id', logger(), required(), delete_hub)
app.get('/api/hub/printers', logger(), get_hub_printers)

app.get('/api/brands', logger(), required('get_brand'), brand.get_brand_list)
app.get('/api/brands/:brand_id', logger(), required('get_brand'), brand.get_brand_details)
app.post('/api/brands', logger(), required('add_brand'), brand.create_brand)
app.put('/api/brands/:brand_id', logger(), required('update_brand'), brand.update_brand)
app.post('/api/brands/:brand_id/status', logger(), required('update_brand'), brand.update_brand_status)
app.put('/api/brands/:brand_id/banners', logger(), required('update_brand'), brand.update_banners)
app.delete('/api/brands/:brand_id', logger(), required(), required('delete_brand'), brand.delete_brand)
app.get('/api/brand/menu/requests', logger(), required(), brand_request.get_brand_request_list)
app.post('/api/brand/menu/requests', logger(), required(), brand_request.create_brand_request)
app.delete('/api/brand/menu/requests/:request_id', logger(), required(), brand_request.delete_brand_request)
app.post('/api/brand/menu/requests/:request_id/clone', logger(), required(), brand_request.clone_brand_request)
app.post('/api/brand/menu/requests/:request_id/approve', logger(), required(), brand_request.approve_brand_request)

app.get('/api/brands/:brand_id/bills', logger(), brand_bill.get_bill_list)
app.post('/api/brands/:brand_id/bills', logger(), brand_bill.create_bill)
app.put('/api/brands/:brand_id/bills/:bill_id', logger(), brand_bill.update_bill)
app.post('/api/brands/:brand_id/bills/:bill_id/delete', logger(), brand_bill.delete_bill)

app.get('/api/brand/sites', logger(), required(), site.get_site_list)
app.get('/api/brand/site/exports', logger(), required(), site.get_site_exports)
app.get('/api/brand/site-details', logger(), required(), site.get_site)
app.post('/api/brands/:brand_id/sites', logger(), required(['site', 'partner', 'add_site']), site.create_site)
app.put('/api/brands/:brand_id/sites/:site_id', logger(), required(['site', 'partner', 'update_site']), site.update_site)
app.delete('/api/brands/:brand_id/sites/:site_id', logger(), required(['site', 'partner', 'delete_site']), site.delete_site)

app.get('/api/users', logger(), required(), user.get_user_list)
app.get('/api/users/:user_id', logger(), required(), user.get_user_by_id)
app.get('/api/users/phone/:phone', logger(), user.get_user_by_phone)
app.post('/api/users', logger(), required(['user', 'add_user']), user.create_user)
app.put('/api/users/:user_id', logger(), required(['user', 'update_user']), user.update_user)
app.get('/api/user/current_user', logger(), required(), user.get_current_user)
app.put('/api/user/basic_infos', logger(), required(), user.update_user_basic_infos)
app.delete('/api/users/:user_id', logger(), required(['user', 'delete_user']), user.delete_user)
app.post('/api/user/change_password', logger(), required(), change_password)
app.post('/api/user/forgot_password', logger(), forgot_password)
app.post('/api/user/change_password_by_codes', logger(), change_password_by_code)
app.post('/api/user/contact_us', logger(), user.contact_us)
app.get('/api/user/notifications', logger(), required(), user.get_user_notifications)
app.post('/api/user/notifications', logger(), required(), user.user_read_notifications)

app.get('/api/sites/:site_id/user/carts', logger(), required(), cart.get_cart)
app.post('/api/sites/:site_id/user/carts', logger(), required(), cart.update_cart)
app.post('/api/sites/:site_id/user/cart/items', logger(), required(), cart.update_cart_item)
app.post('/api/sites/:site_id/user/cart/clone', logger(), required(), cart.clone_cart_from_order)
app.get('/api/sites/:site_id/user/cart/gifts', logger(), required(), cart.get_cart_gifts)
app.post('/api/sites/:site_id/user/cart/addresses', logger(), required(), cart.update_cart_address)
app.get('/api/sites/:site_id/user/cart/shipments', logger(), required(), cart.get_cart_shipment)
app.post('/api/sites/:site_id/user/cart/shipments', logger(), required(), cart.update_cart_shipment)
app.get('/api/sites/:site_id/user/cart/shipment/pickup_slots', logger(), required(), cart.get_pickup_slots)
// app.post('/api/sites/:site_id/user/cart/shipment/schedules', logger(), required(), cart.update_cart_shipment_schedule)
app.post('/api/sites/:site_id/user/cart/shipping_promo', logger(), required(), cart.apply_promo_code)
app.post('/api/sites/:site_id/user/cart/apply_voucher', logger(), required(), cart.apply_voucher)
app.post('/api/sites/:site_id/user/cart/apply_nexpos_voucher', logger(), required(), cart.apply_nexpos_voucher)
app.post('/api/sites/:site_id/user/cart/check_voucher', logger(), required(), cart.check_voucher)
app.get('/api/sites/:site_id/payment_services', logger(), required(), cart.get_site_payment_services)
app.get('/api/payment_services', logger(), required(), cart.get_payment_services)
app.post('/api/sites/:site_id/user/cart/create', logger(), required(), cart.create_order)
app.get('/api/sites/:site_id/user/cart/assignable_hubs', logger(), required(), cart.get_assignable_hubs)
app.post('/api/sites/:site_id/user/cart/reset', logger(), required(), cart.reset_cart)

// app.put('/api/sites/:site_id/user/orders/:order_id/cancel', logger(), required(), cart.cancel_order)
app.get('/api/sites/:site_id/user/orders/:order_id/payment_urls', logger(), required(), cart.get_order_payment_urls)
app.get('/api/sites/:site_id/user/orders/:order_id/re_payments', logger(), required(), cart.re_payment_for_order)
app.get('/api/site/orders/:order_id/transactions/:transaction_id', logger(), required(), cart.get_order_transaction)
app.get('/api/public/site/orders/:order_id/payment_urls', logger(), cart.get_public_order_payment_urls)
app.get('/api/share/payments', logger(), cart.share_order_payment_url)


app.get('/api/addresses', user_address.get_address_list)
app.get('/api/map/suggest_addresses', logger(), user_address.get_map_suggest_address_list)
app.get('/api/map/v2/suggest_addresses', logger(), user_address.get_map_suggest_address_list_v2)
app.get('/api/user/addresses', logger(), required(), user_address.get_user_address_list)
app.post('/api/user/addresses', logger(), required(), user_address.create_user_address)
app.put('/api/user/addresses/:address_id', logger(), required(), user_address.update_user_address)
app.delete('/api/user/addresses/:address_id', logger(), required(), user_address.delete_user_address)
app.get('/api/user/addresses', logger(), required(), user_address.get_user_address_list)

app.get('/api/roles', required('get_role'), get_role_list)
app.post('/api/roles', logger(), required('add_role'), create_role)
app.put('/api/roles/:role_id', logger(), required('update_role'), update_role)
app.delete('/api/roles/:role_id', logger(), required('delete_role'), delete_role)

// Hub menu
// app.get('/api/hubs/:hub_id/menu/categories', logger(), menu_hub_category.get_hub_menu_category_list)
// app.post('/api/hubs/:hub_id/menu/categories', logger(), required('menu_hub'), menu_hub_category.create_update_hub_menu_category)
// app.post('/api/hubs/:hub_id/menu/clone_from_brands', logger(), required('menu_hub'), menu_hub_category.clone_hub_menu_from_a_brand)

// Hub stock
app.post('/api/hubs/:hub_id/menu/get_stock_from_servers', logger(), required('hub_stock'), menu_hub_category.get_menu_stock_from_server)
app.get('/api/hubs/:hub_id/stock/items', logger(), required('get_hub_stock'), hub_stock.get_stock_items)
app.post('/api/hubs/:hub_id/stock/items', logger(), required('add_hub_stock'), hub_stock.create_stock_item)
app.put('/api/hubs/:hub_id/stock/items/:item_id', logger(), required('update_hub_stock'), hub_stock.update_stock_item)
app.delete('/api/hubs/:hub_id/stock/items/:code', logger(), required('delete_hub_stock'), hub_stock.delete_stock_item)
app.get('/api/hubs/:hub_id/stock/file/templates', logger(), required('hub_stock'), hub_stock.get_stock_file_template)
app.get('/api/hubs/:hub_id/stock/files', logger(), hub_stock.get_stock_files)
app.post('/api/hubs/:hub_id/stock/files', logger(), hub_stock.create_stock_file)
app.get('/api/hubs/:hub_id/stock/items/:code/histories', logger(), required('view_hub_stock_history'), hub_stock.get_stock_histories)
app.get('/api/hubs/:hub_id/stock/summary', logger(), required(['download_import_hub_stock_report', 'download_export_hub_stock_report', 'export_report_finish_working_shift']), hub_stock_ticket.get_stock_summary)

// Hub stock ticket
app.get('/api/hubs/:hub_id/stock/tickets', logger(), required(), hub_stock_ticket.get_hub_stock_ticket_list)
app.post('/api/hubs/:hub_id/stock/tickets', logger(), required(), hub_stock_ticket.create_hub_stock_ticket)
app.put('/api/hubs/:hub_id/stock/tickets/:ticket_id', logger(), required(), hub_stock_ticket.update_hub_stock_ticket)
app.post('/api/hubs/:hub_id/stock/tickets/:ticket_id/submit', logger(), required(), hub_stock_ticket.submit_hub_stock_ticket)
app.post('/api/hubs/:hub_id/stock/tickets/:ticket_id/approve', logger(), required(), hub_stock_ticket.approve_hub_stock_ticket)
app.post('/api/hubs/:hub_id/stock/tickets/:ticket_id/reject', logger(), required(), hub_stock_ticket.reject_hub_stock_ticket)
app.delete('/api/hubs/:hub_id/stock/tickets/:ticket_id', logger(), required(), hub_stock_ticket.delete_hub_stock_ticket)
app.post('/api/hubs/:hub_id/stock/tickets/:ticket_id/print', logger(), required(), hub_stock_ticket.print_hub_stock_ticket)
app.post('/api/hubs/:hub_id/stock/tickets/:ticket_id/download', logger(), required(), hub_stock_ticket.print_hub_stock_ticket)
app.get('/api/hubs/:hub_id/stock/ticket/templates', logger(), required(), hub_stock_ticket.get_ticket_template)
app.post('/api/hubs/:hub_id/stock/ticket/templates', logger(), required(), hub_stock_ticket.create_ticket_from_template)
app.get('/api/hubs/:hub_id/stock/materials', logger(), required(), hub_stock_ticket.get_stock_materials)

// Brand menu
app.get('/api/brands/:brand_id/menu/categories', logger(), required('get_menu_brand'), menu_category.get_brand_menu_category_list)
app.post('/api/brands/:brand_id/menu/get_site_app_menu', logger(), required(), menu_category.get_brand_menu_site_app_menu)
app.post('/api/brands/:brand_id/menu/get_all_site_app_menu', logger(), required(), menu_category.get_brand_menu_all_site_app_menu)
app.post('/api/brands/:brand_id/menu/apply_site_app_menu', logger(), required(), menu_category.post_brand_menu_from_all_site_apps)
app.post('/api/brands/:brand_id/menu/apply_menu_for_all_sites', logger(), required(), menu_category.apply_brand_menu_for_all_sites)
app.post('/api/brands/:brand_id/menu/categories', logger(), required(['add_menu_brand', 'update_menu_brand', 'delete_menu_brand']), menu_category.create_update_brand_menu_category)
app.delete('/api/brands/:brand_id/menu/category/:category_id', logger(), required('delete_menu_brand'), menu_category.delete_brand_menu_category)
app.post('/api/brands/:brand_id/menu/import_from_excels', logger(), required(), menu_category.get_brand_menu_from_excel)
app.get('/api/brands/:brand_id/menu/export_to_excels', logger(), required(), menu_category.export_brand_menu_to_excel)
app.post('/api/brands/:brand_id/menu/import_from_google_sheets', logger(), required(), menu_category.get_brand_menu_from_excel)
// app.get('/api/brands/:brand_id/menu/export_to_google_sheets', logger(), required(), menu_category.export_brand_menu_to_google_sheet)
app.get('/api/google_template/set_brand_menu', logger(), google_template.set_brand_menu)
app.get('/api/google_template/validate_brand_menu', logger(), google_template.validate_brand_menu)

app.get('/api/brands/campaigns', logger(), required('brand'), campaigns.get_campaign_list)
app.post('/api/brands/:brand_id/campaigns', logger(), required('brand'), campaigns.create_campaign)
app.put('/api/brands/:brand_id/campaigns/:id', logger(), required('brand'), campaigns.update_campaign)
app.delete('/api/brands/:brand_id/campaigns/:id', logger(), required('brand'), campaigns.delete_campaign)
app.get('/api/brands/:brand_id/retailer/report', logger(), required('brand'), report.retailer_report_for_brand)

app.get('/api/gift-campaigns', logger(), required(), campaign_user.get_gift_campaign_list_by_user)
app.get('/api/brands/:brand_id/gift-campaign', logger(), required('brand'), campaign_user.get_gift_campaign_list)
app.post('/api/brands/:brand_id/gift-campaign', logger(), required('brand'), campaign_user.create_gift_campaign)
app.put('/api/brands/:brand_id/gift-campaign/:campaign_id', logger(), required('brand'), campaign_user.update_gift_campaign)
app.post('/api/brands/gift-campaign/:campaign_id/join', logger(), campaign_user.join_gift_campaign)
app.get('/api/brands/gift-campaign/:campaign_id', logger(), campaign_user.get_gift_campaign_details)

app.get('/api/brands/:brand_id/commissions', logger(), required('brand'), brand_commission.get_config)
app.get('/api/brands/commissions', logger(), required('brand'), brand_commission.get_config_by_user)
app.post('/api/brands/:brand_id/commissions', logger(), required('brand'), brand_commission.create_config)
app.put('/api/brands/:brand_id/commissions/:id', logger(), required('brand'), brand_commission.update_config)
app.delete('/api/brands/:brand_id/commissions/:id', logger(), required('brand'), brand_commission.delete_config)
app.get('/api/brands/:brand_id/withdraws', logger(), required('brand'), brand_withdraw.get_list)
app.put('/api/brands/:brand_id/withdraws/:id', logger(), required('brand'), brand_withdraw.update)

// Site menu
app.get('/api/sites/:site_id/menu/categories', logger(), menu_site_category.get_site_menu_category_list)
app.get('/api/sites/:site_id/momo-mini/sync', logger(), momo_mini_app.sync_master_data)
app.post('/api/sites/:site_id/menu/categories', logger(), required('add_menu_site'), menu_site_category.create_update_site_menu_category)
app.put('/api/sites/:site_id/menu/categories', logger(), required('update_menu_site'), menu_site_category.update_site_menu_category_item)
app.get('/api/sites/:site_id/menu/item/:item_id', logger(), required('delete_menu_site'), menu_site_category.get_menu_item_from_menu_brand)

// app.delete('/api/sites/:site_id/menu', logger(), required("menu"), delete_all_site_menu_category)
app.post('/api/sites/:site_id/menu/clone_from_brands', logger(), required('menu_site'), menu_site_category.clone_menu_from_a_brand)
app.post('/api/sites/:site_id/menu/clone_from_sites', logger(), required('menu_site'), menu_site_category.clone_menu_from_a_site)
app.get('/api/sites/:site_id/menu/latest', logger(), required('menu_site'), menu_site_category.get_latest_site_menu)
app.get('/api/sites/:site_id/menu/diffs', logger(), required('menu_site'), menu_site_category.diff_menu_from_apps)
app.get('/api/sites/:site_id/menu/menu_app_with_stocks', logger(), required('menu_site'), menu_site_category.get_site_menu_app_with_stocks)
app.post('/api/sites/:site_id/menu/items/:item_id/sync', logger(), required(), menu_site_category.sync_menu_item_to_apps)
app.post('/api/sites/:site_id/menu/items/:item_id/delete_and_sync', logger(), required(), menu_site_category.delete_and_sync_menu_item_to_apps)
app.post('/api/sites/:site_id/menu/categories/:category_id/delete_and_sync', logger(), required(), menu_site_category.delete_and_sync_menu_category_to_apps)

app.post('/api/sites/:site_id/opens', logger(), required('setting_open_time'), site.update_store_open)
app.get('/api/sites/:site_id/open/hours', logger(), required('get_site_opening'), site.get_store_opening_hours)
app.post('/api/sites/:site_id/opening_hour/sync', logger(), required('setting_open_time'), site.sync_store_opening_hours)
app.get('/api/sites/:site_id/open/special_hours', logger(), required('get_site_opening'), site.get_store_special_opening_hours)

app.get('/api/site/reports', logger(), required(), report.get_report)
app.get('/api/site/report_by_durations', logger(), required(), report.get_report_by_duration)
app.get('/api/site/report_by_duration/exports', logger(), required(), report.get_report_by_duration_to_excels)
app.get('/api/site/report/histories', logger(), required(), report.get_report_history_list)

// Summary API endpoints
app.get('/api/summary/dashboard', logger(), required(), summary.get_dashboard_summary)

app.get('/api/sites/complete-orders', logger(), required(), report.get_completed_orders)
app.get('/api/sites/complete-orders/export', logger(), required(), report.export_completed_orders)
app.get('/api/sites/accountant-orders/export', logger(), required(), report.export_accountant_orders)
app.get('/api/sites/cancel-orders/export', logger(), required(), report.export_cancelled_orders)
app.get('/api/sites/modify-orders/export', logger(), required(), report.export_modify_orders)
app.get('/api/sites/orders/feedbacks', logger(), required(), report.export_order_feedbacks)

app.get('/api/site/export/orders_by_user', logger(), required(), report.export_orders_by_user)

app.post('/api/order/create_v2', logger(), required('order'), orders.create_local_order_v2)
app.post('/api/order/create_temp_bill', logger(), required('order'), orders.print_temporary_bill_for_cart)
app.post('/api/order/draft', logger(), required('order'), orders.save_local_draft_order)
app.post('/api/order/draft_v2', logger(), required('order'), orders.save_local_draft_order_v2)
app.post('/api/order/draft/:order_id/cart', logger(), required('order'), orders.draft_order_to_cart)
app.delete('/api/order/draft/:order_id', logger(), required('order'), orders.delete_local_order)
app.post('/api/verify_token', logger(), verify_token)
app.post('/api/order/check_hub_stocks', logger(), required('order'), orders.check_hub_stocks)

// pos app
app.post('/api/pos/sites/:site_id/get_vouchers', logger(), required(), pos_order.get_available_vouchers)
app.post('/api/pos/sites/:site_id/get_shipments', logger(), required(), pos_order.get_shipment)
app.post('/api/pos/sites/:site_id/get_pickup_slots', logger(), required(), pos_order.get_pickup_slots)
app.post('/api/pos/sites/:site_id/apply_vouchers', logger(), required(), pos_order.apply_vouchers)
app.post('/api/pos/sites/:site_id/orders', logger(), required(), pos_order.create_order)

// it tickets
app.post(`/api/tickets`, required(), tickets.create_ticket)
app.put(`/api/tickets/:code`, required(), tickets.update_ticket)
app.post(`/api/tickets/:code/comments`, required(), tickets.add_comment)
// app.post(`/api/tickets/:code/jira`, required('it_support'), tickets.create_jira_ticket)
app.get(`/api/tickets`, required(), tickets.get_tickets)
app.get(`/api/tickets/:code`, required(), tickets.get_ticket)
app.delete(`/api/tickets/:code`, required(), tickets.delete_ticket)

// HE
app.post('/api/partner/deactivate', logger(), required('partner'), partners.deactivate_self)
app.get(`/api/partner/saved-accounts`, required('partner'), partners.get_saved_bank_accounts)
app.post(`/api/partner/members/invite`, required('partner'), partners.invite_member)
app.post(`/api/partner/members/accept`, partners.accept_invitation)
app.get(`/api/partner/members/check_invitation`, partners.check_invitation)
// app.put(`/api/partner/members/:id`, required('partner'), partners.update_member)
app.get(`/api/partner/members`, required('partner'), partners.get_members_list)
// app.delete(`/api/partner/members/:id`, required('partner'), partners.remove_member)

app.get('/api/partner', logger(), required(), partners.get_partners)
app.post('/api/partner/:partner_id/approve', logger(), required(), partners.approve_partner)

app.get(`/api/partner/customers`, required('partner'), partners.get_customers)
app.get(`/api/partner/customers/:customer_id/orders`, required('partner'), partners.get_customer_orders)
app.post(`/api/partner/customers`, required('partner'), partners.create_customer)
app.get('/api/partner/customers/:customer_id', logger(), required('partner'), partners.get_customer)
app.post(`/api/partner/select-customer`, required('partner'), partners.select_customer)
app.put('/api/partner/customers/:customer_id', logger(), required('partner'), partners.update_customer)
app.get(`/api/partner/campaigns`, required('partner'), campaigns.partner_get_campaign_list)
app.post(`/api/partner/campaigns`, required('partner'), campaigns.partner_join_campaign)
app.get(`/api/partner/campaigns/:id`, required('partner'), campaigns.partner_get_campaign_detail)
app.get(`/api/partner/commission`, required('partner'), partner_report.get_partner_commissions)
app.get(`/api/partner/report`, required('partner'), partner_report.get_report)
app.get(`/api/partner/top-sales`, required('partner'), partner_report.get_top_sale_items)
app.post('/api/partner/sites/:site_id/cart/create', logger(), required('partner'), cart.create_order)
app.get('/api/partner/sites/:site_id/orders', logger(), required('partner'), partner_order.partner_get_user_orders)
app.get('/api/sites/:site_id/user/orders', logger(), required(), partner_order.he_get_user_orders)
app.get('/api/sites/:site_id/user/orders/:order_id', logger(), required(), partner_order.he_get_user_order_details)
app.get('/api/partner/withdraws', logger(), required('partner'), partner_withdraw.get_list)
app.get('/api/partner/banners', logger(), required('partner'), partners.get_banners)
app.get('/api/partner/withdraws/reports', logger(), required('partner'), partner_withdraw.get_balance_reports)
app.post('/api/partner/withdraws', logger(), required('partner'), partner_withdraw.create)
app.put('/api/partner/withdraws/:id', logger(), required('partner'), partner_withdraw.update)
app.delete('/api/partner/withdraws/:id', logger(), required('partner'), partner_withdraw.delete)

// Seller app
app.get('/api/partner/sites/:site_id/user/carts', logger(), required('partner'), cart.partner_get_cart)
app.post('/api/partner/sites/:site_id/user/carts', logger(), required('partner'), cart.partner_update_cart)
app.post('/api/partner/sites/:site_id/user/cart/items', logger(), required('partner'), cart.partner_update_cart_item)
app.post('/api/partner/sites/:site_id/user/cart/addresses', logger(), required('partner'), cart.partner_update_cart_address)
app.get('/api/partner/sites/:site_id/user/cart/shipments', logger(), required('partner'), cart.partner_get_cart_shipment)
app.post('/api/partner/sites/:site_id/user/cart/shipping_promo', logger(), required('partner'), cart.partner_apply_promo_code)
// app.post('/api/partner/sites/:site_id/user/cart/check_voucher', logger(), required('partner'), cart.check_voucher)
app.post('/api/partner/sites/:site_id/user/cart/shipments', logger(), required('partner'), cart.partner_update_cart_shipment)
app.post('/api/partner/sites/:site_id/user/cart/create', logger(), required('partner'), cart.partner_create_order)
app.get('/api/partner/users/phone/:phone', logger(), required('partner'), partners.get_user_by_phone)
app.post('/api/partner/register', logger(), partners.register_partner)
app.get('/api/partner/menu/price_config', logger(), required('partner'), partners.get_brand_price_config)

app.get('/api/provinces', logger(), province.get_provinces)

app.get('/api/app_versions', app_version.get_app_version_list)
app.post('/api/app_versions', app_version.create_app_version)
app.get('/api/app_versions/:bundle_identifier', app_version.get_app_version_details)
app.put('/api/app_versions/:bundle_identifier', app_version.update_app_version)
app.delete('/api/app_versions/:bundle_identifier', app_version.delete_app_version)

// User auth by API Key
app.post('/v1/partner/orders', logger(), auth_by_api_key(), partner_3rd.create_order)

// delivery
// ahamove, grabexpress callback, shopee
app.all('/v1/partners/:partner_id/update-order-status', logger(), delivery.update_order_status_webhook)


// Grab mart merchant API
app.post('/v1/partners/grab/oauth/token', logger(), grab_partner.get_access_token)
app.get('/v1/partners/grab/merchant/menu', logger(), grab_partner.get_mart_menu_v2)
app.all('/v1/partners/grab/webhook/orders', logger(), grab_partner.order_webhook)
app.all('/v1/partners/grab/webhook/menu', logger(), grab_partner.menu_webhook)

// Grab Food merchant API
app.post('/v1/partners/grab_food/oauth/token', logger(), grab_partner.get_access_token)
app.get('/v2/partners/grab_food/merchant/menu', logger(), grab_partner.get_food_menu_v2)
app.all('/v1/partners/grab_food/webhook/orders', logger(), grab_partner.grab_food_order_webhook)
app.all('/v1/partners/grab_food/webhook/menu', logger(), grab_partner.grab_food_menu_webhook)

// Shopee merchant API
app.post('/v1/partners/shopee_fresh/oauth/token', logger(), shopee_partner.get_access_token)
app.all('/v1/partners/shopee_fresh/webhook/orders', logger(), shopee_partner.webhook)
// app.get('/v1/partners/shopee_fresh/merchant/menu', logger(), shopee_partner.get_mart_menu)
app.get('/v1/partners/shopee_fresh/merchant/menu', logger(), shopee_partner.get_mart_menu_v2)

app.post('/v1/partners/shopee_food/oauth/token', logger(), shopee_partner.get_access_token)
app.all('/v1/partners/shopee_food/webhook/orders', logger(), shopee_partner.webhook)
app.get('/v1/partners/shopee_food/merchant/menu', logger(), shopee_partner.get_mart_menu_v2)
app.get('/v1/partners/shopee_seller/mcn_list', logger(), shopee_seller.get_mcn_list)



// BE merchant API
app.post('/v1/partners/be_food/oauth/token', logger(), be_partner.get_access_token)
// app.get('/v1/partners/be_food/merchant/menu', logger(), be_partner.get_mart_menu)
app.all('/v1/partners/be_food/webhook/menu', logger(), be_partner.be_food_menu_webhook)
app.all('/v1/partners/be_food/webhook/orders', logger(), be_partner.be_food_order_webhook)

// Banker
app.all('/v1/partners/ocb/api/transaction/create', logger(), banker_callback.ocb_order_webhook)

// Lazada merchant API
app.get('/api/partners/lazada/exchange_tokens', logger(), merchant_ecom.get_lazada_access_token)
app.post('/api/partners/lazada/webhook/orders', logger(), merchant_ecom.lazada_order_webhook)

// Tiktok
app.get('/api/partners/tiktok/exchange_tokens', logger(), tiktok_partner.get_access_token)
app.get('/api/partners/tiktok/webhook/orders', logger(), tiktok_partner.webhook)


// retailer
app.get('/api/brands/retailer/sale-config-template', logger(), required(), brand_retailer.download_config_template)
app.get('/api/brands/retailer/sale-config', logger(), required('sale_config'), brand_retailer.get_sale_config)
app.get('/api/brands/:brand_id/retailer/sale-config', logger(), brand_retailer.get_sale_config_by_brand)
app.post('/api/brands/:brand_id/retailer/sale-config', logger(), required('add_promotion'), brand_retailer.brand_create_config)
app.put('/api/brands/:brand_id/retailer/sale-config/:config_id', logger(), required('update_promotion'), brand_retailer.brand_update_config)
app.delete('/api/brands/:brand_id/retailer/sale-config/:config_id', logger(), required('delete_promotion'), brand_retailer.brand_delete_sale_config)
app.get('/api/brands/:brand_id/retailer/sale-config/:config_id/voucher-usage', logger(), required(), brand_retailer.get_voucher_usage)
app.get('/api/partner/report/export', logger(), required('partner'), report.retailer_report_for_retailer)

// customer profile
app.post('/api/customer_profiles/export', logger(), required(), customer_profiles.export_profiles)
app.post('/api/customer_profiles', logger(), required(), customer_profiles.get_profiles)
app.get('/api/cron/customer_profiles/sync', logger(), required('internal'), customer_profiles.sync_segment_for_updated_profiles)
app.post('/api/customer_profiles/segments', logger(), required(), customer_profiles.create_segment)
app.get('/api/customer_profiles/segments', logger(), required(), customer_profiles.get_segments)
app.put('/api/customer_profiles/segments/:id', logger(), required(), customer_profiles.update_segment)
app.delete('/api/customer_profiles/segments/:id', logger(), required(), customer_profiles.delete_segment)

// momo mini app
app.get('/api/momo/mini-app/stores', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_all_stores)
app.get('/api/momo/mini-app/stores/:store_id', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_store_detail)
app.get('/api/momo/mini-app/stores/:store_id/categories', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_all_product_categories)
app.get('/api/momo/mini-app/stores/:store_id/products', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_products_detail)
app.get('/api/momo/mini-app/stores/:store_id/products/:product_id', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_product_detail)
app.post('/api/momo/mini-app/orders', logger(), auth_by_api_key('momo_mini'), momo_mini_app.create_order)
app.post('/api/momo/mini-app/orders/cancel', logger(), auth_by_api_key('momo_mini'), momo_mini_app.cancel_order)
app.post('/api/momo/mini-app/orders/confirm', logger(), auth_by_api_key('momo_mini'), momo_mini_app.confirm_order)
app.post('/api/momo/mini-app/shipment-fee', logger(), auth_by_api_key('momo_mini'), momo_mini_app.get_shipment_fee)

// voucher
app.get('/api/sites/:site_id/customers/:phone', logger(), required(), voucher.get_customer_info)
app.get('/api/sites/:site_id/customers/:phone/vouchers', logger(), required(), voucher.get_customer_vouchers)
app.post('/api/sites/:site_id/customers/:phone/otp', logger(), required(), voucher.request_otp)
app.post('/api/sites/:site_id/customers', logger(), required(), voucher.create_customer)
app.post('/api/sites/:site_id/vouchers/validate', logger(), required(), voucher.check_voucher)
app.post('/api/dpoint/update-voucher', logger(), auth_by_api_key(), voucher.update_voucher) // callback from dpoint
app.get('/api/dpoint/order-info', logger(), auth_by_api_key('dpoint'), voucher.get_order_info)

// partner-hub
app.get('/api/sites/:site_id/partner-hub/summary', logger(), required(), partner_hub.get_partner_hub_summary)
app.get('/api/sites/:site_id/partner-hub/chart', logger(), required(), partner_hub.get_chart)

// material management, by brand
app.get('/api/brands/:brand_id/materials', logger(), required('brand'), material.get_materials)
app.post('/api/brands/:brand_id/materials', logger(), required('brand'), material.create_material)
app.put('/api/brands/:brand_id/materials/:code', logger(), required('brand'), material.update_material)
app.delete('/api/brands/:brand_id/materials/:code', logger(), required('brand'), material.delete_material)
app.get('/api/brands/:brand_id/materials/import-template', logger(), required('brand'), material.get_import_template)
app.post('/api/brands/:brand_id/materials/import', logger(), required('brand'), material.import_materials)
app.get('/api/brands/:brand_id/materials/export', logger(), required('brand'), material.export_materials)

// core products
app.get('/api/brands/:brand_id/core-products', logger(), required(), core_product.get_core_products)
app.post('/api/brands/:brand_id/core-products', logger(), required(), core_product.create_core_product)
app.put('/api/brands/:brand_id/core-products/:code', logger(), required(), core_product.update_core_product)
app.delete('/api/brands/:brand_id/core-products/:code', logger(), required(), core_product.delete_core_product)
app.get('/api/brands/:brand_id/core-products/import-template', logger(), required(), core_product.get_import_template)
app.post('/api/brands/:brand_id/core-products/import', logger(), required(), core_product.import_core_products)
app.get('/api/brands/:brand_id/core-products/export', logger(), required(), core_product.export_core_products)
app.get('/api/brands/:brand_id/core-products/:code', logger(), required(), core_product.get_core_product_details)

// core product categories
app.get('/api/brands/:brand_id/core-product-categories', logger(), required(), core_product.get_core_product_categories)
app.post('/api/brands/:brand_id/core-product-categories', logger(), required(), core_product.create_core_product_category)
app.delete('/api/brands/:brand_id/core-product-categories/:id', logger(), required(), core_product.delete_core_product_category)

// core product source
app.get('/api/brands/:brand_id/core-product-sources', logger(), required(), core_product.get_core_product_sources)
app.post('/api/brands/:brand_id/core-product-sources', logger(), required(), core_product.create_core_product_source)
app.delete('/api/brands/:brand_id/core-product-sources/:id', logger(), required(), core_product.delete_core_product_source)

// core product unit
app.get('/api/brands/:brand_id/core-product-units', logger(), required(), core_product.get_core_product_units)
app.post('/api/brands/:brand_id/core-product-units', logger(), required(), core_product.create_core_product_unit)
app.delete('/api/brands/:brand_id/core-product-units/:id', logger(), required(), core_product.delete_core_product_unit)

// core product, menu template
app.get('/api/v2/brands/:brand_id/menu_templates', logger(), required(), cp_menu_template.get_menu_templates)
app.post('/api/v2/brands/:brand_id/menu_templates', logger(), required(), cp_menu_template.create_template)
app.put('/api/v2/brands/:brand_id/menu_templates/:template_id', logger(), required(), cp_menu_template.update_template)
app.delete('/api/v2/brands/:brand_id/menu_templates/:template_id', logger(), required(), cp_menu_template.delete_template)
app.get('/api/v2/brands/:brand_id/menu_templates/:template_id', logger(), required(), cp_menu_template.get_template)
app.post('/api/v2/menu_templates/:template_id/categories', logger(), required(), cp_menu_template.create_category)
app.get('/api/v2/menu_templates/:template_id/categories', logger(), required(), cp_menu_template.get_categories)
app.put('/api/v2/menu_templates/:template_id/categories/:category_id', logger(), required(), cp_menu_template.update_category)
app.delete('/api/v2/menu_templates/:template_id/categories/:category_id', logger(), required(), cp_menu_template.delete_category)
app.post('/api/v2/menu_templates/:template_id/categories/:category_id/items', logger(), required(), cp_menu_template.create_item)
app.put('/api/v2/menu_templates/:template_id/categories/:category_id/items/:item_id', logger(), required(), cp_menu_template.update_item)
app.delete('/api/v2/menu_templates/:template_id/categories/:category_id/items/:item_id', logger(), required(), cp_menu_template.delete_item)
// options category
app.post('/api/v2/menu_templates/:template_id/option-categories', logger(), required(), cp_menu_template.create_option_category)
app.put('/api/v2/menu_templates/:template_id/option-categories/:id', logger(), required(), cp_menu_template.update_option_category)
app.delete('/api/v2/menu_templates/:template_id/option-categories/:id', logger(), required(), cp_menu_template.delete_option_category)
app.post('/api/v2/menu_templates/:template_id/apply', logger(), required(), cp_menu_template.apply_template_to_site)

// core product, site menu
app.get('/api/v2/sites/:site_id/menu', logger(), required(), site_menu.get_menu)
app.post('/api/v2/sites/:site_id/menu/categories', logger(), required(), site_menu.create_category)
app.put('/api/v2/sites/:site_id/menu/categories/:category_id', logger(), required(), site_menu.update_category)
app.delete('/api/v2/sites/:site_id/menu/categories/:category_id', logger(), required(), site_menu.delete_category)
app.post('/api/v2/sites/:site_id/menu/categories/:category_id/items', logger(), required(), site_menu.create_item)
app.put('/api/v2/sites/:site_id/menu/categories/:category_id/items/:item_id', logger(), required(), site_menu.update_item)
app.delete('/api/v2/sites/:site_id/menu/categories/:category_id/items/:item_id', logger(), required(), site_menu.delete_item)
app.post('/api/v2/sites/:site_id/menu/categories/:category_id/items/:item_id/channel', logger(), required(), site_menu.update_item_sale_channel)
app.post('/api/v2/sites/:site_id/menu/bulk-update-channel', logger(), required(), site_menu.bulk_update_item_sale_channel)
app.delete('/api/v2/sites/:site_id/menu/categories/:category_id/items/:item_id/channel/:channel', logger(), required(), site_menu.delete_item_sale_channel)
app.post('/api/v2/sites/:site_id/menu/publish', logger(), required(), site_menu.publish_menu)
// options category
app.post('/api/v2/sites/:site_id/menu/option-categories', logger(), required(), site_menu.create_option_category)
app.put('/api/v2/sites/:site_id/menu/option-categories/:id', logger(), required(), site_menu.update_option_category)
app.delete('/api/v2/sites/:site_id/menu/option-categories/:id', logger(), required(), site_menu.delete_option_category)

// core product, grab
app.post('/api/v2/core-product/grab_food/oauth/token', logger(), cp_grab.get_access_token)
app.get('/api/v2/core-product/grab_food/merchant/menu', logger(), cp_grab.get_menu)
// app.all('/v1/partners/grab_food/webhook/orders', logger(), grab_partner.grab_food_order_webhook)
app.all('/api/v1/core-product/grab_food/webhook/menu', logger(), cp_grab.grab_food_menu_webhook)

// site cms
app.get('/api/sites/:site_id/cms', logger(), required(), site_cms.get_site_cms)
app.post('/api/sites/:site_id/cms', logger(), required(), site_cms.update_site_cms)
// customer support
app.post('/api/customer-support/orders', logger(), customer_support_create_order)

// api v2
app.post('/api/v2/login', logger(), login_v2)
app.post('/api/v2/verify_token', logger(), verify_token_v2)
app.post('/api/v2/logout', logout_v2);

app.get('/api/v1/hub/working-shifts', logger(), required(), working_shift.get_all_hubs_working_shift)
app.get('/api/v1/hub/working-shift/reports', logger(), required(), working_shift.get_working_shifts_report)
app.get('/api/v1/hubs/:hub_id/last-working-shifts', logger(), required(), working_shift.get_last_working_shift)
app.get('/api/v1/hubs/:hub_id/working-shifts', logger(), required(), working_shift.get_working_shifts)
app.post('/api/v1/hubs/:hub_id/working-shifts', logger(), required(), working_shift.create_working_shifts)
app.put('/api/v1/hubs/:hub_id/working-shifts/:shift_id', logger(), required(), working_shift.update_working_shifts)
app.delete('/api/v1/hubs/:hub_id/working-shifts/:shift_id', logger(), required(), working_shift.delete_working_shifts)
app.get('/api/v1/hubs/:hub_id/working-shifts/:shift_id/prints', logger(), required(), working_shift.print_working_shifts)

app.post('/api/v1/sites/:site_id/integration/apps/:app_id/activate', logger(), required(), app_integration.activate)
app.post('/api/v1/sites/:site_id/integration/apps/:app_id/deactivate', logger(), required(), app_integration.deactivate)
app.post('/api/v1/sites/:site_id/integration/shopee/login', logger(), required(), app_integration.shopee_login)
app.post('/api/v1/sites/:site_id/integration/be/login', logger(), required(), app_integration.be_login)


app.use((err, req, res, next) => {
  console.log(err)
  console.log(err)
  if (err.errors) {
    res.json({
      success: false,
      errors: Object.keys(err.errors).map((key) => {
        return {
          kind: err.errors[key].kind,
          path: err.errors[key].path,
          message: err.errors[key].message,
        }
      }),
    })
    return
  }

  console.log(err)

  res.status(500).json({ error: err.message })
})

app.use((req, res) => {
  res.status(404).send('404 - Not Found')
})


app.listen(port, () => {
  console.log('API ver: 1.1.0')
  console.log('Server listening on port:', port)
})
