const { <PERSON><PERSON>, Site, User, BrandMenu, BrandMasterData, VendorCallback } = require('../../.shared/database')
const _ = require('lodash')
const moment = require('moment')
const axios = require('../../.shared/axios')
const { text_slugify, deep_merge_object } = require('../../.shared/helper');
const jwt = require('jsonwebtoken')

let router = {};

const CLIENT_ID = "be_food";
const CLIENT_SECRET = "7X9bD-KcJLszYGx2";

router.get_access_token = async (req, res) => {
    const { client_id, client_secret, grant_type, scope } = req.body;

    if (client_id !== CLIENT_ID || client_secret != CLIENT_SECRET) {
        return res.status(401).json({
            success: false,
            message: "invalid_client_id_or_client_secret",
        });
    }
    const access_token = jwt.sign({ username: <PERSON><PERSON>IENT_ID }, CLIENT_SECRET, { expiresIn: '7d' })


    res.json({
        access_token: access_token,
        token_type: "Bearer",
        expires_in: 7 * 60 * 60,
    })
}



router.get_food_menu = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }

    if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
        res.status(400).json({
            success: false,
            message: "invalid_token",
        });
        return;
    }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }

    const master_data = await BrandMasterData.findOne({ brand_id: site.brand_id, key: 'be_food_menu' }).lean();
    if (!master_data) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }

    const menu = master_data.value
    const result = {
        merchantID: merchantID,
        partnerMerchantID: partnerMerchantID,
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sellingTimes: [
            {
                "id": "nexpos_sell_time_daily",
                "startTime": "2023-10-01 10:00:00",
                "endTime": "2025-01-01 00:00:00",
                "name": "Best deal",
                "serviceHours": {
                    "mon": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "tue": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "wed": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "thu": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "fri": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sat": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sun": {
                        "openPeriodType": "OpenAllDay",
                    },
                }
            }
        ],
        categories: menu.categories.map(category => {
            return {
                id: category.categoryID,
                sellingTimeID: "nexpos_sell_time_daily",
                name: category.categoryName,
                availableStatus: category.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                items: category.items?.map(v => {
                    const grabModifyGroups = menu.modifierGroups.filter(v2 => v2.relatedItemIDs?.includes(v.itemID));
                    return {
                        id: v.itemID + '_' + moment().format('DD'),
                        sellingTimeID: "nexpos_sell_time_daily",
                        name: v.itemName,
                        availableStatus: v.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                        description: v.description,
                        price: v.priceInMin,
                        taxable: false,
                        photos: v.imageURL ? [v.imageURL] : [],
                        weight: v.weight,
                        modifierGroups: grabModifyGroups.map(v2 => {
                            return {
                                "id": v2.modifierGroupID,
                                "name": v2.modifierGroupName,
                                "nameTranslation": {},
                                "availableStatus": v2.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                "selectionRangeMin": v2.selectionRangeMin,
                                "selectionRangeMax": v2.selectionRangeMax,
                                "modifiers": v2.modifiers?.map(v3 => {
                                    return {
                                        "id": v3.modifierID,
                                        "name": v3.modifierName,
                                        "nameTranslation": {},
                                        "availableStatus": v3.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                        "price": v3.priceInMin,
                                        "barcode": "",
                                        "advancedPricing": {}
                                    }
                                })
                            }
                        }) || [],
                    }
                }).filter(Boolean)
            }
        }).filter(Boolean).filter(v => v.items.length > 0)
    }

    return res.json(result);
}


router.be_food_menu_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'be',
        type: 'menu',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

router.be_food_order_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'be',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

module.exports = router;