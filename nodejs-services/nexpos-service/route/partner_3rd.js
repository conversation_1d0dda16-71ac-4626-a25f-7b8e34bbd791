const axios = require('axios')
const helper = require('./helper')
const moment = require('moment')
const _ = require('lodash')
const { User, Role, Order, Site, UserCart, SiteMenuGroup, SiteOrderIndex } = require('../../.shared/database')
const ahamove = require('../../.shared/delivery/ahamove')
const be = require('../../.shared/delivery/be')
const grab = require('../../.shared/delivery/grab_express')
const { v4 } = require('uuid')
const { map_order } = require('../../.shared/merchant/mapping')
const validator = require('validator')
const { gen_external_id } = require('../../.shared/helper')
let router = {}

router.create_order = async (req, res) => {
  const {
    store_id,
    products,
    customer_name,
    customer_phone,
    customer_address,
    customer_address_obj,
    total_discount,
    total_paid,
    payment_method,
    note
  } = req.body

  if (validator.isEmpty(store_id)) {
    return res.status(400).json({ success: false, error_code: ' ' })
  }

  if (validator.isEmpty(payment_method)) {
    return res.status(400).json({ success: false, error: 'payment_method_is_empty' })
  }

  const site = await Site.findById(store_id)

  const order_group = `${site.code?.toUpperCase() || "NEXDOR"}-${moment().format('YYMMDD')}`
  const site_next_index = await SiteOrderIndex.findOneAndUpdate(
    { site_id: site._id, group: order_group },
    { $inc: { current_index: 1 } },
    { new: true, upsert: true }
  )
  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  const now = moment()
  const order = {
    source: 'local',
    site_id: store_id,
    hub_id: site.hub_id,
    status: 'PENDING',
    order_id: order_id,
    data_mapping: {},
    data: {
      id: order_id,
      order_id: order_id,
      source: 'local',
      order_time: now.toISOString(),
      pick_time: null,
      delivery_time: null,
      order_time_sort: now.unix(),
      driver_name: '',
      driver_phone: '',
      customer_phone: customer_phone || '',
      customer_name: customer_name || '',
      customer_address: customer_address || '',
      customer_address_obj,
      dishes: products.map((v) => {
        return {
          id: v.id,
          name: v.name,
          quantity: v.quantity,
          price: v.price,
          description: '',
          options: [],
          note: v.note || '',
        }
      }),
      commission: 0,
      total: total_paid,
      total_for_biz: total_paid,
      total_shipment: 0,
      total_discount: 0,
      total_display: total_paid.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
      affiliate_source: '',
      note: note || '',
      cancel_reason: '',
      payments: [{ method: payment_method, total: total_paid, status: 'PENDING' }],
      shipment_fee: 0,
      payment_method: payment_method,
    }
  }
  order.data_mapping = map_order(order.source, order.data)
  order.external_id = gen_external_id()

  if (payment_method !== 'CASH') {
    order.status = 'WAITING_PAYMENT'
  }

  const new_order = await Order.create(order)

  res.json({
    success: true,
    data: new_order,
  })
}

module.exports = router
