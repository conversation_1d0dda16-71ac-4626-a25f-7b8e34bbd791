const { VendorCallback } = require('../../.shared/database')
const _ = require('lodash')
const moment = require('moment')
const axios = require('../../.shared/axios')
const { text_slugify } = require('../../.shared/helper');
const tiktok = require('../../.shared/merchant/tiktok')

let router = {};


router.get_access_token = async (req, res) => {
    const { app_key, code } = req.query;
    const data = await tiktok.get_token(code)
    if (!data || !data.access_token) {
        return res.json({ success: false, error: 'tiktok_invalid_code' })
    }

    return res.json({
        success: true,
        data: {
            access_token: data.access_token,
            refresh_token: data.refresh_token || data.access_token,
            site_name: data.seller_name,
        },
    })
}

router.webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'tiktok',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

module.exports = router;