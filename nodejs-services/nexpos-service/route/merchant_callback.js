const { Hub, Site, User, VendorCallback } = require('../../.shared/database')
const { toNumber } = require('lodash')
const router = {}

router.payment_callbacks = async (req, res) => {
    const { vendor, callback_type } = req.query

    res.json({
        success: true,
    });
}

router.order_callbacks = async (req, res) => {
    const { callback_type } = req.params
    await VendorCallback.create({
        vendor: 'shopee',
        type: callback_type,
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        method: req.method,
    })

    res.json({
        success: true,
    });
}
module.exports = router