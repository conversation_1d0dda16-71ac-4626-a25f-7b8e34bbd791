const { Brand, User, Role, BrandBillConfig } = require('../../.shared/database')
const fs = require('fs');

const get_bill_list = async (req, res) => {
    try {
        const { brand_id } = req.params;
        const bill_list = await BrandBillConfig.paginate({ brand_id }, {
            page: Number(req.query.page || 1),
            limit: Number(req.query.limit || 100),
            sort: { created_at: -1 },
            customLabels: { docs: 'data' }
        });

        res.json({
            success: true,
            ...bill_list
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const create_bill = async (req, res) => {
    const { brand_id } = req.params;
    const {
        name,
        bill_type,
        bill_size,
        show_bank_payment,
    } = req.body;

    try {
        const content_html = bill_type === 'bill_for_label' ?
            fs.readFileSync('files/label_default.ckeditor.html', 'utf8') :
            fs.readFileSync('files/bill_default.ckeditor.html', 'utf8');

        const result = await BrandBillConfig.create({
            name,
            brand_id,
            bill_type,
            bill_size,
            show_bank_payment,
            content_html,
            active: false,
        });

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const update_bill = async (req, res) => {
    const {
        name,
        bill_type,
        bill_size,
        show_bank_payment,
        content_html,
        active,
    } = req.body;

    try {
        const result = await BrandBillConfig.findByIdAndUpdate(req.params.bill_id,
            {
                name,
                bill_type,
                bill_size,
                show_bank_payment,
                content_html,
                active,
            },
            { new: true }
        );
        if (!result) {
            return res.status(404).json({ success: false, message: 'Bill config not found' });
        }
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const delete_bill = async (req, res) => {
    try {
        const deletedConfig = await BrandBillConfig.findByIdAndDelete(req.params.bill_id);
        if (!deletedConfig) {
            return res.status(404).json({ success: false, message: 'Bill config not found' });
        }
        res.json({
            success: true,
            data: deletedConfig,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

module.exports = {
    get_bill_list,
    create_bill,
    update_bill,
    delete_bill
}
