const { Order, VendorCallback } = require('../../.shared/database')
const lazada = require('../../.shared/merchant/lazada')
const _ = require('lodash')
const moment = require('moment')
const axios = require('../../.shared/axios')
const { text_slugify } = require('../../.shared/helper');

let router = {};

router.get_lazada_access_token = async (req, res) => {
    const data = await lazada.get_token(req.query.code)

    return res.json({
        success: true,
        data: {
            access_token: data.access_token,
            refresh_token: data.refresh_token,
        },
    })
}

router.lazada_order_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'lazada',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    // const order_id = req.body?.data?.trade_order_id
    // const order = await lazada.get_order_detail(order_id)
    // const db_order = await Order.findOne({ order_id })

    res.json({ success: true });
}


module.exports = router;