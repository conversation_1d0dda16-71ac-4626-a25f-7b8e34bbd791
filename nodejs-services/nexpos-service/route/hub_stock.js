const { Hub, HubStock, HubStockFile, HubStockHistory, Site, BrandMenu } = require('../../.shared/database')
const _ = require('lodash');
const moment = require('moment');
const xlsx = require('xlsx');
const { upload_file } = require('../../.shared/storage')
const { upload_single } = require('../middlewares/upload_file');

const { text_slugify } = require('../../.shared/helper');

const router = {}

router.get_stock_items = async (req, res) => {
    const { hub_id } = req.params;
    const { page, limit, name } = req.query;
    try {
        let filter = { hub_id }
        let sort = { name: 1 }
        if (name) {
            filter['$text'] = { $search: name, $caseSensitive: false }
            sort = { score: { $meta: "textScore" } }
        }
        const hub_stocks = await HubStock.paginate(filter, {
            page: Number(page || 1),
            limit: Number(limit || 100), // Max 100 docs
            sort,
            customLabels: { docs: 'data' },
        })
        res.json({
            success: true,
            ...hub_stocks,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.create_stock_item = async (req, res) => {
    const { hub_id } = req.params;
    const { name, code, quantity, unit, locked_status } = req.body
    try {
        const hub_stock = await HubStock.findOneAndUpdate(
            { hub_id, code },
            { name, quantity, unit, locked_status },
            { upsert: true, new: true }
        )
        res.json({
            success: true,
            data: hub_stock,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.update_stock_item = async (req, res) => {
    const { hub_id, item_id } = req.params;
    const { name, code, quantity, unit, locked_status } = req.body
    try {
        const hub_stock = await HubStock.findOneAndUpdate(
            { hub_id, code },
            { name, quantity, unit, locked_status },
            { upsert: false, new: false }
        )
        if (!hub_stock) {
            return res.status(404).json({
                success: false,
                error: "item_not_found",
            });
        }
        if (hub_stock.quantity != quantity) {
            await HubStockHistory.create({
                hub_id,
                code,
                from_quantity: hub_stock.quantity,
                to_quantity: quantity,
                updated_type: 'edit',
                updated_by: req.user.username,
            })
        }
        res.json({
            success: true,
            data: hub_stock,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.delete_stock_item = async (req, res) => {
    const { hub_id, code } = req.params;
    try {
        await HubStock.deleteOne({ hub_id, code })

        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.get_stock_file_template = async (req, res) => {
    const { hub_id } = req.params;
    try {
        let filter = { hub_id }
        const hub = await Hub.findById(hub_id).lean()
        let hub_stocks = await HubStock.find(filter).sort({ name: 1 }).lean()
        if (hub_stocks.length == 0) {
            hub_stocks = ["name", "code", "quantity", "unit"].map((key) => ({ [key]: "" }))
        }

        hub_stocks = hub_stocks.map(v => ({
            Name: v.name,
            Code: v.code,
            Quantity: "",
            Unit: v.unit,
            Note: "",
        }))

        const hub_stock_worksheet = xlsx.utils.json_to_sheet(hub_stocks);

        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Stocks');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const timestamp = moment().format('YYMMDDHHmm');
        const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stocks/${text_slugify(hub.name)}_${timestamp}.xlsx`, buff });
        res.json({
            success: true,
            data: file
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}

router.get_stock_files = async (req, res) => {
    const { hub_id } = req.params;
    const { page, limit, name } = req.query;
    try {
        let filter = { hub_id }
        let sort = { created_at: -1 }
        const hub_files = await HubStockFile.paginate(filter, {
            page: Number(page || 1),
            limit: Number(limit || 20), // Max 20 docs
            sort,
            customLabels: { docs: 'data' },
        })
        res.json({
            success: true,
            ...hub_files,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}

router.create_stock_file = [upload_single, async (req, res) => {
    const { hub_id } = req.params;
    const workbook = xlsx.read(req.file.buffer)
    const sheetNames = workbook.SheetNames;
    const excel_stock_data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNames[0]]).map(v => ({
        Code: String(v.Code),
        Name: String(v.Name),
        Quantity: Number(v.Quantity),
        Unit: String(v.Unit),
        Note: String(v.Note),
    }));

    const hub = await Hub.findById(hub_id).lean()
    const timestamp = moment().format('YYMMDDHHmm');
    const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stocks/${text_slugify(hub.name)}_${timestamp}.xlsx`, buff: req.file.buffer });

    for (const excel_stock_item of excel_stock_data) {
        if (!excel_stock_item.Code || !excel_stock_item.Quantity) {
            continue
        }
        const hub_stock = await HubStock.findOne({ hub_id, code: excel_stock_item.Code })
        if (hub_stock) {
            const from_quantity = hub_stock.quantity
            hub_stock.name = excel_stock_item.Name
            hub_stock.quantity += Number(excel_stock_item.Quantity)
            hub_stock.unit = excel_stock_item.Unit
            await hub_stock.save()

            await HubStockHistory.create({
                hub_id,
                code: excel_stock_item.Code,
                from_quantity: from_quantity,
                to_quantity: hub_stock.quantity,
                updated_type: 'import',
                updated_by: req.user.username,
                updated_import_file: file,
            })

        } else {
            await HubStock.create({
                hub_id,
                code: excel_stock_item.Code,
                name: excel_stock_item.Name,
                quantity: Number(excel_stock_item.Quantity),
                unit: excel_stock_item.Unit
            })

            await HubStockHistory.create({
                hub_id,
                code: excel_stock_item.Code,
                from_quantity: 0,
                to_quantity: Number(excel_stock_item.Quantity),
                updated_type: 'import',
                updated_by: req.user.username,
                updated_import_file: file,
            })
        }


    }
    try {
        const hub_file = await HubStockFile.create({ hub_id, file, note: "" })
        res.json({
            success: true,
            data: hub_file,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}]


router.get_stock_histories = async (req, res) => {
    const { hub_id, code } = req.params;
    const { page, limit } = req.query;
    try {
        let filter = { hub_id, code }
        let sort = { created_at: -1 }
        const hub_files = await HubStockHistory.paginate(filter, {
            page: Number(page || 1),
            limit: Number(limit || 20), // Max 20 docs
            sort,
            customLabels: { docs: 'data' },
        })
        res.json({
            success: true,
            ...hub_files,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}

router.create_stock_file = [upload_single, async (req, res) => {
    const { hub_id } = req.params;
    const workbook = xlsx.read(req.file.buffer)
    const sheetNames = workbook.SheetNames;
    const excel_stock_data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNames[0]]).map(v => ({
        Code: String(v.Code),
        Name: String(v.Name),
        Quantity: Number(v.Quantity),
        Unit: String(v.Unit),
        Note: String(v.Note),
    }));

    const hub = await Hub.findById(hub_id).lean()
    const timestamp = moment().format('YYMMDDHHmm');
    const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stocks/${text_slugify(hub.name)}_${timestamp}.xlsx`, buff: req.file.buffer });

    for (const excel_stock_item of excel_stock_data) {
        if (!excel_stock_item.Code || !excel_stock_item.Quantity) {
            continue
        }
        const hub_stock = await HubStock.findOne({ hub_id, code: excel_stock_item.Code })
        if (hub_stock) {
            const from_quantity = hub_stock.quantity
            hub_stock.name = excel_stock_item.Name
            hub_stock.quantity += Number(excel_stock_item.Quantity)
            hub_stock.unit = excel_stock_item.Unit
            await hub_stock.save()

            await HubStockHistory.create({
                hub_id,
                code: excel_stock_item.Code,
                from_quantity: from_quantity,
                to_quantity: hub_stock.quantity,
                updated_type: 'import',
                updated_by: req.user.username,
                updated_import_file: file,
            })

        } else {
            await HubStock.create({
                hub_id,
                code: excel_stock_item.Code,
                name: excel_stock_item.Name,
                quantity: Number(excel_stock_item.Quantity),
                unit: excel_stock_item.Unit
            })

            await HubStockHistory.create({
                hub_id,
                code: excel_stock_item.Code,
                from_quantity: 0,
                to_quantity: Number(excel_stock_item.Quantity),
                updated_type: 'import',
                updated_by: req.user.username,
                updated_import_file: file,
            })
        }


    }
    try {
        const hub_file = await HubStockFile.create({ hub_id, file, note: "" })
        res.json({
            success: true,
            data: hub_file,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}]


module.exports = router