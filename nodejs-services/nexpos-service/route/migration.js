require('dotenv').config({ path: './.env' })
const _ = require('lodash')
const fs = require('fs')
const path = require('path')
const { Site, Hub, User, Order, SiteMenuGroup, Address, EmailTemplate, BrandMenu, Brand, GhnProvinces, GhnDistricts, GhnWards, Role, Partner, Voucher } = require('../../.shared/database')
const lodash = require('lodash')
const { MongoClient, ObjectId } = require('mongodb')
const baemin = require('../../.shared/merchant/baemin')
const shopee = require('../../.shared/merchant/shopee')
const gojek = require('../../.shared/merchant/gojek')
const grab = require('../../.shared/merchant/grab')
const be = require('../../.shared/merchant/be')
const { map_order, map_menu } = require('../../.shared/merchant/mapping')
const slugify = require('slugify')
const ghn = require('../../.shared/delivery/ghn')

const map_site = async () => {
  const hubs = await Hub.find({})
  for (const hub of hubs) {
    const sites = hub.sites
    await Site.updateMany({ _id: sites }, { hub_id: hub._id })
  }
}
// map_site()


const site_create_test = async () => {
  for (let i = 2; i < 1001; i++) {
    await Site.create({
      brand_id: '648edb989e356cf1eda79f84',
      hub_id: '648edbb99e356cf1eda79f99',
      type: 'food',
      name: 'NutiFood Site #' + String(i),
      code: 'NUTIFOOD.' + String(i),
      address: 'Address #' + String(i),
      description: '',
      tokens: [
        {
          source: 'grab',
          site_id: '',
          username: '',
          password: '',
        },
        {
          source: 'grab_mart',
          site_id: '',
          username: '',
          password: '',
        },
        {
          source: 'baemin',
          site_id: '',
          username: '',
          password: '',
        },
        {
          source: 'shopee',
          site_id: '',
          access_token: '',
          refresh_token: '',
        },
        {
          source: 'shopee_fresh',
          site_id: '',
          access_token: '',
          refresh_token: '',
        },
        {
          source: 'gojek',
          site_id: '',
          access_token: '',
          refresh_token: '',
        },
        {
          source: 'be',
          site_id: '',
          username: '',
          password: '',
        },
      ],
      active: true,
      auto_confirm: false,
      auto_print: false,
    })
  }
}

const map_order_site_hub_brand = async () => {
  const orders = await Order.find({}).sort({ created_at: -1 })
  for (const order of orders) {
    order.data_mapping = map_order(order.source, order.data)
    await order.save()
  }
}

const map_cities = async () => {
  const data = require('../tree.json')
  for (const key of Object.keys(data).sort((a, b) => Number(a.code) - Number(b.code))) {
    await Address.create({
      id: data[key].code,
      name: data[key].name,
      slug: data[key].slug,
      type: data[key].type,
      name_with_type: data[key].name_with_type,
      children: Object.keys(data[key]['quan-huyen'])
        .sort((a, b) => Number(a.code) - Number(b.code))
        .map((key2) => {
          return {
            id: data[key]['quan-huyen'][key2].code,
            parent_id: data[key]['quan-huyen'][key2].parent_code,
            slug: data[key]['quan-huyen'][key2].slug,
            type: data[key]['quan-huyen'][key2].type,
            name: data[key]['quan-huyen'][key2].name,
            name_with_type: data[key]['quan-huyen'][key2].name_with_type,
            name_with_path: data[key]['quan-huyen'][key2].path_with_type,
            children: Object.keys(data[key]['quan-huyen'][key2]['xa-phuong'])
              .sort((a, b) => Number(a.code) - Number(b.code))
              .map((key3) => {
                return {
                  id: data[key]['quan-huyen'][key2]['xa-phuong'][key3].code,
                  parent_id: data[key]['quan-huyen'][key2]['xa-phuong'][key3].parent_code,
                  slug: data[key]['quan-huyen'][key2]['xa-phuong'][key3].slug,
                  type: data[key]['quan-huyen'][key2]['xa-phuong'][key3].type,
                  name: data[key]['quan-huyen'][key2]['xa-phuong'][key3].name,
                  name_with_type: data[key]['quan-huyen'][key2]['xa-phuong'][key3].name_with_type,
                  name_with_path: data[key]['quan-huyen'][key2]['xa-phuong'][key3].path_with_type,
                }
              }),
          }
        }),
    })
  }
}

const email_templates = async () => {
  const templates = [
    { name: 'forgot_password', subject: 'Email Khôi phục mật khẩu' },
    { name: 'register', subject: 'Xác minh tài khoản' },
    { name: 'contact_us', subject: 'Liên hệ với chúng tôi' },
    { name: 'member_invitation', subject: 'Lời mời tham gia Đại lý cá nhân' },
  ]

  const templateDirectory = path.join(__dirname, '../.shared/email/templates/')

  const files = fs.readdirSync(templateDirectory)

  await Promise.all(
    templates.map(async ({ name, subject }) => {
      const fileName = `${name}.ejs`
      const filePath = path.join(templateDirectory, fileName)

      if (files.includes(fileName)) {
        const fileBody = fs.readFileSync(filePath)
        await EmailTemplate.findOneAndUpdate({ name }, { subject, body: String(fileBody) }, { upsert: true })
      }
    })
  )
}

// const migrate_menu = async () => {
//   const menu_for_foods = await SiteMenuForFood.find()
//   const menu_for_marts = await SiteMenuForMart.find()
//   for (const menu of menu_for_foods) {
//     await SiteMenuGroup.create({
//       site_id: menu.site_id,
//       categories: menu.categories,
//       option_categories: menu.option_categories,
//     })
//   }
//   for (const menu of menu_for_marts) {
//     await SiteMenuGroup.create({
//       site_id: menu.site_id,
//       categories: menu.categories,
//       option_categories: [],
//     })
//   }
// }

const text_slugify = (text) => slugify(text.toLowerCase(), { replacement: '_', locale: 'vi', trim: true })

const menu_with_code = async () => {
  const menus = await SiteMenuGroup.find()
  for (const menu of menus) {
    for (let c = 0; c < menu.categories.length; c++) {
      menu.categories[c].code = text_slugify(menu.categories[c].name)
      for (let s = 0; s < menu.categories[c].sub_categories.length; s++) {
        menu.categories[c].sub_categories[s].code = text_slugify(menu.categories[c].sub_categories[s].name)
        for (let i = 0; i < menu.categories[c].sub_categories[s].items.length; i++) {
          menu.categories[c].sub_categories[s].items[i].code = text_slugify(menu.categories[c].sub_categories[s].items[i].name)
        }
      }

      for (let i = 0; i < menu.categories[c].items.length; i++) {
        menu.categories[c].items[i].code = text_slugify(menu.categories[c].items[i].name)
      }
    }
    menu.markModified('categories')
    await menu.save()
  }
}

// const seed_cs_site_for_brand = async () => {
//   console.log('seed_cs_site_for_brand')
//   const brands = await Brand.find({})

//   const hub = await Hub.findOneAndUpdate(
//     { code: 'cs_hub' },
//     {
//       name: 'CS Support',
//       code: 'cs_hub',
//       address: 'Address',
//     },
//     { upsert: true, new: true }
//   )

//   for(let i = 0; i < brands.length; i++) {
//     const brand = brands[i]
//     if (hub) {
//       const site_code = `cs_support_${i}`
//       await Site.create({
//         brand_id: brand._id,
//         hub_id: hub._id,
//         name: 'CS Support',
//         code: site_code,
//         address: 'Address',
//         active: true,
//       }).catch(e => console.log(`site created with same code ${site_code} of brand ${brand.name}`))
//       .then(() => console.log(`site created with code ${site_code} of brand ${brand.name}`))
//     }
//   }
// }

const seed_ghn = async () => {
  await GhnProvinces.deleteMany({})
  await GhnDistricts.deleteMany({})
  await GhnWards.deleteMany({})

  const is_migrated = await GhnProvinces.findOne({})
  if (is_migrated) {
    return
  }

  const provinces = await ghn.get_provinces()
  await GhnProvinces.create(provinces)
  console.log('Done ghn province', provinces.length)

  for (const province of provinces) {
    const districts = await ghn.get_districts(province.ProvinceID)
    await GhnDistricts.create(districts)
    console.log('Done ghn district', districts.length)

    for (const district of districts) {
      const wards = await ghn.get_wards(district.DistrictID)
      await GhnWards.create(wards)
      console.log('Done ghn ward', wards?.length)
    }
  }
}

const migrate_he_id = async () => {
  const sites = await Site.find({ type: { $in: ['retailer', 'partner'] } })

  const roles = await Role.find({ name: { $in: ['Partner Manager', 'Retailer'] } })
  // console.log(roles.map(r => r.name))
  for (const site of sites) {
    const users = await User.find({ sites: site._id, role_id: { $in: roles.map(r => r._id) } })
    const he = users.find(u => u.username !== '<EMAIL>')

    if (he) {
      site.he_id = he._id
      await site.save()
      // console.log(site.name, he_id.username)
    }
  }
}

const migrate_is_first_order = async () => {
  const orders = await Order.aggregate([
    { $match: { status: 'FINISH', user_id: { $exists: true } } },
    { $sort: { 'data_mapping.order_time': 1 } },
    {
      $group: {
        _id: '$user_id',
        firstOrderId: { $first: '$order_id' },
      },
    }
  ])

  console.log('orders to update', orders.length)
  // update is_first_order
  for (const order of orders) {
    await Order.updateOne({ order_id: order.firstOrderId }, { is_first_order: true })
  }
}

const migrate_partner_site = async () => {
  await Site.updateMany({ type: 'partner' }, { apply_commission: true })
}

const migrate_retailer_site = async () => {
  await Site.updateMany({ type: 'retailer' }, { apply_gift: true, type: 'partner' })
  console.log('Done migrate_retailer_site')
}

const migrate_partner_to_user = async () => {
  const partners = await Partner.find({})
  for (const partner of partners) {
    const user_id = partner.user_id
    await User.updateOne({ _id: user_id }, { $set: { 
      'he_info.status': partner.status,
      'he_info.referrer_id': partner.member_of,
      'he_info.approved_at': partner.status === 'active' ? partner.created_at : null,
      'he_info.saved_accounts': partner.saved_accounts,
     } })
  }

  console.log('Done migrate_partner_to_user', partners.length)
// }

// const map_order_shipment_data = async () => {
  
//   const orders = await Order.find({ source: 'local', created_at: { $gte: '2024-10-01' }, 'data.ship_discount': { $exists: false }, status: {$ne: "DRAFT"} })
//   console.log('orders to update', orders.length)
//   for (const order of orders) {
//     order.data.ship_discount = order.data.raw.cart.ship_discount
//     order.data.shipment_fee = order.data.raw.cart.shipping_fee
//     order.data.total_for_biz = order.data.total_for_biz - _.sumBy(order.data.raw.cart.ship_discount, v => _.toNumber(v.amount) || 0)

//     order.data_mapping = map_order(order.source, order.data)

//     console.log(order.order_id, order.data_mapping.shipment_fee, order.data_mapping.total_shipment, order.data_mapping.shipment_discount, order.data.total_for_biz)
//     order.markModified('data_mapping')
//     order.markModified('data')
//     await order.save()
//   }

//   console.log('Done map_order_shipment_data', orders.length)
// }

// map_order_shipment_data()

// const seed_voucher = async () => {
//   const brand_id = '660d2a32049ab56239c50b10'
//   const vouchers = Array.from({ length: 100 }, (_, i) => {
//     return {
//       brand_id,
//       code: `CYBER${i + 1}`,
//       discount: 50000,
//       vendor: 'dpoint',
//       active: true,
//     }
//   })

//   await Voucher.create(vouchers)
//   console.log('Done seed_voucher')
// }
// seed_voucher()
// migrate_partner_to_user()

// migrate_is_first_order()
// migrate_he_id()
// migrate_menu()
// map_order_site_hub_brand()
// site_create_test()
// map_cities()
// email_templates()
// fix_odoo_data()
// menu_with_code()
// seed_cs_hub_and_site()
// seed_ghn()
// migrate_partner_site()
// add_brand_to_order()
// migrate_retailer_site()
// seed_cs_site_for_brand()

module.exports = {}
