const { Hub, Site, User } = require('../../.shared/database')
const redis = require('../../.shared/redis')
const { get_location } = require('../../.shared/delivery/gmap')
const { uniqBy } = require('lodash')

exports.get_hub_list = async (req, res) => {
    let filter = {}

    if (req.user && !req.permissions?.includes('system')) {
        filter._id = req.user.hubs
    }

    if (req.query.name) {
        const escaped_name = req.query.name.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
        filter.name = { $regex: new RegExp(`.*${escaped_name}.*`, 'i') };
    }

    if (req.query.brand_ids) {
        filter.brand_ids = { $in: req.query.brand_ids }
    }

    const hub_paginate = await Hub.paginate(filter, {
        useEstimatedCount: false,
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 200), // Max 100 sites
        sort: { name: 1 },
        customLabels: { docs: 'data' }
    });

    if (req.query.selected_ids) {
        let data = [...hub_paginate.data]
        const selected_hubs = await Hub.find({ _id: { $in: req.query.selected_ids } })
        if (selected_hubs) {
            data = data.concat(selected_hubs)
            hub_paginate.data = uniqBy(data, '_id')
        }
    }

    res.json({
        success: true,
        ...hub_paginate,
    });
}


// Create a new hub
exports.create_hub = async (req, res) => {
    const { name, code, phone, address, address_obj, description, sites, inventory_source, zalo_group, printer_ip, status, enable_working_shift, brand_ids } = req.body;

    try {
        if (address_obj) {
            const location = await get_location(address_obj.formatted_address);
            address_obj.location = location.geometry.location;
        }

        const result = await Hub.create({ name, code, address, address_obj, description, phone, sites, inventory_source, zalo_group, printer_ip, status, enable_working_shift, brand_ids });
        await User.findByIdAndUpdate(req.user._id, { hubs: [...req.user.hubs, result._id] })

        res.json({
            success: true,
            data: result,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Update an existing hub
exports.update_hub = async (req, res) => {
    const { name, code, phone, address, address_obj, description, sites, inventory_source, zalo_group, printer_ip, status, enable_working_shift, brand_ids } = req.body;

    try {
        if (address_obj) {
            const location = await get_location(address_obj.formatted_address);
            address_obj.location = location.geometry.location;
        }
        const result = await Hub.findByIdAndUpdate(
            req.params.hub_id,
            { name, code, phone, address, address_obj, description, sites, inventory_source, zalo_group, printer_ip, status, enable_working_shift, brand_ids },
            { new: true } // Return the updated document
        );

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.delete_hub = async (req, res) => {
    try {
        await Hub.findByIdAndDelete(req.params.hub_id);
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_hub_printers = async (req, res) => {
    if (!req.query.code) {
        res.json({ success: true, data: [] })
        return
    }

    let hub = await redis.getObj(`hubs:${req.query.code}`);
    if (!hub) {
        hub = await Hub.findOne({ code: req.query.code }).lean()
        await redis.setObj(`hubs:${req.query.code}`, hub, 3600);
    }

    if (!hub) {
        res.json({ success: true, data: [] })
        return
    }
    res.json({
        success: true,
        data: [{
            printers: hub.printer_ip?.split(';'),
            hub_id: hub._id,
            hub_name: hub.name,
            hub_code: hub.code,
        }]
    })
}