const { OrderPayment } = require('../../.shared/database');
const axios = require('axios');


exports.get_order_payment_histories = async (req, res) => {
    let filter = {}
    if (req.query.search) {
        filter = {
            $or: [
                { order_id: { $regex: req.query.search, $options: 'i' } },
                { transaction_id: { $regex: req.query.search, $options: 'i' } },
            ]
        }
    }

    await axios.get(`${process.env.NEXDORPAY_PAYMENT_URL}/api/transactions`, {}).catch(console.log)

    const order_payments = await OrderPayment.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 sites
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
    })

    res.json({
        success: true,
        ...order_payments,
    })
}