const bcrypt = require('bcrypt')
const jwt = require('jsonwebtoken')
const moment = require('moment')
const lodash = require('lodash')
const { User, Role, Hub, Site, UserOTP, FCMToken, Brand } = require('../../.shared/database')
const { send_email_with_gmail, send_email } = require('../../.shared/email')
const { subscribe_a_topic, unsubscribe_a_topic } = require('../../.shared/firebase')
const { v4 } = require('uuid')
const validator = require('validator');
const zalo = require('../../.shared/merchant/zalo')
const PhoneNumber = require('libphonenumber-js')

const _send_otp = async (brand_id, phone, otp) => {
  if (process.env.USE_MERCHANT_APPS === "true") {
    const brand = await Brand.findById(brand_id)
    const zalo_token = brand.getToken('zalo')

    if (!zalo_token?.access_token || !zalo_token?.site_data) {
      return { error: 'zalo_token_not_configured' }
    }

    const zalo_otp_zns_id = zalo_token?.site_data?.zalo_otp_zns_id

    const phone_number = PhoneNumber(phone, 'VN')
    const parsed_phone = phone_number.format('E.164') // +84123456789
    await zalo.send_template_message_to_phone(zalo_token, {
      phone: parsed_phone,
      template_id: zalo_otp_zns_id,
      template_data: { otp },
    })
  }

  return { success: true }
}

exports.login = async (req, res) => {
  const { email, phone, password, fcm_token } = req.body
  const username = email || phone
  if (!username) {
    return res.json({
      success: false,
      error: 'username_required',
    })
  }


  const user = await User.findOne({ username })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  if (!user.password) {
    return res.json({
      success: false,
      error: 'password_not_set',
    })
  }

  const result = await bcrypt.compare(password, user.password)
  if (!result) {
    return res.json({
      success: false,
      error: 'wrong_password',
    })
  }

  if (user.expired_at && moment(user.expired_at).isSameOrBefore(moment())) {
    user.is_active = false
    await user.save()
  }

  if (!user.is_active)
    return res.json({
      success: false,
      error: 'user_is_inactive',
    })

  user.last_login_device = JSON.stringify(req.useragent)

  if (!user.last_login_devices.includes(user.last_login_device))
    user.last_login_devices.push(user.last_login_device)

  // Allow max 3 devices connected
  // if (user.last_login_devices.length > 3) {
  //   user.last_login_devices.shift(); // Remove the first item (last device login)
  // }

  // Save fcm token
  if (fcm_token) {
    const token = await FCMToken.findOneAndUpdate({ user_id: user._id, fcm_token }, {
      useragent: JSON.stringify(req.useragent || {})
    }, { upsert: true })

    if (!token) {
      await subscribe_a_topic(fcm_token, 'topic_new_order')
    }
  }

  const auth_data = await _user_auth(user)

  res.json({ success: true, data: auth_data })
}


exports.login_as_guest = async (req, res) => {
  let sessionId = req.cookies.sessionId;
  if (!sessionId) {
    sessionId = v4()
    res.cookie('sessionId', sessionId);
  }

  const defaultPassword = await bcrypt.hash('guest', 10)
  const user = await User.findOneAndUpdate({ username: sessionId }, {
    name: 'Guest',
    email: sessionId + '@nexdor.vn',
    password: defaultPassword,
  }, { upsert: true, new: true })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  user.last_login_device = JSON.stringify(req.useragent)

  if (!user.last_login_devices.includes(user.last_login_device)) {
    user.last_login_devices.push(user.last_login_device)
  }
  const role = await Role.findOneAndUpdate({ name: "Guest", permissions: ['individual'] }, {}, { upsert: true, new: true })
  user.role_id = role._id
  user.is_guest = true

  const auth_data = await _user_auth(user)

  res.json({ success: true, data: auth_data })
}

exports.verify_token = async (req, res, next) => {
  const { access_token, user_id } = req.body
  const { username } = jwt.verify(access_token, process.env.JWT_SECRET)
  const user = await User.findOne({ username, _id: user_id })
  if (!user?.id) {
    res.status(401).send('invalid_token')
    return
  }
  const auth_data = await _user_auth(user)

  res.json({ success: true, data: auth_data })
}


const _user_auth = async (user) => {
  let role = await Role.findById(user.role_id).lean()

  const site_projection = { _id: 1, brand_id: 1, hub_id: 1, address: 1, name: 1 }
  const hub_projection = { _id: 1, code: 1, name: 1, address: 1, enable_working_shift: 1 }
  const brand_projection = { _id: 1, name: 1, address: 1, logo: 1 }

  let managed_hubs = []
  let managed_sites = []
  let managed_brands = []

  if (lodash.isNil(role?.selectors)) {
    role.selectors = []
  }

  if (lodash.isNil(role?.selectors)) {
    role.selectors = []
  }

  if (role.permissions.includes('system')) {
    managed_sites = await Site.find({}, site_projection).lean()
    managed_brands = await Brand.find({}, brand_projection).lean()
    managed_hubs = await Hub.find({}, hub_projection).lean()
  } else {
    if (role.selectors.includes('brand')) {
      managed_brands = await Brand.find({ _id: user.brands }, brand_projection).lean()
      const sites = await Site.find({ brand_id: user.brands }, site_projection).lean()

      if (role.selectors.includes('site')) {
        const user_sites = await Site.find({ _id: user.sites }, site_projection).lean()
        managed_sites = user_sites
      } else {
        managed_sites = sites
      }

      if (role.selectors.includes('hub')) {
        managed_hubs = await Hub.find({ _id: { $in: user.hubs } }, hub_projection).lean()
      } else {
        const hub_ids = lodash.union(sites.flatMap(s => s.hub_id))
        managed_hubs = await Hub.find({ _id: { $in: hub_ids } }, hub_projection).lean()
      }

    }
    if (role.selectors.includes('hub')) {
      managed_hubs = await Hub.find({ _id: user.hubs }, hub_projection).lean()
      const sites = await Site.find({ hub_id: user.hubs }, site_projection).lean()

      // if (role.selectors.includes('brand')) {
      //   const brand_ids = lodash.union(sites.map(v => v.brand_id))
      //   managed_brands = await Brand.find({ _id: { $in: brand_ids } }, brand_projection).lean()
      // }
      if (role.selectors.includes('site')) {
        const user_sites = await Site.find({ _id: user.sites }, site_projection).lean()
        managed_sites = user_sites
      } else {
        managed_sites = sites
      }
    }
    if (role.selectors.includes('site')) {
      const sites = await Site.find({ _id: user.sites }, site_projection).lean()
      managed_sites = sites

      // if (role.selectors.includes('hub')) {
      //   const hub_ids = lodash.union(sites.flatMap(s => s.hub_id))
      //   managed_hubs = await Hub.find({ _id: { $in: hub_ids } }, hub_projection).lean()
      // }
      // if (role.selectors.includes('brand')) {
      //   const brand_ids = lodash.union(sites.map(v => v.brand_id))
      //   managed_brands = await Brand.find({ _id: { $in: brand_ids } }, brand_projection).lean()
      // }
    }
  }

  if (role.permissions.includes('partner')) {
    const sites = await Site.find({ brand_id: user.brands }, site_projection).lean()
    managed_sites = lodash.intersectionWith(sites, user.sites, (siteObject, siteId) => siteObject._id.equals(siteId))
  }
  user.sites = managed_sites.map(v => v._id)
  user.hubs = managed_hubs.map(v => v._id)
  user.brands = managed_brands.map(v => v._id)

  const brandIdsBySite = managed_sites.map(v => v.brand_id)
  const brandListBySite = await Brand.find({ _id: brandIdsBySite }, brand_projection).lean()

  console.log(brandListBySite)

  managed_sites = managed_sites.map(v => {
    const brand = brandListBySite.find(b => String(b._id) === String(v.brand_id))
    return { ...v, brand }
  })

  await user.save()
  user.permissions = role.permissions

  const access_token = jwt.sign({ username: user.username }, process.env.JWT_SECRET)
  const user_json = user.toJSON()
  user_json.is_pwd_set = !!user.password
  delete user_json.password
  delete user_json.last_login_device
  delete user_json.last_login_devices
  user_json.role = role

  user_json.managed_hubs = managed_hubs
  user_json.managed_sites = managed_sites
  user_json.managed_brands = managed_brands

  return { user: user_json, access_token }
}
exports._user_auth = _user_auth

exports.register = async (req, res, next) => {
  const { name, email, phone, address, password, brand_id } = req.body

  if (!email && !phone) {
    return res.json({
      success: false,
      error: 'email_or_phone_is_required',
    })
  }

  if (email && !validator.isEmail(email)) {
    return res.json({
      success: false,
      error: 'email_invalid',
    })
  }

  if (phone && !validator.isMobilePhone(phone, 'vi-VN')) {
    return res.json({
      success: false,
      error: 'phone_invalid',
    })
  }

  // if register as phone, brand_id is required to send OTP
  if (phone && !brand_id) {
    return res.json({
      success: false,
      error: 'brand_id_is_required',
    })
  }

  if (email && !password) {
    return res.json({
      success: false,
      error: 'password_is_required_for_email',
    })
  }

  const username = email || phone
  const existed = await User.findOne({ username })
  if (existed) {
    return res.json({
      success: false,
      error: 'user_is_existed',
    })
  }

  const hashedPassword = password ? await bcrypt.hash(password, 10) : null
  const role = await Role.findOneAndUpdate({ name: "Individual", permissions: ['individual'] }, {}, { upsert: true, new: true })

  const user = await User.create({ username, name, email: email || username + '@nexdor.vn', address, phone, password: hashedPassword, role_id: role._id, is_active: false })

  const data = await _user_auth(user)

  // const otp = process.env.USE_MERCHANT_APPS === 'true' ? Math.floor(100000 + Math.random() * 900000).toString() : '123456'
  const otp = '123456'
  if (validator.isEmail(username)) {
    await send_email({}, { email: user.email, name: user.name }, 'register', {
      name: user.name,
      code: otp,
      url: `${process.env.WEB_URL}/verify_account?code=${otp}&email=${email}&type=register`
    })
    await UserOTP.findOneAndUpdate({ verify_type: 'register', user_uid: username }, { otp, expired_at: moment().add(15, 'minute') }, { upsert: true })
  } else {
    const result = await _send_otp(brand_id, username, otp)
    if (result.error) {
      return res.status(400).json({
        success: false,
        error_code: result.error,
      })
    }
    await UserOTP.findOneAndUpdate({ verify_type: 'register', user_uid: username }, { receive_method: 'zalo', otp, expired_at: moment().add(15, 'minute') }, { upsert: true })
  }

  res.json({
    success: true,
    data: data
  })
}

exports.user_register = async (req, res, next) => {
  const { name, email, phone, password } = req.body

  if (email && !validator.isEmail(email)) {
    return res.json({
      success: false,
      error: 'email_invalid',
    })
  }

  if (phone && !validator.isMobilePhone(phone, 'vi-VN')) {
    return res.json({
      success: false,
      error: 'phone_invalid',
    })
  }

  if (!phone) {
    return res.json({
      success: false,
      error: 'phone_is_required',
    })
  }

  if (!password) {
    return res.json({
      success: false,
      error: 'password_is_required_for_email',
    })
  }

  const username = email || phone
  const existed = await User.findOne({ $or: [{ email }, { phone }] })
  if (existed) {
    return res.json({
      success: false,
      error: 'user_is_existed',
    })
  }

  const hashedPassword = password ? await bcrypt.hash(password, 10) : null
  const role = await Role.findOneAndUpdate({ name: "Individual", permissions: ['individual'] }, {}, { upsert: true, new: true })

  const user = await User.create({ username, name, email, phone, password: hashedPassword, role_id: role._id, is_active: false })

  const data = await _user_auth(user)

  const otp = Math.floor(100000 + Math.random() * 900000).toString()
  if (validator.isEmail(email)) {
    await send_email({}, { email: user.email, name: user.name }, 'register', {
      name: user.name,
      code: otp,
      url: `${process.env.WEB_URL}/verify_account?code=${otp}&email=${email}&type=register`
    })
    await UserOTP.findOneAndUpdate({ verify_type: 'register', user_uid: username }, { otp, expired_at: moment().add(15, 'minute') }, { upsert: true })
  }

  res.json({
    success: true,
    data: data
  })
}


exports.verify_otp = async (req, res) => {
  const { type, email, phone, code } = req.body
  const username = email || phone

  if (type === 'forgot_password') {
    const otp = await UserOTP.findOne({ verify_type: type, user_uid: username, otp: code })
    if (!otp) {
      return res.json({
        success: false,
        error: 'otp_invalid_or_expired',
      })
    }
    return res.json({
      success: true,
      data: true,
    })
  }

  const otp = await UserOTP.findOneAndUpdate({ verify_type: type, user_uid: username, otp: code, expired_at: { $gt: moment() } }, { expired_at: moment() })

  if (!otp) {
    res.json({
      success: false,
      error: 'otp_invalid_or_expired',
    })
    return
  }

  const user = await User.findOne({ username })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  if (type !== 'register') {
    return res.json({
      success: false,
      error: 'type_invalid',
    })
  }

  const role = await Role.findOne({ _id: user.role_id })
  const is_partner_or_retailer = ['Partner Manager'].includes(role.name)
  if (is_partner_or_retailer) {
    // need approval to be activated
    // user.is_verified = true
  } else {
    user.is_active = true
    // user.is_verified = true
  }

  await user.save()

  const data = await _user_auth(user)

  res.json({
    success: true,
    data
  })
}

exports.resend_otp = async (req, res) => {
  const { type, email, phone, brand_id } = req.body

  const username = email || phone
  const user = await User.findOne({ username })
  if (!user)
    return res.json({ success: false, error: 'user_not_found' })

  if (!['forgot_password', 'register'].includes(type))
    return res.json({ success: false, error: 'type_invalid' })

  const otp = await UserOTP.findOne({ verify_type: type, user_uid: username })
  if (otp && moment.duration(moment().diff(moment(otp.updated_at))).asMinutes() <= 1)
    return res.json({ success: false, error: 'too_many_request' })

  // const code = process.env.USE_MERCHANT_APPS === 'true' ? Math.floor(100000 + Math.random() * 900000).toString() : '123456'
  const code = '123456'
  if (validator.isEmail(username)) {
    await send_email_with_gmail({}, { email: user.email, name: user.name }, type, {
      name: user.name,
      code,
      url: `${process.env.WEB_URL}/forgot_password?code=${code}&email=${user.email}&type=${type}`
    })
    await UserOTP.findOneAndUpdate({ verify_type: type, user_uid: username }, { otp: code, expired_at: moment().add(15, 'minute') }, { upsert: true })
  } else {
    if (!brand_id) {
      return res.json({
        success: false,
        error: 'brand_id_is_required',
      })
    }

    const result = await _send_otp(brand_id, username, code)
    if (result.error) {
      return res.status(400).json({
        success: false,
        error_code: result.error,
      })
    }
    await UserOTP.findOneAndUpdate({ verify_type: type, user_uid: username }, { receive_method: 'zalo', otp: code, expired_at: moment().add(15, 'minute') }, { upsert: true })
  }

  res.json({
    success: true,
  })
}

exports.forgot_password = async (req, res) => {
  const { email, phone, brand_id } = req.body

  const username = email || phone
  const user = await User.findOne({ username: { $ne: null, $eq: username } })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  const code = Math.floor(100000 + Math.random() * 900000).toString()
  if (validator.isEmail(username)) {
    await send_email({}, { email: user.email, name: user.name }, 'forgot_password', {
      name: user.name,
      code,
      url: `${process.env.WEB_URL}/forgot-password?code=${code}&email=${encodeURIComponent(user.email)}&type=forgot_password`
    })
    await UserOTP.findOneAndUpdate({ verify_type: 'forgot_password', user_uid: username }, { otp: code, expired_at: moment().add(15, 'minute') }, { upsert: true })
  } else {
    if (!brand_id) {
      return res.json({
        success: false,
        error: 'brand_id_is_required',
      })
    }

    const result = await _send_otp(brand_id, username, code)
    if (result.error) {
      return res.status(400).json({
        success: false,
        error_code: result.error,
      })
    }
    await UserOTP.findOneAndUpdate({ verify_type: 'forgot_password', user_uid: username }, { receive_method: 'zalo', otp: code, expired_at: moment().add(15, 'minute') }, { upsert: true })
  }

  res.json({ success: true })
}

exports.change_password_by_code = async (req, res) => {
  const { email, phone, code, new_password } = req.body
  const username = email || phone
  const user = await User.findOne({ username })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  const otp = await UserOTP.findOne({ verify_type: 'forgot_password', user_uid: username, otp: code, expired_at: { $gt: moment() } })

  if (!otp) {
    return res.json({ success: false, error: 'otp_invalid_or_expired' })
  }

  if (user.login_fail_count > 3)
    return res.json({ success: false, error: 'user_try_login_many_times' })

  const hashedPassword = await bcrypt.hash(new_password, 10)
  user.password = hashedPassword
  user.login_fail_count = 0
  await user.save()

  const data = await _user_auth(user)

  res.json({ success: true, data })
}

exports.change_password = async (req, res) => {
  const { old_password, new_password } = req.body
  const user = await User.findOne({ _id: req.user._id })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  const result = await bcrypt.compare(old_password, user.password)
  if (!result) {
    return res.json({
      success: false,
      error: 'wrong_password',
    })
  }
  const hashedPassword = await bcrypt.hash(new_password, 10)
  user.password = hashedPassword
  await user.save()
  res.json({ success: true })
}

exports.login_v2 = async (req, res) => {
  const { email, phone, password, fcm_token } = req.body
  const username = email ?? phone
  if (!username) {
    return res.json({
      success: false,
      error: 'username_required',
    })
  }


  const user = await User.findOne({ username })
  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  if (!user.password) {
    return res.json({
      success: false,
      error: 'password_not_set',
    })
  }

  const result = await bcrypt.compare(password, user.password)
  if (!result) {
    return res.json({
      success: false,
      error: 'wrong_password',
    })
  }

  if (user.expired_at && moment(user.expired_at).isSameOrBefore(moment())) {
    user.is_active = false
    await user.save()
  }

  if (!user.is_active)
    return res.json({
      success: false,
      error: 'user_is_inactive',
    })

  user.last_login_device = JSON.stringify(req.useragent)

  if (!user.last_login_devices.includes(user.last_login_device))
    user.last_login_devices.push(user.last_login_device)

  // Allow max 3 devices connected
  // if (user.last_login_devices.length > 3) {
  //   user.last_login_devices.shift(); // Remove the first item (last device login)
  // }

  // Save fcm token
  if (fcm_token) {
    const token = await FCMToken.findOneAndUpdate({ user_id: user._id, fcm_token }, {
      useragent: JSON.stringify(req.useragent ?? {})
    }, { upsert: true })

    if (!token) {
      await subscribe_a_topic(fcm_token, 'topic_new_order')
    }
  }

  const auth_data = await _user_auth(user)

  res.cookie(process.env.COOKIE_TOKEN_KEY, auth_data.access_token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "prod",
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
  });

  res.json({ success: true, data: auth_data })
}

exports.verify_token_v2 = async (req, res, next) => {
  const token = req.cookies[process.env.COOKIE_TOKEN_KEY];

  if (!token) {
    return res.status(401).send('No token provided');
  }

  jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).send('Failed to authenticate token');
    } else {
      const user = await User.findOne({ username: decoded.username });
      if (!user?.id) {
        res.status(401).send('invalid_token')
        return
      }
      const auth_data = await _user_auth(user)

      res.json({ success: true, data: auth_data })
    }
  });
}

exports.logout_v2 = async (req, res) => {
  const fcm_token = await FCMToken.findOneAndDelete({ user_id: req.user?._id })
  if (fcm_token) {
    unsubscribe_a_topic(fcm_token.fcm_token, 'topic_new_order')
  }
  res.clearCookie(process.env.COOKIE_TOKEN_KEY);
  return res.status(200).json({ success: true })
}