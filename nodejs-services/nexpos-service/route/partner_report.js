const { Order, Site, User, SiteMenuGroup, CommissionSummary } = require('../../.shared/database')
const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok')
const _ = require('lodash')
const { calculate_partner_commission_by_brand, default_commission } = require('../common/partner_commission')

// job: sync commission summary for all partner, run at 1st day of next month
const sync_commission_summary = async (req, res) => {
  const month = moment().month() + 1
  const year = moment().year()

  try {
    const partners = await User.find({ 'he_info.status': 'active' })
    for (const user of partners) {
      const brand_ids = user.brands

      for (const brand of brand_ids) {
        
        const summary = await CommissionSummary.findOne({ user_id: String(user._id), brand_id: brand, month, year })

        
        if (!summary) {
          const data = await calculate_partner_commission_by_brand(user, month, year, brand)

          await CommissionSummary.create({ user_id: String(user._id), brand_id: brand, month, year, balance: data.total_income, details: data })
        }
      }
    }
    res.json({
      success: true,
      data: { month, year, partners: partners.length },
    })
    
  } catch (error) {
    return res.json({
      success: false,
      error: error.message,
    })
  }
}

const get_partner_commissions = async (req, res) => {
  const { month: q_month, year: q_year, brand_id } = req.query

  const month = Number(q_month || moment().month())  // month from 1-12
  const year = Number(q_year || moment().year())

  const is_current_month = moment().month() + 1 === month && moment().year() === year


  if(!brand_id) {
    return res.json({
      success: false,
      error: 'brand_id_is_required',
    })
  }
  
  let data = {}
  if (is_current_month) {
    data = await calculate_partner_commission_by_brand(req.user, month, year, brand_id)
  } else {
    data = await CommissionSummary.findOne({ user_id: req.user._id, month, year, brand_id }) || default_commission
  }

  const last_month = moment({ year, month: month - 1 })

  const last_data = await CommissionSummary.find({
    user_id: req.user._id,
    ...(brand_id ? { brand_id } : {}),
    month: last_month.get('month'),
    year: last_month.get('year'),
  }).lean() 

  const last_month_total_income = last_data.reduce((acc, cur) => acc + cur.balance, 0)

  const { orders, brand_configs, ...response } = data
  res.json({
    success: true,
    data: { ...response, last_month_income: last_month_total_income },
  })
}

const get_report = async (req, res) => {
  const { start_date, end_date, type, brand_id, apply_commission, apply_gift } = req.query

  let sites = []
  let team = []
  // let sub_team = []
  switch (type) {
    case 'personal': {
      const site_ids = req.user.sites
      sites = await Site.find({
        _id: { $in: site_ids },
        ...(apply_commission ? { apply_commission: Boolean(apply_commission) } : {}),
        ...(apply_gift ? { apply_gift: Boolean(apply_gift) } : {}),
        type: 'partner',
        ...(brand_id ? { brand_id } : {})
      })
      break
    }

    case 'team': {
      team = await User.find({ 'he_info.referrer_id': req.user._id }, { _id: 1, sites: 1 }).lean()
      // sub_team = await Partner.find({ member_of: team.map((t) => t.user_id) })

      const users = await User.find({ _id: team.map((t) => t.user_id) })
      const site_ids = users.reduce((acc, cur) => [...acc, ...cur.sites], [])
      sites = await Site.find({
        _id: { $in: site_ids },
        type: 'partner',
        ...(apply_commission ? { apply_commission: Boolean(apply_commission) } : {}),
        ...(apply_gift ? { apply_gift: Boolean(apply_gift) } : {}),
        ...(brand_id ? { brand_id } : {})
      })

      // prepare which site belongs to team members
      team.forEach((t) => {
        const user = users.find((u) => String(u._id) === t.user_id)
        // const sub_user_ids = sub_team.filter((st) => st.member_of === t.user_id).map((st) => st.user_id)
        // const sub_users = users.filter((u) => sub_user_ids.includes(String(u._id)))
        t.sites = user.sites
      })

      break
    }
  }

  let filter = {
    status: 'FINISH',
    source: 'he',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    site_id: sites.map((s) => String(s._id)),
  }

  const orders = await Order.find(filter, {
    source: 1,
    site_id: 1,
    'data_mapping.total': 1,
    'data_mapping.delivery_time_unix': 1,
    _id: 0,
  }).lean()

  const result = {
    chart_data: {
      data_labels: ['all'], // Labels for the chart's x-axis
      axis_labels: [], // Labels for the chart's y-axis
      datasets: [], // Data values for the chart
      order_count_datasets: [], // Order count values for the chart
    },
  }

  // team
  if (type === 'team') {
    const order_sale_by_site = orders.reduce((acc, cur) => ({ ...acc, [cur.site_id]: (acc[cur.site_id] || 0) + cur.data_mapping.total }), {})

    team.forEach((t) => {
      const total_sale = _.sum(t.sites.map((site_id) => order_sale_by_site[site_id] || 0))
      t.total_sale = total_sale
    })

    result.team = team
  }

  // Calculating the duration of the report in terms of days
  const report_duration = moment.duration(moment(end_date).diff(moment(start_date)))

  const dataset = []
  const order_count_dataset = []
  // If the report duration is less than or equal to 1 day
  if (report_duration.asDays() <= 1) {
    for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(1, 'hour')) {
      result.chart_data.axis_labels.push(date.clone().format('DD/MM HH:mm'))

      // Filtering orders based on the date
      const order_filtered = orders.map((v) => v.data_mapping).filter((v) => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(1, 'hour')))

      // Calculating the total order amount and order count for the date
      dataset.push(_.sumBy(order_filtered, 'total'))
      order_count_dataset.push(order_filtered.length)
    }
  }

  // If the report duration is less than or equal 3 days
  else if (report_duration.asDays() <= 3) {
    for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(4, 'hour')) {
      result.chart_data.axis_labels.push(date.clone().format('DD/MM HH:mm'))

      // Filtering orders based on the date
      const order_filtered = orders.map((v) => v.data_mapping).filter((v) => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(4, 'hour')))

      // Calculating the total order amount and order count for the date
      dataset.push(_.sumBy(order_filtered, 'total'))
      order_count_dataset.push(order_filtered.length)
    }
  }
  // If the report duration is less than or equal 90 days
  else if (report_duration.asDays() <= 90) {
    for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(1, 'day')) {
      result.chart_data.axis_labels.push(date.clone().format('DD/MM/YYYY'))

      // Filtering orders based on the date
      const order_filtered = orders.map((v) => v.data_mapping).filter((v) => moment.unix(v.delivery_time_unix).isSame(date.clone(), 'day'))

      // Calculating the total order amount and order count for the date
      dataset.push(_.sumBy(order_filtered, 'total'))
      order_count_dataset.push(order_filtered.length)
    }
  } else { 
    res.json({
      success: false,
      error: 'not_support_more_than_90_days',
    })
  }

  // Adding the dataset and order count dataset to the chart data
  result.chart_data.datasets.push(dataset)
  result.chart_data.order_count_datasets.push(order_count_dataset)

  res.json({
    success: true,
    data: result,
  })
}

const get_top_sale_items = async (req, res) => {
  const { start_date, end_date, site_id, brand_id, apply_commission, apply_gift } = req.query
  
  const site_ids = site_id ? [site_id] : req.user.sites
  const sites = await Site.find({
    _id: { $in: site_ids },
    type: 'partner',
    ...(apply_commission ? { apply_commission: Boolean(apply_commission) } : {}),
    ...(apply_gift ? { apply_gift: Boolean(apply_gift) } : {}),
    ...(brand_id ? { brand_id } : {})
  })

  const filter = {
    status: 'FINISH',
    source: 'he',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    site_id: { $in: sites.map((s) => String(s._id)) },
  }

  const orders = await Order.find(filter, {
    'data_mapping.delivery_time_unix': 1,
    'data_mapping.dishes': 1,
    'data_mapping.total': 1,
    _id: 0,
  })

  const total_sale = orders.reduce((sum, o) => sum + o.data_mapping.total, 0)
  const total_order = orders.length

  let top_sales = []
  const flat_items = orders.reduce((acc, cur) => [...acc, ...cur.data_mapping.dishes], [])
  flat_items.forEach((item) => {
    const existed = top_sales.find((i) => i.code === item.code)
    if (existed) {
      existed.total = existed.total + item.price
      existed.sold_quantity = existed.sold_quantity + item.quantity
    } else {
      top_sales.push({ code: item.code, name: item.name, image: item.image, total: item.price, sold_quantity: item.quantity })
    }
  })

  top_sales = top_sales.sort((a, b) => (a.total > b.total ? -1 : 1)).slice(0, 10)

  const site_menus = await SiteMenuGroup.find({ site_id: { $in: sites.map((s) => String(s._id)) } })

  const flatten_items = site_menus.reduce((acc, cur) => [...acc, ...cur.categories, ...cur.categories.reduce((acc, cur) => [...acc, ...cur.sub_categories], [])], []).reduce((acc, cur) => [...acc, ...cur.items], [])


  top_sales.forEach((item, index) => {
    const site_item = flatten_items.find((i) => i.code === item.code)
    if (site_item) {
      top_sales[index] = { ...site_item.toObject(), ...item }
    }
  })

  res.json({
    success: true,
    data: { total_sale, total_order, top_sales },
  })
}

module.exports = {
  get_top_sale_items,
  get_partner_commissions,
  get_report,
  sync_commission_summary
}
