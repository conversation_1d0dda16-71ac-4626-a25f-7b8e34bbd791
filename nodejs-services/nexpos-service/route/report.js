require('dotenv').config({ path: './.env' })
const moment = require('moment-timezone')
const _ = require('lodash');
const fs = require('fs');
const xlsx = require('xlsx');
moment.tz.setDefault('Asia/Bangkok');
const { Order, Site, Hub, Brand, BrandMenu, User, UserNotification, SiteFinance, OrderFeedback, SiteFeedback, OrderReport } = require('../../.shared/database')
const { sumBy, sum, zipWith, toNumber, pickBy, negate, isNil } = require('lodash')
const { upload_file, pre_sign_file } = require('../../.shared/storage')
const helper = require('../../.shared/helper')

const {
  Parser: JSON2CSVParser,
  transforms: { unwind },
} = require('json2csv')
const { MERCHANTS, MERCHANT_INFO, TRANSACTION_TYPE } = require('../../.shared/const');
const { get_order_list_filter_v2 } = require('../common/report');
const { publisher } = require('../../.shared/pubsub');

exports.get_report = async (req, res) => {
  const { start_date, end_date, filter_type, hub_ids, brand_ids, site_ids } = req.query;

  const order_filter = await get_order_list_filter_v2(req)
  let filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    ...order_filter
  }

  // Retrieving orders from the database based on site IDs and date range
  const orders = await Order.find(filter, {
    source: 1,
    'data_mapping.total': 1,
    'data_mapping.total_for_biz': 1,
    'data_mapping.total_discount': 1,
    'data_mapping.delivery_time_unix': 1,
    'data_mapping.commission': 1,
    'data_mapping.finance_data': 1,
    _id: 0
  }).lean();

  const order_data = {};

  // List of merchants to include in the report
  const REPORT_MERCHANTS = ['all', ...MERCHANTS];

  // Categorizing orders based on merchant type
  for (const merchant of REPORT_MERCHANTS) {
    order_data[merchant] = orders.filter(v => merchant === 'all' || v.source === merchant);
  }

  const result = {
    count: {},
    total: {},
    total_after_discount: {},
    total_for_biz: {},
    commission: {},
    chart_data: {
      data_labels: REPORT_MERCHANTS, // Labels for the chart's x-axis
      axis_labels: [], // Labels for the chart's y-axis
      datasets: [], // Data values for the chart
      order_count_datasets: [], // Order count values for the chart
    },
  };

  // Calculating the duration of the report in terms of days
  const report_duration = moment.duration(moment(end_date).diff(moment(start_date)));

  // Generating chart data based on report duration and merchant type
  for (const label of result.chart_data.data_labels) {
    const dataset = [];
    const order_count_dataset = [];

    // If the report duration is less than or equal to 1 day
    if (report_duration.asDays() <= 1) {
      for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(1, 'hour')) {
        if (label === 'all') {
          result.chart_data.axis_labels.push(date.clone().format('DD/MM HH:mm'));
        }

        // Filtering orders based on the date
        const order_filtered = order_data[label].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(1, 'hour')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total'));
        order_count_dataset.push(order_filtered.length)
      }
    }
    // If the report duration is less than or equal 3 days
    else if (report_duration.asDays() <= 3) {
      for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(4, 'hour')) {
        if (label === 'all') {
          result.chart_data.axis_labels.push(date.clone().format('DD/MM HH:mm'));
        }

        // Filtering orders based on the date
        const order_filtered = order_data[label].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(4, 'hour')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total'));
        order_count_dataset.push(order_filtered.length)
      }
    }
    // If the report duration is less than or equal 90 days
    else if (report_duration.asDays() <= 90) {
      for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(1, 'day')) {
        if (label === 'all') {
          result.chart_data.axis_labels.push(date.clone().format('DD/MM/YYYY'));
        }

        // Filtering orders based on the date
        const order_filtered = order_data[label].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isSame(date.clone(), 'day'));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total'));
        order_count_dataset.push(order_filtered.length)
      }


    } else {
      // TODO: Support later avoid leak memory
      res.json({
        success: false,
        error: 'not_support_more_than_90_days',
      });
    }
    // Adding the dataset and order count dataset to the chart data
    result.chart_data.datasets.push(dataset);
    result.chart_data.order_count_datasets.push(order_count_dataset);
  }

  // Calculating order count, total order amount, and total order amount for business by merchant type
  for (const merchant of REPORT_MERCHANTS) {
    const mapping_data = order_data[merchant].map(v => v.data_mapping?.finance_data)
    result.count[merchant] = order_data[merchant].length;
    result.total[merchant] = sumBy(mapping_data, 'original_price');
    result.total_for_biz[merchant] = sumBy(mapping_data, 'net_received');
    result.total_after_discount[merchant] = sumBy(mapping_data, 'gross_received');
    result.commission[merchant] = {}
    result.commission[merchant]["co_fund"] = sumBy(mapping_data, 'co_fund_promotion_price') + sumBy(mapping_data, 'shipment_discount')
    result.commission[merchant]["commission"] = Math.ceil(sumBy(mapping_data, 'commission'))
    // result.commission[merchant]["revenue"] = result.total_for_biz[merchant] - result.commission[merchant]["commission"] - result.commission[merchant]["co_fund"]
    result.commission[merchant]["revenue"] = result.total_for_biz[merchant] - result.commission[merchant]["co_fund"]
    result.commission[merchant]["nexpos_commission"] = Math.ceil(result.total_for_biz[merchant] * 0.05) // TODO:Hard code 5% for now
    result.commission[merchant]["actual_revenue"] = result.commission[merchant]["revenue"] - result.commission[merchant]["nexpos_commission"]
  }

  res.json({
    success: true,
    data: result,
  });
};


exports.get_report_by_duration = async (req, res) => {
  const result = await _get_report_by_duration(req)

  res.json({
    success: true,
    data: result,
  });
};

const _get_report_by_duration = async (req) => {
  // Extracting query parameters from request
  const { start_date, end_date, duration, filter_type, hub_ids, brand_ids } = req.query;

  const order_filter = await get_order_list_filter_v2(req)
  let filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    ...order_filter
  }

  let sites = []
  let hubs = []
  let view_bys = [] // hub or brand
  if (filter_type == 'hub') {
    // sites = await Site.find({ _id: { $in: site_ids } }, { _id: 1, hub_id: 1 })
    view_bys = await Hub.find({ _id: { $in: order_filter.hub_id } }, { _id: 1, name: 1 })
    hubs = view_bys
  } else if (filter_type == 'brand') {
    sites = await Site.find({ _id: { $in: order_filter.site_id } }, { _id: 1, brand_id: 1 })
    view_bys = await Brand.find({ _id: { $in: sites.map(s => s.brand_id) } }, { _id: 1, name: 1 })
  }

  // Retrieving orders from the database based on site IDs and date range
  const orders = await Order.find(filter, {
    source: 1,
    site_id: 1,
    'data_mapping.total': 1,
    'data_mapping.total_for_biz': 1,
    'data_mapping.delivery_time_unix': 1,
    'data_mapping.commission': 1,
    _id: 0
  }).lean();

  const order_data = {};

  const result = {
    count: {},
    total: {},
    total_for_biz: {},
    commission: {},
    chart_data: {
      data_labels: view_bys.map(v => v.name), // Labels for the chart's x-axis
      axis_labels: [], // Labels for the chart's y-axis
      datasets: [], // Data values for the chart
      order_count_datasets: [], // Order count values for the chart
    },
    table_data: [],
  };

  // Categorizing orders based on merchant type
  const siteMapById = _.keyBy(sites, '_id')
  const hubMapById = _.keyBy(hubs, '_id')
  for (const view_by of view_bys) {
    const dataset = [];
    const order_count_dataset = [];

    order_data[view_by._id] = filter_type === 'hub' ?
      orders.filter(v => String(hubMapById[v.hub_id]?._id) === String(view_by._id)) :
      orders.filter(v => siteMapById[v.site_id]?.brand_id === String(view_by._id));

    if (duration === '1d') {
      for (let date = moment(start_date).clone(); date.isSameOrBefore(moment(end_date)); date.add(1, 'day')) {
        view_by._id === view_bys[0]._id && result.chart_data.axis_labels.push(date.clone().format('DD/MM'));
        // Filtering orders based on the date
        const order_filtered = order_data[view_by._id].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(1, 'day')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total_for_biz'));
        order_count_dataset.push(order_filtered.length)
      }
    }
    if (duration === '1w') {
      for (let date = moment(start_date).startOf('week').clone(); date.isSameOrBefore(moment(end_date).endOf('week')); date.add(1, 'week')) {
        view_by._id === view_bys[0]._id && result.chart_data.axis_labels.push("Tuần: " + date.clone().week());
        // Filtering orders based on the date
        const order_filtered = order_data[view_by._id].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(1, 'week')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total_for_biz'));
        order_count_dataset.push(order_filtered.length)
      }
    }
    if (duration === '2w') {
      for (let date = moment(start_date).startOf('week').clone(); date.isSameOrBefore(moment(end_date).endOf('week')); date.add(2, 'week')) {
        view_by._id === view_bys[0]._id && result.chart_data.axis_labels.push(`Tuần: ${date.clone().week()}, ${date.clone().week() + 1}`);
        // Filtering orders based on the date
        const order_filtered = order_data[view_by._id].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(2, 'week')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total_for_biz'));
        order_count_dataset.push(order_filtered.length)
      }
    }
    if (duration === '1M') {
      for (let date = moment(start_date).startOf('month').clone(); date.isSameOrBefore(moment(end_date).endOf('month')); date.add(1, 'month')) {
        view_by._id === view_bys[0]._id && result.chart_data.axis_labels.push(`Tháng: ${date.clone().month() + 1}`);
        // Filtering orders based on the date
        const order_filtered = order_data[view_by._id].map(v => v.data_mapping)
          .filter(v => moment.unix(v.delivery_time_unix).isBetween(date.clone(), date.clone().add(1, 'month')));

        // Calculating the total order amount and order count for the date
        dataset.push(sumBy(order_filtered, 'total_for_biz'));
        order_count_dataset.push(order_filtered.length)
      }
    }

    result.chart_data.datasets.push(dataset);
    result.chart_data.order_count_datasets.push(order_count_dataset);
  }


  // Group total
  result.chart_data.axis_labels.push('Tổng')
  result.chart_data.data_labels.push('Tất cả')
  let total_
  for (let i = 0; i < result.chart_data.datasets.length; i++) {
    result.chart_data.datasets[i].push(sum(result.chart_data.datasets[i]))
    result.chart_data.order_count_datasets[i].push(sum(result.chart_data.order_count_datasets[i]))
  }
  result.chart_data.datasets.push(zipWith(...result.chart_data.datasets, (...args) => sum(args)))
  return result
}

const _chart_to_table = (data) => {
  const result = []
  for (let i = 0; i < data.chart_data.data_labels.length; i++) {
    const Name = data.chart_data.data_labels[i];
    const item = { Name }
    for (let j = 0; j < data.chart_data.axis_labels.length; j++) {
      const date_name = data.chart_data.axis_labels[j];
      const value = data.chart_data.datasets[i][j];
      item[date_name] = value
    }
    result.push(item)
  }
  return result
}

exports.get_report_by_duration_to_excels = async (req, res) => {
  try {
    const result = await _get_report_by_duration(req)
    const table = _chart_to_table(result)
    const table_worksheet = xlsx.utils.json_to_sheet(table);

    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, table_worksheet, 'Report');
    const buff = xlsx.write(workbook, { type: 'buffer' });

    const file = await upload_file({ bucket: 'nexpos-files', key: `reports/Báo_cáo_${Date.now()}.xlsx`, buff })
    res.json({
      success: true,
      data: file
    })
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: err.message,
    });
  }
}


const _get_completed_orders = async ({ status, site_id, source, start_date, end_date }) => {
  const filterObject = pickBy(
    {
      status,
      site_id,
      source,
      start_date,
      end_date,
    },
    negate(isNil),
  )
  const { start_date: gte, end_date: lt, ...params } = filterObject
  console.log("GetCompletedOrders", {
    ...params,
    'data_mapping.delivery_time_unix': {
      $gte: moment(gte).unix(),
      $lt: moment(lt).unix(),
    },
  })
  return await Order.find(
    {
      ...params,
      'data_mapping.delivery_time_unix': {
        $gte: moment(gte).unix(),
        $lt: moment(lt).unix(),
      },
    },
    { data: false },
  ).sort({ delivery_time_unix: 1 })
}

exports.get_completed_orders = async (req, res) => {
  let source = req.query.source
  try {
    const orders = await _get_completed_orders({
      status: 'FINISH',
      site_id: { $in: req.query.site_ids || [] },
      source,
      start_date: req.query.start_date,
      end_date: req.query.end_date,
    })

    res.json({
      success: true,
      data: orders.map((v) => v.data_mapping),
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      data: error?.message,
    })
  }
}

exports.get_report_history_list = async (req, res) => {
  let filter = {}
  if (req.query.search) {
    // TODO:
  }

  if (!req.permissions?.includes('system')) {
    filter.created_by = req.user._id
  }

  const order_payments = await OrderReport.paginate(filter, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 sites
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    lean: true,
  })
  const creator_ids = _.uniq(order_payments.data.map(v => v.created_by))

  if (creator_ids.length > 0) {
    const creators = await User.find({ _id: creator_ids }, { _id: 1, name: 1, email: 1 }).lean()
    const creator_map = _.keyBy(creators, '_id')
    for (const order_report of order_payments.data) {
      order_report.creator = creator_map[order_report.created_by]
      if (order_report.filter_type === 'hub') {
        if (order_report.hub_ids[0] === '*') {
          order_report.filter_names = ['Tất cả các hub']
          continue
        }
        const items = await Hub.find({ _id: order_report.hub_ids }, { _id: 1, name: 1 }).lean()
        order_report.filter_names = items.map(v => v.name)
      }
      if (order_report.filter_type === 'brand') {
        if (order_report.brand_ids[0] === '*') {
          order_report.filter_names = ['Tất cả các brand']
          continue
        }
        const items = await Brand.find({ _id: order_report.brand_ids }, { _id: 1, name: 1 }).lean()
        order_report.filter_names = items.map(v => v.name)
      }
      if (order_report.filter_type === 'sites') {
        if (order_report.site_ids[0] === '*') {
          order_report.filter_names = ['Tất cả các site']
          continue
        }
        const items = await Site.find({ _id: order_report.site_ids }, { _id: 1, name: 1 }).lean()
        order_report.filter_names = items.map(v => v.name)
      }
      if (order_report.filter_names?.length > 6) {
        order_report.filter_names = [...order_report.filter_names.slice(0, 5), `, và ${order_report.filter_names.length - 5} ${order_report.filter_type} khác`]
      }
    }
  }


  res.json({
    success: true,
    ...order_payments,
  })
}

const _export_completed_orders = async (req, file_key, report_sub_list = [], order_status = 'FINISH') => {
  const { filter_type, hub_ids, brand_ids, site_ids, start_date, end_date } = req.query;

  const order_filter = await get_order_list_filter_v2(req)
  const filter = {
    status: order_status,
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    ...order_filter
  }
  try {
    const order_report = await OrderReport.create({
      filter_type,
      brand_ids,
      hub_ids,
      site_ids,
      start_date,
      end_date,
      order_filter: filter,
      file_key,
      file_url: '',
      order_status,
      report_sub_list,
      status: 'PENDING',
      created_by: req.user._id,
      created_at_unix: moment().unix(),
      processing_at_unix: null,
      completed_at_unix: null,
    })
    await publisher("REPORT", { _id: order_report._id })

  } catch (error) {
    console.error(error)
  }
}
exports.export_completed_orders = async (req, res) => {
  const file_key = `reports/Bao_Cao_Don_Hang_${Date.now()}.xlsx`
  const file = await pre_sign_file({ bucket: 'nexpos-files', key: file_key })
  res.json({
    success: true,
    data: file
  })
  _export_completed_orders(req, file_key, [
    "orders",
    "items",
    "stocks",
    "transactions",
    "feedbacks",
    "shipments",
    "compare",
    "gross_by_day",
    "gross",
    "brand_group_by_day",
    "hub_group_by_day",
  ], 'FINISH')
}

exports.export_cancelled_orders = async (req, res) => {
  const file_key = `reports/Bao_cao_huy_don_${Date.now()}.xlsx`
  const file = await pre_sign_file({ bucket: 'nexpos-files', key: file_key })
  res.json({
    success: true,
    data: file
  })
  _export_completed_orders(req, file_key, [
    "orders",
    "items",
    "stocks",
  ], 'CANCEL')
}

exports.export_accountant_orders = async (req, res) => {
  const file_key = `reports/Bao_cao_ke_toan_${Date.now()}.xlsx`
  const file = await pre_sign_file({ bucket: 'nexpos-files', key: file_key })
  res.json({
    success: true,
    data: file
  })
  _export_completed_orders(req, file_key, ['accountant_orders'], 'FINISH')
}


exports.export_modify_orders = async (req, res) => {
  const { start_date, end_date } = req.query;
  const order_filter = await get_order_list_filter_v2(req)

  let filter = {
    'data_mapping.created_at_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    site_id: order_filter.site_id,
  }

  const sites = await Site.find({ _id: order_filter.site_id }, { name: 1 }).lean()
  const finances = await SiteFinance.find(filter).lean();

  const sheet1 = []
  for (const transaction of finances) {
    const site = sites.find(v => String(v._id) === String(transaction.site_id))
    sheet1.push({
      'Mã giao dịch': transaction.transaction_id,
      'Nguồn đặt': MERCHANT_INFO[transaction.source]?.label || transaction.source,
      'Loại điều chỉnh': TRANSACTION_TYPE[transaction.data_mapping.type]?.label || transaction.data_mapping.type,
      'Site bán hàng': site?.name,
      'Mã đơn hàng': transaction.data_mapping.order_id,
      'Giá trị giao dịch': transaction.data_mapping.net_total,
      'Miêu tả': transaction.data_mapping.description,
      'Ngày tạo': moment.unix(transaction.data_mapping.created_at_unix).format('DD-MM-YYYY HH:mm:ss'),
    })
  }
  const workbook = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet1), 'Báo cáo từ sàn');
  const buff = xlsx.write(workbook, { type: 'buffer' });

  const file_key = `reports/Merchant_Report_${Date.now()}.xlsx`
  const file = await upload_file({ bucket: 'nexpos-files', key: file_key, buff })
  console.log(file)
  res.json({
    success: true,
    data: file,
  })
}


exports.export_order_feedbacks = async (req, res) => {
  const { start_date, end_date } = req.query;
  const order_filter = await get_order_list_filter_v2(req)

  const order_feedbacks = await OrderFeedback.find({
    site_id: order_filter.site_id,
    created_at_unix: {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
  }, { source: 1, site_id: 1, order_id: 1, customer_name: 1, rating: 1, comment: 1, created_at_unix: 1 }).lean()

  const site_feedbacks = await SiteFeedback.find({
    site_id: order_filter.site_id,
    created_at_unix: {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
  }, { order_id: 1, rating: 1, comment: 1, created_at_unix: 1 }).lean()

  const orders = await Order.find({ order_id: order_feedbacks.map(v => v.order_id) }, {
    order_id: 1,
    hub_id: 1,
    site_id: 1,
    source: 1,
    'data_mapping.order_id': 1,
    'data_mapping.order_time_sort': 1,
    'data_mapping.delivery_time_unix': 1,
  })
  const sites = await Site.find({ _id: _.uniq(order_feedbacks.map(v => v.site_id)) }, {
    _id: 1,
    name: 1,
    code: 1,
    hub_id: 1,
    brand_id: 1,
  }).lean()
  const hubs = await Hub.find({ _id: _.uniq(sites.map(v => v.hub_id)) }, {
    _id: 1,
    name: 1,
    code: 1,
  }).lean()
  const brands = await Brand.find({ _id: sites.map(v => v.brand_id) }, {
    _id: 1,
    name: 1,
  }).lean()

  const siteMap = _.keyBy(sites, "_id")
  const hubMap = _.keyBy(hubs, "_id")
  const brandMap = _.keyBy(brands, "_id")

  const sheet1 = []
  const sheet2 = []
  for (const order_feedback of order_feedbacks) {
    const order = orders.find(v => String(v.order_id) === String(order_feedback.order_id))

    const order_site = siteMap[order_feedback.site_id]
    const order_brand = brandMap[order_site?.brand_id]
    const order_hub = hubMap[order_site?.hub_id]

    if (order) {
      let record1 = {
        'Mã tham chiếu': order.order_id,
        'Mã đơn hàng': order.data_mapping.order_id,
        'Nguồn đặt': order.source,
        'Hub bán hàng': order_hub?.name,
        'Brand bán hàng': order_brand?.name,
        'Site bán hàng': order_site?.name,
        'Khách hàng': order_feedback.customer_name,
        'Đánh giá': order_feedback.rating,
        'Bình luận': order_feedback.comment,
        'Ngày đánh giá': moment.unix(order_feedback.created_at_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
        'Giờ đánh giá': moment.unix(order_feedback.created_at_unix).utcOffset('+07:00').format('HH:mm:ss'),
        'Ngày đặt hàng': moment.unix(order.data_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY'),
        'Giờ đặt hàng': moment.unix(order.data_mapping.order_time_sort).utcOffset('+07:00').format('HH:mm:ss'),
        'Ngày giao hàng': moment.unix(order.data_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
        'Giờ giao hàng': moment.unix(order.data_mapping.delivery_time_unix).utcOffset('+07:00').format('HH:mm:ss'),
      }
      sheet1.push(record1);
    } else {
      let record1 = {
        'Mã tham chiếu': '',
        'Mã đơn hàng': '',
        'Nguồn đặt': order_feedback.source,
        'Hub bán hàng': order_hub?.name,
        'Brand bán hàng': order_brand?.name,
        'Site bán hàng': order_site?.name,
        'Khách hàng': order_feedback.customer_name,
        'Đánh giá': order_feedback.rating,
        'Bình luận': order_feedback.comment,
        'Ngày đánh giá': moment.unix(order_feedback.created_at_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
        'Giờ đánh giá': moment.unix(order_feedback.created_at_unix).utcOffset('+07:00').format('HH:mm:ss'),
        'Ngày đặt hàng': '',
        'Giờ đặt hàng': '',
        'Ngày giao hàng': '',
        'Giờ giao hàng': '',
      }
      sheet1.push(record1);
    }

  }
  for (const site_feedback of site_feedbacks) {
    const order_site = siteMap[site_feedback.site_id]
    const order_brand = brandMap[order_site?.brand_id]

    let record2 = {
      'Brand bán hàng': order_brand?.name,
      'Site bán hàng': order_site?.name,
      'Sàn': MERCHANT_INFO[site_feedback.source]?.label ?? site_feedback.source,
      'Điểm trung bình': site_feedback.avg_rating,
      'Tổng số đánh giá': site_feedback.total_rating,
      'Tỉ lệ 1*': site_feedback.total_star1,
      'Tỉ lệ 2*': site_feedback.total_star2,
      'Tỉ lệ 3*': site_feedback.total_star3,
      'Tỉ lệ 4*': site_feedback.total_star4,
      'Tỉ lệ 5*': site_feedback.total_star5,
      'Ngày cập nhật': moment.unix(site_feedback.created_at_unix).utcOffset('+07:00').format('DD-MM-YYYY'),
    }
    sheet2.push(record2);
  }

  const workbook = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet1), 'Order Feedbacks');
  xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet2), 'Site Feedbacks');
  const buff = xlsx.write(workbook, { type: 'buffer' });

  const file_key = `reports/Feedback_Report_${Date.now()}.xlsx`
  const file = await upload_file({ bucket: 'nexpos-files', key: file_key, buff })
  console.log(file)
  res.json({
    success: true,
    data: file,
  })
}


exports.export_orders_by_user = async (req, res) => {
  const { start_date, end_date, filter_type, hub_ids, brand_ids } = req.query;
  const isAdmin = req.permissions?.includes('system')
  const order_filter = await get_order_list_filter_v2(req)

  let filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    ...order_filter
  }

  // if (filter_type == 'hub') {
  //   const sites = await Site.find({ hub_id: hub_ids })
  //   filter.site_id = sites.map(v => String(v._id))
  // }
  // if (filter_type == 'brand') {
  //   const sites = await Site.find({ brand_id: brand_ids })
  //   filter.site_id = sites.map(v => String(v._id))
  // }
  // if (filter_type == 'site') filter.site_id = site_ids

  // Retrieving orders from the database based on site IDs and date range
  const orders = await Order.find(filter).lean();

  const map_data = _.groupBy(orders, 'data_mapping.customer_phone')

  const sort_data = Object.keys(map_data).sort((a, b) => map_data[b].length - map_data[a].length) // Sort by the total number of orders

  const data = []
  for (const k of sort_data) {
    if (k === "") continue
    const orders_by_phone = map_data[k]
    data.push({
      'Tên khách hàng': orders_by_phone[0].data_mapping.customer_name,
      'SĐT khách hàng': isAdmin ? orders_by_phone[0].data_mapping.customer_phone : '',
      'Địa chỉ khách hàng': isAdmin ? orders_by_phone[0].data_mapping.customer_address : '',
      'Tổng số đơn đặt': orders_by_phone.length,
      'Grab': orders_by_phone.filter(v => v.source === 'grab').length,
      'Grab Mart': orders_by_phone.filter(v => v.source === 'grab_mart').length,
      'Shopee': orders_by_phone.filter(v => v.source === 'shopee').length,
      'Shopee Fresh': orders_by_phone.filter(v => v.source === 'shopee_fresh').length,
      'H.E': orders_by_phone.filter(v => v.source === 'he').length,
      'Gojek': orders_by_phone.filter(v => v.source === 'gojek').length,
      'BE': orders_by_phone.filter(v => v.source === 'be').length,
      'Mã đơn': orders_by_phone.map(v => v.order_id).join(', '),
    })
  }
  const parser = new JSON2CSVParser({})
  const file_data = parser.parse(data)
  res.json({
    success: true,
    data: file_data,
  })
}

exports.retailer_report_for_brand = async (req, res) => {
  const { brand_id } = req.params
  const { start_date, end_date } = req.query

  const sites = await Site.find({ brand_id, type: 'partner', apply_gift: true }, {
    code: 1,
    name: 1,
    he_id: 1,
    brand_id: 1,
    _id: 1,
  })
  const site_map = _.keyBy(sites, '_id')

  const filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    site_id: sites.map(v => String(v._id))
  }

  // Retrieving orders from the database based on site IDs and date range
  const orders = await Order.find(filter, {
    _id: 0,
    site_id: 1,
    order_id: 1,
    data_mapping: 1,
    user_id: 1,
  }).sort({ delivery_time_unix: 1 }).lean();


  const brand_menu = await BrandMenu.findOne({ brand_id })
  const brand_menu_map = {}
  for (const category of brand_menu.categories) {
    for (const item of category.items) {
      brand_menu_map[item.code] = item
    }
  }

  const hes = await User.find({ _id: sites.map(s => s.he_id).filter(v => v) }, {
    name: 1,
    username: 1,
    _id: 1
  }).lean()
  const he_map = _.keyBy(hes, '_id')

  const customers = await User.find({ _id: orders.map(v => v.user_id).filter(v => v) }, {
    name: 1,
    username: 1,
    address: 1,
    is_guest: 1,
    phone: 1,
    _id: 1
  }).lean()
  const customer_map = _.keyBy(customers, '_id')

  const sheet1 = []
  for (const order of orders) {
    const order_mapping = order.data_mapping
    const customer = customer_map[order.user_id]
    for (const dish of order_mapping.dishes) {
      let skus = []

      const item = helper.find_item_in_menu_by_name(brand_menu.categories, dish)
      if (item?.combo?.length > 0) {
        item?.combo?.map(c => {
          if (c.quantity > 0) {
            skus.push(c.code)
          }
        })
      }

      if (dish?.options?.length > 0) {
        for (const option_items of dish.options) {
          for (const o of option_items) {
            const option = helper.find_option_item_in_menu_by_name(brand_menu.option_categories, o.option_name, o.option_item)
            if (option) {
              option?.combo?.map(c => {
                if (c.quantity > 0) {
                  skus.push(c.code)
                }
              })
            }
          }
        }
      }

      skus = skus.length > 0 ? skus : ['']
      skus.forEach(sku => {
        sheet1.push({
          'Mã site': site_map[order.site_id]?.code,
          'Tên site': site_map[order.site_id]?.name,
          'Tên Retailer': he_map[site_map[order.site_id]?.he_id]?.name,
          'Tên người dùng Retailer': he_map[site_map[order.site_id]?.he_id]?.username,
          'Mã đơn hàng': order.order_id,
          'Thời gian đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY HH:mm:ss'),
          'Thời gian giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY HH:mm:ss'),
          'Mã món': dish.code,
          'Tên món': dish.name,
          'SKU': sku,
          'Số lượng': dish.quantity,
          'Giá': dish.price / dish.quantity,
          'Giảm giá': dish.discount,
          'Tổng cộng': dish.price - (dish.discount || 0),
          'Ghi Chú': dish.note,
          'Là quà tặng': dish.is_gift ? 'YES' : 'NO',
          'Giá của thương hiệu': brand_menu_map[dish.code]?.price || 0,
          'Tên khách hàng': customer ? customer.name : '',
          'SĐT khách hàng': customer ? customer.phone : '',
          'Thông tin giao hàng': `${order_mapping.customer_name} - ${order_mapping.customer_phone} - ${order_mapping.customer_address}`,
          'Đã đăng kí tài khoản': customer?.is_guest ? 'NO' : 'YES',
        })
      })
    }
  }

  // export to excel
  const workbook = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(sheet1), 'Báo cáo bán hàng');

  const buff = xlsx.write(workbook, { type: 'buffer' });

  const file = await upload_file({ bucket: 'nexpos-files', key: `reports/Báo_cáo_${Date.now()}.xlsx`, buff })
  res.json({
    success: true,
    data: file
  })
}

exports.retailer_report_for_retailer = async (req, res) => {
  const { start_date, end_date } = req.query

  const sites = await Site.find({ type: 'partner', he_id: req.user._id, apply_gift: true }, {
    code: 1,
    name: 1,
    he_id: 1,
    brand_id: 1,
    _id: 1,
  })

  const site_map = _.keyBy(sites, '_id')

  const filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: start_date ? moment(start_date).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
      $lt: end_date ? moment(end_date).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
    },
    site_id: sites.map(v => String(v._id))
  }

  // Retrieving orders from the database based on site IDs and date range
  const orders = await Order.find(filter, {
    _id: 0,
    site_id: 1,
    order_id: 1,
    data_mapping: 1,
  }).sort({ delivery_time_unix: 1 }).lean();

  const brands = await Brand.find({ _id: sites.map(v => v.brand_id) })
  const brand_map = _.keyBy(brands, '_id')
  const brand_menus = await BrandMenu.find({ brand_id: sites.map(v => v.brand_id) }, {
    brand_id: 1,
    'categories.items.code': 1,
    'categories.items.price': 1,
    'categories.items.name': 1,
  })

  const brand_menu_map = {}
  for (const brand_menu of brand_menus) {
    for (const category of brand_menu.categories) {
      for (const item of category.items) {
        const key = `${brand_menu.brand_id}_${item.code}`
        brand_menu_map[key] = item
      }
    }
  }

  const customers = await User.find({ _id: orders.map(v => v.user_id).filter(v => v) }, {
    name: 1,
    username: 1,
    address: 1,
    is_guest: 1,
    phone: 1,
    _id: 1
  }).lean()
  const customer_map = _.keyBy(customers, '_id')

  const order_data = []
  for (const order of orders) {
    const order_mapping = order.data_mapping
    const customer = customer_map[order.user_id]
    for (const dish of order_mapping.dishes) {
      order_data.push({
        'Mã site': site_map[order.site_id]?.code,
        'Tên site': site_map[order.site_id]?.name,
        'Tên Thương hiệu': brand_map[site_map[order.site_id]?.brand_id]?.name,
        'Mã đơn hàng': order.order_id,
        'Thời gian đặt hàng': moment.unix(order_mapping.order_time_sort).utcOffset('+07:00').format('DD-MM-YYYY HH:mm:ss'),
        'Thời gian giao hàng': moment.unix(order_mapping.delivery_time_unix).utcOffset('+07:00').format('DD-MM-YYYY HH:mm:ss'),
        'Mã món': dish.code,
        'Tên món': dish.name,
        'Số lượng': dish.quantity,
        'Giá': dish.price / dish.quantity,
        'Giảm giá': dish.discount,
        'Tổng cộng': dish.price - (dish.discount || 0),
        'Ghi Chú': dish.note,
        'Là quà tặng': dish.is_gift ? 'YES' : 'NO',
        'Giá của thương hiệu': brand_menu_map[`${site_map[order.site_id]?.brand_id}_${dish.code}`]?.price || 0,
        'Tên khách hàng': customer ? customer.name : '',
        'SĐT khách hàng': customer ? customer.phone : '',
        'Thông tin giao hàng': `${order_mapping.customer_name} - ${order_mapping.customer_phone} - ${order_mapping.customer_address}`,
        'Đã đăng kí tài khoản': customer?.is_guest ? 'NO' : 'YES',
      })
    }
  }

  // export to excel
  const workbook = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(order_data), 'Báo cáo bán hàng');
  const buff = xlsx.write(workbook, { type: 'buffer' });

  const file = await upload_file({ bucket: 'nexpos-files', key: `reports/Báo_cáo_retailer_${Date.now()}.xlsx`, buff })
  res.json({
    success: true,
    data: file
  })
}