const { Hub, HubStockTicket, HubStock, HubStockHistory, Material, Brand, User } = require('../../.shared/database')
const _ = require('lodash');
const xlsx = require('xlsx');
const moment = require('moment');
const moment_tz = require('moment-timezone');
const { upload_file } = require('../../.shared/storage')
const { text_slugify } = require('../../.shared/helper');
const { storage, upload_single } = require('../middlewares/upload_file');

const router = {}

const _gen_ticket_code = async (hub, ticket_type) => {
    const today = moment().format('YYMMDD')

    const count = await HubStockTicket.count({ hub_id: hub._id, ticket_type, created_at: { $gte: moment().startOf('day').toISOString() } })

    const number = count + 1
    return `${ticket_type.slice(0, 2).toUpperCase()}_${today}_${number.toString().padStart(2, '0')}_${hub.code}`
}

const _bundle_user_info = async (hub_stock_tickets) => {
    // bundle user info from created_by, approved_by, rejected_by
    const user_ids = _.uniq(hub_stock_tickets.map(v => [v.created_by, v.approved_by, v.rejected_by]).flat()).filter(v => v)
    const users = await User.find({ _id: user_ids }, { name: 1, email: 1 }).lean()
    const user_map = _.keyBy(users, '_id')
    for (const ticket of hub_stock_tickets) {
        ticket.created_by = user_map[ticket.created_by]
        ticket.approved_by = user_map[ticket.approved_by]
        ticket.rejected_by = user_map[ticket.rejected_by]
        ticket.submitted_by = user_map[ticket.submitted_by]
    }

    return hub_stock_tickets
}

router.get_hub_stock_ticket_list = async (req, res) => {
    const { hub_id } = req.params;
    const { page, limit, ticket_type, status } = req.query;
    try {
        let filter = {
            status,
        }

        let sort = { created_at: -1 }
        if (ticket_type) {
            filter.ticket_type = ticket_type
        }
        if (ticket_type === 'export' || ticket_type === 'trash') {
            filter.hub_id = hub_id
            filter.ticket_type = 'export'
        } else if (ticket_type === 'import') {
            filter = {
                $or: [{
                    target_hub_id: hub_id,
                    ticket_type: 'export'
                }, {
                    hub_id: hub_id,
                    ticket_type: 'import'
                }]
            }
        }
        const hub_stock_ticket_paginate = await HubStockTicket.paginate(filter, {
            page: Number(page || 1),
            limit: Number(limit || 100), // Max 100 docs
            sort,
            customLabels: { docs: 'data' },
            lean: true,
        })

        const hubs = await Hub.find({ _id: hub_stock_ticket_paginate.data.map(v => [v.hub_id, v.target_hub_id]).flat() })
        const hub_pick_info = hubs.map(v => _.pick(v, ['_id', 'name', 'address']))
        for (const ticket of hub_stock_ticket_paginate.data) {
            ticket.hub = hub_pick_info.find(v => String(v._id) === ticket.hub_id)
            ticket.target_hub = hub_pick_info.find(v => String(v._id) === ticket.target_hub_id)
        }

        const hub_stock_tickets = await _bundle_user_info(hub_stock_ticket_paginate.data)

        res.json({
            success: true,
            ...hub_stock_ticket_paginate,
            data: hub_stock_tickets,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            message: err.message,
        });
    }
}

router.create_hub_stock_ticket = async (req, res) => {
    const { hub_id } = req.params;
    const { ticket_type, target_hub_id, target_hub_date, items } = req.body
    try {
        const hub = await Hub.findById(hub_id)
        const ticket_number = await _gen_ticket_code(hub, ticket_type)

        const hub_stock_ticket = await HubStockTicket.create({
            ticket_type, hub_id, target_hub_id, target_hub_date, items, ticket_reports: [], status: 'created', created_by: req.user._id, ticket_number
        })

        if (hub_stock_ticket.ticket_type === 'trash') {
            for (const item of hub_stock_ticket.items) {
                item.exchanged_quantity = item.exchange_quantity
            }
            await hub_stock_ticket.save()
            const result = await approve_hub_stock_ticket({
                ticket_id: hub_stock_ticket._id,
                items: hub_stock_ticket.items,
                stock_report_types: [],
                ticket_report_note: 'Auto approve by system'
            })

            return res.json(result)
        }

        res.json({
            success: true,
            data: hub_stock_ticket,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            error: err.message,
        });
    }
}

router.update_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    const { target_hub_id, target_hub_date, items } = req.body
    try {
        const hub_stock_ticket = await HubStockTicket.findByIdAndUpdate(ticket_id,
            { target_hub_id, target_hub_date, items },
            { upsert: false, new: false }
        )
        res.json({
            success: true,
            data: hub_stock_ticket,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            error: err.message,
        });
    }
}


router.submit_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    const hub_stock_ticket = await HubStockTicket.findById(ticket_id)
    if (!['created', 'submitted'].includes(hub_stock_ticket.status)) {
        return res.json({ success: false, error: 'ticket_can_not_submit' });
    }
    hub_stock_ticket.submitted_by = req.user._id
    hub_stock_ticket.status = 'submitted'
    await hub_stock_ticket.save()
    return res.json({
        success: true,
        data: hub_stock_ticket,
    });
}

const approve_hub_stock_ticket = async ({ ticket_id, items, approved_by }) => {
    try {
        const hub_stock_ticket = await HubStockTicket.findById(ticket_id)
        if (hub_stock_ticket.status !== 'submitted') {
            return { success: false, error: 'ticket_can_not_approve' };
        }
        const hub = await Hub.findById(hub_stock_ticket.hub_id)
        const target_hub = await Hub.findById(hub_stock_ticket.target_hub_id)
        for (const ticket_item of hub_stock_ticket.items) {
            const item = items.find(v => v.code === ticket_item.code)
            if (item) {
                if (item.exchanged_quantity > ticket_item.exchange_quantity) {
                    return { success: false, error: 'some_ticket_item_not_found' };
                }
                ticket_item.exchanged_quantity = item.exchanged_quantity
                ticket_item.stock_report_types = item.stock_report_types
                ticket_item.stock_report_note = item.stock_report_note
            } else {
                ticket_item.exchanged_quantity = 0
                ticket_item.stock_report_types = []
                ticket_item.stock_report_note = ''
            }
        }
        // hub_stock_ticket.ticket_report_types = stock_report_types
        // hub_stock_ticket.ticket_report_note = ticket_report_note
        hub_stock_ticket.status = 'approved'
        hub_stock_ticket.approved_by = approved_by
        hub_stock_ticket.markModified('items')
        await hub_stock_ticket.save()

        for (const ticket_item of hub_stock_ticket.items) {
            if (ticket_item.exchanged_quantity > 0) {
                if (hub_stock_ticket.ticket_type === 'export') {
                    const hub_stock = await HubStock.findOne({ hub_id: hub._id, code: ticket_item.code })
                    const target_hub_stock = await HubStock.findOne({ hub_id: target_hub._id, code: ticket_item.code })
                    if (hub_stock) {
                        let old_quantity = hub_stock.quantity
                        hub_stock.quantity -= ticket_item.exchanged_quantity
                        await hub_stock.save()
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: hub_stock.code,
                            from_quantity: old_quantity,
                            to_quantity: hub_stock.quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    } else {
                        await HubStock.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            name: ticket_item.name,
                            unit: ticket_item.unit,
                            quantity: -ticket_item.exchanged_quantity
                        })
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            from_quantity: 0,
                            to_quantity: -ticket_item.exchanged_quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    }

                    if (target_hub_stock) {
                        let old_quantity = target_hub_stock.quantity
                        target_hub_stock.quantity += ticket_item.exchanged_quantity
                        await target_hub_stock.save()
                        await HubStockHistory.create({
                            hub_id: target_hub._id,
                            code: target_hub_stock.code,
                            from_quantity: old_quantity,
                            to_quantity: target_hub_stock.quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    } else {
                        await HubStock.create({
                            hub_id: target_hub._id,
                            code: ticket_item.code,
                            name: ticket_item.name,
                            unit: ticket_item.unit,
                            quantity: ticket_item.exchanged_quantity
                        })
                        await HubStockHistory.create({
                            hub_id: target_hub._id,
                            code: ticket_item.code,
                            from_quantity: 0,
                            to_quantity: ticket_item.exchanged_quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    }
                } else if (hub_stock_ticket.ticket_type === 'import') {
                    // console.log(`Import ${ticket_item.code} from ${target_hub?.name} to ${hub?.name}`)
                    const hub_stock = await HubStock.findOne({ hub_id: hub._id, code: ticket_item.code })
                    if (hub_stock) {
                        let old_quantity = hub_stock.quantity
                        hub_stock.quantity += ticket_item.exchanged_quantity
                        await hub_stock.save()
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: hub_stock.code,
                            from_quantity: old_quantity,
                            to_quantity: hub_stock.quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    } else {
                        await HubStock.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            name: ticket_item.name,
                            unit: ticket_item.unit,
                            quantity: ticket_item.exchanged_quantity
                        })
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            from_quantity: 0,
                            to_quantity: ticket_item.exchanged_quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    }
                    if (target_hub) {
                        const target_hub_stock = await HubStock.findOne({ hub_id: target_hub._id, code: ticket_item.code })
                        if (target_hub_stock) {
                            let old_quantity = target_hub_stock.quantity
                            target_hub_stock.quantity -= ticket_item.exchanged_quantity
                            await target_hub_stock.save()
                            await HubStockHistory.create({
                                hub_id: target_hub._id,
                                code: target_hub_stock.code,
                                from_quantity: old_quantity,
                                to_quantity: target_hub_stock.quantity,
                                updated_type: 'ticket',
                                updated_ticket_number: hub_stock_ticket.ticket_number,
                            })
                        } else {
                            await HubStock.create({
                                hub_id: target_hub._id,
                                code: ticket_item.code,
                                name: ticket_item.name,
                                unit: ticket_item.unit,
                                quantity: -ticket_item.exchanged_quantity
                            })
                            await HubStockHistory.create({
                                hub_id: target_hub._id,
                                code: ticket_item.code,
                                from_quantity: 0,
                                to_quantity: -ticket_item.exchanged_quantity,
                                updated_type: 'ticket',
                                updated_ticket_number: hub_stock_ticket.ticket_number,
                            })
                        }
                    }

                } else if (hub_stock_ticket.ticket_type === 'trash') {
                    const hub_stock = await HubStock.findOne({ hub_id: hub._id, code: ticket_item.code })
                    if (hub_stock) {
                        let old_quantity = hub_stock.quantity
                        hub_stock.quantity -= ticket_item.exchanged_quantity
                        await hub_stock.save()
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: hub_stock.code,
                            from_quantity: old_quantity,
                            to_quantity: hub_stock.quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    } else {
                        await HubStock.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            name: ticket_item.name,
                            unit: ticket_item.unit,
                            quantity: -ticket_item.exchanged_quantity
                        })
                        await HubStockHistory.create({
                            hub_id: hub._id,
                            code: ticket_item.code,
                            from_quantity: 0,
                            to_quantity: -ticket_item.exchanged_quantity,
                            updated_type: 'ticket',
                            updated_ticket_number: hub_stock_ticket.ticket_number,
                        })
                    }
                }
            }
        }
        return {
            success: true,
            data: hub_stock_ticket,
        };
    } catch (err) {
        console.error(err);
        return {
            success: false,
            error: err.message,
        };
    }
}

router.approve_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    const { items } = req.body
    const result = await approve_hub_stock_ticket({ ticket_id, items, approved_by: req.user._id })
    res.json(result)
}

const reject_hub_stock_ticket = async ({ ticket_id, ticket_report_types, ticket_report_note, rejected_by }) => {
    try {
        const hub_stock_ticket = await HubStockTicket.findById(ticket_id)
        if (hub_stock_ticket.status !== 'submitted') {
            return { success: false, error: 'ticket_can_not_reject' };
        }
        hub_stock_ticket.ticket_report_types = ticket_report_types
        hub_stock_ticket.ticket_report_note = ticket_report_note

        hub_stock_ticket.status = 'rejected'
        hub_stock_ticket.rejected_by = rejected_by
        await hub_stock_ticket.save()
        return {
            success: true,
            data: hub_stock_ticket,
        };
    } catch (err) {
        console.error(err);
        return {
            success: false,
            error: err.message,
        };
    }

}
router.reject_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    const { ticket_report_types, ticket_report_note } = req.body
    const result = await reject_hub_stock_ticket({ ticket_id, ticket_report_types, ticket_report_note, rejected_by: req.user._id })
    res.json(result)
}

router.delete_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    try {
        await HubStockTicket.deleteOne({ _id: ticket_id })
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            error: err.message,
        });
    }
}

router.print_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    try {
        const hub_stock_ticket = await HubStockTicket.findById(ticket_id)
        const hub = await Hub.findById(hub_stock_ticket.hub_id)
        const rows = []
        const localize_text = {
            'stock_incorrect': 'Sai số lượng',
            'stock_outdated': 'Hàng hết hạn',
            'stock_missed': 'Hàng thiếu',
            'export': 'Xuất kho',
            'import': 'Nhập kho',
            'trash': 'Hủy hàng',
        }
        for (const item of hub_stock_ticket.items) {
            rows.push({
                'Loại phiếu': localize_text[hub_stock_ticket.ticket_type] || hub_stock_ticket.ticket_type,
                'Mã hàng': item.code,
                'Tên hàng': item.name,
                'Đơn vị': item.unit,
                'Số lượng tồn': item.stock_quantity,
                'Số lượng yêu cầu': item.exchange_quantity,
                'Số lượng đã trao đổi': item.exchanged_quantity,
                'Lý do': item.stock_report_types.map(v => localize_text[v] || v).join(', '),
                'Ghi chú': item.stock_report_note,
            })
        }
        const hub_stock_worksheet = xlsx.utils.json_to_sheet(rows);

        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Stocks');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const timestamp = moment().format('YYMMDDHHmm');
        const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Ticket_${hub_stock_ticket.ticket_number}_${timestamp}.xlsx`, buff });
        res.json({
            success: true,
            data: file
        })
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            error: err.message,
        });
    }
}

router.print_hub_stock_ticket = async (req, res) => {
    const { ticket_id } = req.params;
    try {
        const hub_stock_ticket = await HubStockTicket.findById(ticket_id)
        const hub = await Hub.findById(hub_stock_ticket.hub_id)
        const target_hub = await Hub.findById(hub_stock_ticket.target_hub_id)
        const rows = []
        const localize_text = {
            'stock_incorrect': 'Sai số lượng',
            'stock_outdated': 'Hàng hết hạn',
            'stock_missed': 'Hàng thiếu',
            'export': 'Xuất kho',
            'import': 'Nhập kho',
            'trash': 'Hủy hàng',
        }
        for (const item of hub_stock_ticket.items) {
            if (hub_stock_ticket.ticket_type === 'export') {
                rows.push({
                    'Loại phiếu': 'Xuất kho',
                    'Kho yêu cầu': hub.name,
                    'Kho nhận hàng': target_hub?.name || '',
                    'Mã hàng': item.code,
                    'Tên hàng': item.name,
                    'Đơn vị': item.unit,
                    'Số lượng tồn': item.stock_quantity,
                    'Số lượng yêu cầu': item.exchange_quantity,
                    'Số lượng đã trao đổi': item.exchanged_quantity,
                    'Lý do': item.stock_report_types.map(v => localize_text[v] || v).join(', '),
                    'Ghi chú': item.stock_report_note,
                })
            } else if (hub_stock_ticket.ticket_type === 'import') {
                rows.push({
                    'Loại phiếu': 'Nhập kho',
                    'Kho yêu cầu': hub.name,
                    'Kho lấy hàng': target_hub?.name || '',
                    'Mã hàng': item.code,
                    'Tên hàng': item.name,
                    'Đơn vị': item.unit,
                    'Số lượng tồn': item.stock_quantity,
                    'Số lượng yêu cầu': item.exchange_quantity,
                    'Số lượng đã trao đổi': item.exchanged_quantity,
                    'Lý do': item.stock_report_types.map(v => localize_text[v] || v).join(', '),
                    'Ghi chú': item.stock_report_note,
                })

            } else if (hub_stock_ticket.ticket_type === 'trash') {
                rows.push({
                    'Loại phiếu': 'Hủy hàng',
                    'Kho yêu cầu': hub.name,
                    'Mã hàng': item.code,
                    'Tên hàng': item.name,
                    'Đơn vị': item.unit,
                    'Số lượng tồn': item.stock_quantity,
                    'Số lượng yêu cầu': item.exchange_quantity,
                    'Số lượng đã trao đổi': item.exchanged_quantity,
                    'Lý do': item.stock_report_types.map(v => localize_text[v] || v).join(', '),
                    'Ghi chú': item.stock_report_note,
                })
            }

        }
        const hub_stock_worksheet = xlsx.utils.json_to_sheet(rows);

        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Stocks');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const timestamp = moment().format('YYMMDDHHmm');
        const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Ticket_${hub_stock_ticket.ticket_number}_${timestamp}.xlsx`, buff });
        res.json({
            success: true,
            data: file
        })
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.json({
            success: false,
            error: err.message,
        });
    }
}

router.get_ticket_template = async (req, res) => {
    try {
        const { hub_id } = req.params
        const { ticket_type } = req.query
        const hub = await Hub.findById(hub_id)
        const hub_stocks = await HubStock.find({ hub_id })

        if (ticket_type === 'import') {
            const rows = [{
                from_hub_code: 'Nhập mã nguồn hàng...',
                code: '',
                name: '',
                unit: '',
                exchange_quantity: 0,
            }]
            const hub_stock_worksheet = xlsx.utils.json_to_sheet(rows);

            const workbook = xlsx.utils.book_new();
            xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Import Stocks');
            const buff = xlsx.write(workbook, { type: 'buffer' });

            const timestamp = moment().format('YYMMDDHHmm');
            const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Ticket_Import_Template_${timestamp}.xlsx`, buff });
            res.json({
                success: true,
                data: file
            })
            return
        }

        if (ticket_type === 'export') {
            const rows = hub_stocks.map(v => ({
                target_hub_code: 'Nhập mã kho xuất đến....',
                code: v.code,
                name: v.name,
                unit: v.unit,
                stock_quantity: v.quantity,
                exchange_quantity: 'Nhập số lượng trao đổi...',
                stock_report_note: '',
            }))
            const hub_stock_worksheet = xlsx.utils.json_to_sheet(rows);

            const workbook = xlsx.utils.book_new();
            xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Export Stocks');
            const buff = xlsx.write(workbook, { type: 'buffer' });

            const timestamp = moment().format('YYMMDDHHmm');
            const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Ticket_Export_Template_${timestamp}.xlsx`, buff });
            res.json({
                success: true,
                data: file
            })
            return
        }

        if (ticket_type === 'trash') {
            const rows = hub_stocks.map(v => ({
                code: v.code,
                name: v.name,
                unit: v.unit,
                stock_quantity: v.quantity,
                exchange_quantity: 'Nhập số lượng bỏ đi...',
                stock_report_note: '',
            }))
            const hub_stock_worksheet = xlsx.utils.json_to_sheet(rows);

            const workbook = xlsx.utils.book_new();
            xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, 'Trash Stocks');
            const buff = xlsx.write(workbook, { type: 'buffer' });

            const timestamp = moment().format('YYMMDDHHmm');
            const file = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Ticket_Trash_Template_${timestamp}.xlsx`, buff });
            res.json({
                success: true,
                data: file
            })
            return
        }

        res.json({
            success: false,
            error: 'invalid_ticket_type'
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.create_ticket_from_template = [upload_single, async (req, res) => {
    try {
        const { hub_id } = req.params
        const { ticket_type } = req.query
        const workbook = xlsx.read(req.file.buffer)
        const sheetNames = workbook.SheetNames;
        let rows = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNames[0]]);
        rows = rows.filter(v => v.code && v.exchange_quantity > 0)
        const hub = await Hub.findById(hub_id)
        const ticket_number = await _gen_ticket_code(hub, ticket_type)

        if (ticket_type === 'import') {
            if (sheetNames[0] != 'Import Stocks') {
                return res.json({ success: false, error: 'invalid_template' });
            }
            // const rows = [{
            //     from_hub_code: 'Nhập nguồn hàng...',
            //     code: '',
            //     name: '',
            //     unit: '',
            //     exchange_quantity: 0,
            // }]
            const from_hub_codes = _.uniq(rows.map(v => v.from_hub_code?.trim() || ''))
            let target_hub = null
            let target_hub_stocks = []
            if (from_hub_codes.length === 1 && from_hub_codes[0]) {
                target_hub = await Hub.findOne({ code: from_hub_codes[0] })
                if (target_hub) {
                    target_hub_stocks = await HubStock.find({ hub_id: target_hub._id })
                }
            }

            const new_rows = []
            for (const row of rows) {
                const target_hub_stock = target_hub_stocks.find(v => v.code === row.code)
                const item = {
                    code: row.code,
                    name: row.name,
                    unit: row.unit,
                    stock_quantity: target_hub_stock?.stock_quantity || 0,
                    exchange_quantity: row.exchange_quantity,
                    exchanged_quantity: 0,
                    stock_report_note: row.stock_report_note,
                }
                new_rows.push(item)
            }
            const ticket = await HubStockTicket.create({
                ticket_number,
                ticket_type: 'import',
                hub_id,
                target_hub_id: target_hub ? target_hub._id : null,
                items: new_rows,
                status: 'submitted',
            })
            return res.json({
                success: true,
                data: ticket,
            })
        }

        if (ticket_type === 'export') {
            if (sheetNames[0] != 'Export Stocks') {
                return res.json({ success: false, error: 'invalid_template' });
            }
            // const rows = hub_stocks.map(v => ({
            //     hub_name: hub.name,
            //     target_hub_code: 'Nhập mã kho....',
            //     code: v.code,
            //     name: v.name,
            //     unit: v.unit,
            //     stock_quantity: v.quantity,
            //     exchange_quantity: 'Nhập số lượng trao đổi...',
            //     stock_report_note: '',
            // }))
            const target_hub_codes = _.uniq(rows.map(v => v.target_hub_code))
            if (target_hub_codes.length > 1) {
                return res.json({ success: false, error: 'invalid_target_hub_code' });
            }
            const target_hub = await Hub.findOne({ name: target_hub_codes[0] })
            if (!target_hub) {
                return res.json({ success: false, error: 'hub_not_found' });
            }
            const target_hub_stocks = await HubStock.find({ hub_id: target_hub._id })
            const new_rows = []
            for (const row of rows) {
                const target_hub_stock = target_hub_stocks.find(v => v.code === row.code)
                const item = {
                    code: row.code,
                    name: row.name,
                    unit: row.unit,
                    stock_quantity: target_hub_stock?.stock_quantity || 0,
                    exchange_quantity: row.exchange_quantity,
                    exchanged_quantity: 0,
                    stock_report_note: row.stock_report_note,
                }
                new_rows.push(item)
            }
            const ticket = await HubStockTicket.create({
                ticket_number,
                ticket_type: 'export',
                hub_id,
                target_hub_id: target_hub._id,
                items: new_rows,
                status: 'submitted',
            })
            return res.json({
                success: true,
                data: ticket,
            })
        }

        if (ticket_type === 'trash') {
            if (sheetNames[0] != 'Trash Stocks') {
                return res.json({ success: false, error: 'invalid_template' });
            }
            // const rows = hub_stocks.map(v => ({
            //     code: v.code,
            //     name: v.name,
            //     unit: v.unit,
            //     stock_quantity: v.quantity,
            //     exchange_quantity: 'Nhập số lượng bỏ đi...',
            //     stock_report_note: '',
            // }))
            const new_rows = []
            for (const row of rows) {
                const item = {
                    code: row.code,
                    name: row.name,
                    unit: row.unit,
                    stock_quantity: row.stock_quantity,
                    exchange_quantity: row.exchange_quantity,
                    exchanged_quantity: 0,
                    stock_report_note: row.stock_report_note,
                }
                new_rows.push(item)
            }
            const ticket = await HubStockTicket.create({
                ticket_number,
                ticket_type: 'trash',
                hub_id,
                items: new_rows,
                status: 'created',
            })
            return res.json({
                success: true,
                data: ticket,
            })
        }

        res.json({
            success: false,
            error: 'invalid_ticket_type',
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}]

router.get_stock_summary = async (req, res) => {
    const { hub_id } = req.params;
    const { from, to } = req.query;
    const start = new Date(from).toISOString();
    const end = new Date(to).toISOString();

    // Find all HubStockHistory records for the given hub and period
    const records = await HubStockHistory.find({
        hub_id: hub_id,
        created_at: { $gte: start, $lte: end }
    });

    // Group records by code
    const grouped_records = records.reduce((groups, record) => {
        if (!groups[record.code]) {
            groups[record.code] = [];
        }
        groups[record.code].push(record);
        return groups;
    }, {});

    // Calculate stock summary for each code
    const stock_summary = Object.entries(grouped_records).map(([code, records]) => {
        // Sort records by date
        records.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

        // Calculate beginning and ending stock
        const beginning_stock = records[0].from_quantity;
        const ending_stock = records[records.length - 1].to_quantity;

        // Calculate imported and exported units
        let imported_units = 0;
        let exported_units = 0;
        records.forEach(record => {

            const diff = record.to_quantity - record.from_quantity;
            if (diff > 0) {
                imported_units += diff;
            } else {
                exported_units -= diff;
            }
        });

        // Return stock summary for this code
        return {
            code,
            beginning_stock,
            ending_stock,
            imported_units,
            exported_units
        };
    });

    const hub = await Hub.findById(hub_id)
    const hub_stocks = await HubStock.find({ hub_id, code: { $in: stock_summary.map(v => v.code) } })

    const start_date = moment_tz(start).tz('Asia/Ho_Chi_Minh').format('DD/MM/YYYY')
    const end_date = moment_tz(end).tz('Asia/Ho_Chi_Minh').format('DD/MM/YYYY')
    const rows = stock_summary.map(v => {
        const hub_stock = hub_stocks.find(s => s.code === v.code) || {}
        return {
            'Mã kho': hub.code,
            'Mã hàng': v.code,
            'Tên hàng': hub_stock.name,
            'Đơn vị': hub_stock.unit,
            [`Tồn đầu kỳ (${start_date})`]: v.beginning_stock,
            'Nhập kho': v.imported_units,
            'Xuất kho': v.exported_units,
            [`Tồn cuối kỳ (${end_date})`]: v.ending_stock,
        }
    })
    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(rows);
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Stock Summary');
    const buff = xlsx.write(workbook, { type: 'buffer' });

    const url = await upload_file({ bucket: 'nexpos-files', key: `hub_stock_tickets/${text_slugify(hub.name)}_Stock_Summary_${start_date}_${end_date}_${moment().valueOf()}.xlsx`, buff });

    res.json({
        success: true,
        data: url,
    });
}

router.get_stock_materials = async (req, res) => {
    let query = {}

    if (req.query.search) {
        query.$or = [
            { name: { $regex: req.query.search, $options: 'i' } },
            { code: { $regex: req.query.search, $options: 'i' } },
        ]
    }

    if (req.query.brand_id) {
        query.brand_id = req.query.brand_id
    }

    const result = await Material.paginate(
        query,
        {
            page: Number(req.query.page || 1),
            limit: Number(req.query.limit || 100), // Max 100 docs
            sort: { created_at: -1 },
            customLabels: { docs: 'data' },
        }
    )

    const brands = await Brand.find({ _id: result.data.map(v => v.brand_id) }, { name: 1, code: 1 }).lean()
    const map_data = result.data.map(v => {
        const brand = brands.find(b => String(b._id) === String(v.brand_id))
        return {
            ...v.toObject(),
            brand: brand || {},
        }
    })

    res.json({
        success: true,
        ...result,
        data: map_data,
    })
}

module.exports = router