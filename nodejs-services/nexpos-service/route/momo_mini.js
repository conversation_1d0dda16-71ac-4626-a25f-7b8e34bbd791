const { Site, SiteMenuGroup, Order, SiteOrderIndex, Hub, Brand, VendorCallback, HubStock, RetailerSaleConfig, PartnerAPIKey, BrandMenu } = require('../../.shared/database')
const moment = require('moment')
const axios = require('axios')
const { get_location } = require('../../.shared/delivery/gmap')
const { get_hubs_has_stocks, get_item_stock } = require('../common/order')
const ahamove = require('../../.shared/delivery/ahamove')
const grab_express = require('../../.shared/delivery/grab_express')
const viettel_post = require('../../.shared/delivery/viettel_post')
const token_account = require('../../.shared/token_account')
const { map_order } = require('../../.shared/merchant/mapping')
const redis = require('../../.shared/redis')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const { gen_external_id } = require('../../.shared/helper')
const { SELF_PICKUP } = require('../../.shared/const')

const day_map = {
  sunday: 0,
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6,
}

// const body = {
//   username: process.env.MOMO_MINI_APP_USERNAME || 'cyberkitchenfnb',
//   password: process.env.MOMO_MINI_APP_PASSWORD || '1q2wmmlakzpp',
// }

const baseUrl = process.env.NODE_ENV === 'prod' ? 'https://business.momo.vn/api/' : 'https://uat-business.momo.vn/api/'

const _get_login_token = async (brand_id) => {
  const cached_token = await redis.getObj(`momo_mini_token:${brand_id}`)
  if (cached_token) {
    return cached_token
  }

  const brand = await Brand.findOne({ _id: brand_id })
  const token = brand?.getToken('momo_mini')
  if (!token) {
    throw new Error('Momo mini app token not found')
  }

  const body = {
    username: token.username,
    password: token.password,
  }

  const { data } = await axios.post(baseUrl + 'authentication/login', body)

  if (data.data.token) {
    await redis.setObj(`momo_mini_token:${brand._id}`, data.data.token, 30 * 60)
    return data.data.token
  }

  throw new Error('Momo mini app token not found')
}

const _build_menu_items = (menu) => {
  return [
    ...menu.categories.flatMap((cat) => cat.items.map((item) => ({ ...item, cat_code: cat.code, cat_name: cat.name }))),
    ...menu.categories.flatMap((cat) => cat.sub_categories.flatMap((sub_cat) => sub_cat.items.map((item) => ({ ...item, cat_name: `${cat.name} - ${sub_cat.name}`, cat_code: cat.code })))),
  ]
}

const _build_menu_with_discount = async (menu, brand_id) => {
  const items = _build_menu_items(menu)
  const brand_sales = await RetailerSaleConfig.find({
    brand_id: brand_id,
    active: true,
    type: { $in: ['fixed_discount', 'percent_discount'] },
  })
    .lean()
    .then((configs) =>
      configs.filter((config) => {
        if (config.start_date && config.end_date) {
          return moment().isBetween(config.start_date, config.end_date)
        }
        if (config.start_date) {
          return moment().isAfter(config.start_date)
        }
        if (config.end_date) {
          return moment().isBefore(config.end_date)
        }
        return true
      })
    )

  const flatten_config = brand_sales.flatMap((config) => {
    if (config.type === 'fixed_discount') {
      return config.config?.items.map((item) => ({
        ...item,
        type: config.type,
        discount: Number(item.discount),
      }))
    }

    if (config.type === 'percent_discount') {
      return config.config?.items.map((item) => ({
        ...item,
        type: config.type,
        discount: Number(config.config.percent_discount),
      }))
    }
  })

  items.forEach((item) => {
    const sale = flatten_config.find((v) => v.category_name === item.cat_name && v.item_name === item.name)
    item.discounted_price = item.price // default price
    if (sale) {
      if (sale.type === 'fixed_discount') {
        item.discounted_price = item.price - sale.discount
      } else if (sale.type === 'percent_discount') {
        item.discounted_price = Math.floor(item.price * (1 - sale.discount / 100))
      }
    }
  })

  return items
}

const _site_to_momo_store = (site, hubs, brand) => {
  const openingTimes = Object.keys(site.working_hours || {})
    .filter((dayOfWeek) => !site.working_hours[dayOfWeek]?.closed)
    .map((dayOfWeek) => {
      const { start, end } = site.working_hours[dayOfWeek]
      return { dayOfWeek: day_map[dayOfWeek], from: start, to: end }
    })

  return hubs.map((hub) => ({
    id: hub.code,
    name: hub.name,
    address: hub.address,
    latitude: hub.address_obj?.location?.lat,
    longitude: hub.address_obj?.location?.lng,
    openingTimes,
    status: hub.status === 'active' ? 'ACTIVE' : 'CLOSED',
    servingType: ['DELIVERY', 'PICK_UP'],
    hotline: hub.phone,
    logoUrl: brand.logo,
  }))
}

const _site_menu_to_momo_menu = async (site, hubs) => {
  const site_menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()

  // here
  // const hub_stocks = await HubStock.find(
  //   { hub_id: { $in: hubs.map((hub) => String(hub._id)) } },
  //   {
  //     code: 1,
  //     quantity: 1,
  //   }
  // ).lean()

  // const hub_stocks_by_item = hub_stocks.reduce((acc, cur) => {
  //   acc[cur.code] = Math.max(acc[cur.code] || 0, cur.quantity)
  //   return acc
  // }, {})

  for (const category of site_menu.categories) {
    for (const item of category.items) {
      const quantity = 100
      item.quantity = quantity > 0 ? quantity : 0
    }

    for (const subCategory of category.sub_categories) {
      for (const item of subCategory.items) {
        const quantity = 100
        item.quantity = quantity > 0 ? quantity : 0
      }
    }
  }

  const items = await _build_menu_with_discount(site_menu, site.brand_id)
  const data = items.map((item, index) => {
    const item_options = site_menu.option_categories.filter((v) => v.category_ids?.includes(item.id))
    const selection_map = {
      SELECT_ONE: 'SINGLE_CHOICE',
      SELECT_MANY: 'MULTI_CHOICE',
    }

    const options = item_options.map((v, index) => ({
      id: v.id,
      name: v.name,
      selectionType: selection_map[v.rule.type],
      isRequired: v.rule.required,
      minSelectableQuantity: v.rule.min_quantity || 0,
      maxSelectableQuantity: v.rule.max_quantity || Math.min(item.quantity, 10),
      displayOrder: index,
      optionItems: v.options.map((item, index) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        originalPrice: item.price,
        displayOrder: index,
      })),
    }))

    return {
      id: item.code,
      name: item.name,
      price: item.discounted_price,
      originalPrice: item.price,
      categoryId: item.cat_code,
      status: item.active && item.quantity > 0 ? 'ACTIVE' : 'OUT_OF_STOCK',
      displayOrder: index,
      imageUrls: [item.image],
      options,
    }
  })

  return data
}

const _site_menu_to_momo_categories = async (site) => {
  const site_menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()

  const categories = site_menu.categories.map((cat) => ({ id: cat.code, name: cat.name, status: 'ACTIVE' }))
  const sub_categories = site_menu.categories.flatMap((cat) => cat.sub_categories.map((sub_cat) => ({ id: sub_cat.code, name: sub_cat.name, status: 'ACTIVE' })))

  return [...categories, ...sub_categories].map((v, index) => ({ ...v, displayOrder: index }))
}

// API GET all stores
const get_all_stores = async (req, res) => {
  const site_id = req.partner.metadata?.site_id
  if (!site_id) {
    return res.status(400).json({ code: 2, message: 'site_id not configured in token' })
  }

  const site = await Site.findOne({ _id: site_id }).lean()
  const hubs = await Hub.find({ _id: { $in: site.hub_ids } }).lean()
  const brand = await Brand.findOne({ _id: site.brand_id })
  const stores = _site_to_momo_store(site, hubs, brand)

  res.status(200).json({ data: stores, code: 0, message: 'Success' })
}

// API GET all product categories
const get_all_product_categories = async (req, res) => {
  const site_id = req.partner.metadata?.site_id
  const hub_code = req.params.store_id

  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const data = await _site_menu_to_momo_categories(site)
  res.status(200).json({ data, code: 0, message: 'Success' })
}

// API GET all products and its options in store / merchant
const get_products_detail = async (req, res) => {
  const hub_code = req.params.store_id
  const site_id = req.partner.metadata?.site_id
  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const hubs = await Hub.find({ _id: { $in: site.hub_ids } }).lean()
  const data = await _site_menu_to_momo_menu(site, hubs)
  res.status(200).json({ data, code: 0, message: 'Success' })
}

// API GET store detail
const get_store_detail = async (req, res) => {
  const hub_code = req.params.store_id
  const site_id = req.partner.metadata?.site_id

  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const hub = await Hub.findOne({ code: hub_code }).lean()
  const brand = await Brand.findOne({ _id: site.brand_id })
  const [data] = _site_to_momo_store(site, [hub], brand)

  res.status(200).json({ data })
}

// API GET 1 product and its options detail
const get_product_detail = async (req, res) => {
  const { store_id: hub_code, product_id } = req.params
  const site_id = req.partner.metadata?.site_id
  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const site_menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()

  const items = await _build_menu_with_discount(site_menu, site.brand_id)
  const item = items.find((item) => item.code === product_id)

  if (!item) {
    return res.status(400).json({ code: 2, message: 'Invalid product' })
  }

  const item_options = site_menu.option_categories.filter((v) => v.category_ids?.includes(item.id))
  const selection_map = {
    SELECT_ONE: 'SINGLE_CHOICE',
    SELECT_MANY: 'MULTI_CHOICE',
  }

  const options = item_options.map((v, index) => ({
    id: v.id,
    name: v.name,
    selectionType: selection_map[v.rule.type],
    isRequired: v.rule.required,
    minSelectableQuantity: v.rule.min_quantity || 0,
    maxSelectableQuantity: v.rule.max_quantity || Math.min(item.quantity, 10),
    displayOrder: index,
    optionItems: v.options.map((item, index) => ({
      id: item.id,
      name: item.name,
      price: item.price,
      originalPrice: item.price,
      displayOrder: index,
    })),
  }))

  const data = {
    id: item.code,
    name: item.name,
    price: item.price,
    originalPrice: item.price,
    categoryId: item.cat_code,
    status: item.active && item.quantity > 0 ? 'ACTIVE' : 'OUT_OF_STOCK',
    displayOrder: 0,
    imageUrls: [item.image],
    options,
  }

  res.status(200).json({ data })
}

/* OFFLINE sync, */
// An API from merchant side so that MoMo could send request for data to Merchant

// Merchant sends prepared data in file to a MoMo’s endpoint - authentication required

/* ORDER flow */
// API Validate Order: Store, Order Item (Product) price and inventory (OPTIONAL)

// API Create order: validate and create order
const create_order = async (req, res) => {
  const {
    storeId: hub_code,
    customer,
    noteForStore,
    noteForDriver,
    orderType,
    paymentMethod,
    paymentStatus,
    orderItems,
    delivery,
    subTotal,
    deliveryFee = 0,
    pickUpTime,
    momoTransactionId,
    merchantFundPromos,
  } = req.body

  const site_id = req.partner.metadata?.site_id
  await VendorCallback.create({
    vendor: 'momo_mini',
    type: 'order',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    method: req.method,
  })

  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const now = moment()

  const menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()

  const menu_items = await _build_menu_with_discount(menu, site.brand_id)

  // validate orderItems
  const invalidItems = orderItems.filter((item) => !menu_items.find((v) => v.code === item.id))
  if (invalidItems.length > 0) {
    return res.status(400).json({
      code: 2,
      message: 'Invalid order items',
      orderItemErrors: invalidItems.map((v) => ({
        id: v.id,
        errorCode: 1, // out of stock
      })),
    })
  }

  const order_group = `${site.code?.toUpperCase() || 'NEXDOR'}-${moment().format('YYMMDD')}`
  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })
  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  const dishes = orderItems.map((v) => {
    const item = menu_items.find((item) => item.code === v.id)
    const item_options = menu.option_categories.filter((option) => option.category_ids?.includes(item.id))

    let selected_options = []
    item_options.forEach((item_option) => {
      const selected = item_option.options.filter((item) => v.options.find((i) => i.id === item.id))

      if (selected.length) {
        selected_options.push(
          selected.map((item) => ({
            id: item.id,
            option_name: item.name,
            option_item: item.name,
            price: item.price,
            quantity: v.options.find((v) => v.id === item.id).quantity,
          }))
        )
      }
    })

    const option_price = selected_options.flat().reduce((acc, option) => acc + option.price * option.quantity, 0) * v.quantity

    return {
      id: v.id,
      code: v.id,
      quantity: v.quantity,
      note: v.note,
      options: selected_options,
      price: item.price * v.quantity + option_price,
      name: item.name,
      category_name: item.cat_name,
      image: item.image,
      description: item.description,
      discount: (item.price - item.discounted_price) * v.quantity,
      discount_price: item.discounted_price * v.quantity + option_price,
      combo: item.combo,
    }
  })

  const calculated_sub_total = dishes.reduce((acc, item) => acc + item.discount_price, 0)
  if (calculated_sub_total !== subTotal) {
    return res.status(400).json({ code: 2, message: 'Subtotal not match' })
  }

  let hub = await Hub.findOne({ code: hub_code }).lean()
  // const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }, { categories: 1 }).lean()
  // if (orderType === 'DELIVERY') {
  //   const hubs_with_stocks = await get_hubs_has_stocks(
  //     {
  //       categories: brand_menu.categories,
  //       items: dishes,
  //       address: delivery?.address,
  //     },
  //     site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
  //   )

  //   const hub_to_assign = hubs_with_stocks.find((v) => v.has_stock)
  //   if (hub_to_assign) {
  //     hub = hub_to_assign
  //   }
  // }

  const shipment = {
    service: orderType === 'DELIVERY' ? {} : SELF_PICKUP,
    cod: 0,
    payment_method: 'COD',
    status: 'ORDER_CREATING',
    from: {
      address: hub.address,
      phone: hub.phone,
      name: hub.name,
    },
    to: orderType === 'DELIVERY' ? { address: delivery?.address, phone: customer.phoneNumber, name: customer.name } : {},
    price: 0,
    note: noteForDriver,
  }

  const total = subTotal + deliveryFee
  const { momoVoucherId, momoVoucherName, merchantSponsorAmount } = merchantFundPromos?.[0] || {}
  const notes = {
    'Ghi chú cho cửa hàng': noteForStore,
    'Ghi chú cho tài xế': noteForDriver,
    'Voucher Momo': [momoVoucherName, momoVoucherId, merchantSponsorAmount].filter((v) => v).join(' - '),
  }

  const order = {
    source: 'momo',
    site_id: site._id,
    status: 'WAITING_PAYMENT',
    order_id,
    hub_id: hub._id,
    shipment,
    data: {
      id: order_id,
      order_id: order_id,
      source: 'momo',
      order_type: orderType,
      order_time: now.toISOString(),
      pick_time: null,
      delivery_time: null,
      order_time_sort: now.unix(),
      driver_name: '',
      driver_phone: '',
      customer_phone: customer.phoneNumber,
      customer_name: customer.name,
      customer_address: delivery?.address,
      dishes,
      commission: 0,
      total,
      total_for_biz: total,
      total_shipment: deliveryFee,
      total_display: total.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
      note: Object.entries(notes)
        .filter((v) => v[1])
        .map((v) => `${v[0]}: ${v[1]}`)
        .join('; '),
      payments: [{ method: 'MOMO', total: total, status: 'PENDING', code: momoTransactionId }],
      raw: req.body,
      last_transaction: {
        vendor: 'momo',
        callback_data: {
          transId: momoTransactionId,
          amount: total,
          resultCode: '0',
        },
      },
      shipment_fee: deliveryFee,
      coupons: merchantFundPromos ? [{ name: 'Voucher Momo', code: momoVoucherId, total: merchantSponsorAmount }] : [],
    },
  }

  order.data_mapping = map_order('momo', order.data)
  order.external_id = gen_external_id()
  const new_order = await Order.create(order)

  res.status(200).json({ code: 0, message: 'Success', data: { orderNumber: new_order.order_id } })
}

// API Cancel an order that has not been paid yet (payment status is WAITING) - cancel order from momo side
const cancel_order = async (req, res) => {
  const { storeId: hub_code, orderNumber } = req.body

  const site_id = req.partner.metadata?.site_id
  await VendorCallback.create({
    vendor: 'momo_mini',
    type: 'order',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    method: req.method,
  })

  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const order = await Order.findOne({
    site_id: site._id,
    order_id: orderNumber,
  })

  if (!order) {
    return res.status(400).json({ code: 2, message: 'Invalid order' })
  }

  if (['PENDING', 'WAITING_PAYMENT'].includes(order.status)) {
    order.status = 'CANCEL'
    await order.save()
  }

  order.status = 'CANCEL'
  await order.save()

  res.status(200).json({ code: 0, message: 'Success' })
}

// API Update order payment status from WAITING to PAID (CONFIRM) - pay success in momo side
const confirm_order = async (req, res) => {
  const { storeId: hub_code, orderNumber } = req.body
  const site_id = req.partner.metadata?.site_id
  await VendorCallback.create({
    vendor: 'momo_mini',
    type: 'order',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    method: req.method,
  })

  const site = await Site.findOne({
    _id: site_id,
  }).lean()

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Invalid site' })
  }

  const order = await Order.findOne({
    site_id: site._id,
    order_id: orderNumber,
    status: 'WAITING_PAYMENT',
  })

  if (!order) {
    return res.status(400).json({ code: 2, message: 'Invalid order' })
  }

  order.status = 'PENDING'
  order.data.payments.forEach((payment) => {
    payment.status = 'COMPLETED'
  })
  order.data_mapping = map_order('momo', order.data)
  await order.save()

  await send_zalo_message_by_order_id({
    order_id: order.order_id,
    message: [
      `Bạn có đơn hàng mới từ MOMO <b>${order.order_id}</b>,`,
      `Khách hàng: ${order.data_mapping.customer_name}, ${order.data_mapping.customer_address},`,
      `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
    ].join('\n'),
  }).catch(console.log)

  res.status(200).json({ code: 0, message: 'Success' })
}

const update_order_status = async (payload) => {
  const { order_id, order_status, driver_name, driver_phone, site_id } = payload

  const data = {
    orderNumber: order_id,
    orderStatus: order_status,
    deliveryStatus: order_status,
    timestamp: moment().unix(),
    extraData: {},
  }

  if (driver_name || driver_phone) {
    data.extraData.delivery = {
      driverName: driver_name,
      driverPhoneNumber: driver_phone,
    }
  }

  const site = await Site.findOne({ _id: site_id }).lean()
  const token = await _get_login_token(site.brand_id)

  let error
  await axios
    .post(baseUrl + `order-and-delivery/hooks/orders/v1/status`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    .catch((err) => {
      error = `Momo: ${err.response?.data?.message}`
    })

  return error
}

const get_shipment_fee = async (req, res) => {
  const { storeId: hub_code, address } = req.body
  const site_id = req.partner.metadata?.site_id

  if (!hub_code || !address) {
    return res.status(400).json({ code: 2, message: 'Invalid params, store_id and address are required' })
  }

  const site = await Site.findById(site_id)

  if (!site) {
    return res.status(400).json({ code: 2, message: 'Site not found' })
  }

  const hub = await Hub.findOne({ code: hub_code }).lean()

  const shipment = {
    from: {
      address: hub.address,
      phone: hub.phone,
      name: hub.name,
    },
    to: {
      address: address,
      phone: '',
      name: '',
    },
  }

  const [ahamove_shipments, grab_express_shipments, grab_express_2h_shipments, viettel_post_shipments] = await Promise.all([
    ahamove.get_shipments(await token_account.get_token_by_site(site, 'ahamove'), shipment),
    grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express'), shipment, 'grab_express'),
    grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express_2h'), shipment, 'grab_express_2h'),
    viettel_post.get_shipments(await token_account.get_token_by_site(site, 'viettel_post'), shipment),
  ])

  const shipments = [...ahamove_shipments, ...grab_express_shipments, ...grab_express_2h_shipments, ...viettel_post_shipments]

  const shipment_fee = shipments.length ? Math.min(...shipments.map((v) => v.price)) : null
  if (!shipment_fee) {
    return res.status(400).json({ code: 2, message: 'No shipment service available' })
  }

  res.status(200).json({ message: 'Success', data: { shipment_fee }, code: 0 })
}

const sync_master_data = async (req, res) => {
  const site_id = req.params.site_id

  const partner_key = await PartnerAPIKey.findOne({ name: 'momo_mini' })
  if (!partner_key || partner_key.metadata.site_id !== site_id) {
    return res.json({ success: false, error: 'no momo_mini configured' })
  }

  const site = await Site.findOne({ _id: site_id })

  if (!site) {
    return res.status(404).json({ error: 'site_not_found' })
  }

  const token = await _get_login_token(site.brand_id)
  const hubs = await Hub.find({ _id: { $in: site.hub_ids } }).lean()
  for (const hub of hubs) {
    await axios.post(
      baseUrl + 'product-management/hooks/master-data/v1/updates',
      {
        timestamp: Date.now(),
        action: 'CREATE',
        storeId: hub.code,
        objectType: 'STORE',
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    )

    await axios.post(
      baseUrl + 'product-management/hooks/master-data/v1/updates',
      {
        timestamp: Date.now(),
        action: 'CREATE',
        storeId: hub.code,
        objectType: 'MENU',
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    )
  }

  res.status(200).json({
    message: 'Success',
    success: true,
    data: {
      synced: hubs.map((v) => v.code),
    },
  })
}

module.exports = {
  get_all_stores,
  get_all_product_categories,
  get_products_detail,
  get_store_detail,
  get_product_detail,
  create_order,
  cancel_order,
  confirm_order,
  update_order_status,
  get_shipment_fee,
  sync_master_data,
}
