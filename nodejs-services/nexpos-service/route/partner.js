const { User, Order, UserAddress, Role, Brand, UserOTP, Site, SiteMenuGroup, BrandMenu, FCMToken } = require('../../.shared/database')
const bcrypt = require('bcrypt')
const { send_email } = require('../../.shared/email')
const _ = require('lodash')
const moment = require('moment-timezone')
const { calculate_commissions } = require('../common/partner_commission')
moment.tz.setDefault('Asia/Bangkok')
const { nanoid } = require('nanoid')

exports.get_members_list = async (req, res) => {
  const query = {
    'he_info.referrer_id': req.user._id,
    'he_info.status': 'active',
  }
  if (req.query.search) {
    query['$text'] = { $search: req.query.search, $caseSensitive: false }
  }

  const result = await User.paginate(query, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: 1 },
    customLabels: { docs: 'data' },
    lean: true,
  })

  // const users = await User.find({ _id: { $in: result.data.map((d) => d.user_id) } }, { password: 0, last_login_devices: 0, last_login_device: 0 })
  // const usersMap = _.keyBy(users, '_id')

  const current_month = moment().month() + 1
  const current_year = moment().year()

  const commissions = await calculate_commissions(result.data, current_month, current_year, req.user.brands[0])

  res.json({
    success: true,
    ...result,
    data: result.data.map((d) => ({
      ...d,
      status: d.he_info?.status,
      user: d,
      total_sale: commissions?.[d._id]?.total_sale || 0,
      commission: commissions?.[d._id]?.commission || 0,
      total_order: commissions?.[d._id]?.total_order || 0,
      commission_percentage: commissions?.[d._id]?.percentage || 0,
    })),
  })
}

exports.invite_member = async (req, res) => {
  const { _id: user_id } = req.user
  const { type, email } = req.body

  const existedUser = await User.findOne({ email })
  if (existedUser) {
    return res.status(409).json({
      success: false,
      error: 'member_is_existed',
    })
  }

  // const pendingMember = await Partner.create({
  //   member_of: user_id,
  //   type,
  //   status: 'pending',
  //   email,
  // })

  const pendingMember = await User.create({
    username: email,
    email,
    password: '',
    name: email,
    role_id: req.user.role_id,
    is_active: false,
    hubs: [],
    brands: [],
    sites: [],
    he_info: {
      referrer_id: user_id,
      status: 'pending',
    },
  })

  await send_email({}, { email, name: email }, 'member_invitation', {
    name: email,
    link: `${process.env.WEB_URL}/invitation?id=${pendingMember._id}&email=${email}&type=${type}`,
    inviter: req.user.name,
  })

  res.json({
    success: true,
    data: pendingMember,
  })
}

exports.check_invitation = async (req, res) => {
  const { invitation_id } = req.query

  if (!invitation_id) {
    return res.status(400).json({
      success: false,
      error: 'invalid_invitation',
    })
  }
  const pendingPartner = await User.findOne({ _id: invitation_id })

  if (pendingPartner.he_info?.status !== 'pending') {
    return res.status(400).json({
      success: false,
      error: 'invitation_already_accepted',
    })
  }

  // const { email } = pendingPartner
  // const existedUser = await User.findOne({ email })
  // if (existedUser) {
  //   return res.status(409).json({
  //     success: false,
  //     error: 'email_already_existed',
  //   })
  // }

  return res.status(200).json({ success: true })
}

exports.accept_invitation = async (req, res) => {
  const { password, name, invitation_id } = req.body

  const partner = await User.findOne({ _id: invitation_id })
  if (!partner) {
    return res.status(404).json({
      success: false,
      error: 'invitation_not_found',
    })
  }

  if (partner.he_info?.status !== 'pending') {
    return res.status(400).json({
      success: false,
      error: 'invitation_already_accepted',
    })
  }

  // const { email } = pendingPartner
  // const existedUser = await User.findOne({ email })
  // if (existedUser) {
  //   return res.status(409).json({
  //     success: false,
  //     error: 'email_already_existed',
  //   })
  // }

  const hashedPassword = await bcrypt.hash(password, 10)

  // const parent = await User.findOne({ _id: pendingPartner.member_of })

  // const createdUser = await User.create({
  //   username: email,
  //   email,
  //   password: hashedPassword,
  //   name,
  //   role_id: parent.role_id, // same role partner manager
  //   is_active: true,
  //   approval_status: 'approved',
  //   hubs: parent.hubs,
  //   brands: parent.brands,
  //   sites: [],
  //   he_info: {
  //     referrer_id: parent._id,
  //     approved_at: moment().toDate(),
  //   }
  // })

  await User.findOneAndUpdate({ _id: invitation_id }, { password: hashedPassword, name, is_active: true, 'he_info.status': 'active', 'he_info.approved_at': moment().toDate() })

  // await Partner.findOneAndUpdate({ _id: pendingPartner._id }, { user_id: createdUser._id, status: 'active' }, { runValidators: true })

  // clone site of parent
  const parent = await User.findOne({ _id: partner.he_info?.referrer_id })

  const cloned_site_ids = []
  for (const site_id of parent.sites) {
    const parent_he_site = await Site.findOne({ _id: site_id, type: 'partner', apply_commission: true })
    if (!parent_he_site) {
      continue
    }

    const generated_code = nanoid(8)
    const cloned_site = await Site.create({
      ...parent_he_site.toObject(),
      _id: undefined,
      name: `${parent_he_site.name} - ${name}`,
      code: `${parent_he_site.code}-${generated_code}`,
      tokens: [],
      he_id: String(partner._id),
    })
    const parent_site_menu = await SiteMenuGroup.findOne({ site_id: parent_he_site._id })
    if (parent_site_menu) {
      await SiteMenuGroup.create({
        ...parent_site_menu.toObject(),
        _id: undefined,
        site_id: cloned_site._id,
      })
    }
    cloned_site_ids.push(cloned_site._id)
  }

  partner.sites = cloned_site_ids
  partner.hubs = parent.hubs
  partner.brands = parent.brands
  await partner.save()

  return res.json({
    success: true,
    data: partner,
  })
}

// exports.update_member = async (req, res) => {
//   const user_id = req.user._id
//   const member = await Partner.findOne({ _id: req.params.id, member_of: user_id })
//   if (!member) {
//     return res.status(404).json({
//       success: false,
//       error: 'member_not_found',
//     })
//   }

//   const { type } = req.body

//   const updated = await Partner.findOneAndUpdate({ _id: member._id }, { type }, { runValidators: true, returnDocument: 'after' })
//   const user = await User.findOne({ _id: member.user_id }, { password: 0 })

//   return res.json({ success: true, data: { ...updated._doc, user } })
// }

// exports.remove_member = async (req, res) => {
//   const user_id = req.user._id

//   const member = await Partner.findOne({ _id: req.params.id, member_of: user_id })
//   if (!member) {
//     return res.status(404).json({
//       success: false,
//       error: 'member_not_found',
//     })
//   }

//   const user = await User.findOne({ email: member.email })
//   if (user) {
//     await User.deleteOne({ email: member.email })
//   }

//   await Partner.deleteOne({ _id: member._id })
//   return res.json({ success: true })
// }

exports.create_customer = async (req, res) => {
  const { phone, password, name, address } = req.body
  if (!phone || !name || !address) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  // check if user existed by phone
  const existedUser = await User.findOne({ phone })
  if (existedUser) {
    return res.status(409).json({
      success: false,
      error: 'phone_is_existed',
    })
  }

  let email = req.body.email
  if (!req.body.email) {
    email = phone + 'nexdor.tech'
  }

  const hashedPassword = await bcrypt.hash(password, 10)

  const user_role = await Role.findOne({ name: 'Individual' })

  const created_user = await User.create({
    username: phone,
    email,
    phone,
    password: hashedPassword,
    name,
    role_id: user_role._id,
    is_active: true,
    hubs: [],
    brands: [],
    sites: [],
    created_by: req.user._id,
  })

  const created_address = await UserAddress.create({
    user_id: created_user._id,
    address: address?.formatted_address,
    address_obj: address,
    phone,
    name,
    is_default: true,
  })

  return res.json({
    success: true,
    data: { ...created_user.toObject(), password: undefined, default_address: created_address },
  })
}

exports.update_customer = async (req, res) => {
  const { name } = req.body

  const results = await User.findByIdAndUpdate(req.params.customer_id, { name }, { new: true, runValidators: true })

  if (!results) {
    res.json({
      success: false,
      error: 'customer_not_found',
    })
  }

  res.json({
    success: true,
    data: true,
  })
}

exports.get_customer = async (req, res) => {
  const result = await User.findById(req.params.customer_id)

  if (!result) {
    res.json({
      success: false,
      error: 'customer_not_found',
    })
  }

  res.json({
    success: true,
    data: result,
  })
}

exports.get_customers = async (req, res) => {
  const { search } = req.query
  const orders = await Order.find({ he_id: String(req.user._id) })

  const lodash = _(orders)

  const user_ids = lodash.uniqBy('user_id').map('user_id').value()

  let filter = { _id: { $in: user_ids } }

  const user_paginate = await User.paginate(
    {
      $or: [filter, { created_by: req.user._id }, { he_ids: req.user._id }],
      ...(search ? { username: { $regex: new RegExp(search, 'i') } } : {}),
      is_guest: false,
    },
    {
      page: Number(req.query.page || 1),
      limit: Number(req.query.limit || 100), // Max 100 docs
      sort: { updated_at: -1 },
      customLabels: { docs: 'data' },
      projection: {
        last_login_devices: 0,
        last_login_device: 0,
        password: 0,
      },
      lean: true,
    }
  )

  const unique_user_ids = user_paginate.data.map((u) => String(u._id))
  const addresses = await UserAddress.find({ user_id: { $in: unique_user_ids } }).then((user_addresses) => {
    const results = unique_user_ids
      .map((user_id) => {
        const addresses = user_addresses.filter((user_address) => user_address.user_id === user_id).map((address) => _.assign(address, user_id))
        const results = _.orderBy(addresses, ['is_default', 'updated_at'], ['desc', 'desc'])
        return results?.[0]
      })
      .filter(Boolean)

    return _.keyBy(results, 'user_id')
  })

  const mapping_data = user_paginate?.data?.map?.((user) => {
    const default_address = addresses?.[String(user._id)] || null
    return {
      ...user,
      default_address,
      orders: orders?.filter((order) => order?.user_id === String(user._id)) || [],
    }
  })

  return res.json({ success: true, ...user_paginate, data: mapping_data })
}

exports.get_customer_orders = async (req, res) => {
  const { customer_id } = req.params
  if (!customer_id) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  const orders = await Order.find({ he_id: String(req.user._id), user_id: customer_id })
  return res.json({ success: true, data: orders })
}

exports.get_banners = async (req, res) => {
  const { brand_ids } = req.query
  const brands = req.user.brands

  if (brand_ids && !brand_ids.every((brand_id) => brands.includes(brand_id))) {
    return res.status(404).json({
      success: false,
      error: 'brand_not_found',
    })
  }

  const brand_filter = brand_ids ? { _id: { $in: brand_ids } } : { _id: { $in: brands } }
  const result = await Brand.find(brand_filter, { banners: 1, _id: 1, name: 1 })

  return res.json({ success: true, data: result })
}

exports.get_saved_bank_accounts = async (req, res) => {
  const user_id = String(req.user._id)

  const partner = await User.findOne({ _id: user_id })

  return res.json({ success: true, data: partner.he_info.saved_accounts || [] })
}

exports.deactivate_self = async (req, res) => {
  const user_id = String(req.user._id)

  const partner = await User.findOne({ _id: user_id })
  if (!partner) {
    return res.status(404).json({ success: false, error: 'partner_not_found' })
  }

  await User.findOneAndUpdate({ _id: user_id }, { is_active: false })
  return res.json({ success: true })
}

exports.get_user_by_phone = async (req, res) => {
  const { phone } = req.params
  const user = await User.findOne(
    { username: phone },
    {
      _id: 1,
      name: 1,
      email: 1,
      username: 1,
      phone: 1,
      password: 1,
      he_ids: 1,
      created_by: 1,
    }
  ).lean()

  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  const default_address = await UserAddress.find({ user_id: user._id }).then((user_addresses) => {
    const results = _.orderBy(user_addresses, ['is_default', 'updated_at'], ['desc', 'desc'])
    return results?.[0]
  })

  const is_pwd_set = !!user.password
  delete user.password

  return res.json({
    success: true,
    data: { ...user, is_pwd_set, default_address: default_address || {}, added: user.he_ids?.includes(String(req.user._id)) || false },
  })
}

exports.register_partner = async (req, res) => {
  const { phone, password, name, address, email, brand_ids, referral_phone } = req.body
  if (!phone || !email || !name || !password) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  // check if user existed
  const username = email
  const existedUser = await User.findOne({ username })
  if (existedUser) {
    return res.status(409).json({
      success: false,
      error: 'user_is_existed',
    })
  }

  const user_role = await Role.findOne({ name: 'Partner Manager' })
  if (!user_role) {
    return res.status(404).json({
      success: false,
      error: 'role_not_found',
    })
  }

  let referral_user = null
  if (referral_phone) {
    referral_user = await User.findOne({ phone: referral_phone, role_id: String(user_role._id) })
    if (!referral_user) {
      return res.status(404).json({
        success: false,
        error: 'referral_user_not_found',
      })
    }
  }

  const hashedPassword = await bcrypt.hash(password, 10)
  await User.create({
    username,
    email,
    phone,
    password: hashedPassword,
    address,
    name,
    role_id: user_role._id,
    is_active: false,
    approval_status: 'pending',
    hubs: [],
    brands: brand_ids || [],
    sites: [],
    he_info: {
      referrer_id: referral_user?._id,
    },
  })
    .then(async (user) => {
      // create partner record
      // await Partner.create({
      //   user_id: String(user._id),
      //   member_of: null,
      //   type: 'member',
      //   status: 'active',
      //   // level: 1,
      //   email,
      // })
      // send otp verify user
      // const otp = Math.floor(100000 + Math.random() * 900000).toString()
      // await send_email({}, { email, name }, 'register', {
      //   name: name,
      //   code: otp,
      //   url: `${process.env.WEB_URL}/verify_account?code=${otp}&email=${email}&type=register`
      // })
      // await UserOTP.findOneAndUpdate({ verify_type: 'register', user_uid: username }, { otp, expired_at: moment().add(15, 'minute') }, { upsert: true })
    })
    .catch((err) => {
      let error = err.message
      const patternKeys = Object.keys(err.keyPattern)
      if (err.code === 11000 && patternKeys?.length > 0) {
        error = `${patternKeys[0]}_is_existed`
      }
      return res.json({
        success: false,
        error,
      })
    })

  return res.json({
    success: true,
  })
}

exports.select_customer = async (req, res) => {
  const { user_id } = req.body

  const user = await User.findOne({ _id: user_id })
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'user_not_found',
    })
  }

  user.he_ids = user.he_ids || []
  if (!user.he_ids.includes(String(req.user._id))) {
    await User.findOneAndUpdate({ _id: user._id }, { $addToSet: { he_ids: String(req.user._id) } })
  }

  return res.json({
    success: true,
  })
}

exports.get_partners = async (req, res) => {
  const { status, brand_id } = req.query

  const roles = await Role.find({ name: 'Partner Manager' }, { _id: 1, name: 1 })

  let query = { role_id: { $in: roles.map((r) => r._id) } }
  if (brand_id) {
    query.brands = brand_id
  }

  if (status && status !== 'approved') {
    query.approval_status = status
  }
  if (status === 'approved') {
    // fallback for old data
    query['$or'] = [{ approval_status: 'approved' }, { approval_status: { $exists: false } }]
  }

  const user_paginated = await User.paginate(query, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    projection: { password: 0, last_login_device: 0, last_login_devices: 0 },
  })

  return res.json({
    success: true,
    ...user_paginated,
    data: user_paginated.data.map((u) => ({ ...u.toObject(), role: roles.find((r) => r._id.toString() === u.role_id.toString()) })),
  })
}

exports.approve_partner = async (req, res) => {
  const { partner_id } = req.params
  const { brand_ids, hub_ids } = req.body

  const partner = await User.findOne({ _id: partner_id })

  if (!partner || partner.is_active) {
    return res.status(404).json({ success: false, error: 'partner_not_found_or_already_approved' })
  }

  await User.findByIdAndUpdate(partner_id, {
    brands: brand_ids || [],
    hubs: hub_ids || [],
    is_active: true,
    approval_status: 'approved',
    'he_info.approved_at': moment().toDate(),
  })
  return res.json({ success: true })
}


exports.get_brand_price_config = async (req, res) => {
  const brands = req.user.brands
  const brand_menu = await BrandMenu.findOne({ brand_id: { $in: brands } }).lean()
  const allItems = _.flatMap(brand_menu?.categories, ({ items, sub_categories }) => [
    ...items.map((item) => ({ ...item, floor_price: item.floor_price, ceiling_price: item?.ceiling_price || item?.price || 0 })),
    ..._.flatMap(sub_categories, ({ items }) =>
      items.map((item) => ({ ...item, floor_price: item.floor_price, ceiling_price: item?.ceiling_price || item?.price || 0 })),
    ),
  ])

  res.json({
    success: true,
    data: allItems,
  });
}