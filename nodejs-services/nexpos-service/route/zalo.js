const ejs = require('ejs')
const axios = require('axios')
const _ = require('lodash')
const fs = require('fs')
const AWS = require('aws-sdk')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { Brand, VendorCallback, TokenAccount, ShorternCode } = require('../../.shared/database')
const { send_slack_message } = require('../../.shared/slack')
const helper = require('../../.shared/helper')
const qs = require('qs');

const router = {}

router.callback = async (req, res, next) => {
    // await VendorCallback.create({
    //     vendor: 'zalo',
    //     type: 'order',
    //     url: req.originalUrl,
    //     method: req.method,
    //     headers: req.headers,
    //     success: true,
    //     request_data: req.body,
    //     response_data: { success: true },
    // })

    /*
    const { event_name, source, sender, recipient, follower } = req.body
    if (event_name === 'follow') {
        const { app_id, oa_id } = req.body
        const brand = await Brand.findOne({ "tokens.source": "zalo", "tokens.site_id": oa_id })
        if (brand) {
            const zalo_token = brand.getToken('zalo')
            if (zalo_token?.access_token) {
                await zalo.send_request_user_info({
                    access_token: zalo_token?.access_token
                }, {
                    user_id: follower.id,
                    title: 'Xin chào!',
                    subtitle: '[Test] Để nhận được quà tặng từ chương trình khuyến mãi, vui lòng click vào ảnh và đồng ý cung cấp thông tin cá nhân',
                    image_url: 'https://media.nutifoodshop.com/file/muRk8Ul9E'
                })
            }
        }

    }

    if (event_name === 'user_submit_info') {

        const brand = await Brand.findOne({ "tokens.source": "zalo", "tokens.site_id": recipient?.id })
        if (brand) {
            const zalo_token = brand.getToken('zalo')
            if (zalo_token?.access_token) {
                const otp = Math.floor(100000 + Math.random() * 900000).toString()
                await zalo.send_message_to_user_id({
                    access_token: zalo_token?.access_token
                }, {
                    user_id: sender.id,
                    text: [
                        `[Test] Cảm ơn bạn đã cung cấp thông tin cá nhân.`,
                        `Vui lòng cung cấp tin nhắn này cho nhân viên bán hàng để nhận quà tặng.`,
                        `Mã xác thực của bạn là: ${otp}.`,
                        `Quay lại trang mua hàng:`,
                        `https://he.nexpos.io/HE.VARNA.CT`,
                    ].join('\n')
                })
            }
        }
    }
    */
    res.json({ success: true })
}

router.client_callback = async (req, res, next) => {
    const { oa_id, code } = req.query
    const resp = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://oauth.zaloapp.com/v4/oa/access_token',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'secret_key': 'R2POMbJ6GNGVVEYFX3EI'
        },
        data: qs.stringify({
            'code': code,
            'app_id': "1616787572616286289",
            'grant_type': 'authorization_code',
            'code_verifier': ''
        })
    })
    if (resp.data.access_token) {
        await ShorternCode.findOneAndUpdate({ code: `${oa_id}_zalo` }, {
            data: {
                source: 'zalo',
                username: oa_id,
                access_token: resp.data.access_token,
                refresh_token: resp.data.refresh_token,
            }
        }, { upsert: true })

    }
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${resp.data.access_token ? 'Đăng nhập thành công' : 'Đăng nhập thất bại'}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 40px;
                    margin: 0;
                }
                .success { color: #28a745; }
                .error { color: #dc3545; }
                .message { margin-top: 20px; }
            </style>
        </head>
        <body>
            ${resp.data.access_token ?
            `<h1 class="success">Đăng nhập thành công</h1>
                 <p class="message">Vui lòng nhập mã <strong>${oa_id}_zalo</strong> để kích hoạt</p>`
            :
            `<h1 class="error">Đăng nhập thất bại</h1>
                 <p class="message">${JSON.stringify(resp.data)}</p>`
        }
        </body>
        </html>
    `);

}
module.exports = router