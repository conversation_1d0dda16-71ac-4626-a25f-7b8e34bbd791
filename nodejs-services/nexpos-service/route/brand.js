const { uniqBy } = require('lodash');
const { Brand, User, Role } = require('../../.shared/database')

const get_brand_list = async (req, res) => {
    let filter = {}
    if (req.user && !req.permissions?.includes('system')) {
        filter._id = req.user.brands
    }

    if (req.query.name) {
        const escaped_name = req.query.name.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
        filter.name = { $regex: new RegExp(`.*${escaped_name}.*`, 'i') };
    }

    const brand_paginate = await Brand.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 docs
        sort: { name: 1 },
        customLabels: { docs: 'data' }
    });

    let data = brand_paginate.data
    if (req.query.selected_ids) {
        const selected_brands = await Brand.find({ _id: { $in: req.query.selected_ids } })
        if (Array.isArray(selected_brands)) {
            data.concat(selected_brands)
            data = uniqBy(data, '_id')
        }
    }

    res.json({
        success: true,
        data,
    });
}

const get_brand_details = async (req, res) => {
    console.log(req.params.brand_id)
    const data = await Brand.findById(req.params.brand_id)
    if (!data) {
        return res.json({ success: false, error: 'brand_not_found' })
    }
    return res.json({
        success: true,
        data,
    })
}

// Create a new brand
const create_brand = async (req, res) => {
    const { name, address, address_obj, description, logo, menu_sheet_file_id, tokens, hotline } = req.body;

    try {
        const result = await Brand.create({ name, address, address_obj, description, logo, menu_sheet_file_id, tokens, hotline });
        await User.findByIdAndUpdate(req.user._id, { brands: [...req.user.brands, result._id] })

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

// Update an existing brand
const update_brand = async (req, res) => {
    const { name, address, address_obj, description, logo, menu_sheet_file_id, tokens, hotline } = req.body;

    try {
        const result = await Brand.findByIdAndUpdate(
            req.params.brand_id,
            { name, address, address_obj, description, logo, menu_sheet_file_id, tokens, hotline },
            { new: true } // Return the updated document
        );
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const update_brand_status = async (req, res) => {
    const { status } = req.body;

    try {
        const result = await Brand.findByIdAndUpdate(
            req.params.brand_id,
            { status },
            { new: true } // Return the updated document
        );
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const delete_brand = async (req, res) => {
    try {
        const deletedBrand = await Brand.findByIdAndDelete(req.params.brand_id);
        if (!deletedBrand) {
            return res.status(404).json({ success: false, message: 'Brand not found' });
        }
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const update_banners = async (req, res) => {
    const { banners } = req.body
    const { brand_id } = req.params

    const updated = await Brand.findOneAndUpdate({ _id: brand_id }, { banners }, { runValidators: true, returnDocument: 'after' })

    return res.json({ success: true, data: updated.banners })
}

module.exports = { get_brand_list, get_brand_details, create_brand, update_brand, update_brand_status, delete_brand, update_banners }
