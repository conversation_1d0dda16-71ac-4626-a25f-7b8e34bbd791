const { Partner<PERSON>ithdraw, User, CommissionSummary, Brand } = require('../../.shared/database')
const moment = require('moment-timezone')
const { calculate_partner_commission_by_brand } = require('../common/partner_commission')
moment.tz.setDefault('Asia/Bangkok')

exports.get_list = async (req, res) => {
    const { _id: user_id } = req.user
    const result = await PartnerWithdraw.paginate({
        user_id,
    }, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 docs
        sort: { created_at: 1 },
        customLabels: { docs: 'data' },
        lean: true,
    })
    res.json({
        success: true,
        ...result,
    })
}

exports.create = async (req, res) => {
    const { _id: user_id, brands } = req.user
    const { amount, note, bank_account, brand_id } = req.body

    const partner = await User.findOne({
        _id: user_id,
        'he_info.status': 'active'
    })

    if (!partner) {
        return res.json({
            success: false,
            error: 'not_a_partner',
        })
    }

    if (!brands.includes(brand_id)) {
        return res.json({
            success: false,
            error: 'not_a_partner_of_this_brand',
        })
    }

    const brand_balance = await get_balance_by_brand(req.user, brand_id)
    if (brand_balance.total_balance - brand_balance.total_pending < amount) {
        return res.json({
            success: false,
            error: 'insufficient_balance',
        })
    }

    const created = await PartnerWithdraw.create({
        user_id,
        amount,
        note,
        bank_account,
        status: 'pending',
        brand_id,
    })

    const { bank_name, account_name, account_number } = bank_account
    const is_account_exist = partner.saved_accounts?.some(account => {
        return account.bank_name === bank_name && account.account_name === account_name && account.account_number === account_number
    })
    if (!is_account_exist) {
        await User.findOneAndUpdate({
            _id: user_id,
        }, {
            $push: {
                'he_info.saved_accounts': bank_account,
            },
        })
    }


    res.json({
        success: true,
        data: created,
    })
}

exports.update = async (req, res) => {
    const { _id: user_id } = req.user
    const { id } = req.params
    const { amount, note, bank_account, status } = req.body

    const entity = await PartnerWithdraw.findOne({
        _id: id,
        user_id,
    })
    if (!entity) {
        return res.json({
            success: false,
            error: 'request_not_found',
        })
    }
    if (entity.status !== 'pending') {
        return res.json({
            success: false,
            error: 'only_update_in_pending_state_allowed',
        })
    }

    if (amount) {
        const brand_balance = await get_balance_by_brand(req.user, entity.brand_id)
        if (brand_balance.total_balance - brand_balance.total_pending + entity.amount < amount) {
            return res.json({
                success: false,
                error: 'insufficient_balance',
            })
        }
    }

    const updated = await PartnerWithdraw.findOneAndUpdate({
        _id: id,
        user_id,
    }, {
        amount,
        note,
        bank_account,
        status,
    }, {
        new: true,
    })

    res.json({
        success: true,
        data: updated,
    })
}

exports.delete = async (req, res) => {
    const { _id: user_id } = req.user
    const { id } = req.params

    const entity = await PartnerWithdraw.findOne({
        _id: id,
        user_id,
    })
    if (!entity) {
        return res.json({
            success: false,
            error: 'request_not_found',
        })
    }
    if (entity.status !== 'pending') {
        return res.json({
            success: false,
            error: 'only_delete_in_pending_state_allowed',
        })
    }

    await PartnerWithdraw.findOneAndDelete({
        _id: id,
        user_id,
    })

    res.json({
        success: true,
    })
}

exports.get_balance_reports = async (req, res) => {
    const { _id: user_id, brands: brand_ids } = req.user;

    if (!brand_ids?.length) {
        return res.json({
            success: false,
            error: 'no_brand_id',
        })
    }

    const data = await Promise.all(brand_ids.map(async brand_id => {
        return get_balance_by_brand(req.user, brand_id)
    }));

    res.json({
        success: true,
        data,
    })
}

const get_balance_by_brand = async (user, brand_id) => {
    const user_id = String(user._id)
    const current_month = moment().month() + 1
    const current_year = moment().year()

    const brand = await Brand.findOne({ _id: brand_id })
    const total_balance = await CommissionSummary.find({
        user_id,
        brand_id,
    },
        { details: 0 })
        .lean()
        .then(data => data.reduce((total, doc) => total + doc.balance, 0))

    const this_month_commission = await calculate_partner_commission_by_brand(user, current_month, current_year, brand_id)
    const this_month_balance = this_month_commission.total_income

    const withdraw_request = await PartnerWithdraw.find({
        user_id,
        brand_id,
    })
    const completed = withdraw_request.filter(doc => doc.status === 'completed').reduce((total, doc) => total + doc.amount, 0)
    const pending = withdraw_request.filter(doc => doc.status === 'pending').reduce((total, doc) => total + doc.amount, 0)

    return {
        // remaining balance
        total_balance: total_balance - completed,
        // current month balance, sofar
        this_month_balance,
        // the amount has been requested, but not approved yet
        total_pending: pending || 0,
        // the amount has been approved and transferred to partner
        total_withdrew: completed,
        brand,
    }
}