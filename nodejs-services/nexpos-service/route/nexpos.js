const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { Site, Order, User, PrintQueue, SiteOrderIndex, Hub, Brand, BrandMenu } = require('../../.shared/database')
const { send_slack_message } = require('../../.shared/slack')
const helper = require('../../.shared/helper')
const nexpos = require('../../.shared/merchant/nexpos')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')


const router = {}

router.sync_orders = async (req, res, next) => {
    const hubs = await Hub.find({ inventory_source: 'nexpos' })
    const sites = await Site.find({ hub_id: hubs.map(v => String(v._id)) })
    const orders = await Order.find({
        site_id: sites.map(v => String(v._id)),
        status: 'FINISH',
        vendor_sync: null,
        created_at: {
            $gte: moment().subtract(2, 'days').startOf('day').toDate(),
        },
    }).limit(20)

    for (const order of orders) {
        const sync_order_resp = await nexpos.sync_an_order(order)

        const site = sites.find(v => String(v._id) == String(order.site_id))
        const brand_menu = await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, {
            $setOnInsert: {
                categories: [],
                option_categories: [],
            }
        }, { upsert: true, new: true }).lean()

        const { dishes } = helper.add_stock_to_dishes(order.data_mapping.dishes, brand_menu)
        order.data_mapping.dishes = dishes
        await Order.findByIdAndUpdate(order._id, {
            data_mapping: order.data_mapping,
            vendor_sync: {
                at: new Date(),
                retry: 1,
                success: sync_order_resp.success,
                message: sync_order_resp.message,
                callback: sync_order_resp.data,
            }
        })
    }

    res.json({
        success: true,
    })
}

module.exports = router