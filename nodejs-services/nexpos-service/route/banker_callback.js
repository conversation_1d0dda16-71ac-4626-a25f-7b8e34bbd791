const { Order, VendorCallback } = require('../../.shared/database')
const _ = require('lodash')
const crypto = require('crypto');

let router = {};

const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxFA0ZTaG4qm1hgFbeu8G
OP4dVxJTBp53uOXyZaEB9IDs27Trslo5gRTgX2Mcp6c8b5HN8Zf1fXbYdat84I1k
wqopmAQRu7zc3eXEg85mUbmXGFF8HIoCzpvLLppmunT2j3sKuOXHKDWtOIF5zPYV
ndS8c2K5e6L3Db3CUVYFdC5/oqkX/yUkjVSYhJE/A0q00z69hWnhN4s68/ZxqflV
41F036lpDjfEZuzyhx/XWtaZSulRKVxw+cFkkAaWJoiF9R5NbcrtPJb7zzr4eKjf
bjtq4qat/q3Vh/z6ofqCH4XNMoeOMy9wWDyHSdHliRmNhmKFhogj1DyGnAdnrOkq
pQIDAQAB
-----END PUBLIC KEY-----
`;
function verifySignature(data, signature) {
    try {
        // const publicKey = process.env.OCB_PUBLIC_KEY;
        const verifier = crypto.createVerify('sha256WithRSAEncryption');
        verifier.update(Buffer.from(data));
        const signatureBuffer = Buffer.from(signature, 'hex');
        const result = verifier.verify(publicKey, signatureBuffer);
        console.log(result)
        return result
    } catch (error) {
        console.error('Signature verification error:', error);
        return false;
    }
}

router.ocb_order_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'ocb',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })

    const signature = req.headers['x-signature'];
    const requestTimestamp = req.headers['x-request-timestamp'];

    if (!signature || !requestTimestamp) {
        return res.status(400).json({
            code: '400',
            message: 'Bad Request',
            detail: 'Missing required headers',
            refTransactionId: ''
        });
    }

    const bodyString = JSON.stringify(req.body);
    const dataToVerify = `${requestTimestamp}:${bodyString}`;

    const isValidSignature = verifySignature(dataToVerify, signature);
    if (!isValidSignature) {
        return res.status(401).json({
            code: '401',
            message: 'Invalid signature',
            detail: 'Signature verification failed',
            refTransactionId: req.body.bankTransactionId || ''
        });
    }

    const {
        pushNotifyId,
        bankTransactionId,
        bankTransactionDate,
        beneficialAccount,
        transactionAmount,
        transactionDesc,
        transactionCurrency,
        transactionType,
        transactionDateTime
    } = req.body;

    if (!pushNotifyId || !bankTransactionId || !bankTransactionDate ||
        !beneficialAccount || !transactionAmount || !transactionDesc ||
        !transactionCurrency || !transactionType || !transactionDateTime) {
        return res.status(400).json({
            code: '400',
            message: 'Bad Request',
            detail: 'Missing required fields',
            refTransactionId: bankTransactionId || ''
        });
    }

    return res.status(200).json({
        code: '00',
        message: 'success',
        detail: '',
        refTransactionId: bankTransactionId
    });
}


module.exports = router;