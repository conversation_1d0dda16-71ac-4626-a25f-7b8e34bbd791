const express = require('express');
const { TokenAccount, Site, ShorternCode, Brand } = require('../../.shared/database');
const ahamove = require('../../.shared/delivery/ahamove');
const grab = require('../../.shared/merchant/grab');
const be = require('../../.shared/merchant/be');
const facebook = require('../../.shared/merchant/facebook');
const { toNumber } = require('lodash');
const moment = require('moment');
const axios = require('axios');

const activate = async (req, res) => {
    const { app_id, site_id } = req.params;
    const { username, password } = req.body;
    try {
        if (app_id === 'ahamove') {
            const site = await Site.findById(site_id);
            const ahamove_token = await ahamove.get_token(username);
            if (!ahamove_token?.access_token) {
                res.status(400).json({
                    success: false,
                    message: 'Invalid username'
                });
                return
            }
            await TokenAccount.findOneAndUpdate({ token_code: `${ahamove_token.username}_${app_id}` }, {
                source: 'ahamove',
                username,
                access_token: ahamove_token.access_token,
                refresh_token: ahamove_token.refresh_token,
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: `${ahamove_token.username}_${app_id}`,
                    username: ahamove_token.username,
                    working: true,
                })
            } else {
                site_token.token_code = `${ahamove_token.username}_${app_id}`
                site_token.username = ahamove_token.username
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'grab_express') {
            const site = await Site.findById(site_id);
            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: 'NEXDOR_grab_express',
                    working: true,
                })
            } else {
                site_token.token_code = 'NEXDOR_grab_express'
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'grab' || app_id === 'grab_mart') {
            const site = await Site.findById(site_id);
            const grab_token = await grab.get_token(username, password);
            if (!grab_token?.access_token) {
                res.status(400).json({
                    success: false,
                    message: 'Invalid username'
                });
                return
            }
            await TokenAccount.findOneAndUpdate({ token_code: `${site.code}_${app_id}` }, {
                source: 'grab',
                username,
                password,
                site_id: grab_token.site_id,
                access_token: grab_token.access_token,
                refresh_token: grab_token.refresh_token,
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: `${site.code}_${app_id}`,
                    username: grab_token.username,
                    working: true,
                })
            } else {
                site_token.token_code = `${site.code}_${app_id}`
                site_token.username = grab_token.username
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'be') {
            const site = await Site.findById(site_id);
            const be_token = await be.get_token(username, password);
            if (!be_token?.access_token) {
                res.status(400).json({
                    success: false,
                    message: 'Invalid username'
                });
                return
            }

            await TokenAccount.findOneAndUpdate({ token_code: `${site.code}_${app_id}` }, {
                source: 'be',
                username,
                password,
                site_id: req.body.site_id || be_token.site_id,
                access_token: be_token.access_token,
                refresh_token: be_token.refresh_token,
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: `${site.code}_${app_id}`,
                    username: be_token.username,
                    working: true,
                })
            } else {
                site_token.token_code = `${site.code}_${app_id}`
                site_token.username = be_token.username
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'shopee' || app_id === 'shopee_fresh') {
            const site = await Site.findById(site_id);
            const token_code = `${username}_${app_id}`
            await TokenAccount.findOneAndUpdate({ token_code }, {
                source: 'shopee',
                username,
                password,
                site_id: "",
                access_token: req.body.access_token,
                refresh_token: '',
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code,
                    site_id: req.body.site_id,
                    username: username,
                    working: true,
                })
            } else {
                site_token.token_code = token_code
                site_token.username = username
                site_token.site_id = req.body.site_id
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'facebook') {
            const site = await Site.findById(site_id);
            const facebook_token = await facebook.get_token(req.body.access_token);
            await TokenAccount.findOneAndUpdate({ token_code: `${site.code}_${app_id}` }, {
                source: 'facebook',
                site_id: req.body.site_id,
                access_token: facebook_token.access_token,
                refresh_token: '',
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: `${site.code}_${app_id}`,
                    username: req.body.site_id,
                    working: true,
                })
            } else {
                site_token.token_code = `${site.code}_${app_id}`
                site_token.username = req.body.site_id
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        if (app_id === 'zalo') {
            const site = await Site.findById(site_id);
            const shortern_code = await ShorternCode.findOne({ code: req.body.token_code })
            if (!shortern_code) {
                res.status(400).json({
                    success: false,
                });
                return
            }
            await TokenAccount.findOneAndUpdate({ token_code: `${site.code}_${app_id}` }, {
                source: 'zalo',
                username: shortern_code.data.username,
                access_token: shortern_code.data.access_token,
                refresh_token: shortern_code.data.refresh_token,
                working: true,
                last_working_at: new Date(),
                expired_at: moment().add(1, 'days').toDate(),
            }, { upsert: true })

            const site_token = site.tokens.find(token => token.source === app_id);
            if (!site_token) {
                site.tokens.push({
                    source: app_id,
                    token_code: `${site.code}_${app_id}`,
                    username: shortern_code.data.username,
                    working: true,
                })
            } else {
                site_token.token_code = `${site.code}_${app_id}`
                site_token.username = shortern_code.data.username
                site_token.working = true
            }
            await site.save()
            return res.json({
                success: true,
                data: site_token
            });
        }

        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

const deactivate = async (req, res) => {
    const { app_id, site_id } = req.params;
    try {
        const site = await Site.findById(site_id);
        const site_token = site.tokens.find(token => token.source === app_id);
        if (site_token) {
            site_token.token_code = ''
            site_token.working = false
        }
        await site.save()
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
}

const shopee_login = async (req, res) => {
    const { username, password } = req.body;
    const resp = await axios.post(`https://shopee.nexpos.io/login`, { email: username, password: password })
    res.json(resp.data)
}

const be_login = async (req, res) => {
    const { username, password } = req.body;
    const resp = await be.get_store_list_by_auth(username, password)
    res.json({
        data: resp,
        success: true
    })
}

module.exports = { activate, deactivate, shopee_login, be_login };
