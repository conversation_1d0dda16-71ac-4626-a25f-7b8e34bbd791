const { CampaignUser, UserOTP } = require('../../.shared/database')
const { isArray } = require('lodash')
const PhoneNumber = require('libphonenumber-js');

exports.get_gift_campaign_list_by_user = async (req, res) => {
  let filter = {}
  if (req.user && !req.permissions?.includes('system')) {
    filter.brand_id = req.user.brands
  }

  console.log({ filter })

  const result = await CampaignUser.paginate(filter, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    lean: true,
  })

  res.json({
    success: true,
    ...result,
  })
}

exports.get_gift_campaign_list = async (req, res) => {
  const { brand_id } = req.params

  const result = await CampaignUser.paginate({
    brand_id
  }, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    lean: true,
  })

  res.json({
    success: true,
    ...result,
  })
}


exports.create_gift_campaign = async (req, res) => {
  const { brand_id } = req.params
  const { title, description, status } = req.body

  const campaignUser = await CampaignUser.create({
    brand_id,
    title,
    description,
    status,
    participants: [],
  })

  res.json({
    success: true,
    data: campaignUser,
  })
}


exports.update_gift_campaign = async (req, res) => {
  const { brand_id } = req.params
  const { title, description, status } = req.body

  const campaignUser = await CampaignUser.findOneAndUpdate({
    brand_id,
  }, {
    title, description, status,
  })

  res.json({
    success: true,
    data: campaignUser,
  })
}

exports.join_gift_campaign = async (req, res) => {
  const { campaign_id } = req.params
  const { phone, name, address, verified_hash } = req.body

  const phone_number = PhoneNumber(phone, 'VN')
  const parsed_phone = phone_number.format('E.164')

  const user_otp = await UserOTP.findOne({
    verify_type: 'verify_phone',
    user_uid: parsed_phone,
    verified_hash,
  })

  if (!user_otp) {
    return res.status(400).json({
      success: false,
      error: 'invalid_verified_hash',
    })
  }

  const currentCapaign = await CampaignUser.findById(campaign_id)

  if (!currentCapaign) {
    return res.status(400).json({
      success: false,
      error: 'campaign_does_not_exist',
    })
  }

  const participants = isArray(currentCapaign?.participants) ? [...currentCapaign?.participants] : [];
  const findUser = participants.findIndex(participant => participant.phone === parsed_phone)

  if (findUser > -1) {
    return res.status(400).json({
      success: false,
      error: 'phone_number_has_registered',
    })
  }

  participants.push({
    name, address, phone: parsed_phone,
  })

  currentCapaign.participants = participants

  await currentCapaign.save();

  res.json({
    success: true,
  })
}


exports.get_gift_campaign_details = async (req, res) => {
  const data = await CampaignUser.findById(req.params.campaign_id)

  if (!data) {
    return res.status(404).json({
      success: false,
      error: 'gift_campaign_not_found',
    })
  }

  return res.status(200).json({
    success: true,
    data,
  })
}