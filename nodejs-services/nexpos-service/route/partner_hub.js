const { Site, Order, BrandCommission } = require('../../.shared/database')
const moment = require('moment')

const _get_orders = async (site_id, start_date, end_date) => {
  return Order.find(
    {
      site_id,
      status: 'FINISH',
      'data_mapping.delivery_time_unix': {
        $gte: moment(start_date).unix(),
        $lte: moment(end_date).unix(),
      },
    },
    {
      'data_mapping.total': 1,
      'data_mapping.delivery_time_unix': 1,
      order_id: 1,
      source: 1,
    }
  )
}

const get_partner_hub_summary = async (req, res) => {
  const { site_id } = req.params
  const { start_date, end_date } = req.query

  const site = await Site.findOne({ _id: site_id })
  if (!site) {
    return res.status(400).json({
      success: false,
      error: 'site_not_found',
    })
  }

  if (!site.partner_hub_tier) {
    return res.json({
      success: false,
      error: 'site_not_partner_hub',
    })
  }

  const orders = await _get_orders(site_id, start_date, end_date)
  const brand_commission = await BrandCommission.findOne({
    brand_id: site.brand_id,
    type: 'partner_hub',
    status: 'active',
  })

  if (!brand_commission) {
    return {
      orders: {
        value: 0,
      },
      revenue: {
        value: 0,
      },
      packaging_commission: {
        amount: 0,
      },
      sale_commission: {
        amount: 0,
      },
    }
  }

  const offline_orders = orders.filter((order) => ['local', 'he'].includes(order.source))
  const online_orders = orders.filter((order) => !['local', 'he'].includes(order.source))

  const applied = brand_commission.partner_hub[site.partner_hub_tier]
  const { online_order, offline_order } = applied

  const total_online_sales = online_orders.reduce((acc, order) => acc + order.data_mapping.total, 0)
  const packaging_commission = offline_orders.length * offline_order.packaging + online_orders.length * online_order.packaging

  const total_offline_sales = offline_orders.reduce((acc, order) => acc + order.data_mapping.total, 0)
  const growth_commission = (total_offline_sales * offline_order.commission) / 100 + (total_online_sales * online_order.commission) / 100

  res.json({
    success: true,
    data: {
      orders: {
        value: orders.length,
      },
      revenue: {
        value: total_offline_sales + total_online_sales,
      },
      packaging_commission: {
        amount: packaging_commission,
      },
      sale_commission: {
        amount: Math.floor(growth_commission),
      },
    },
  })
}

const get_chart = async (req, res) => {
  const { site_id } = req.params
  const { start_date, end_date, metric } = req.query
  const site = await Site.findOne({ _id: site_id })
  if (!site) {
    return res.status(400).json({
      success: false,
      error: 'site_not_found',
    })
  }

  const orders = await _get_orders(site_id, start_date, end_date)
  const brand_commission = await BrandCommission.findOne({
    brand_id: site.brand_id,
    type: 'partner_hub',
    status: 'active',
  })
  const order_by_date = orders.reduce((acc, order) => {
    const date = moment(order.data_mapping.delivery_time_unix * 1000).format('DD/MM/YY')
    acc[date] = [...(acc[date] || []), order]
    return acc
  }, {})

  const result = { metric, data: [] }
  switch (metric) {
    case 'revenue': {
      result.data = Object.keys(order_by_date).map((date) => ({
        date,
        value: order_by_date[date].reduce((acc, order) => acc + order.data_mapping.total, 0),
      }))
      break
    }

    case 'orders':
      result.data = Object.keys(order_by_date).map((date) => ({
        date,
        value: order_by_date[date].length,
      }))
      break
    case 'packaging_commission':
      result.data = Object.keys(order_by_date).map((date) => ({
        date,
        value: order_by_date[date].reduce((acc, order) => {
          const applied = brand_commission.partner_hub[site.partner_hub_tier]
          const { online_order, offline_order } = applied
          return acc + (['local', 'he'].includes(order.source) ? offline_order.packaging : online_order.packaging)
        }, 0),
      }))
      break

    case 'sale_commission':
      result.data = Object.keys(order_by_date).map((date) => {
        const total_offline_sales = order_by_date[date].reduce((acc, order) => acc + order.data_mapping.total, 0)
        const total_online_sales = order_by_date[date].reduce((acc, order) => acc + order.data_mapping.total, 0)
        const applied = brand_commission.partner_hub[site.partner_hub_tier]
        const { online_order, offline_order } = applied

        const growth_commission = (total_offline_sales * offline_order.commission) / 100 + (total_online_sales * online_order.commission) / 100
        return {
          date,
          value: Math.floor(growth_commission),
        }
      })
      break
  }

  res.json({
    success: true,
    data: result,
  })
}

module.exports = {
  get_partner_hub_summary,
  get_chart,
}
