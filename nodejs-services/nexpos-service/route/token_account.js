const { TokenAccount, Site } = require('../../.shared/database');
const { refresh_new_token } = require('../../.shared/token_account');

exports.get_token_list = async (req, res) => {
    let filter = {
        source: { $ne: 'gojek' }
    }

    if (req.query.name) {
        filter.$or = [
            { username: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
            { site_name: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
            { token_code: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
            { description: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
            { access_token: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
            { site_id: { $regex: new RegExp(`.*${req.query.name}.*`, 'i') } },
        ];
    }

    const token_paginate = await TokenAccount.paginate(filter, {
        useEstimatedCount: false,
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 sites
        sort: { working: false, token_code: 1 },
        customLabels: { docs: 'data' },
        lean: true,
    });

    res.json({
        success: true,
        ...token_paginate,
    });
}


exports.create_token = async (req, res) => {
    try {
        const result = await TokenAccount.create(req.body);
        res.json({
            success: true,
            data: result,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.update_token = async (req, res) => {
    try {
        const result = await TokenAccount.findByIdAndUpdate(
            req.params.token_id,
            req.body,
            { new: true }
        );

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_token = async (req, res) => {
    try {
        const result = await TokenAccount.findById(req.params.token_id).lean();
        const sites = await Site.find({ 'tokens.token_code': result.token_code }, { name: 1, code: 1, tokens: 1 }).lean()
        result.sites = sites.map(v => ({
            _id: v._id,
            name: v.name,
            code: v.code,
            token: v.tokens.find(t => t.token_code === result.token_code)
        }))
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.refresh_token = async (req, res) => {
    try {
        const token = await TokenAccount.findByIdAndUpdate(
            req.params.token_id,
            req.body,
            { new: true }
        );

        const result = await refresh_new_token(token.token_code);

        res.json(result);
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


exports.delete_token = async (req, res) => {
    try {
        await TokenAccount.findByIdAndDelete(req.params.token_id);
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}