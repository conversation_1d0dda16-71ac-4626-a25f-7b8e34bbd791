const { BrandCommission } = require('../../.shared/database')

exports.get_config = async (req, res) => {
  const brand_id = req.params.brand_id
  const result = await BrandCommission.find({ brand_id }).sort({ created_at: -1 })

  res.json({
    success: true,
    data: result,
  })
}

exports.get_config_by_user = async (req, res) => {
  let filter = {}
  if (req.user && !req.permissions?.includes('system')) {
    filter.brand_id = req.user.brands
  }

  const result = await BrandCommission.paginate(filter, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
  })

  res.json({
    success: true,
    ...result,
  })
}

exports.create_config = async (req, res) => {
  const brand_id = req.params.brand_id

  const { name, status, type, personal, team, new_customer, referral, partner_hub } = req.body

  // check exited by brand_id and type
  const existed = await BrandCommission.findOne({
    brand_id,
    type,
  })
  if (existed) {
    return res.status(400).json({
      success: false,
      error: 'commission_is_existed_for_this_type',
    })
  }

  const data = await BrandCommission.create({
    brand_id,
    name,
    status,
    type,
    personal,
    team,
    new_customer,
    referral,
    partner_hub
  })

  res.json({
    success: true,
    data,
  })
}

exports.update_config = async (req, res) => {
  const brand_id = req.params.brand_id
  const config_id = req.params.id

  const { name, status, type, personal, team, new_customer, referral, partner_hub } = req.body

  const configs = await BrandCommission.find({ brand_id })

  if (!configs.find((c) => String(c._id) === config_id)) {
    return res.json({
      success: false,
      error: `config_not_found`,
    })
  }

  const existed = await configs.find((c) => c.type === type && String(c._id) !== config_id)
  if (existed) {
    return res.status(400).json({
      success: false,
      error: 'commission_is_existed_for_this_type',
    })
  }

  const updated = await BrandCommission.findOneAndUpdate(
    { _id: config_id },
    {
      name,
      status,
      new_customer,
      type,
      personal,
      team,
      referral,
      partner_hub
    },
    { runValidators: true, returnDocument: 'after' }
  )

  res.json({
    success: true,
    data: updated.toObject()
  })
}

exports.delete_config = async (req, res) => {
  const brand_id = req.params.brand_id
  const config_id = req.params.id

  const existed = await BrandCommission.findOne({ _id: config_id, brand_id })
  if (!existed) {
    return res.json({
      success: false,
      error: `config_not_found`,
    })
  }
  await BrandCommission.deleteOne({ _id: config_id, brand_id })

  res.json({
    success: true,
  })
}
