const { default: axios } = require('axios')
const { Site, VendorCallback, CoreProduct, HubStock } = require('../../.shared/database')
const { build_menu, get_stock_for_cp_item } = require('../common/core-product')

const _flatten_categories = (categories) => {
  const flatten = []
  for (const category of categories) {
    flatten.push(category)
    for (const sub_category of category.sub_categories) {
      flatten.push(sub_category)
    }
  }
  return flatten
}

const _build_core_product_grab_menu = async function (site) {
  const site_menu = await build_menu(site._id, { active: true })
  const flatten_cates = _flatten_categories(site_menu.categories).map((category) => {
    category.items = category.items
      .filter((item) => (item.channels || []).find((channel) => channel.channel === 'grab_food' && channel.active))
      .map((item) => {
        const channel_item = item.channels.find((channel) => channel.channel === 'grab_food' && channel.active)
        return {
          ...item,
          ...channel_item,
          ...(channel_item.additional || {}),
          _id: String(item._id),
          code: item.code,
        }
      })
      .filter((v) => v)
    return category
  })

  // get stocks
  const cps = await CoreProduct.find({ brand_id: site.brand_id, status: 'active' }, { code: 1, quantity_unlimited: 1 })
  const hub_stocks = await HubStock.find({ hub_id: site.hub_id })
  const flatten_items = []
  for (const category of flatten_cates) {
    for (const item of category.items) {
      flatten_items.push(item)
    }
  }

  for (const category of site_menu.option_categories) {
    for (const item of category.option_items || []) {
      flatten_items.push(item)
    }
  }

  const selling_time_id = 'sell_time_daily'
  const sellingTimes = {
    id: selling_time_id,
    startTime: '2023-10-01 10:00:00',
    endTime: '2026-01-01 00:00:00',
    name: 'Best deal',
    serviceHours: {
      mon: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.monday?.start || '10:00',
            endTime: site_menu.working_hours?.monday?.end || '22:00',
          },
        ],
      },
      tue: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.tuesday?.start || '10:00',
            endTime: site_menu.working_hours?.tuesday?.end || '22:00',
          },
        ],
      },
      wed: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.wednesday?.start || '10:00',
            endTime: site_menu.working_hours?.wednesday?.end || '22:00',
          },
        ],
      },
      thu: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.thursday?.start || '10:00',
            endTime: site_menu.working_hours?.thursday?.end || '22:00',
          },
        ],
      },
      fri: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.friday?.start || '10:00',
            endTime: site_menu.working_hours?.friday?.end || '22:00',
          },
        ],
      },
      sat: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.saturday?.start || '10:00',
            endTime: site_menu.working_hours?.saturday?.end || '22:00',
          },
        ],
      },
      sun: {
        openPeriodType: 'OpenPeriod',
        periods: [
          {
            startTime: site_menu.working_hours?.sunday?.start || '10:00',
            endTime: site_menu.working_hours?.sunday?.end || '22:00',
          },
        ],
      },
    },
  }

  flatten_cates.forEach((category) => {
    category.items?.forEach((element) => {
      get_stock_for_cp_item(element, hub_stocks, cps)
    })
  })

  // grab does not has sub category
  const menu_in_grab_format = {
    categories: flatten_cates
      .filter((v) => v.items?.length)
      .map((category) => {
        return {
          id: category._id,
          name: category.name,
          availableStatus: category.active ? 'AVAILABLE' : 'UNAVAILABLE',
          sellingTimeID: selling_time_id,
          items: category.items.map((v) => {
            const modifierGroups = site_menu.option_categories
              .filter((v2) => v2.item_ids?.includes(String(v._id)))
              .map((v2) => {
                return {
                  id: v2._id,
                  name: v2.name,
                  availableStatus: v2.active ? 'AVAILABLE' : 'UNAVAILABLE',
                  maxStock: v2.quantity_unlimited ? undefined : v2.quantity || 0,
                  selectionRangeMin: v2.rule?.min_quantity || 1,
                  selectionRangeMax: v2.rule?.max_quantity || 1,
                  modifiers: (v2.option_items || []).map((v3) => {
                    const item_option_active = v3.active && (v3.quantity_unlimited || v3.quantity > 0)

                    return {
                      id: v3.code,
                      name: v3.name,
                      availableStatus: item_option_active ? 'AVAILABLE' : 'UNAVAILABLE',
                      price: v3.price,
                      barcode: v3.barcode,
                      advancedPricing: {},
                    }
                  }),
                }
              })

            const is_item_active = v.active && (v.quantity_unlimited || v.quantity > 0)
            return {
              id: v.code,
              name: v.name,
              availableStatus: is_item_active ? 'AVAILABLE' : 'UNAVAILABLE',
              description: v.description,
              price: v.price,
              photos: v.images,
              weight: v.weight,
              maxStock: v.quantity_unlimited ? undefined : v.quantity,
              modifierGroups: modifierGroups,
            }
          }),
        }
      }),
    currency: {
      code: 'VND',
      symbol: '₫',
      exponent: 0,
    },
    sellingTimes: [sellingTimes],
    merchantID: site.tokens.find((v) => v.source === 'grab_food').merchant_id,
  }
  return menu_in_grab_format
}

const ACCESS_TOKEN = `7X9bD-KcJLszYGx2`

const get_access_token = async (req, res) => {
  const { client_id, client_secret, grant_type, scope } = req.body
  // grant_type: client_credentials
  // scope: mart.partner_api
  // TODO: check scope

  await VendorCallback.create({
    vendor: 'grab_food',
    type: 'menu',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    method: req.method,
  })

  res.json({
    access_token: ACCESS_TOKEN,
    token_type: 'Bearer',
    expires_in: 86400,
  })
}

const get_menu = async function (req, res) {
  await VendorCallback.create({
    vendor: 'grab_food',
    type: 'menu',
    headers: req.headers,
    request_data: req.query,
    url: req.url,
    method: req.method,
  })

  const { authorization: Authorization } = req.headers
  const { merchantID, partnerMerchantID } = req.query
  if (!Authorization || !merchantID || !partnerMerchantID) {
    res.status(400).json({
      success: false,
      message: 'missing_headers',
    })
    return
  }

  if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
    res.status(400).json({
      success: false,
      message: 'invalid_token',
    })
    return
  }

  // find site that has merchantID in token
  const site = await Site.findOne({
    'tokens.source': 'grab_food',
    'tokens.merchant_id': merchantID,
  })

  if (!site) {
    res.status(400).json({
      success: false,
      message: 'invalid_partner_merchant_id',
    })
    return
  }

  const grab_menu = await _build_core_product_grab_menu(site)

  return res.json({
    ...grab_menu,
    merchantID,
    partnerMerchantID,
  })
}

const grab_food_menu_webhook = async (req, res) => {
  await VendorCallback.create({
    vendor: 'grab_food',
    type: 'menu',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    success: true,
    method: req.method,
  })
  res.json({ success: true })
}

// #area: nexpos call grab partner api
const env =
  process.env.NODE_ENV === 'prod'
    ? {
        baseUrl: 'https://partner-api.grab.com/grabfood',
      }
    : {
        baseUrl: 'https://partner-api.grab.com/grabfood-sandbox',
      }

const get_grab_access_token = async (site) => {
  const token = site.tokens.find((v) => v.source === 'grab_food')

  if (!token) {
    throw new Error('grab_access_token_not_found')
  }
  const { client_id, client_secret, access_token, expired_at } = token
  // still valid token, return
  if (access_token && expired_at > new Date()) {
    return access_token
  }

  const url = `https://api.grab.com/grabid/v1/oauth2/token`
  const headers = {
    'Content-Type': 'application/json',
  }

  const body = {
    client_id: client_id,
    client_secret: client_secret,
    grant_type: 'client_credentials',
    scope: 'food.partner_api',
  }

  const { data } = await axios.post(url, body, { headers })

  token.access_token = data.access_token
  token.expired_at = new Date(Date.now() + (data.expires_in - 30) * 1000)

  await token.save()
  return token.access_token
}

const invalidate_menu = async (site) => {
  const url = `${env.baseUrl}/partner/v1/merchant/menu/notification`

  const access_token = await get_grab_access_token(site)
  const headers = {
    Authorization: `Bearer ${access_token}`,
    'Content-Type': 'application/json',
  }

  const body = {
    merchantID: site.tokens.find((v) => v.source === 'grab_food').merchant_id,
  }

  await axios.post(url, body, { headers })
}

// const batch_update_menu_item = async (site, items) => {
//   const url = `${env.baseUrl}/partner/v1/batch/menu`

//   const access_token = await get_grab_access_token(site)
//   const headers = {
//     Authorization: `Bearer ${access_token}`,
//     'Content-Type': 'application/json',
//   }

//   const body = {
//     merchantID: site.tokens.find((v) => v.type === 'grab_food')['merchantID'],
//     field: 'ITEM',
//     menuEntities: items.map((item) => {
//       return {
//         id: item.code,
//         price: item.price,
//         availableStatus: item.active ? 'AVAILABLE' : 'UNAVAILABLE',
//         maxStock: 1000,
//         advancedPricings: [],
//         purchasabilities: [],
//       }
//     }),
//   }
//   await axios.put(url, body, { headers })
// }

// const update_1_menu_item = async () => {
//   const url = `${env.baseUrl}/partner/v1/merchant/menu`
//   const site = await Site.findOne({ _id: '66cdb9883f25835d083c8923' })
//   const item = {
//     _id: '66fd413a54cebe99b25c66d3',
//     site_id: '66cdb9883f25835d083c8923',
//     name: 'ko có stock in grab format',
//     code: 'kocostock',
//     category_id: '66fd401254cebe99b25c52a5',
//     order: 1727873338510,
//     unit: 'Con',
//     sources: [],
//     description: 'kocostock',
//     images: [],
//     price: 100000,
//     active: true,
//     quantity_unlimited: false,
//     channels: [
//       {
//         channel: 'grab_food',
//         active: true,
//         price: 100000,
//         name: 'ko có stock in grab format',
//         categories: [],
//         additional: {},
//       },
//     ],

//     __v: 1,
//     channel: 'grab_food',
//     categories: [],
//     additional: { description: 'kocostock', images: [] },
//     quantity: 0,
//   }
//   const access_token = await get_grab_access_token(site)
//   const headers = {
//     Authorization: `Bearer ${access_token}`,
//     'Content-Type': 'application/json',
//   }

//   const body = {
//     merchantID: site.tokens.find((v) => v.source === 'grab_food').merchant_id,
//     field: 'ITEM',
//     id: item.code,
//     price: item.price,
//     maxStock: 1000,
//   }

//   await axios.put(url, body, { headers })
// }

// update_1_menu_item().then((v) => console.log('v', v)).catch((e) => console.log('e', e))

module.exports = {
  get_menu,
  grab_food_menu_webhook,
  get_access_token,
  invalidate_menu,
  // batch_update_menu_item,
}
