const { Site, SiteMenuGroup, Order, SiteOrderIndex, Hub, Brand, VendorCallback, HubStock, RetailerSaleConfig, PartnerAPIKey, BrandMenu } = require('../../.shared/database')
const moment = require('moment')
const axios = require('axios')
const { get_location } = require('../../.shared/delivery/gmap')
const { get_hubs_has_stocks, get_item_stock } = require('../common/order')
const ahamove = require('../../.shared/delivery/ahamove')
const grab_express = require('../../.shared/delivery/grab_express')
const viettel_post = require('../../.shared/delivery/viettel_post')
const token_account = require('../../.shared/token_account')
const { map_order } = require('../../.shared/merchant/mapping')
const redis = require('../../.shared/redis')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const { gen_external_id } = require('../../.shared/helper')

const customer_support_create_order = async (req, res) => {
  try {
    const {
      items,
      name,
      phone,
      address,
      shippingFee,
      grandTotal,
      messages
    } = req.body;

    const site = await Site.findOne({ code: 'NEXDOR.TEST' });
    if (!site) {
      return res.status(404).json({ code: 1, message: 'Site not found' });
    }

    const hub = await Hub.findById(site.hub_id);
    if (!hub) {
      return res.status(404).json({ code: 1, message: 'Hub not found' });
    }

    const now = moment();
    const order_group = `${site.code?.toUpperCase() || 'NEXDOR'}-${now.format('YYMMDD')}`;
    const site_next_index = await SiteOrderIndex.findOneAndUpdate(
      { site_id: site._id, group: order_group },
      { $inc: { current_index: 1 } },
      { new: true, upsert: true }
    );
    const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`;

    const shipment = {
      service: {},
      cod: grandTotal,
      payment_method: 'COD',
      status: 'ORDER_CREATING',
      from: {
        address: hub.address,
        phone: hub.phone,
        name: hub.name,
      },
      to: { address: address, phone: phone, name: name },
      price: shippingFee,
      note: '',
    };

    const order = {
      source: 'he',
      site_id: site._id,
      status: 'PENDING',
      order_id,
      hub_id: hub._id,
      shipment,
      data: {
        id: order_id,
        order_id: order_id,
        source: 'he',
        order_time: now.toISOString(),
        pick_time: null,
        delivery_time: null,
        order_time_sort: now.unix(),
        driver_name: '',
        driver_phone: '',
        customer_phone: phone,
        customer_name: name,
        customer_address: address,
        dishes: items.map((item) => ({
          name: item.name,
          price: item.price * item.quantity,
          quantity: item.quantity,
          code: '',
          options: [],
          discount: 0,
          discount_price: item.price * item.quantity,
          quantity_unlimited: true,
          combo: [],
        })),
        commission: 0,
        total: grandTotal,
        total_for_biz: grandTotal,
        total_shipment: shippingFee,
        total_display: grandTotal.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
        note: messages.map(v => v.role + ': ' + v.message).join('\n'),
        payments: [{ method: 'COD', total: grandTotal, status: 'PENDING' }],
        raw: req.body,
        shipment_fee: shippingFee,
        coupons: [],
        notes: messages.map(v => v.role + ': ' + v.message).join('\n'),
      },
    };

    order.data_mapping = map_order('he', order.data);
    order.external_id = gen_external_id();
    const new_order = await Order.create(order);

    res.status(200).json({ code: 0, message: 'Success', data: { orderNumber: new_order.order_id } });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ code: 1, message: 'Error creating order', error: error.message });
  }
};

module.exports = {
  customer_support_create_order,
}
