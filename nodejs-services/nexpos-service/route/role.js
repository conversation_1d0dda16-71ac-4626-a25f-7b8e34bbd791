const { Role } = require('../../.shared/database')
const { toNumber } = require('lodash')

const get_role_list = async (req, res) => {
    const data = await Role.find({})

    res.json({
        success: true,
        data,
        totalPages: 1,
    })
}

// Create a new role
const create_role = async (req, res) => {
    const { name, permissions, selectors } = req.body;
    try {
        const result = await Role.create({ name, permissions, selectors: selectors ?? [] });
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Update an existing role
const update_role = async (req, res) => {
    const { name, permissions, selectors } = req.body;

    try {
        const result = await Role.findByIdAndUpdate(
            req.params.role_id,
            { name, permissions, selectors: selectors ?? [] },
            { new: true } // Return the updated document
        );
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Delete a role
const delete_role = async (req, res) => {
    try {
        await Role.findByIdAndDelete(req.params.role_id);
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

module.exports = { get_role_list, create_role, update_role, delete_role }