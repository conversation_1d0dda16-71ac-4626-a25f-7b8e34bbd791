const { Order, UserCart, User, Site, SiteMenuGroup, SiteOrderIndex, Hub, Brand } = require('../../.shared/database')
const _ = require('lodash')
const ahamove = require('../../.shared/delivery/ahamove')
const ghn = require('../../.shared/delivery/ghn')
const grab_express = require('../../.shared/delivery/grab_express')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const token_account = require('../../.shared/token_account');
const moment = require('moment-timezone')
const momo_mini = require('./momo_mini')
moment.tz.setDefault('Asia/Bangkok')

const partner_get_user_orders = async (req, res) => {
    const { site_id } = req.params
    const { status } = req.query

    let filter = {
        site_id: site_id,
    }
    if (['PENDING'].includes(status)) {
        filter.$or = [{
            status: 'WAITING_PAYMENT'
        }, {
            status: ['PENDING'],
            'data_mapping.order_time_sort': {
                $gte: moment.tz('Asia/Jakarta').startOf('day').unix(),
                $lte: moment.tz('Asia/Jakarta').unix(),
            }
        }]
    } else {
        filter.status = status
    }

    const orders = await Order.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 200), // Max 100 sites
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
        projection: {
            'data_mapping.raw': 0,
        },
    })

    res.json({
        success: true,
        ...orders,
        data: orders.data.map((v) => ({
            ...v.data_mapping,
            status: v.status,
            total_shipping: v?.data?.shipment_fee || 0,
            shipment: v.shipment,
            site_id: v.site_id,
        })),
    })
}

const he_get_user_orders = async (req, res, next) => {
    const { site_id } = req.params
    const { status } = req.query

    let filter = {
        site_id: site_id,
        user_id: req.user._id,
    }
    if (['PENDING'].includes(status)) {
        filter.$or = [{
            status: 'WAITING_PAYMENT'
        }, {
            status: ['PENDING'],
            'data_mapping.order_time_sort': {
                $gte: moment.tz('Asia/Jakarta').startOf('day').unix(),
                $lte: moment.tz('Asia/Jakarta').unix(),
            }
        }]
    } else {
        filter.status = status
    }

    const orders = await Order.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 200), // Max 100 sites
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
        projection: {
            'data_mapping.raw': 0,
        },
    })

    res.json({
        success: true,
        ...orders,
        data: orders.data.map((v) => ({
            ...v.data_mapping,
            status: v.status,
            total_shipping: v?.data?.shipment_fee || 0,
            shipment: v.shipment,
            site_id: v.site_id,
            order_status: v.status,
        })),
    })
}

const he_get_user_order_details = async (req, res) => {
    const { site_id, order_id } = req.params

    const data = await Order.findOne({ site_id, user_id: req.user._id, order_id });
    if (!data) {
        return res.status(400).json({
            success: false,
            data: {}
        })
    }
    return res.json({
        success: true,
        data: {
            ...data.data_mapping,
            status: data.status,
            total_shipping: data?.data?.shipment_fee || 0,
            shipment: data.shipment,
            site_id: data.site_id,
        }
    })
}



module.exports = { he_get_user_order_details, partner_get_user_orders, he_get_user_orders }
