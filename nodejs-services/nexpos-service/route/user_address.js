const { User, UserAddress, Address } = require('../../.shared/database')
const { get_suggestion_addresses } = require('../../.shared/delivery/gmap')
// const { get_suggestion_addresses: get_suggestion_addressesv2 } = require('../../.shared/delivery/ghn')
const { toNumber } = require('lodash')

const get_address_list = async (req, res) => {
    const { level, slug } = req.query
    if (slug) {
        const address = await Address.findOne({ slug }).sort({ name: 1 })

        res.json({
            success: true,
            data: address.children
        });
        return
    }
    const addresses = await Address.find({}, { children: 0 }).sort({ name: 1 })

    res.json({
        success: true,
        data: addresses
    });

}

const get_map_suggest_address_list = async (req, res) => {
    const { address } = req.query
    const addresses = await get_suggestion_addresses(address)

    res.json({
        success: true,
        data: addresses.map(v => v.formatted_address)
    });

}


const get_map_suggest_address_list_v2 = async (req, res) => {
    const { address } = req.query
    const addresses = await get_suggestion_addresses(address)

    res.json({
        success: true,
        data: addresses
    });
}



const get_user_address_list = async (req, res) => {
    const user_user_addresses = await UserAddress.find({ user_id: req.user._id })

    res.json({
        success: true,
        data: user_user_addresses
    });
}

const create_user_address = async (req, res) => {
    const { address, phone, name, is_default, note, user_id } = req.body;
    const actual_user_id = user_id || req.user._id
    const user_user_address = new UserAddress({ user_id: actual_user_id, address, phone, name, is_default, note });
    await user_user_address.validate();

    await user_user_address.save();

    // set other address is_default = false
    if (is_default) {
        await UserAddress.updateMany({ user_id: req.user._id, _id: { $ne: user_user_address._id } }, { is_default: false });
    }

    const current_user = await User.findById(actual_user_id)
    if (!current_user?.name) {
        await User.updateOne({ _id: String(actual_user_id) }, { name });
    }

    res.json({
        success: true,
        data: user_user_address
    });
}

// Update an existing user
const update_user_address = async (req, res) => {
    const { address, phone, name, note, is_default } = req.body;

    const result = await UserAddress.findByIdAndUpdate(
        req.params.address_id,
        { address, phone, name, note, is_default },
        { new: true, runValidators: true }
    );

    // set other address is_default = false
    if (is_default) {
        await UserAddress.updateMany({ user_id: req.user._id, _id: { $ne: result._id } }, { is_default: false });
    }

    res.json({
        success: true,
    });
}

const delete_user_address = async (req, res) => {
    await UserAddress.findByIdAndDelete(req.params.address_id);
    res.json({
        success: true,
    });
}

module.exports = { get_address_list, get_map_suggest_address_list, get_map_suggest_address_list_v2, get_user_address_list, create_user_address, update_user_address, delete_user_address }
