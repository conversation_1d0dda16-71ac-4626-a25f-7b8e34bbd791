const { customAlphabet } = require('nanoid')
const { Material, Brand, CoreProductCategory, CoreProductSource, CoreProductUnit } = require('../../.shared/database')
const { exist_file } = require('../../.shared/storage')
const { upload_file } = require('../../.shared/storage')
const xlsx = require('xlsx')
const { upload_single } = require('../middlewares/upload_file')

exports.get_materials = async (req, res) => {
  const brand_id = req.params.brand_id
  const query = { brand_id }
  if (req.query.search) {
    query.$or = [{ name: { $regex: req.query.search, $options: 'i' } }, { code: { $regex: req.query.search, $options: 'i' } }]
  }

  const result = await Material.paginate(query, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
  })

  res.json({
    success: true,
    ...result,
  })
}

exports.create_material = async (req, res) => {
  const { name, category, unit, price, sale_price, code, images, description, weight, source } = req.body
  const brand_id = req.params.brand_id
  const brand = await Brand.findById(brand_id)
  if (!brand) {
    return res.status(404).json({
      success: false,
      message: 'brand_not_found',
    })
  }

  const result = await Material.create({
    name,
    brand_id,
    category,
    weight,
    description,
    sale_price,
    unit,
    price,
    images,
    source: source || 'Other',
    code: code || customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')(6),
  })

  res.json({
    success: true,
    data: result,
  })
}

exports.update_material = async (req, res) => {
  const { name, category, unit, price, images, sale_price, weight, description, source } = req.body
  const code = req.params.code
  const brand_id = req.params.brand_id

  const result = await Material.findOneAndUpdate(
    {
      code,
      brand_id,
    },
    {
      name,
      category,
      unit,
      price,
      images,
      sale_price,
      weight,
      description,
      source,
    },
    {
      new: true,
    }
  )

  res.json({
    success: true,
    data: result,
  })
}

exports.import_materials = [
  upload_single,
  async (req, res) => {
    const brand_id = req.params.brand_id
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' })
    const sheet = workbook.Sheets[workbook.SheetNames[0]]
    const rows = xlsx.utils.sheet_to_json(sheet)

    const materials = rows.map((row) => ({
      brand_id,
      name: row['Tên'],
      category: row['Loại'],
      unit: row['Đơn vị'],
      price: row['Giá'],
      code: row['Mã'] || customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')(6),
      images: row['Hình ảnh'] ? row['Hình ảnh'].split(',') : [],
      sale_price: row['Giá bán'],
      description: row['Mô tả'],
      weight: row['Trọng lượng'],
      source: row['Nguồn'] || 'Other',
    }))

    const sources = await CoreProductSource.find({ brand_id })
      .lean()
      .then((res) =>
        res.reduce((acc, s) => {
          acc[s.name] = s.name
          return acc
        }, {})
      )
    const categories = await CoreProductCategory.find({ brand_id })
      .lean()
      .then((res) =>
        res.reduce((acc, c) => {
          acc[c.name] = c.name
          return acc
        }, {})
      )
    const units = await CoreProductUnit.find({ brand_id })
      .lean()
      .then((res) =>
        res.reduce((acc, u) => {
          acc[u.name] = u.name
          return acc
        }, {})
      )

    for (let material of materials) {
      await Material.updateOne(
        {
          code: material.code,
          brand_id: material.brand_id,
        },
        material,
        {
          upsert: true,
        }
      )

      if (material.source && !sources[material.source]) {
        await CoreProductSource.create({
          brand_id,
          name: material.source,
        })

        sources[material.source] = material.source
      }

      if (material.category && !categories[material.category]) {
        await CoreProductCategory.create({
          brand_id,
          name: material.category,
        })
        categories[material.category] = material.category
      }

      if (material.unit && !units[material.unit]) {
        await CoreProductUnit.create({
          brand_id,
          name: material.unit,
        })
        units[material.unit] = material.unit
      }
    }

    res.json({
      success: true,
      data: { total: materials.length },
    })
  },
]

exports.get_import_template = async (req, res) => {
  const key = 'import_material_template_v2.xlsx'
  const existed_file = await exist_file({ bucket: 'nexpos-files', key })

  if (existed_file) {
    return res.json({
      success: true,
      data: existed_file,
    })
  }

  const template = {
    Tên: '',
    Loại: '',
    'Đơn vị': '',
    Giá: '',
    Mã: '',
    'Hình ảnh': '',
    'Giá bán': '',
    'Mô tả': '',
    'Trọng lượng': '',
    Nguồn: '',
  }

  const workbook = xlsx.utils.book_new()
  const config_worksheet = xlsx.utils.json_to_sheet([template])
  xlsx.utils.book_append_sheet(workbook, config_worksheet, 'Config')
  const buff = xlsx.write(workbook, { type: 'buffer' })

  const file = await upload_file({ bucket: 'nexpos-files', key, buff })

  res.json({
    success: true,
    data: file,
  })
}

exports.export_materials = async (req, res) => {
  const brand_id = req.params.brand_id
  const materials = await Material.find({ brand_id }, { _id: 0, _v: 0, brand_id: 0 }).lean()
  const workbook = xlsx.utils.book_new()
  const sheet = xlsx.utils.json_to_sheet(
    materials.map((m) => ({
      Tên: m.name,
      Loại: m.category,
      'Đơn vị': m.unit,
      Giá: m.price,
      Mã: m.code,
      'Hình ảnh': m.images.join(','),
      'Giá bán': m.sale_price,
      'Mô tả': m.description,
      'Trọng lượng': m.weight,
      Nguồn: m.source,
    }))
  )
  xlsx.utils.book_append_sheet(workbook, sheet, 'Materials')
  const buff = xlsx.write(workbook, { type: 'buffer' })

  const key = `export_materials_${Date.now()}.xlsx`
  const file = await upload_file({ bucket: 'nexpos-files', key, buff })

  res.json({
    success: true,
    data: file,
  })
}

exports.delete_material = async (req, res) => {
  const code = req.params.code
  const brand_id = req.params.brand_id

  await Material.findOneAndDelete({
    code,
    brand_id,
  })

  res.json({
    success: true,
  })
}
