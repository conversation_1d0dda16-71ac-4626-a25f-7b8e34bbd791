const { BrandRequest, User, Brand, Site } = require('../../.shared/database')
const { publisher } = require('../../.shared/pubsub')

const get_brand_request_list = async (req, res) => {
    let filter = {}
    if (req.user && !req.permissions?.includes('system')) {
        filter.brand_id = { $in: req.user.brands }
    }

    if (req.query.type) {
        filter.type = req.query.type
    }

    const brand_request_paginate = await BrandRequest.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 docs
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
        lean: true
    });

    const users = await User.find({ _id: { $in: brand_request_paginate.data.map(item => item.created_by) } }, { email: 1, name: 1 }).lean();
    const brands = await Brand.find({ _id: { $in: brand_request_paginate.data.map(item => item.brand_id) } }, { name: 1 }).lean();
    const sites = await Site.find({ _id: { $in: brand_request_paginate.data.map(item => item.site_ids).flat() } }, { name: 1 }).lean();
    for (const brand_request of brand_request_paginate.data) {
        let user = users.find(user => user._id.equals(brand_request.created_by));
        let brand = brands.find(brand => brand._id.equals(brand_request.brand_id));
        let request_sites = sites.filter(site => brand_request.site_ids.includes(String(site._id)));
        if (user) {
            brand_request.creator = user;
        }
        if (brand) {
            brand_request.brand = brand;
        }
        if (request_sites) {
            brand_request.sites = request_sites;
        }
    }
    res.json({
        success: true,
        ...brand_request_paginate
    });
}

const create_brand_request = async (req, res) => {
    const { request_types, brand_id, site_ids, description, promise_completed_at, source } = req.body;

    try {
        const result = await BrandRequest.create({
            request_types,
            brand_id,
            site_ids,
            description,
            source,
            file: '',
            promise_completed_at: promise_completed_at || 0,
            created_by: req.user._id
        });

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}


const clone_brand_request = async (req, res) => {
    try {
        const brand_request = await BrandRequest.findById(req.params.request_id);
        if (!brand_request) {
            return res.status(404).json({ success: false, message: 'Brand request not found' });
        }

        const clone_request = new BrandRequest({
            brand_id: brand_request.brand_id,
            site_ids: brand_request.site_ids,
            request_types: brand_request.request_types,
            description: brand_request.description,
            source: brand_request.source,
            promise_completed_at: 0,
            created_by: req.user._id,
            status: 'PENDING'
        });

        await clone_request.save();

        res.json({
            success: true,
            message: 'Brand request cloned successfully',
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

const approve_brand_request = async (req, res) => {
    try {
        const updated = await BrandRequest.findByIdAndUpdate(
            req.params.request_id,
            { status: 'APPROVED' },
            { new: true }
        );

        if (!updated) {
            return res.status(404).json({ success: false, message: 'Brand request not found' });
        }

        // await publisher("BRAND_REQUEST", { _id: updated._id })

        res.json({
            success: true,
            message: 'Brand request approved and set to PROCESSING',
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}


const delete_brand_request = async (req, res) => {
    try {
        const deletedBrandRequest = await BrandRequest.findByIdAndDelete(req.params.request_id);
        if (!deletedBrandRequest) {
            return res.status(404).json({ success: false, message: 'Brand request not found' });
        }
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            error: err.message,
        });
    }
}

module.exports = { get_brand_request_list, create_brand_request, clone_brand_request, approve_brand_request, delete_brand_request }