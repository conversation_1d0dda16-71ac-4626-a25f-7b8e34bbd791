const { AppVersion } = require('../../.shared/database')

exports.get_app_version_list = async (req, res) => {
  const result = await AppVersion.find({}).sort({ created_at: -1 })

  res.json({
    success: true,
    data: result,
  })
}

exports.get_app_version_details = async (req, res) => {
    const { bundle_identifier } = req.params
  
    const app_version = await AppVersion.findOne({ bundle_identifier })
  
    if (!app_version) {
      return res.status(404).json({
        success: false,
        error: 'app_version_not_found',
      })
    }

    res.json({
        success: true,
        data: app_version,
    })
}

exports.create_app_version = async (req, res) => {
    const { bundle_identifier, version_name, version_code, android_version_code, environment, name } = req.body
    
    const data = await AppVersion.create({ bundle_identifier, version_name, version_code, android_version_code, environment, name });
    return res.status(200).json({ success: true, data })
}

exports.update_app_version = async (req, res) => {
    if(!req.params.bundle_identifier) {
        return res.status(400).json({ success: false, error: 'app_bundle_identifier_not_found' })
    }
    const { version_name, version_code, android_version_code, environment, name } = req.body
    
    const updated = await AppVersion.findOneAndUpdate(
        { bundle_identifier: req.params.bundle_identifier },
        { version_name, version_code, android_version_code, environment, name },
        { runValidators: true, returnDocument: 'after' }
    )
    return res.status(200).json({ success: true, data: { ...updated.toObject() } })
}

exports.delete_app_version = async (req, res) => {
    const deleted_app_version = await AppVersion.findByIdAndDelete(req.params.bundle_identifier);
    if (!deleted_app_version) {
        return res.status(404).json({ success: false, error: 'app_bundle_identifier_not_found' });
    }
    res.json({
        success: true,
    });
}