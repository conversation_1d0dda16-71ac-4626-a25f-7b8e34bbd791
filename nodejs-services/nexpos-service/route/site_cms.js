const { SiteCMS } = require("../../.shared/database");

exports.get_site_cms = async (req, res) => {
    const { site_id } = req.params;

    const siteCMS = await SiteCMS.findOne({ _id: site_id });

    res.status(200).json({ 
        success: true,
        data: siteCMS ?? {},
    });
}

exports.update_site_cms = async (req, res) => {
    const { site_id } = req.params;

    const data = await SiteCMS.findOneAndUpdate({ _id: site_id }, { pages: req.body }, { upsert: true, new: true });

    res.status(200).json({ 
        success: true,
        data,
    });
}