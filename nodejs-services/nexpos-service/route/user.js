const bcrypt = require('bcrypt')
const { User, Role, Partner, Hub, Brand, Site, UserNotification } = require('../../.shared/database')
const { isEmpty, isNil } = require('lodash')
const { _user_auth } = require('./login')
const { send_email } = require('../../.shared/email')

exports.get_user_list = async (req, res) => {
  let filter = {
    // filter out pending partner
    approval_status: { $ne: 'pending' },
  }

  if (req.query.role_ids) {
    filter.role_id = { $in: req.query.role_ids }
  }

  if (req.query.search) {
    filter = {
      $or: [
        {
          ...filter,
          name: { $regex: new RegExp(`.*${req.query.search}.*`, 'i') },
        },
        {
          ...filter,
          email: { $regex: new RegExp(`.*${req.query.search}.*`, 'i') },
        },
      ],
    }
  }

  const user_paginate = await User.paginate(filter, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    projection: { last_login_device: 0, last_login_devices: 0, password: 0 },
  })

  res.json({
    success: true,
    ...user_paginate,
  })
}

exports.get_user_by_id = async (req, res) => {
  const { user_id } = req.params

  const user = await User.findById(user_id, {
    password: 0,
    last_login_device: 0,
    last_login_devices: 0,
  })

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found',
    })
  }

  // populate brands, hubs, sites
  const hub_list = await Hub.find({ _id: { $in: user.hubs } }, { name: 1 })
  const brand_list = await Brand.find({ _id: { $in: user.brands } }, { name: 1 })
  const site_list = await Site.find({ _id: { $in: user.sites } }, { name: 1 })

  res.json({
    success: true,
    data: {
      ...user.toObject(),
      hub_list,
      brand_list,
      site_list,
    },
  })
}

const SELECT_ALL = '*' // special value to select all
const _get_brand_hub_site_list = async (req) => {
  const { hubs: hub_ids, brands: brand_ids, sites: site_ids, query = {} } = req.body

  let hubs = hub_ids
  if (query.hubs === SELECT_ALL) {
    const filter = query.hub_search ? { name: { $regex: new RegExp(`.*${query.hub_search}.*`, 'i') } } : {}
    hubs = await Hub.find(filter, { _id: 1 }).then((res) => res.map((h) => h._id))
  }

  let sites = site_ids
  if (query.sites === SELECT_ALL) {
    const filter = query.site_search ? { name: { $regex: new RegExp(`.*${query.site_search}.*`, 'i') } } : {}
    sites = await Site.find(filter, { _id: 1 }).then((res) => res.map((s) => s._id))
  }

  let brands = brand_ids
  if (query.brands === SELECT_ALL) {
    const filter = query.brand_search ? { name: { $regex: new RegExp(`.*${query.brand_search}.*`, 'i') } } : {}
    brands = await Brand.find(filter, { _id: 1 }).then((res) => res.map((b) => b._id))
  }

  return { hubs, brands, sites }
}

// Create a new user
exports.create_user = async (req, res) => {
  try {
    const { email, password, name, role_id, is_active, expired_at } = req.body

    const existed = await User.findOne({ email })
    if (existed) {
      return res.status(400).json({
        success: false,
        error: 'email_already_existed',
      })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const role = await Role.findById(role_id)
    const is_partner = role.permissions.find((p) => p === 'partner')

    const { hubs, brands, sites } = await _get_brand_hub_site_list(req)
    const result = await User.create({
      username: email,
      email,
      password: hashedPassword,
      name,
      role_id,
      is_active,
      expired_at,
      hubs,
      brands,
      sites,
      created_by: req.user._id,
      ...(is_partner
        ? {
          approval_status: 'approved',
          he_info: { approve_at: new Date() },
        }
        : {}),
    })

    // if (is_partner) {
    //   await Partner.create({
    //     user_id: String(result._id),
    //     member_of: null,
    //     type: 'member',
    //     status: 'active',
    //     // level: 1,
    //     email,
    //   })
    // }

    res.json({
      success: true,
      data: result,
    })
  } catch (err) {
    let error = err.message
    const patternKeys = Object.keys(err.keyPattern)
    if (err.code === 11000 && patternKeys?.length > 0) {
      error = `${patternKeys[0]}_is_existed`
    }
    return res.status(400).json({
      success: false,
      error,
    })
  }
}

// Get the existing user
exports.get_current_user = async (req, res) => {
  const user = await User.findById(req.user._id)
  const user_auth = await _user_auth(user)
  res.json({
    success: true,
    data: user_auth,
  })
}

// Update an existing user
exports.update_user = async (req, res) => {
  const { email, name, password, is_active, expired_at, role_id } = req.body

  const { hubs, brands, sites } = await _get_brand_hub_site_list(req)

  const user = await User.findByIdAndUpdate(
    req.params.user_id,
    { username: email, email, name, role_id, is_active, expired_at, hubs, brands, sites },
    { new: true } // Return the updated document
  )

  if (!isNil(password) && !isEmpty(password)) {
    const hashedPassword = await bcrypt.hash(password, 10)
    await User.findByIdAndUpdate(req.params.user_id, { password: hashedPassword })
  }
  const user_auth = await _user_auth(user)
  res.json({
    success: true,
    data: user_auth,
  })
}

// Update an existing user
exports.update_user_basic_infos = async (req, res) => {
  const { email, phone, name, address, password } = req.body
  const update = {}
  // if (email) { update[email] = email }
  if (phone) {
    update.phone = phone
  }
  if (name) {
    update.name = name
  }
  if (address) {
    update.address = address
  }
  if (password) {
    update.password = await bcrypt.hash(password, 10)
  }

  const user = await User.findByIdAndUpdate(req.user._id, update, { new: true })
  const user_auth = await _user_auth(user)
  res.json({
    success: true,
    data: user_auth,
  })
}

// Delete a user
exports.delete_user = async (req, res) => {
  await User.findByIdAndDelete(req.params.user_id)
  res.json({
    success: true,
  })
}

exports.contact_us = async (req, res) => {
  const { email, phone, name, message } = req.body
  await send_email({}, { email, name }, 'contact_us', { email, phone, name, message })

  res.json({
    success: true,
  })
}

exports.get_user_by_phone = async (req, res) => {
  const { phone } = req.params
  const user = await User.findOne(
    { phone },
    {
      _id: 1,
      name: 1,
      email: 1,
      username: 1,
      phone: 1,
      is_active: 1,
      password: 1,
      role_id: 1,
    }
  ).lean()

  if (!user) {
    return res.json({
      success: false,
      error: 'user_not_found',
    })
  }

  if (!user.is_active) {
    return res.json({
      success: false,
      error: 'user_is_inactive',
    })
  }

  const is_pwd_set = !!user.password

  const role = await Role.findOne({ _id: user.role_id })
  const is_partner = role?.permissions.find((p) => p === 'partner')

  delete user.password

  return res.json({
    success: true,
    data: { ...user, is_pwd_set, is_partner: !!is_partner },
  })
}

exports.get_user_notifications = async (req, res) => {
  const unread_count = await UserNotification.find({ user_id: req.user._id, status: 'unread' })

  const user_paginate = await UserNotification.paginate(
    { user_id: req.user._id },
    {
      page: Number(req.query.page || 1),
      limit: Number(req.query.limit || 100), // Max 100 docs
      sort: { created_at: -1 },
      customLabels: { docs: 'data' },
    }
  )

  res.json({
    success: true,
    ...user_paginate,
    unread_count: unread_count.length,
  })
}

exports.user_read_notifications = async (req, res) => {
  const { notification_ids } = req.body
  await UserNotification.updateMany({ _id: { $in: notification_ids } }, { $set: { status: 'read' } })
  res.json({
    success: true,
  })
}
