const { HubStock, CoreProduct } = require('../../.shared/database')
const { get_stock_for_cp_item } = require('../common/core-product')
const { build_menu } = require('./core_product_sync_stock')

const _flatten_categories = (categories) => {
  const flatten = []
  for (const category of categories) {
    flatten.push(category)
    for (const sub_category of category.sub_categories) {
      flatten.push(sub_category)
    }
  }
  return flatten
}

const build_shopee_food_menu = async (site) => {
  const menu = await build_menu(site)

  const service_hours = {
    mon: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.monday?.start || '08:30', endTime: site.working_hours?.monday?.end || '20:30' }],
    },
    tue: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.tuesday?.start || '08:30', endTime: site.working_hours?.tuesday?.end || '20:30' }],
    },
    wed: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.wednesday?.start || '08:30', endTime: site.working_hours?.wednesday?.end || '20:30' }],
    },
    thu: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.thursday?.start || '08:30', endTime: site.working_hours?.thursday?.end || '20:30' }],
    },
    fri: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.friday?.start || '08:30', endTime: site.working_hours?.friday?.end || '20:30' }],
    },
    sat: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.saturday?.start || '08:30', endTime: site.working_hours?.saturday?.end || '20:30' }],
    },
    sun: {
      openPeriodType: 'OpenPeriod',
      periods: [{ startTime: site.working_hours?.sunday?.start || '08:30', endTime: site.working_hours?.sunday?.end || '20:30' }],
    },
  }


  const hub_stocks = await HubStock.find({ hub_id: site.hub_id })
  const cps = await CoreProduct.find({ brand_id: site.brand_id, status: 'active' }, { code: 1, quantity_unlimited: 1 })

  const flat_categories = _flatten_categories(menu.categories)
  flat_categories.forEach((category) => {
    category.items?.forEach((element) => {
      get_stock_for_cp_item(element, hub_stocks, cps)
    })
  })

  const menu_in_shopee = flat_categories.map((c) => {
    return {
      id: c.id,
      name: c.name,
      sort_type: c.sort_type,
      sequence: c.sequence,
      availableStatus: 'AVAILABLE',
      items: c.items
        ?.filter((i) => i.channels?.find((c) => c.channel === 'shopee_food' && c.active))
        .map((i) => {
          const modify_groups = menu.option_categories.filter((v2) => v2.item_ids?.includes(String(i._id)))

          const item_available = i.quantity_unlimited || i.quantity > 0
          return {
            id: i.code,
            name: i.name,
            sequence: i.sequence,
            availableStatus: item_available ? 'AVAILABLE' : 'UNAVAILABLE',
            description: i.description,
            price: i.price,
            photos: i.images,
            modifierGroups: modify_groups?.map((oc) => {
              return {
                id: oc._id,
                name: oc.name,
                sequence: oc.sequence,
                availableStatus: 'AVAILABLE',
                selectionRangeMin: oc.rule.min_quantity,
                selectionRangeMax: oc.rule.max_quantity,
                modifiers: oc.option_items?.map((o) => {
                  const item_available = o.quantity_unlimited || o.quantity > 0
                  return {
                    id: o.code,
                    name: o.name,
                    sequence: o.sequence,
                    availableStatus: item_available ? 'AVAILABLE' : 'UNAVAILABLE',
                    price: o.price,
                  }
                }),
              }
            }),
          }
        }),
    }
  })

  return {
    service_hours,
    categories: menu_in_shopee,
  }
}

module.exports = {
  build_shopee_food_menu,
}
