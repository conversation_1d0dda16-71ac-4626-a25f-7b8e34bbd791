const { customAlphabet } = require('nanoid')
const { CoreProduct, Brand, CoreProductCategory, CoreProductSource, CoreProductUnit } = require('../../.shared/database')
const { exist_file } = require('../../.shared/storage')
const { upload_file } = require('../../.shared/storage')
const xlsx = require('xlsx')
const { upload_single } = require('../middlewares/upload_file')

function removeUndefinedProperties(obj) {
  return Object.entries(obj).reduce((a, [k, v]) => (v === undefined ? a : { ...a, [k]: v }), {})
}

async function populate_ingredients(core_products, brand_id) {
  const codes = core_products.map((i) => i.ingredients.map((i) => i.code)).flat()
  const ingredients = await CoreProduct.find(
    { code: { $in: codes }, brand_id },
    {
      name: 1,
      unit: 1,
      code: 1,
      images: 1,
    }
  )

  const updated = core_products.map((i) => {
    i = i.toObject()
    i.ingredients = i.ingredients.map((j) => {
      const ing = ingredients.find((k) => k.code === j.code) || {}
      j.unit = ing.unit
      j.images = ing.images
      j.name = ing.name

      return j
    })

    return i
  })

  return updated
}

exports.get_core_products = async (req, res) => {
  const { source, category, available_for_sale, search, types } = req.query
  const brand_id = req.params.brand_id
  const query = { brand_id, source, category, available_for_sale, ...(types ? { type: { $in: types } } : {}) }
  if (search) {
    query.$or = [{ name: { $regex: search, $options: 'i' } }, { code: { $regex: search, $options: 'i' } }, { bar_code: { $regex: search, $options: 'i' } }]
  }

  const result = await CoreProduct.paginate(removeUndefinedProperties(query), {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
  })

  const populated = await populate_ingredients(result.data, brand_id)

  res.json({
    success: true,
    ...result,
    data: populated,
  })
}

exports.get_core_product_details = async (req, res) => {
  const { brand_id, code } = req.params
  const query = { brand_id, code }

  const data = await CoreProduct.findOne(query)

  if (!data) {
    return res.status(404).json({
      success: false,
      message: 'core_product_not_found',
    })
  }

  const populated = await populate_ingredients([data], brand_id)

  res.json({
    success: true,
    data: populated[0],
  })
}

exports.create_core_product = async (req, res) => {
  const { name, category, unit, price, images, sale_price, weight, description, code, height, available_for_sale, status, bar_code, length, ingredients, source, type, quantity_unlimited } = req.body
  const brand_id = req.params.brand_id

  const brand = await Brand.findById(brand_id)
  if (!brand) {
    return res.status(404).json({
      success: false,
      message: 'brand_not_found',
    })
  }

  const existed_code = await CoreProduct.findOne({ code, brand_id })
  if (existed_code) {
    return res.status(400).json({
      success: false,
      message: 'core_product_code_existed',
    })
  }

  if (ingredients?.length) {
    const existed = await CoreProduct.find({ status: 'active', code: { $in: ingredients.map((i) => i.code) } })
    if (existed.length !== ingredients.length) {
      return res.status(404).json({
        success: false,
        message: 'ingredient_not_found',
      })
    }
  }

  const result = await CoreProduct.create({
    name,
    category,
    unit,
    price,
    images,
    sale_price,
    weight,
    description,
    code: code || customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')(8),
    height,
    available_for_sale,
    type,
    status,
    bar_code,
    length,
    ingredients: ingredients?.map(({ code, amount, display_name, unit }) => ({ code, amount, display_name, unit })),
    brand_id,
    source,
    quantity_unlimited,
  })

  res.json({
    success: true,
    data: result,
  })
}

exports.update_core_product = async (req, res) => {
  const { code, brand_id } = req.params
  const { name, category, unit, price, images, sale_price, weight, description, height, available_for_sale, status, bar_code, length, ingredients, source, type, quantity_unlimited } = req.body

  if (ingredients?.length) {
    const ings = await CoreProduct.find({
      code: { $in: ingredients.map((i) => i.code) },
      status: 'active',
      brand_id,
    })

    if (ingredients.length !== ings.length) {
      return res.status(404).json({
        success: false,
        message: 'ingredient_not_found',
      })
    }
  }

  const result = await CoreProduct.findOneAndUpdate(
    {
      code,
    },
    {
      name,
      category,
      unit,
      price,
      images,
      sale_price,
      weight,
      description,
      height,
      available_for_sale,
      type,
      status,
      bar_code,
      length,
      ingredients: ingredients?.map(({ code, amount, display_name, unit }) => ({ code, amount, display_name, unit })),
      source,
      quantity_unlimited,
    },
    {
      new: true,
    }
  )

  res.json({
    success: true,
    data: result,
  })
}

exports.delete_core_product = async (req, res) => {
  const { code, brand_id } = req.params
  await CoreProduct.findOneAndDelete({ code, brand_id })

  res.json({
    success: true,
  })
}

exports.import_core_products = [
  upload_single,
  async (req, res) => {
    const { brand_id } = req.params

    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' })
    const core_product_sheet = workbook.Sheets['Core Product']
    const ingredient_sheet = workbook.Sheets['Ingredient']
    const core_product_rows = xlsx.utils.sheet_to_json(core_product_sheet)
    const ingredient_rows = xlsx.utils.sheet_to_json(ingredient_sheet)

    // insert core product
    for (let row of core_product_rows) {
      const core_product = {
        brand_id,
        name: row['Tên sản phẩm (Bắt buộc)'],
        unit: row['Đơn vị tính'],
        category: row['Danh mục'],
        price: row['Giá vốn'],
        code: row['Mã sản phẩm'] || customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')(8),
        images: row['Hình ảnh'] ? row['Hình ảnh'].split(',') : [],
        sale_price: row['Giá bán'],
        description: row['Mô tả'],
        weight: row['Trọng lượng'],
        height: row['Chiều cao'],
        available_for_sale: row['Cho phép bán (Bắt buộc)'],
        status: row['Trạng thái'],
        bar_code: row['Mã vạch'],
        length: row['Chiều dài'],
        source: row['Nhà cung cấp'],
        type: row['Loại hàng (Bắt buộc)'],
        quantity_unlimited: row['Không giới hạn số lượng'],
      }

      await CoreProduct.updateOne(
        {
          code: core_product.code,
          brand_id: core_product.brand_id,
        },
        core_product,
        {
          upsert: true,
        }
      )
    }

    // sync category, source, unit
    const categories = core_product_rows.map((i) => i['Danh mục']).filter((i) => i)
    const sources = core_product_rows.map((i) => i['Nhà cung cấp']).filter((i) => i)
    const units = core_product_rows.map((i) => i['Đơn vị tính']).filter((i) => i)

    // upsert category,
    for (let category of categories) {
      await CoreProductCategory.updateOne(
        {
          name: category,
          brand_id,
        },
        {
          name: category,
          brand_id,
        },
        {
          upsert: true,
        }
      )
    }

    // upsert source
    for (let source of sources) {
      await CoreProductSource.updateOne(
        {
          name: source,
          brand_id,
        },
        {
          name: source,
          brand_id,
        },
        {
          upsert: true,
        }
      )
    }

    // upsert unit
    for (let unit of units) {
      await CoreProductUnit.updateOne(
        {
          name: unit,
          brand_id,
        },
        {
          name: unit,
          brand_id,
        },
        {
          upsert: true,
        }
      )
    }

    const ingredients = ingredient_rows.reduce((acc, row) => {
      if (acc[row['Mã']]) {
        acc[row['Mã']].push(row)
      } else {
        acc[row['Mã']] = [row]
      }
      return acc
    }, {})

    for (let code in ingredients) {
      const core_product = await CoreProduct.findOne({ code })
      if (!core_product) {
        continue
      }

      const ingredient = ingredients[code].map((i) => ({ code: i['Mã nguyên liệu'], amount: i['Số lượng'] }))
      core_product.ingredients = ingredient
      await core_product.save()
    }

    res.json({
      success: true,
    })
  },
]

exports.get_import_template = async (req, res) => {
  const key = 'import_core_product_template_v6.xlsx'
  const existed_file = await exist_file({ bucket: 'nexpos-files', key })

  if (existed_file) {
    return res.json({
      success: true,
      data: existed_file,
    })
  }

  // template need to have 2 tabs, 1 for core-product data, and 1 for ingredient data
  const template = {
    'Core Product': {
      'Loại hàng (Bắt buộc)': '',
      'Danh mục': '',
      'Mã sản phẩm': '',
      'Mã vạch': '',
      'Tên sản phẩm (Bắt buộc)': '',
      'Đơn vị tính': '',
      'Nhà cung cấp': '',
      'Giá vốn': '',
      'Giá bán': '',
      'Mô tả': '',
      'Hình ảnh': '',
      'Trọng lượng': '',
      'Chiều cao': '',
      'Chiều dài': '',
      'Cho phép bán (Bắt buộc)': '',
      'Trạng thái': '',
      'Không giới hạn số lượng': '',
    },
    Ingredient: {
      Mã: '',
      'Mã nguyên liệu': '',
      'Số lượng': '',
    },
  }

  const workbook = xlsx.utils.book_new()

  // example data for each tabs
  const example_data = {
    'Core Product': [
      {
        'Loại hàng (Bắt buộc)': 'nguyen_lieu/ban_thanh_pham/thanh_pham/hang_hoa',
        'Danh mục': 'Đồ uống',
        'Mã sản phẩm': 'CP001',
        'Mã vạch': 'CP001',
        'Tên sản phẩm (Bắt buộc)': 'Cà phê sữa',
        'Đơn vị tính': 'Ly',
        'Nhà cung cấp': 'NCC001',
        'Giá vốn': 20000,
        'Giá bán': 25000,
        'Mô tả': 'Cà phê sữa ngon tuyệt',
        'Hình ảnh': 'https://example.com/image.jpg',
        'Trọng lượng': 0.2,
        'Chiều cao': 10,
        'Chiều dài': 5,
        'Cho phép bán (Bắt buộc)': true,
        'Trạng thái': 'active',
        'Không giới hạn số lượng': false,
      },
    ],
    Ingredient: [
      {
        Mã: 'SBT001',
        'Mã nguyên liệu': 'NG001',
        'Số lượng': 1,
      },
    ],
  }

  // insert to file
  const core_product_worksheet = xlsx.utils.json_to_sheet([template['Core Product']])
  const ingredient_worksheet = xlsx.utils.json_to_sheet([template['Ingredient']])

  // insert example to file
  xlsx.utils.sheet_add_json(core_product_worksheet, example_data['Core Product'], { skipHeader: true, origin: 'A2' })
  xlsx.utils.sheet_add_json(ingredient_worksheet, example_data['Ingredient'], { skipHeader: true, origin: 'A2' })

  xlsx.utils.book_append_sheet(workbook, core_product_worksheet, 'Core Product')
  xlsx.utils.book_append_sheet(workbook, ingredient_worksheet, 'Ingredient')
  const buff = xlsx.write(workbook, { type: 'buffer' })

  const file = await upload_file({ bucket: 'nexpos-files', key, buff })

  res.json({
    success: true,
    data: file,
  })
}

exports.export_core_products = async (req, res) => {
  const { source, category, available_for_sale } = req.query
  const brand_id = req.params.brand_id

  const query = { brand_id, source, category, available_for_sale }
  const core_products = await CoreProduct.find(removeUndefinedProperties(query), { _id: 0, _v: 0, brand_id: 0 }).lean()
  const workbook = xlsx.utils.book_new()

  // export with 2 sheets, 1 for core-product data, and 1 for ingredient data, ingredient data is from core product
  const core_product_sheet = xlsx.utils.json_to_sheet(
    core_products.map((i) => ({
      'Loại hàng (Bắt buộc)': i.type,
      'Danh mục': i.category,
      'Mã sản phẩm': i.code,
      'Mã vạch': i.bar_code,
      'Tên sản phẩm (Bắt buộc)': i.name,
      'Đơn vị tính': i.unit,
      'Nhà cung cấp': i.source,
      'Giá vốn': i.price,
      'Giá bán': i.sale_price,
      'Mô tả': i.description,
      'Hình ảnh': i.images.join(','),
      'Trọng lượng': i.weight,
      'Chiều cao': i.height,
      'Chiều dài': i.length,
      'Cho phép bán (Bắt buộc)': i.available_for_sale,
      'Trạng thái': i.status,
      'Không giới hạn số lượng': i.quantity_unlimited,
    }))
  )
  xlsx.utils.book_append_sheet(workbook, core_product_sheet, 'Core Product')

  const ingredients = core_products.reduce((acc, core_product) => {
    if (!core_product.ingredients?.length) {
      return acc
    }

    core_product.ingredients.forEach((ingredient) => {
      acc.push({ ...ingredient, product_code: core_product.code })
    })
    return acc
  }, [])

  const ingredient_sheet = xlsx.utils.json_to_sheet(
    ingredients.map((i) => ({
      Mã: i.product_code,
      'Mã nguyên liệu': i.code,
      'Số lượng': i.amount,
    }))
  )
  xlsx.utils.book_append_sheet(workbook, ingredient_sheet, 'Ingredient')

  const buff = xlsx.write(workbook, { type: 'buffer' })
  const key = `export_core_product_${Date.now()}.xlsx`
  const file = await upload_file({ bucket: 'nexpos-files', key, buff })

  res.json({
    success: true,
    data: file,
  })
}

// core product category
exports.get_core_product_categories = async (req, res) => {
  const brand_id = req.params.brand_id
  const query = { brand_id }
  if (req.query.search) {
    query.name = { $regex: req.query.search, $options: 'i' }
  }

  const categories = await CoreProductCategory.find(query).sort({ created_at: -1 })

  res.json({
    success: true,
    data: categories,
  })
}

exports.create_core_product_category = async (req, res) => {
  const { name } = req.body
  const brand_id = req.params.brand_id

  const existed = await CoreProductCategory.findOne({ name, brand_id })
  if (existed) {
    return res.status(400).json({
      success: false,
      message: 'category_existed',
    })
  }
  const category = await CoreProductCategory.create({
    name,
    brand_id,
  })

  res.json({
    success: true,
    data: category,
  })
}

exports.delete_core_product_category = async (req, res) => {
  const { id, brand_id } = req.params

  const existed = await CoreProductCategory.findOne({ _id: id, brand_id })
  if (!existed) {
    return res.status(404).json({
      success: false,
      message: 'category_not_found',
    })
  }

  // if any product using this category, cannot delete
  const products = await CoreProduct.find({ category: existed.name, brand_id })
  if (products.length) {
    return res.status(400).json({
      success: false,
      message: 'category_in_use',
    })
  }

  await CoreProductCategory.deleteOne({ _id: id })
  res.json({
    success: true,
  })
}

// Core product source
exports.get_core_product_sources = async (req, res) => {
  const brand_id = req.params.brand_id
  const query = { brand_id }
  if (req.query.search) {
    query.name = { $regex: req.query.search, $options: 'i' }
  }

  const sources = await CoreProductSource.find(query).sort({ created_at: -1 })

  res.json({
    success: true,
    data: sources,
  })
}

exports.create_core_product_source = async (req, res) => {
  const { name } = req.body
  const brand_id = req.params.brand_id

  const existed = await CoreProductSource.findOne({ name, brand_id })
  if (existed) {
    return res.status(400).json({
      success: false,
      message: 'source_existed',
    })
  }
  const source = await CoreProductSource.create({
    name,
    brand_id,
  })

  res.json({
    success: true,
    data: source,
  })
}

exports.delete_core_product_source = async (req, res) => {
  const { id, brand_id } = req.params

  const existed = await CoreProductSource.findOne({ _id: id, brand_id })
  if (!existed) {
    return res.status(404).json({
      success: false,
      message: 'source_not_found',
    })
  }

  // if any product using this source, cannot delete
  const products = await CoreProduct.find({ source: existed.name, brand_id })
  if (products.length) {
    return res.status(400).json({
      success: false,
      message: 'source_in_use',
    })
  }

  await CoreProductSource.deleteOne({ _id: id })
  res.json({
    success: true,
  })
}

// Core product unit
exports.get_core_product_units = async (req, res) => {
  const brand_id = req.params.brand_id
  const query = { brand_id }
  if (req.query.search) {
    query.name = { $regex: req.query.search, $options: 'i' }
  }

  const units = await CoreProductUnit.find(query).sort({ created_at: -1 })

  res.json({
    success: true,
    data: units,
  })
}

exports.create_core_product_unit = async (req, res) => {
  const { name } = req.body
  const brand_id = req.params.brand_id

  const existed = await CoreProductUnit.findOne({ name, brand_id })
  if (existed) {
    return res.status(400).json({
      success: false,
      message: 'unit_existed',
    })
  }
  const unit = await CoreProductUnit.create({
    name,
    brand_id,
  })

  res.json({
    success: true,
    data: unit,
  })
}

exports.delete_core_product_unit = async (req, res) => {
  const { id, brand_id } = req.params

  const existed = await CoreProductUnit.findOne({ _id: id, brand_id })
  if (!existed) {
    return res.status(404).json({
      success: false,
      message: 'unit_not_found',
    })
  }

  // if any product using this unit, cannot delete
  const products = await CoreProduct.find({ unit: existed.name, brand_id })
  if (products.length) {
    return res.status(400).json({
      success: false,
      message: 'unit_in_use',
    })
  }

  await CoreProductUnit.deleteOne({ _id: id })
  res.json({
    success: true,
  })
}
