const { Site, Voucher, Vendor<PERSON>allback, Order, PartnerAPIKey } = require('../../.shared/database')
const dpoint = require('../../.shared/dpoint')
const moment = require('moment')
// const mockToken = {
//   username: 'cyber-kitchen',
//   password: '9uAwG89bvn2Igmfe_GFQRF5sq9vcXHAfvU8Y7qYZp7A',
//   access_key: 'T4OuRO3wf7B3ZoOPgXvmwWHjoEKuzev2',
// }

const _get_token = async (site_id) => {
  const site = await Site.findById(site_id)
  if (!site) {
    return {}
  }

  const site_token = site.getToken('dpoint')
  if (!site_token?.access_token || !site_token?.username) {
    return {}
  }

  const partner = await PartnerAPIKey.findOne({
    name: 'dpoint',
    'metadata.username': site_token.username,
  })

  if (!partner) {
    return {}
  }

  return {
    token: {
      username: partner.metadata.username,
      password: partner.metadata.password,
      access_token: site_token?.access_token,
    },
    site,
  }
}

const get_customer_info = async (req, res) => {
  const { token } = await _get_token(req.params.site_id)

  if (!token) {
    return res.status(400).json({
      success: false,
      error: 'token_not_configured',
    })
  }

  const phone = req.params.phone
  const { data, error } = await dpoint.get_customer_info(phone, token)

  if (data) {
    return res.status(200).json({
      success: true,
      data: {
        full_name: data.fullName,
        phone_number: data.phoneNumber,
        date_of_birth: data.dateOfBirth,
        full_address: data.fullAddress,
        tier_name: data.tierName,
        reward_value: data.rewardValue,
        gender: data.gender,
        email: data.email,
      },
    })
  }

  return res.status(400).json({
    success: false,
    error: error,
  })
}

const get_customer_vouchers = async (req, res) => {
  const { site_id, phone } = req.params
  const site = await Site.findById(site_id)
  if (!site) {
    return res.status(400).json({
      success: false,
      error: 'site_not_found',
    })
  }
  const dpoint_username = site.getToken('dpoint')?.username
  if (!dpoint_username) {
    return res.json({
      success: true,
      data: [],
    })
  }

  const vouchers = await Voucher.find({ owner_phone: phone, dpoint_username, expired_at: { $gte: new Date() }, is_used: false })

  return res.json({
    success: true,
    data: vouchers,
  })
}

const request_otp = async (req, res) => {
  const { phone } = req.params
  if (!phone) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  const { token } = await _get_token(req.params.site_id)
  if (!token) {
    return res.status(400).json({
      success: false,
      error: 'token_not_configured',
    })
  }

  const { data, error } = await dpoint.request_otp(phone, token)
  if (data) {
    return res.status(200).json({
      success: true,
    })
  }

  return res.status(400).json({
    success: false,
    error: error,
  })
}

const create_customer = async (req, res) => {
  const { phone, full_name, date_of_birth, full_address, otp } = req.body

  if (!phone || !full_name) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  const { token } = await _get_token(req.params.site_id)
  if (!token) {
    return res.status(400).json({
      success: false,
      error: 'token_not_configured',
    })
  }

  const { data, error } = await dpoint.create_customer(
    {
      phone_number: phone,
      full_name: full_name,
      date_of_birth: date_of_birth,
      full_address: full_address,
      otp,
    },
    token
  )

  if (data) {
    return res.status(200).json({
      success: true,
    })
  }

  return res.status(400).json({
    success: false,
    error: error,
  })
}

const send_completed_order = async (payload, site_id) => {
  const { token, site } = await _get_token(site_id)
  if (!token) {
    return
  }

  const { phone_number, total, discount, delivery_on, voucher_code, external_id } = payload
  const { data, error } = await dpoint.create_transaction(
    {
      order_id: external_id,
      phone_number,
      total,
      discount: discount || 0,
      delivery_on,
      voucher_code: voucher_code || '',
      order_type: 'COMPLETED',
      site_code: site.code,
    },
    token
  )

  return { success: Boolean(data), detail: { data, error }, at: new Date() }
}

const send_return_order = async (payload, site_id) => {
  const { token } = await _get_token(site_id)
  if (!token) {
    return
  }

  const p = { ...payload, order_type: 'RETURN' }
  const { data, error } = await dpoint.create_transaction(p, token)
  if (data) {
    return data
  }

  throw new Error(error)
}

const validate_voucher = async (site, voucher_code, phone) => {
  const dpoint_username = site.getToken('dpoint')?.username
  const voucher = await Voucher.findOne({
    code: voucher_code,
    dpoint_username,
    owner_phone: phone,
  })

  if (!voucher) {
    return { error: 'voucher_not_found' }
  }

  if (voucher.expired_at && voucher.expired_at < new Date()) {
    return { error: 'voucher_expired' }
  }

  if (voucher.is_used) {
    return { error: 'voucher_already_used' }
  }

  return { voucher }
}

const check_voucher = async (req, res) => {
  const { site_id } = req.params
  const { code, phone } = req.body
  const site = await Site.findById(site_id)
  if (!site) {
    return res.status(400).json({
      success: false,
      error: 'site_not_found',
    })
  }

  const { error, voucher } = await validate_voucher(site, code, phone)

  if (error) {
    return res.status(400).json({
      success: false,
      error,
    })
  }

  return res.status(200).json({
    success: true,
    data: voucher,
  })
}

const use_voucher = async (code, site) => {
  const dpoint_username = site.getToken('dpoint')?.username
  const voucher = await Voucher.findOne({ code, dpoint_username })
  if (!voucher) {
    return
  }

  voucher.is_used = true
  voucher.used_at = new Date()
  await voucher.save()
}

const release_voucher = async (code, site) => {
  await Voucher.findOneAndUpdate(
    { code, dpoint_username: site.getToken('dpoint')?.username },
    {
      is_used: false,
      used_at: null,
    }
  )
}

// update voucher from dpoint to nexpos
const update_voucher = async (req, res) => {
  const { voucher_code, phone, expired_at, trade_at } = req.body
  const { username } = req.partner.metadata || {}
  if (!username) {
    return res.status(400).json({
      success: false,
      error: 'brand_not_config',
    })
  }

  const voucher = await Voucher.findOne({ code: voucher_code, dpoint_username: username })
  if (!voucher) {
    return res.status(400).json({
      success: false,
      error: 'voucher_not_found',
    })
  }

  if (voucher.owner_phone) {
    return res.status(400).json({
      success: false,
      error: 'voucher_already_associated_with_phone_number',
    })
  }

  voucher.owner_phone = phone
  voucher.expired_at = expired_at ? moment(expired_at).toDate() : undefined
  voucher.trade_at = trade_at ? moment(trade_at).toDate() : undefined
  await voucher.save()

  await VendorCallback.create({
    vendor: 'dpoint',
    type: 'order',
    headers: req.headers,
    request_data: req.body,
    url: req.url,
    method: req.method,
  })

  return res.status(200).json({
    success: true,
  })
}

const is_site_apply_voucher = (site, dpoint_brand_username) => {
  if (!site) {
    return false
  }

  const site_token = site.getToken('dpoint')
  if (dpoint_brand_username) {
    return Boolean(site_token?.access_token && site_token?.username === dpoint_brand_username)
  }

  return Boolean(site_token?.access_token && site_token?.username)
}

const get_order_info = async (req, res) => {
  const { username, password } = req.partner.metadata || {}

  await VendorCallback.create({
    vendor: 'dpoint',
    type: 'order',
    headers: req.headers,
    request_data: req.query,
    url: req.url,
    method: req.method,
  })

  if (!username || !password) {
    return res.status(400).json({
      success: false,
      error: 'dpoint_config_not_found',
    })
  }

  const { order_id, phone_number } = req.query
  if (!order_id || !phone_number) {
    return res.status(400).json({
      success: false,
      error: 'missing_required_fields',
    })
  }

  const order = await Order.findOne({
    external_id: order_id,
    status: 'FINISH',
  })

  if (!order) {
    return res.status(400).json({
      success: false,
      error: 'order_not_found',
    })
  }

  const site = await Site.findById(order.site_id)
  // order not in configured brand
  const dpoint_token = site.getToken('dpoint')

  if (!site || !dpoint_token?.access_token || dpoint_token?.username !== username) {
    return res.status(400).json({
      success: false,
      error: 'order_not_found',
    })
  }

  if (order.data_mapping.customer_phone && order.data_mapping.customer_phone !== phone_number) {
    return res.status(400).json({
      success: false,
      error: 'point_already_recorded_by_another',
    })
  }

  // phone match or order does not has phone: depends on point_sync_at field
  if (order.dpoint_sync?.success) {
    return res.status(400).json({
      success: false,
      error: 'point_already_recorded',
    })
  }

  // accept, send to dpoint
  const result = await send_completed_order(
    {
      order_id: order.order_id,
      external_id: order.external_id,
      phone_number,
      total: order.data_mapping.total_for_biz,
      discount: order.voucher?.discount || 0,
      delivery_on: order.data_mapping.delivery_time,
    },
    order.site_id
  )

  // update order
  order.dpoint_sync = result
  order.data_mapping.customer_phone = phone_number
  order.data.customer_name = phone_number
  order.markModified('data_mapping')
  order.markModified('data')
  await order.save()

  const discount = order.voucher?.discount || 0
  const data = {
    phoneNumber: phone_number,
    orderDateTime: moment(order.data_mapping.delivery_time).format('DD/MM/YYYY HH:mm:ss'),
    orderCode: order_id,
    totalBeforeDiscount: order.data_mapping.total,
    totalAfterDiscount: order.data_mapping.total - discount,
    orderDiscountAmount: discount,
    paymentAmount: order.data_mapping.total - discount,
    voucherCode: order.voucher?.code,
    address: order.data_mapping.customer_address,
    name: order.data_mapping.customer_name,
    storeCode: site.code,
  }

  return res.status(200).json({
    success: true,
    data,
  })
}

module.exports = {
  get_customer_info,
  create_customer,
  send_completed_order,
  send_return_order,
  validate_voucher,
  use_voucher,
  check_voucher,
  request_otp,
  is_site_apply_voucher,
  update_voucher,
  get_order_info,
  get_customer_vouchers,
  release_voucher,
}
