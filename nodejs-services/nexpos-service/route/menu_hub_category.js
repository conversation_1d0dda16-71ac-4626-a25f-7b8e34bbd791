const { Site, BrandMenu, HubMenuGroup, Hub, HubStock, HubStockHistory, SiteMenuGroup, GoogleSheetFile, VendorHubStock, Brand } = require('../../.shared/database')
const _ = require('lodash');
const crypto = require('crypto');
const xlsx = require('xlsx');
const moment = require('moment');
const odoo = require('../../.shared/merchant/odoo');
const nutifood = require('../../.shared/merchant/nutifood');
const baemin_menu = require('../../.shared/merchant/baemin_menu');
const grab_menu = require('../../.shared/merchant/grab_menu');
const gojek_menu = require('../../.shared/merchant/gojek_menu');
const shopee_menu = require('../../.shared/merchant/shopee_menu');
const shopee = require('../../.shared/merchant/shopee');
const shopee_official = require('../../.shared/merchant/shopee_official');
const redis = require('../../.shared/redis')


const { text_slugify, calculate_nutifood_item_quantity, calculate_item_min_quantity } = require('../../.shared/helper');
const { _menu_item_history_by_user } = require('./menu_site_category');
const { get_token_by_site } = require('../../.shared/token_account');
const be_menu = require('../../.shared/merchant/be_menu');
const { get_queue, set_queue, pick_queue } = require('../../.shared/redis_queue');
const { ZALO_GROUPS, send_zalo_message } = require('../../.shared/zalo');
const { upload_file } = require('../../.shared/storage');

const router = {}

const update_hub_stock_v2 = async (hub_id) => {
    try {
        const hub = await Hub.findById(hub_id)
        if (!hub) {
            return null
        }

        if (hub.status !== 'active') {
            return null
        }

        if (hub.inventory_source !== 'nutifood') {
            return null
        }

        const sites = await Site.find({ hub_id })

        const file = await GoogleSheetFile.findOne({ file_id: "15sJPu1TNTVzsS-LEraV95jyockeZgfhaXoeL19Ndwv8" })
        const menu_items = file.sheet_data.MD_menu.filter(v =>
            [
                'NutiFood GrowPLUS+',
                'Nutifood NNDD',
                'NutiFood NutiMilk',
                'NutiFood Bliss',
                'NutiFood Varna',
            ].includes(v.brand)
        ).map(v => ({
            name: v.name,
            brand: v.brand,
            min_stock: v.min_stock,
            combo: [
                {
                    name: v.item1_name,
                    code: String(v.item1_code ?? ''),
                    quantity: Number(v.item1_quantity ?? 0),
                }, {
                    name: v.item1_name,
                    code: String(v.item2_code ?? ''),
                    quantity: Number(v.item2_quantity ?? 0),
                }, {
                    name: v.item1_name,
                    code: String(v.item3_code ?? ''),
                    quantity: Number(v.item3_quantity ?? 0),
                }
            ].filter(c => c.code)
        })).filter(v => v.combo.length > 0)

        const vendor_hub_stock = await VendorHubStock.findOne({ hub_id, vendor: 'nutifood' }).lean()
        const hub_stocks = await HubStock.find({ hub_id });
        const stock_items = vendor_hub_stock?.stocks ?? []

        for (const stock_item of stock_items) {
            const hub_stock = hub_stocks.find(v => v.code === stock_item.code)
            if (hub_stock?.locked_status === 'alway_active') {
                stock_item.quantity = 1000000
            }
            if (hub_stock?.locked_status === 'alway_inactive') {
                stock_item.quantity = 0
            }
        }

        const brands = await Brand.find({ _id: { $in: sites.map(v => v.brand_id) } }, { name: 1 }).lean()

        for (const site of sites) {
            const site_menu_items = menu_items.filter(v => brands.find(b => String(b._id) === site.brand_id)?.name === v.brand)
            let app_update_items = []
            let app_update_option_items = []
            // Get update items
            for (const menu_item of site_menu_items) {
                const min_quantity = calculate_item_min_quantity(menu_item, stock_items)
                if (min_quantity !== null) {
                    app_update_items.push({
                        name: menu_item.name,
                        active: min_quantity > menu_item.min_stock,
                        min_stock: menu_item.min_stock,
                        quantity: min_quantity,
                    })
                }
            }

            if (site.tokens.some(v => (v.source === 'shopee' && v.token_code) || (v.source === 'shopee_fresh' && v.token_code))) {
                if (!app_update_items.some(v => v.active)) {
                    // DEBUG INFO:
                    for (const stock_item of stock_items) {
                        const hub_stock = hub_stocks.find(v => v.code === stock_item.code)
                        let old_quantity = stock_item.quantity
                        stock_item.old_quantity = old_quantity
                        stock_item.hub_name = hub.name
                        stock_item.locked_status = hub_stock?.locked_status
                        stock_item.created_at = new Date()
                        stock_item.link_items = menu_items.filter(v => v.combo.some(c => c.code === stock_item.code)).map(v => v.name + '| Min Stock: ' + v.min_stock).join('\n')

                        if (hub_stock?.locked_status === 'alway_active') {
                            stock_item.quantity = 1000000
                        }
                        if (hub_stock?.locked_status === 'alway_inactive') {
                            stock_item.quantity = 0
                        }

                    }
                    const workbook = xlsx.utils.book_new()
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(stock_items), 'Stocks');
                    xlsx.utils.book_append_sheet(workbook, xlsx.utils.json_to_sheet(vendor_hub_stock.request_codes?.map(v => ({ request_code: v }))), 'Request Stocks');

                    const buff = xlsx.write(workbook, { type: 'buffer' });
                    const key = `out_of_stocks/no_stock_${Date.now()}.xlsx`

                    const file = await upload_file({ bucket: 'nexpos-files', key, buff })
                    // END: DEBUG INFO

                    // if (site.brand_id === '64d7c520513d86579aa7713b') {
                    //     await send_zalo_message({
                    //         group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
                    //         message: [
                    //             `[NTF] Toàn bộ mặt hàng của site: <b>${site.name}</b>, hub: <b>${hub.name}</b> đã hết hàng!`,
                    //             `Danh sách mặt hàng hết hàng: ${file}`,
                    //         ].join('\n'),
                    //         min_duplicate_message_duration: 5 * 60 * 60,
                    //     })
                    // }
                    return

                } else {
                    // for (const source of ['shopee', 'shopee_fresh']) {
                    //     const token = await get_token_by_site(site, source)
                    //     await shopee.update_store_status(token, { status: 'open' })
                    // }

                }
            }

            const merchant_functions = {
                grab: {
                    active_menu_item: grab_menu.active_menu_item,
                    active_menu_option_item: grab_menu.active_menu_option_item,
                },
                grab_mart: {
                    active_menu_item: grab_menu.active_menu_item,
                    active_menu_option_item: grab_menu.active_menu_option_item,
                },
                // gojek: {
                //     active_menu_item: gojek_menu.active_menu_item,
                //     active_menu_option_item: gojek_menu.active_menu_option_item,
                // },
                shopee: {
                    active_menu_item: shopee_menu.active_menu_item,
                    active_menu_option_item: shopee_menu.active_menu_option_item,
                },
                shopee_fresh: {
                    active_menu_item: shopee_menu.active_menu_item,
                    active_menu_option_item: shopee_menu.active_menu_option_item,
                },
                be: {
                    active_menu_item: be_menu.active_menu_item,
                    active_menu_option_item: be_menu.active_menu_option_item,
                },
            }
            for (const source in merchant_functions) {
                if (source === 'shopee' || source === 'shopee_fresh') {
                    await shopee_official.sync_menu({ nexpos_site_id: site.code })
                    continue
                }

                const merchant_function = merchant_functions[source]
                const token = await get_token_by_site(site, source)
                const active_menu_items = await merchant_function.active_menu_item(token, app_update_items, true)
                // const active_menu_option_items = await merchant_function.active_menu_option_item(token, app_update_option_items, false)
                if (active_menu_items.length > 0) {
                    for (const item of active_menu_items) {
                        await _menu_item_history_by_user(site._id, source, 'item_status_updated', { name: item.category_name }, null, item, null)
                    }
                }
                // if (active_menu_option_items.length > 0) {
                //     for (const item of active_menu_option_items) {
                //         await _menu_item_history_by_user(site_menu.site_id, source, 'option_item_status_updated', { name: item.category_name }, null, item, null)
                //     }
                // }
            }
        }

        return true
    } catch (err) {
        console.error(err);
        return null
    }
}

router.get_hub_stock_from_servers = async (req, res) => {
    const { hub_id } = req.params;

    try {
        const menu_items = update_hub_stock_v2(hub_id)
        res.json({
            success: true,
            data: menu_items,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.sync_hub_stock_to_apps = async (req, res) => {
    const queue_key = 'cron_job:nutifood_hub_stock'
    let cached_sites = await get_queue(queue_key)
    if (cached_sites.length === 0) {
        const hubs = await Hub.find({
            inventory_source: 'nutifood',
            status: 'active',
        }, { _id: 1, name: 1 }).lean()
        await set_queue(queue_key, hubs)
    }
    const selected_hubs = await pick_queue(queue_key, { size: 4, min_duration: 5 * 60 })

    try {
        for (const hub of selected_hubs) {
            await update_hub_stock_v2(hub._id)
        }
    } catch (err) {
        console.error(err);
    }
    res.json({
        success: true,
    });
}


router.get_menu_stock_from_server = async (req, res) => {
    const { hub_id } = req.params;

    try {
        const menu_items = await update_hub_stock_v2(hub_id)
        res.json({
            success: true,
            data: menu_items,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

module.exports = router