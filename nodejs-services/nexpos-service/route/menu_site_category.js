const { Site, BrandMenu, SiteMenuGroup, User, HubStock, SiteMenuGroupHistory, CPHubStock } = require('../../.shared/database')
const { get_menu } = require('../../.shared/merchant/grab')
const { map_menu } = require('../../.shared/merchant/mapping')
const { reverse_map_menu_item } = require('../../.shared/merchant/mapping.menu_item')
const baemin = require('../../.shared/merchant/baemin');
const shopee = require('../../.shared/merchant/shopee');
const gojek = require('../../.shared/merchant/gojek');
const grab = require('../../.shared/merchant/grab');
const be = require('../../.shared/merchant/be');
const gojek_menu = require('../../.shared/merchant/gojek_menu');
const baemin_menu = require('../../.shared/merchant/baemin_menu');
const grab_menu = require('../../.shared/merchant/grab_menu');
const shopee_menu = require('../../.shared/merchant/shopee_menu');
const be_menu = require('../../.shared/merchant/be_menu');
const { upload_file } = require('../../.shared/storage')
const token_account = require('../../.shared/token_account')
const helper = require('./helper');

const _ = require('lodash');
const fs = require('fs');
const moment = require('moment');
const xlsx = require('xlsx');
const XlsxTemplate = require('xlsx-template');
const { get_item_stock } = require('../common/order');
const { get_token_by_site } = require('../../.shared/token_account');

const router = {}
router.get_site_menu_category_list = async (req, res) => {
    const { site_id } = req.params;
    const { name, only_in_apps, only_sell_in_apps, only_has_combo } = req.query;
    const filter = { site_id };

    const site = await Site.findById(site_id, { brand_id: 1, he_id: 1, use_core_product: 1 }).lean()
    if (!site) {
        res.status(404).json({
            success: false,
            message: 'site_not_found',
        });
        return
    }

    const result = await SiteMenuGroup.findOne(filter).lean();
    if (!result) {
        await SiteMenuGroup.findOneAndUpdate({ site_id }, { categories: [], option_categories: [] }, { upsert: true, new: true })
        res.json({
            success: true,
            data: {
                categories: [],
                option_categories: [],
            },
        });
        return
    }
    result.histories = await SiteMenuGroupHistory.find({ site_id }).sort({ created_at: -1 }).limit(100).lean()

    const filterItems = (items, query) =>
        _.filter(items, item => _.includes(_.toLower(item.name), _.toLower(query)));

    const filterItemsWithSources = items => _.filter(items, item => item.sources && item.sources.length > 0);

    const filterItemsWithActive = items => _.filter(items, item => item.active);

    const filterItemsWithCombo = items => _.filter(items, item => item.combo && item.combo?.length > 0);

    result.categories = _.reject(result.categories, category => {
        category.items = name ? filterItems(category.items, name) : category.items;
        category.sub_categories = _.reject(category.sub_categories, subCategory => {
            subCategory.items = name ? filterItems(subCategory.items, name) : subCategory.items;
            return _.isEmpty(subCategory.items);
        });
        return _.isEmpty(category.sub_categories) && _.isEmpty(category.items);
    });

    if (only_in_apps === 'true') {
        result.categories = _.reject(result.categories, category => {
            category.items = filterItemsWithSources(category.items);
            category.sub_categories = _.reject(category.sub_categories, subCategory => {
                subCategory.items = filterItemsWithSources(subCategory.items);
                return _.isEmpty(subCategory.items);
            });
            return _.isEmpty(category.sub_categories) && _.isEmpty(category.items);
        });
    }

    if (only_sell_in_apps === 'true') {
        result.categories = _.reject(result.categories, category => {
            category.items = filterItemsWithActive(category.items);
            category.sub_categories = _.reject(category.sub_categories, subCategory => {
                subCategory.items = filterItemsWithActive(subCategory.items);
                return _.isEmpty(subCategory.items);
            });
            return _.isEmpty(category.sub_categories) && _.isEmpty(category.items);
        });
    }

    if (only_has_combo === 'true') {
        result.categories = _.reject(result.categories, category => {
            category.items = filterItemsWithCombo(category.items);
            category.sub_categories = _.reject(category.sub_categories, subCategory => {
                subCategory.items = filterItemsWithCombo(subCategory.items);
                return _.isEmpty(subCategory.items);
            });
            return _.isEmpty(category.sub_categories) && _.isEmpty(category.items);
        });
    }

    if (site.he_id) {
        if (site.use_core_product) {
            const he = await User.findOne({ _id: site.he_id }, { hubs: 1 })
            if (he) {
                const hub_stocks = await HubStock.find({ hub_id: { $in: he.hubs }, locked_status: { $in: ['use_stock', 'alway_active'] } }, {
                    code: 1,
                    quantity: 1,
                    locked_status: 1,
                }).lean()

                const hub_stocks_by_item = hub_stocks.reduce((acc, cur) => {
                    const current = acc[cur.code]
                    if (current) {
                        if (cur.locked_status === 'alway_active') {
                            current.locked_status = 'alway_active'
                        }

                        current.quantity = Math.max(current.quantity, cur.quantity)
                    } else {
                        acc[cur.code] = cur
                    }
                    return acc
                }, {})
                for (const category of result.categories) {
                    for (const item of category.items) {
                        item.quantity_unlimited = false
                        item.quantity = 0
                        const stock = hub_stocks_by_item[item.code]
                        if (stock) {
                            if (stock.locked_status === 'alway_active') {
                                item.quantity_unlimited = true
                            } else {
                                item.quantity = stock.quantity
                            }
                        }

                    }

                    for (const subCategory of category.sub_categories) {
                        for (const item of subCategory.items) {
                            item.quantity_unlimited = false
                            item.quantity = 0
                            const stock = hub_stocks_by_item[item.code]
                            if (stock) {
                                if (stock.locked_status === 'alway_active') {
                                    item.quantity_unlimited = true
                                } else {
                                    item.quantity = stock.quantity
                                }
                            }
                        }
                    }
                }
            }
        } else {
            const he = await User.findOne({ _id: site.he_id }, { hubs: 1 })
            if (he) {
                const hub_stocks = await HubStock.find({ hub_id: { $in: he.hubs } }, {
                    code: 1,
                    quantity: 1,
                }).lean()

                const hub_stocks_by_item = hub_stocks.reduce((acc, cur) => {
                    acc[cur.code] = Math.max(acc[cur.code] || 0, cur.quantity)
                    return acc
                }, {})

                for (const category of result.categories) {
                    for (const item of category.items) {
                        const quantity = get_item_stock(item, hub_stocks_by_item)
                        item.quantity = quantity > 0 ? quantity : 0
                    }

                    for (const subCategory of category.sub_categories) {
                        for (const item of subCategory.items) {
                            const quantity = get_item_stock(item, hub_stocks_by_item)
                            item.quantity = quantity > 0 ? quantity : 0
                        }
                    }
                }
            }
        }
    }

    res.json({
        success: true,
        data: result,
    });
};

router.clone_menu_from_a_brand = async (req, res) => {
    const { from_brand_id, item_ids } = req.body;
    const { site_id } = req.params;

    try {
        const site_menu = await SiteMenuGroup.findOneAndUpdate({ site_id }, {
            $setOnInsert: { option_categories: [], categories: [] }
        }, { upsert: true, new: true }).lean();

        const from_brand_menu = await BrandMenu.findOne({ brand_id: from_brand_id }).lean();
        const from_categories = from_brand_menu.categories;
        const from_option_categories = from_brand_menu.option_categories;

        let selected_items = [];
        for (const category of from_categories) {
            if (item_ids.includes(String(category._id))) {
                for (const item of category.items) {
                    selected_items.push({
                        item,
                        category: category,
                        sub_category: null,
                    })
                }
                for (const sub_category of category.sub_categories) {
                    for (const item of sub_category.items) {
                        selected_items.push({
                            item,
                            category: category,
                            sub_category: sub_category,
                        })
                    }
                }
            }
            for (const sub_category of category.sub_categories) {
                if (item_ids.includes(String(sub_category._id))) {
                    for (const item of sub_category.items) {
                        selected_items.push({
                            item,
                            category: category,
                            sub_category: sub_category,
                        })
                    }
                }
                for (const item of sub_category.items) {
                    if (item_ids.includes(String(item._id))) {
                        selected_items.push({
                            item,
                            category: category,
                            sub_category: sub_category,
                        })
                    }
                }
            }
            for (const item of category.items) {
                if (item_ids.includes(String(item._id))) {
                    selected_items.push({
                        item,
                        category: category,
                        sub_category: null,
                    })
                }
            }
        }

        for (const selected_item of selected_items) {
            const { category, sub_category, item } = selected_item;
            if (sub_category) {
                const site_category = site_menu.categories?.find(c => c.name === category.name);
                const site_sub_category = site_category?.sub_categories?.find(s => s.name === sub_category.name);
                const site_item = site_sub_category?.items?.find(i => i.name === item.name);

                if (!site_category) {
                    site_menu.categories.push({
                        ...category,
                        sub_categories: [{
                            ...sub_category,
                            items: [item]
                        }]
                    });
                } else {
                    if (!site_sub_category) {
                        site_category.sub_categories.push({
                            ...sub_category,
                            items: [item]
                        });
                    } else {
                        if (!site_item) {
                            site_sub_category.items.push(item);
                        } else {
                            site_item.code = item.code
                            site_item.price = item.price
                            site_item.description = item.description
                            site_item.image = item.image
                        }
                    }
                }
            } else {
                const site_category = site_menu.categories?.find(c => c.name === category.name);
                const site_item = site_category?.items?.find(i => i.name === item.name);

                if (!site_category) {
                    site_menu.categories.push({
                        ...category,
                        sub_categories: [],
                        items: [item]
                    });
                } else {
                    if (!site_item) {
                        site_category.items.push(item);
                    } else {
                        site_item.code = item.code
                        site_item.price = item.price
                        site_item.description = item.description
                        site_item.image = item.image
                    }
                }
            }
        }

        const selected_option_categories = from_option_categories.filter(v => v.category_ids?.some(id => selected_items.some(v => v.item.id === id)))

        for (const selected_option_category of selected_option_categories) {
            const site_option_category = site_menu.option_categories?.find(c => c.name === selected_option_category.name);

            if (!site_option_category) {
                site_menu.option_categories.push(selected_option_category);
            } else {
                site_option_category.options = selected_option_category.options;
            }
        }

        await SiteMenuGroup.findOneAndUpdate({ site_id }, { categories: site_menu.categories, option_categories: site_menu.option_categories }, { upsert: true, new: true });

        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}



function extractObjectPaths(data, parentPath = "", result = []) {
    if (typeof data === 'object' && data !== null && data._id !== null && !Array.isArray(data)) {
        result.push({ path: parentPath, value: data });
        Object.keys(data).forEach(key => {
            const path = parentPath ? `${parentPath}.${key}` : key;
            extractObjectPaths(data[key], path, result);
        });
    } else if (Array.isArray(data)) {
        data.forEach((item, index) => {
            const path = parentPath ? `${parentPath}.${index}` : `${index}`;
            extractObjectPaths(item, path, result);
        });
    }

    return result;
}

router.clone_menu_from_a_site = async (req, res) => {
    const { from_site_id, item_ids } = req.body;
    const { site_id } = req.params;

    try {
        const from_site_menu = await SiteMenuGroup.findOne({ site_id: from_site_id }).lean();
        const to_site_menu = await SiteMenuGroup.findOne({ site_id: site_id }).lean();
        let from_categories = from_site_menu.categories
        let from_option_categories = from_site_menu.option_categories
        // Update categories
        const pairs = extractObjectPaths(from_categories)
        let deletePaths = []
        for (const pair of pairs) {
            if (!item_ids.some(v => String(pair.value._id).includes(v))) {
                deletePaths.push(pair.path)
            }
        }

        for (const path of deletePaths) {
            delete from_categories[path];
        }
        const new_categories = _.concat(to_site_menu?.categories || [], from_categories).filter(v => v)
        const new_option_categories = _.concat(to_site_menu?.option_categories || [], from_option_categories)

        await SiteMenuGroup.findOneAndUpdate({ site_id }, { categories: new_categories, option_categories: new_option_categories }, { upsert: true, new: true })
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.get_latest_site_menu = async (req, res) => {
    try {
        const { site_id } = req.params;

        const site = await Site.findById(site_id)
        let site_menu = await SiteMenuGroup.findOneAndUpdate({ site_id: site._id }, {}, { upsert: true, new: true }).lean()

        // get all menu from merchant apps
        const merchant_function = {
            shopee: shopee.get_menu,
            shopee_fresh: shopee.get_menu,
            grab: grab.get_menu,
            grab_mart: grab.get_mart_menu,
            // gojek: gojek.get_menu,
            be: be.get_menu,
        }
        for (const source in merchant_function) {
            const func = merchant_function[source]
            const token = await get_token_by_site(site, source)
            const menu = await func(token)
            const new_menu = map_menu(source, {
                categories: menu.categories,
                option_categories: menu.option_categories,
            })
            site_menu = await helper.compose_site_menu_item(site_menu, new_menu.categories)
            site_menu = await helper.compose_site_menu_option_item(site_menu, new_menu.option_categories)
        }

        await SiteMenuGroup.findOneAndUpdate({ site_id: site._id }, {
            categories: site_menu.categories,
            option_categories: site_menu.option_categories,
        }, { upsert: true, new: true })

        res.json({
            success: true,
            data: site_menu,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.sync_menu_item_to_apps = async (req, res) => {
    try {
        const { site_id, item_id } = req.params;
        const site = await Site.findById(site_id)
        let site_menu = await SiteMenuGroup.findOneAndUpdate({ site_id: site._id }, {}, { upsert: true, new: true })

        let all_items = [...site_menu.categories.flatMap(c => c.items), ...site_menu.categories.flatMap(c => c.sub_categories.flatMap(s => s.items))]
        const item = all_items.find(i => String(i._id) === item_id)
        const category = site_menu.categories.find(c => c.items.some(i => String(i._id) === item_id))
        const sub_category = site_menu.categories.flatMap(c => c.sub_categories).find(s => s.items.some(i => String(i._id) === item_id))
        // Save history
        await menu_item_history_by_user(site_menu._id, 'User', 'item_synced', category, sub_category, item, req.user)

        const merchant_functions = {
            shopee: shopee_menu,
            shopee_fresh: shopee_menu,
            baemin: baemin_menu,
            grab: grab_menu,
            // gojek: gojek_menu,
            be: be_menu,
        }
        for (const source in merchant_functions) {
            const merchant_function = merchant_functions[source]
            const token = await token_account.get_token_by_site(site, source)
            await merchant_function.sync_menu(token, { site_menu, item_id })
        }

        res.json({
            success: true,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.delete_and_sync_menu_category_to_apps = async (req, res) => {
    try {
        const { site_id, category_id } = req.params;
        const site = await Site.findById(site_id)

        let site_menu = await SiteMenuGroup.findOneAndUpdate({ site_id: site._id }, {}, { upsert: true, new: true }).lean()
        const merchant_functions = {
            shopee: shopee_menu,
            shopee_fresh: shopee_menu,
            grab: grab_menu,
            grab_mart: grab_menu,
        }
        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            if (String(category._id) === category_id) {
                for (const source of merchant_functions) {
                    const merchant_function = await merchant_functions[source]
                    await merchant_function.delete_menu_category(site, { category_name: category.name })
                }
                site_menu.categories.splice(c, 1)
                await menu_item_history_by_user(site_menu._id, 'User', 'category_deleted', category, null, category, req.user)
            }
        }
        await SiteMenuGroup.updateOne({ site_id: site._id }, { categories: site_menu.categories })

        res.json({
            success: true,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


router.delete_and_sync_menu_item_to_apps = async (req, res) => {
    try {
        const { site_id, item_id } = req.params;
        const site = await Site.findById(site_id)


        let site_menu = await SiteMenuGroup.findOneAndUpdate({ site_id: site._id }, {}, { upsert: true, new: true })

        for (let c = 0; c < site_menu.categories.length; c++) {
            const category = site_menu.categories[c];
            for (let i = 0; i < site_menu.categories[c].items.length; i++) {
                const item = site_menu.categories[c].items[i];
                if (String(item._id) === item_id) {
                    await Promise.all([
                        // grab_menu.delete_menu_category_item(grab_token, { category_name: category.name, item_name: item.name }),
                        // shopee_menu.sync_menu(shopee_token, { site_menu, item_id }),
                        // baemin_menu.sync_menu(baemin_token, { site_menu, item_id }),
                        // gojek_menu.sync_menu(gojek_token, { site_menu, item_id }),
                        // be_menu.sync_menu(be_token, { site_menu, item_id }),
                    ])
                    site_menu.categories[c].items.splice(i, 1)
                    // await menu_item_history_by_user(site_menu._id, 'User', 'item_deleted', category, null, item, req.user)
                }
            }
        }
        await SiteMenuGroup.updateOne({ site_id: site._id }, { categories: site_menu.categories })

        await site_menu.save()

        res.json({
            success: true,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


router.diff_menu_from_apps = async (req, res) => {
    try {
        const { site_id } = req.params;

        const site = await Site.findById(site_id)

        let site_menu = await SiteMenuGroup.findOne({ site_id: site._id }, {}, { upsert: true }).lean()
        site_menu.categories.sort((a, b) => a.name.localeCompare(b.name))
        site_menu.option_categories.sort((a, b) => a.name.localeCompare(b.name))
        site_menu.items = []
        site_menu.categories.map(c => {
            c.items.map((i, index) => {
                site_menu.items.push({
                    category_id: index === 0 ? c.id : '',
                    category_name: index === 0 ? c.name : '',
                    id: i.id,
                    name: i.name,
                    description: i.description,
                    price: i.price,
                    active: i.active,
                })
            })
        })

        for (const source of ['shopee', 'shopee_fresh', 'grab', 'grab_mart', 'be']) {
            site_menu[source + '_categories'].sort((a, b) => a.name.localeCompare(b.name))
            site_menu[source + '_option_categories'].sort((a, b) => a.name.localeCompare(b.name))
            site_menu[source + '_items'] = []
            site_menu[source + '_categories'].map(c => {
                c.items.map((i, index) => {
                    site_menu[source + '_items'].push({
                        category_id: index === 0 ? c.id : '',
                        category_name: index === 0 ? c.name : '',
                        id: i.id,
                        name: i.name,
                        description: i.description,
                        price: i.price,
                        active: i.active,
                    })
                })
            })
        }

        const templateFile = fs.readFileSync("files/CompareCategories.xlsx");
        const template = new XlsxTemplate(templateFile);
        template.substitute(1, site_menu);
        template.substitute(2, site_menu);
        const data = template.generate({ type: 'uint8array' });

        fs.writeFileSync(__dirname + "/output.xlsx", data);

        res.setHeader('Content-Disposition', `attachment; filename=Menu_Compare_${site.code}.xlsx`);
        // res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.sendFile(__dirname + "/output.xlsx");
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

const get_menu_app_with_stock = async (site, brand_menu, categories) => {
    const brand_menu_items = []
    brand_menu_items.push(...brand_menu?.categories?.flatMap(v => v.items))
    brand_menu_items.push(...brand_menu?.categories?.flatMap(v => v.sub_categories.flatMap(s => s.items)))

    const all_menu_items = _.uniqBy(brand_menu_items.filter(v => v.combo?.length > 0).flatMap(v => v.combo), 'code')
    const hub_stocks = await HubStock.find({ hub_id: site.hub_id, code: { $in: all_menu_items.map(v => v.code) } }).lean()
    const hub_stock_map = _.keyBy(hub_stocks, 'code')

    const categories_sheet_data = []
    for (const category of categories) {
        for (const item of category.items || []) {
            const brand_item = brand_menu.categories.map(v => v.items).flat().find(i => helper.text_slugify(i.name) === helper.text_slugify(item.name))
            if (brand_item) {
                const row_item = {
                    "Danh mục": category.name,
                    "Danh mục con": "",
                    "Tên món": item.name,
                    "Giá bán": item.price,
                    "Trạng thái": item.active ? 'Đang bật' : 'Đang tắt',
                    "Số lượng tối thiểu sẽ off món": brand_item.quantity_unlimited ? 0 : brand_item.quantity_minimum,
                    "Số lượng tồn kho": 0,
                    "Thời gian cập nhật tồn kho": '',
                    "Cấu hình tắt mở món": '',
                }

                let min_quantity = null
                if (brand_item.combo?.length > 0) {
                    for (let j = 0; j < brand_item.combo.length; j++) {
                        const stock = hub_stock_map[brand_item.combo[j].code]
                        if (stock) {
                            const item_qty = Math.floor((stock?.quantity || 0) / brand_item.combo[j].quantity * 100) / 100; // floor 2 decimal
                            if (min_quantity === null || item_qty < min_quantity) {
                                min_quantity = item_qty
                            }
                            row_item["Số lượng tồn kho"] = min_quantity
                            row_item["Thời gian cập nhật tồn kho"] = moment(stock.updated_at).format('YYYY-MM-DD HH:mm:ss')
                            row_item["Cấu hình tắt mở món"] = { 'use_stock': 'Theo tồn kho', 'alway_active': 'Luôn bật', 'alway_inactive': 'Luôn tắt' }[stock.locked_status] || 'Theo tồn kho'
                        }
                        row_item[`[SP ${j + 1}] Mã món`] = brand_item.combo[j].code
                        row_item[`[SP ${j + 1}] Định mức`] = brand_item.combo[j].quantity
                        row_item[`[SP ${j + 1}] Tồn kho`] = stock?.quantity || 0

                    }
                }
                categories_sheet_data.push(row_item)
            } else {
                const row_item = {
                    "Danh mục": category.name,
                    "Danh mục con": "",
                    "Tên món": item.name,
                    "Giá bán": item.price,
                    "Trạng thái": item.active ? 'Đang bật' : 'Đâng tắt' + ', chưa cấu hình trong brand',
                    "Số lượng tối thiểu sẽ off món": 0,
                    "Số lượng tồn kho": 0,
                    "Thời gian cập nhật tồn kho": '',
                    "Cấu hình tắt mở món": '',
                }

                categories_sheet_data.push(row_item)
            }
        }
        for (const sub_category of category.sub_categories || []) {
            for (const item of sub_category.items || []) {
                const brand_item = brand_menu.categories.map(v => v.items).flat().find(i => helper.text_slugify(i.name) === helper.text_slugify(item.name))
                if (brand_item) {
                    const row_item = {
                        "Danh mục": category.name,
                        "Danh mục con": sub_category.name,
                        "Tên món": item.name,
                        "Giá bán": item.price,
                        "Trạng thái": item.active ? 'Đang bật' : 'Đang tắt',
                        "Số lượng tối thiểu sẽ off món": brand_item.quantity_unlimited ? 0 : brand_item.quantity_minimum,
                        "Số lượng tồn kho": 0,
                        "Thời gian cập nhật tồn kho": '',
                        "Cấu hình tắt mở món": '',
                    }

                    let min_quantity = null
                    if (brand_item.combo?.length > 0) {
                        for (let j = 0; j < brand_item.combo.length; j++) {
                            const stock = hub_stock_map[brand_item.combo[j].code]
                            if (stock) {
                                const item_qty = Math.floor((stock?.quantity || 0) / brand_item.combo[j].quantity * 100) / 100; // floor 2 decimal
                                if (min_quantity === null || item_qty < min_quantity) {
                                    min_quantity = item_qty
                                }
                                row_item["Số lượng tồn kho"] = min_quantity
                                row_item["Thời gian cập nhật tồn kho"] = moment(stock.updated_at).format('YYYY-MM-DD HH:mm:ss')
                                row_item["Cấu hình tắt mở món"] = { 'use_stock': 'Theo tồn kho', 'alway_active': 'Luôn bật', 'alway_inactive': 'Luôn tắt' }[stock.locked_status] || 'Theo tồn kho'

                            }
                            row_item[`[SP ${j + 1}] Mã món`] = brand_item.combo[j].code
                            row_item[`[SP ${j + 1}] Định mức`] = brand_item.combo[j].quantity
                            row_item[`[SP ${j + 1}] Tồn kho`] = stock?.quantity || 0

                        }
                    }
                    categories_sheet_data.push(row_item)
                } else {
                    const row_item = {
                        "Danh mục": category.name,
                        "Danh mục con": sub_category.name,
                        "Tên món": item.name,
                        "Giá bán": item.price,
                        "Trạng thái": item.active ? 'Đang bật' : 'Đâng tắt' + ', chưa cấu hình trong brand',
                        "Số lượng tối thiểu sẽ off món": 0,
                        "Số lượng tồn kho": 0,
                        "Thời gian cập nhật tồn kho": '',
                        "Cấu hình tắt mở món": '',
                    }
                    categories_sheet_data.push(row_item)
                }
            }
        }

    }
    return categories_sheet_data

}
router.get_site_menu_app_with_stocks = async (req, res) => {
    const { site_id } = req.params;
    try {
        const site = await Site.findById(site_id)


        const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean()
        // get all menu from merchant apps


        const workbook = xlsx.utils.book_new();
        const merchants = [
            {
                source: 'grab',
                name: 'Grab',
                get_menu_func: grab.get_menu,
            },
            {
                source: 'grab_mart',
                name: 'Grab Mart',
                get_menu_func: grab.get_mart_menu,
            },
            {
                source: 'shopee',
                name: 'Shopee Food',
                get_menu_func: shopee.get_menu,
            },
            {
                source: 'shopee_fresh',
                name: 'Shopee Fresh',
                get_menu_func: shopee.get_menu,
            },
            // {
            //     source: 'gojek',
            //     name: 'Gojek',
            //     get_menu_func: gojek.get_menu,
            // },
            {
                source: 'be',
                name: 'BE',
                get_menu_func: be.get_menu,
            },
        ]

        for (const merchant of merchants) {
            let site_menu = {
                categories: [],
                option_categories: [],
            }
            const token = await token_account.get_token_by_site(site, merchant.source)
            const merchant_menu = await merchant.get_menu_func(token)
            const new_merchant_menu = map_menu(merchant.source, {
                categories: merchant_menu.categories,
                option_categories: merchant_menu.option_categories,
            })

            site_menu = await helper.compose_site_menu_item(site_menu, new_merchant_menu.categories)
            const app_stock = await get_menu_app_with_stock(site, brand_menu, site_menu.categories)

            if (app_stock.length === 0) {
                continue
            }
            const hub_stock_worksheet = xlsx.utils.json_to_sheet(app_stock);
            xlsx.utils.book_append_sheet(workbook, hub_stock_worksheet, merchant.name);
        }


        const buff = xlsx.write(workbook, { type: 'buffer' });

        const timestamp = moment().format('YYMMDDHHmm');
        const file = await upload_file({ bucket: 'nexpos-files', key: `site_stocks/${helper.text_slugify(site.name)}_${timestamp}.xlsx`, buff });
        res.json({
            success: true,
            data: file
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({ success: false, message: err.message });
    }
}

const menu_item_history_by_user = async (site_id, source, action_type, category, sub_category, item, user) => {
    if (user === null) {
        user = {
            name: 'Admin',
            email: 'Hệ Thống',
        }
    }

    let action_message = action_type;
    if (action_type === 'option_item_status_updated') {
        action_message = `[${source}] Tùy chọn món ${item.name} đã được ${item.active ? 'BẬT' : 'TẮT'}, số lượng: ${item.quantity} (min: ${item.quantity_minimum}) bởi ${user.name} [${user.email}]`;
    } else if (action_type === 'item_status_updated') {
        action_message = `[${source}] Món ${item.name} đã được ${item.active ? 'BẬT' : 'TẮT'}, số lượng: ${item.quantity} (min: ${item.quantity_minimum}) bởi ${user.name} [${user.email}]`
    } else if (action_type === 'item_deleted') {
        action_message = `[${source}] Món ${item.name} đã bị xóa trên ứng dụng bởi ${user.name} [${user.email}]`;
    } else if (action_type === 'item_synced') {
        action_message = `[${source}] Món ${item.name} đã được đồng bộ lên các ứng dụng bởi ${user.name} [${user.email}]`;
    } else if (action_type === 'category_deleted') {
        action_message = `[${source}] Danh mục ${category.name} đã được xóa bởi ${user.name} [${user.email}]`;
    }

    await SiteMenuGroupHistory.create({
        site_id,
        source,
        category_name: category?.name || '',
        sub_category_name: sub_category?.name || '',
        item_name: item?.name || '',
        action_type,
        action_message,
        action_data: item,
        action_by: `${user.name} [${user.email}]`,
    })
};

router._menu_item_history_by_user = menu_item_history_by_user

// Create a new site_menu_category category
router.create_update_site_menu_category = async (req, res) => {
    const { site_id } = req.params;
    const { categories, option_categories, action_type, category, sub_category, item } = req.body
    try {
        const site = await Site.findById(site_id)
        const merchant_functions = {
            grab: grab_menu,
            grab_mart: grab_menu,
            shopee: shopee_menu,
            shopee_fresh: shopee_menu,
            // gojek: gojek_menu,
            be: be_menu,
        }

        const site_menu = await SiteMenuGroup.findOneAndUpdate(
            { site_id },
            {
                categories, option_categories,
            },
            { upsert: true, new: true }
        )

        await menu_item_history_by_user(site_menu._id, 'User', action_type, category, sub_category, item, req.user)

        if (action_type === 'item_status_updated') {
            for (const [source, merchant_function] of Object.entries(merchant_functions)) {
                const token = await get_token_by_site(site, source)
                await merchant_function.active_menu_item(token, [{ category_name: category.name, name: item.name, active: item.active }], false)

            }
        }


        res.json({
            success: true,
            data: site_menu,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.update_site_menu_category_item = async (req, res) => {
    try {
        const { site_id } = req.params;
        const { category_id, sub_category_id, item_id } = req.query;

        if (!site_id || !category_id || !item_id) {
            return res.status(400).json({
                success: false,
                message: 'invalid_data',
            });
        }
        const { name, description, image, price, active } = req.body

        let filter = { site_id, 'categories.items._id': item_id }
        let update = {
            $set: {
                'categories.$[].items.$[item].price': price,
                'categories.$[].items.$[item].active': active,
            },
        }
        let options = { arrayFilters: [{ 'item._id': item_id }], returnNewDocument: true }
        if (sub_category_id) {
            filter = { site_id, 'categories.sub_categories.items._id': item_id };
            update = {
                $set: {
                    'categories.$[].sub_categories.$[].items.$[item].price': price,
                    'categories.$[].sub_categories.$[].items.$[item].active': active,
                },
            }
            options = { arrayFilters: [{ 'item._id': item_id }], returnNewDocument: true }
        }


        await SiteMenuGroup.findOneAndUpdate(filter, update, options);

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.get_menu_item_from_menu_brand = async (req, res) => {
    try {
        const { site_id, item_id } = req.params;
        const { category_id, sub_category_id } = req.query;

        if (!site_id || !category_id || !item_id) {
            return res.status(400).json({
                success: false,
                message: 'invalid_data',
            });
        }

        let filter = { site_id, 'categories.items._id': item_id }
        if (sub_category_id) {
            filter = { site_id, 'categories.sub_categories.items._id': item_id };
        }


        const data = await SiteMenuGroup.findOne(filter);

        res.json({ success: true, data });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

module.exports = router