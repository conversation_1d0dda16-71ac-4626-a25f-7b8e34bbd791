const _ = require('lodash')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { VendorCallback, OrderPayment, Order, Site, Brand } = require('../../.shared/database')
const momo = require('../../.shared/payment/momo');
const { map_order } = require('../../.shared/merchant/mapping');
const { send_zalo_message, ZALO_GROUPS, send_zalo_message_by_order_id } = require('../../.shared/zalo');
const { format_curency } = require('../../.shared/helper');

const router = {}

router.momo_client_callback = async (req, res, next) => {
    const { orderId, resultCode, callback_url } = req.query
    const payment = await OrderPayment.findOne({ transaction_id: orderId })
    const order = await Order.findOne({ order_id: payment.order_id })
    const site = await Site.findById(order.site_id)

    const _callback_url = callback_url ?? `${process.env.STORE_WEB_URL}/${site.code}/payment`

    await VendorCallback.create({
        vendor: 'momo',
        type: 'order',
        url: req.originalUrl,
        method: req.method,
        headers: req.headers,
        success: true,
        request_data: req.query,
        response_data: { success: true },
    })

    let success = (resultCode === '0' || resultCode === '9000')

    res.redirect(`${_callback_url}?success=${success}&order_id=${order.order_id}&payment_method=MOMO`)
}


router.momo_server_callback = async (req, res, next) => {
    await VendorCallback.create({
        vendor: 'momo',
        type: 'order',
        url: req.originalUrl,
        method: req.method,
        headers: req.headers,
        success: true,
        request_data: req.body,
        response_data: { success: true },
    })

    const { orderId, resultCode } = req.body
    const payment = await OrderPayment.findOne({ transaction_id: orderId })
    console.log({ payment })
    const order = await Order.findOne({ order_id: payment.order_id })
    const site = await Site.findById(order.site_id)
    const brand = await Brand.findById(site.brand_id)

    let momo_token = site.getToken('momo')
    if (!momo_token?.username) {
        momo_token = brand.getToken('momo')
    }

    if (resultCode === 0 || resultCode === 9000) {
        payment.callback_data = req.body
        payment.status = 'COMPLETED'
        if (order.status === 'WAITING_PAYMENT') {
            const auto_confirm_order = !site.hub_ids?.length && order.source === 'local'
            order.status = auto_confirm_order ? 'DOING' : 'PENDING'
            // // Send message to zalo group in site or hub configured
            // await send_zalo_message_by_order_id({
            //     order_id: order.order_id,
            //     message: [
            //         `Bạn có đơn hàng mới <b>${order.order_id}</b>,`,
            //         `Khách hàng: ${order.data_mapping.customer_name}, ${order.data_mapping.customer_address},`,
            //         `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
            //     ].join('\n'),
            // }).catch(console.log)

        }

        order.data.payments.forEach(p => { p.status = 'COMPLETED'; p.note = 'Payment with MOMO' })
        order.data.last_transaction = payment
        order.data_mapping = map_order(order.source, order.data)
        order.markModified('data')
        order.markModified('data_mapping')
        if (order.shipment.vendor === 'take_away') {
            order.status = 'FINISH'
        }
        await payment.save()
        await order.save()
    }

    res.json({ success: true })
}



router.payos_client_callback = async (req, res, next) => {
    const { id, code, status, callback_url } = req.query
    const payment = await OrderPayment.findOne({ transaction_id: id })
    const order = await Order.findOne({ order_id: payment.order_id })
    const site = await Site.findById(order.site_id)

    const _callback_url = callback_url ?? `${process.env.STORE_WEB_URL}/${site.code}/payment`

    let success = false
    if (code === '00' && status === 'CANCELLED') {
        payment.status = 'CANCELLED'
        await payment.save()
    }

    if (code === '00' && status === 'PAID') {
        success = true
        payment.callback_data = req.query
        payment.status = 'COMPLETED'
        if (order.status === 'WAITING_PAYMENT') {
            const auto_confirm_order = !site.hub_ids?.length && order.source === 'local'
            order.status = auto_confirm_order ? 'DOING' : 'PENDING'
            // Send message to zalo group in site or hub configured
            // await send_zalo_message_by_order_id({
            //     order_id: order.order_id,
            //     message: [
            //         `Bạn có đơn hàng mới <b>${order.order_id}</b>,`,
            //         `Khách hàng: ${order.data_mapping.customer_name}, ${order.data_mapping.customer_address},`,
            //         `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
            //     ].join('\n'),
            // }).catch(console.log)
        }

        order.data_mapping.payments.forEach(p => { p.status = 'COMPLETED'; p.note = 'Payment with PAYOS' })
        order.markModified('data_mapping')

        await payment.save()
        await order.save()
    }
    res.redirect(`${_callback_url}?success=${success}&order_id=${order.order_id}&payment_method=PAYOS`)
}


router.payos_server_callback = async (req, res, next) => {
    await VendorCallback.create({
        vendor: 'payos',
        type: 'order',
        url: req.originalUrl,
        method: req.method,
        headers: req.headers,
        success: true,
        request_data: req.body,
        response_data: { success: true },
    })
    const { code, data } = req.body
    if (code === '00') {
        const { orderCode, amount, paymentLinkId, signature } = data
        // TODO: check signature and update order status
    }
    res.json({ success: true })
}

router.nexdorpay_client_callback = async (req, res, next) => {
    const { tx, success, callback_url } = req.query
    const payment = await OrderPayment.findOne({ transaction_id: tx })
    const order = await Order.findOne({ order_id: payment.order_id })
    const site = await Site.findById(order.site_id)
    const _callback_url = callback_url ?? `${process.env.STORE_WEB_URL}/${site.code}/payment`

    res.redirect(`${_callback_url}?success=${success}&order_id=${order.order_id}&payment_method=NEXDORPAY`)
}


router.nexdorpay_server_callback = async (req, res, next) => {
    await VendorCallback.create({
        vendor: 'nexdorpay',
        type: 'order',
        url: req.originalUrl,
        method: req.method,
        headers: req.headers,
        success: true,
        request_data: req.body,
        response_data: { success: true },
    })

    const { transaction_id, partner_transaction_id, status, callback_data } = req.body

    if (status === 'COMPLETED') {
        const payment = await OrderPayment.findOne({ transaction_id })
        if (!payment) {
            return res.json({ success: false, message: 'Payment not found' })
        }
        const order = await Order.findOne({ order_id: payment.order_id })
        const site = await Site.findById(order.site_id)

        if (payment.status !== 'COMPLETED') {
            payment.callback_data = callback_data
            payment.status = 'COMPLETED'
            payment.partner_transaction_id = callback_data?.transaction_id
            payment.amount = callback_data?.amount
            payment.description = `Đã nhận thanh toán số tiền: ${format_curency(callback_data?.amount)}, mã giao dịch: ${callback_data?.transaction_id}, ${callback_data?.description}`
            if (order.status === 'WAITING_PAYMENT') {
                const auto_confirm_order = !site.hub_ids?.length && order.source === 'local'
                order.status = auto_confirm_order ? 'DOING' : 'PENDING'
                // Send message to zalo group in site or hub configured
                // await send_zalo_message_by_order_id({
                //     order_id: order.order_id,
                //     message: [
                //         `Bạn có đơn hàng mới <b>${order.order_id}</b>,`,
                //         `Khách hàng: ${order.data_mapping.customer_name}, ${order.data_mapping.customer_address},`,
                //         `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
                //     ].join('\n'),
                // }).catch(console.log)
            }

            order.data.payments.forEach(p => {
                if (p.method === 'NEXDORPAY') {
                    p.status = 'COMPLETED';
                    p.note = payment.description;
                    p.code = callback_data?.transaction_id;
                }
            })
            order.data_mapping.payments = order.data.payments
            order.markModified('data')
            order.markModified('data_mapping')
            if (order.shipment.vendor === 'take_away') {
                order.status = 'FINISH'
            }
            await payment.save()
            await order.save()

            await send_zalo_message_by_order_id({
                order_id: order.order_id,
                message: [
                    `Đơn hàng <bc style="color:#0000ff">ĐƠN HÀNG ${order.order_id} ĐÃ ĐƯỢC THANH TOÁN</bc>,`,
                    `Số tiền: ${format_curency(callback_data.amount)}`,
                    `Mã giao dịch: ${callback_data.transaction_id}`,
                    `Vui lòng kiểm tra chi tiết đơn hàng tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
                ].join('\n'),
            })

        }
    } else if (status === 'UNSETTLED') {
        await OrderPayment.findOneAndUpdate({ partner_transaction_id }, {
            vendor: 'nexdorpay',
            order_id: '',
            transaction_id: '',
            partner_transaction_id: partner_transaction_id,
            amount: callback_data?.amount,
            description: `Đã nhận thanh toán số tiền: ${format_curency(callback_data?.amount)}, mã giao dịch: ${partner_transaction_id}, ${callback_data?.description}`,
            currency: 'VND',
            status: 'COMPLETED',
            callback_data,
        }, { upsert: true })

    } else {
        payment.status = 'CANCELLED'
        await payment.save()
    }

    res.json({ success: true })
}


module.exports = router