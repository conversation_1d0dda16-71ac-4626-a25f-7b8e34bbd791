const { User, ItTicket, ItTicketCounter, UserNotification } = require('../../.shared/database')
const { create_it_ticket_jira } = require('../../.shared/jira')
const { send_slack_msg_as_bot } = require('../../.shared/slack')
const moment = require('moment')

const populate_users = async (tickets) => {
  const userIds = tickets
    .reduce(
      (acc, cur) => [
        ...acc,
        cur.reporter_id,
        cur.assignee_id,
        ...(cur.comments?.map((cmt) => cmt.user_id) || []),
        ...(cur.histories?.map((his) => his.user_id) || []),
        ...(cur.histories?.map((his) => his.assigned_to) || []),
      ],
      []
    )
    .filter((id) => id)

  const users = await User.find({ _id: { $in: userIds } }, { email: 1, name: 1, _id: 1, role_id: 1 }).then((res) => res.reduce((acc, cur) => ({ ...acc, [cur._id]: cur.toObject() }), {}))

  return tickets.map((ticket) => ({
    ...ticket.toObject(),
    reporter: ticket.reporter_id ? users[ticket.reporter_id] : null,
    assignee: ticket.assignee_id ? users[ticket.assignee_id] : null,
    comments: ticket.comments?.map((cmt) => ({ ...cmt.toObject(), user: users[cmt.user_id] })) || [],
    histories: ticket.histories?.map((his) => ({ ...his.toObject(), user: users[his.user_id], assigned_to: users[his.assigned_to] })),
  }))
}

exports.get_tickets = async (req, res) => {
  const { status, priority, assignee_id, category, search } = req.query

  const has_full_permission = req.permissions?.includes('it_support') || req.user.allow_all_brands

  const query = {
    ...(status && { status }),
    ...(priority && { priority }),
    ...(assignee_id && { assignee_id }),
    ...(category && { category }),
  }

  if (!has_full_permission) {
    query.reporter_id = String(req.user._id)
  }

  if (search) {
    query.$or = [{ code: { $regex: search, $options: 'i' } }]
  }

  const result = await ItTicket.paginate(query, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 100), // Max 100 docs
    sort: { [req.query.sort || 'created_at']: req.query.order || 'desc' },
    customLabels: { docs: 'data' },
    projection: {
      comments: 0,
      histories: 0,
    },
  })

  // const populated = await populate_users(result.data)
  res.json({
    success: true,
    ...result,
    data: result.data,
  })
}

async function _getNextSequence() {
  const ret = await ItTicketCounter.findOneAndUpdate({}, { $inc: { seq: 1 } }, { new: true, upsert: true })

  return String(ret.seq).padStart(4, '0')
}

exports.get_ticket = async (req, res) => {
  const code = req.params.code
  const result = await ItTicket.findOne({ code })
  if (!result) {
    return res.status(404).json({ success: false, message: 'it_ticket_not_found' })
  }

  const [data] = await populate_users([result])

  res.json({
    success: true,
    data,
  })
}

exports.create_ticket = async (req, res) => {
  const { description, images, category, priority, title, promise_completed_at } = req.body
  const code = await _getNextSequence()

  const result = await ItTicket.create({
    category,
    title,
    code,
    description,
    images,
    status: 'pending',
    reporter_id: req.user._id,
    priority,
    histories: [
      {
        user_id: req.user._id,
        action: 'create',
      },
    ],
    promise_completed_at: promise_completed_at ? moment(promise_completed_at).toDate() : moment().add(4, 'hours').toDate(),
  })

  const jira_ticket = await create_it_ticket_jira({
    title: result.title,
    description: result.description,
    ticket_code: result.code,
  })

  const slack_thread_ts = await send_slack_msg_as_bot({
    channel: 'C05CGC269D0', // slack "it-support" channel
    text: ` New It ticket: ${code}. Title: ${title}. Category: ${category}`,
    block: description,
  })

  await result.updateOne({ slack_thread_ts, jira_ticket })
  const [data] = await populate_users([result])

  res.json({
    success: true,
    data,
  })
}

exports.update_ticket = async (req, res) => {
  const { status, priority, assignee_id, note, title, promise_completed_at } = req.body
  const code = req.params.code

  const ticket = await ItTicket.findOne({ code })
  if (!ticket) {
    return res.status(404).json({ success: false, message: 'it_ticket_not_found' })
  }

  const history = {
    user_id: req.user._id,
    note,
    created_at: new Date(),
  }

  if (status) {
    history.action = 'change_status'
    history.new_status = status
    if (status === 'fixed') {
      ticket.fixed_at = new Date()
    }
  }

  if (priority) {
    history.action = 'change_priority'
    history.new_priority = priority
  }

  if (assignee_id) {
    history.action = 'assign'
    history.assigned_to = assignee_id
  }

  if (promise_completed_at) {
    history.promise_completed_at = promise_completed_at
  }

  const result = await ItTicket.findOneAndUpdate(
    { code },
    {
      priority,
      status,
      assignee_id,
      title,
      $push: {
        histories: history,
      },
    },
    { new: true, runValidators: true }
  )

  if (status && status === 'doing') {
    await UserNotification.create({
      user_id: ticket.reporter_id,
      type: 'success',
      title: '[Hỗ trợ] Yêu cầu đang được xử lý',
      content: `Yêu cầu hỗ trợ #${ticket.code} của bạn đang được xử lý`,
      topic: 'it_ticket',
      content_data: {
        code: ticket.code,
      }
    })
  }

  if (status && status === 'fixed') {
    await UserNotification.create({
      user_id: ticket.reporter_id,
      type: 'success',
      title: '[Hỗ trợ] Yêu cầu đã xử lý',
      content: `Yêu cầu hỗ trợ #${ticket.code} của bạn đã được xử lý`,
      topic: 'it_ticket',
      content_data: {
        code: ticket.code,
      }
    })
  }

  const [data] = await populate_users([result])

  res.json({
    success: true,
    data,
  })
}

exports.add_comment = async (req, res) => {
  const userId = String(req.user._id)

  const { content, attachments } = req.body
  const code = req.params.code

  const comment = { content, attachments, user_id: userId, created_at: new Date() }

  const ticket = await ItTicket.findOneAndUpdate({ code }, { $push: { comments: comment } }, { new: true, runValidators: true })
  const [data] = await populate_users([ticket])

  // send notification to assignee, reporter
  const { assignee_id, reporter_id } = ticket
  const notify_users = [assignee_id, reporter_id].filter((id) => id && id !== userId)

  for (const user_id of notify_users) {
    await UserNotification.create({
      user_id,
      type: 'info',
      title: `[Hỗ trợ] #${ticket.code} có bình luận mới từ ${req.user.email}`,
      content: content,
      topic: 'it_ticket',
      content_data: {
        code: ticket.code,
      },
    })
  }

  // if reporter comment, send slack msg as reply
  if (userId === reporter_id) {
    const slack_thread_ts = ticket.slack_thread_ts
    if (slack_thread_ts) {
      await send_slack_msg_as_bot({
        channel: 'C05CGC269D0',
        text: `Reporter commented on ticket: ${code}`,
        block: content,
        thread_ts: slack_thread_ts,
      })
    }
  }

  res.json({
    success: true,
    data,
  })
}

exports.create_jira_ticket = async (req, res) => {
  const { code } = req.params
  const ticket = await ItTicket.findOne({ code })
  if (!ticket) {
    return res.status(404).json({ success: false, message: 'it_ticket_not_found' })
  }
  if (ticket.jira_ticket) {
    return res.status(400).json({ success: false, message: 'jira_ticket_already_created' })
  }

  const jira_ticket = await create_it_ticket_jira({
    title: ticket.title,
    description: ticket.description,
    ticket_code: ticket.code,
  })

  await ticket.updateOne({ jira_ticket: jira_ticket })
  return res.json({
    success: true,
    data: jira_ticket,
  })
}

exports.delete_ticket = async (req, res) => {
  await ItTicket.findOneAndDelete({ code: req.params.code })
  res.json({
    success: true,
  })
}

exports.cron_update_status = async (req, res) => {
  // after a days, update fixed ticket to completed
  const tickets = await ItTicket.find({ status: 'fixed', fixed_at: { $lt: new Date(Date.now() - 24 * 3600 * 1000) } })

  for (const ticket of tickets) {
    await ticket.updateOne({
      status: 'completed',
      $push: {
        histories: {
          user_id: null,
          action: 'change_status',
          new_status: 'completed',
        },
      },
    })

    await UserNotification.create({
      user_id: ticket.reporter_id,
      type: 'success',
      title: '[Hỗ trợ] Yêu cầu đã hoàn thành',
      content: `Yêu cầu hỗ trợ #${ticket.code} của bạn đã hoàn thành`,
      topic: 'it_ticket',
      content_data: {
        code: ticket.code,
      },
    })
  }

  res.json({
    success: true,
  })
}
