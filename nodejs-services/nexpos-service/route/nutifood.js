const _ = require('lodash')
const fs = require('fs')
const moment = require('moment');
const { Site, Order, User, PrintQueue, SiteOrderIndex, Hub, Brand, BrandMenu, GoogleSheetFile, VendorHubStock, HubStock } = require('../../.shared/database')
const { send_zalo_message, ZALO_GROUPS } = require('../../.shared/zalo')
const helper = require('../../.shared/helper')
const nutifood = require('../../.shared/merchant/nutifood')
const redis = require('../../.shared/redis');

const router = {}

router.get_hub_stocks = async (req, res) => {
    const hubs = await Hub.find({
        status: 'active',
        inventory_source: 'nutifood',
        last_cron_stock: { $lt: moment().add(-10, 'minutes').toISOString() },
    }).limit(5).sort({ last_cron_stock: 1 })
    if (!hubs)
        return res.json({ success: false, message: 'No hub found' })

    await Hub.updateMany({ _id: hubs.map(v => String(v._id)) }, { last_cron_stock: moment().toISOString() })

    const file = await GoogleSheetFile.findOne({ file_id: "15sJPu1TNTVzsS-LEraV95jyockeZgfhaXoeL19Ndwv8" })
    const all_items = file.sheet_data.MD_menu.filter(v =>
        [
            'NutiFood GrowPLUS+',
            'Nutifood NNDD',
            'NutiFood NutiMilk',
            'NutiFood Bliss',
            'NutiFood Varna',
        ].includes(v.brand)
    )
    const all_item_codes = all_items.map(v => v.item1_code)
    const unique_item_codes = _.uniq(all_item_codes)
    const stock_resp = await nutifood.get_menu_items_v2({ shops: hubs.map(v => v.code), items: unique_item_codes })

    for (const hub of hubs) {

        let success = false
        let hub_menu_items = []

        success = stock_resp.success
        hub_menu_items = stock_resp.menu_items.filter(v => v.shop_code === hub.code)


        // const stock_resp = await nutifood.get_menu_items({ shop: hub.code, items: unique_item_codes })
        // success = stock_resp.success
        // hub_menu_items = stock_resp.menu_items

        if (!success) {
            continue
        }

        if (!hub_menu_items.length) {
            continue
        }

        await VendorHubStock.findOneAndUpdate({ hub_id: hub._id, vendor: 'nutifood' }, {
            request_codes: unique_item_codes,
            stocks: hub_menu_items.map(v => ({
                code: v.item_code,
                name: v.item_name,
                quantity: v.quantity,
                quantity_history: [] // TODO scan update and add history
            })),
        }, { upsert: true })

        await HubStock.deleteMany({ hub_id: hub._id, code: { $nin: unique_item_codes } })
        const hub_stocks = await HubStock.find({ hub_id: hub._id })
        for (const item_code of unique_item_codes) {
            const menu_item = all_items.find(v => v.item1_code === item_code)
            const stock = hub_menu_items.find(v => v.item_code === item_code)
            const hub_stock = hub_stocks.find(v => v.code === item_code)
            if (!hub_stock) {
                await HubStock.create({
                    hub_id: hub._id,
                    code: item_code,
                    name: menu_item?.item1_name,
                    quantity: stock.quantity,
                    locked_status: 'use_stock',
                })
            } else if (hub_stock.quantity !== stock.quantity) {
                hub_stock.quantity = stock.quantity
                await hub_stock.save()
            }
            // await HubStock.updateMany({ code: item_code }, { name: menu_item?.item1_name })
        }
    }
    return res.json({ success: true, data: hubs.map(v => v.name) })
}

// router.get_hub_stocks(null, { json: console.log })

router.sync_orders = async (req, res, next) => {
    const hubs = await Hub.find({ inventory_source: 'nutifood' })
    const sites = await Site.find({ hub_id: hubs.map(v => String(v._id)) })
    const orders = await Order.find({
        site_id: sites.map(v => String(v._id)),
        $and: [{
            $or: [
                { vendor_sync: null },
                {
                    vendor_sync: { $ne: null },
                    'vendor_sync.success': false,
                    vendor_sync_retry: { $lt: 3 },
                },
            ]
        }, {
            $or: [
                { source: { $in: ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'gojek', 'be'] } },
                { source: 'local', status: { $in: ['DOING', 'PICK', 'FINISH'] } },
            ]
        }],
        created_at: { $gte: moment().add(-2, 'months').toDate() },
    })

    await Order.updateMany({ _id: orders.map(v => String(v._id)) }, { vendor_sync: { at: new Date() } })

    for (const order of orders) {
        const sync_order_resp = await nutifood.sync_an_order_v2(order)
        if (!sync_order_resp.success) {
            // await send_slack_message({ channel: "C06356A3PM3", text: sync_order_resp.message, block: 'Thất bại' })
            await send_zalo_message({ group_link: ZALO_GROUPS.ORDER_SYSNCING_NOTIFICATION, message: sync_order_resp.message })
        }

        const site = sites.find(v => String(v._id) == String(order.site_id))
        const brand_menu = await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, {
            $setOnInsert: {
                categories: [],
                option_categories: [],
            }
        }, { upsert: true, new: true }).lean()

        const { dishes } = helper.add_stock_to_dishes(order.data_mapping.dishes, brand_menu)
        order.data_mapping.dishes = order.data.stock_dishes = dishes
        await Order.findByIdAndUpdate(order._id, {
            data_mapping: order.data_mapping,
            data: order.data,
            vendor_sync: {
                at: new Date(),
                retry: 1,
                success: sync_order_resp.success,
                message: sync_order_resp.message,
                callback: sync_order_resp.data,
            },
            vendor_sync_retry: order.vendor_sync_retry + 1,
        })
    }

    res.json({
        success: true,
    })
}

module.exports = router