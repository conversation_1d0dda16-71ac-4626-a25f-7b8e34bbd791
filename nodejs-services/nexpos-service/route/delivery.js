const { Order, VendorCallback, Site, OrderShipment, Voucher } = require("../../.shared/database");
const moment = require('moment');
const _ = require('lodash');
const { map_order } = require("../../.shared/merchant/mapping");
const momo_mini = require("./momo_mini");
const ahamove = require('../../.shared/delivery/ahamove');
const shopee = require('../../.shared/merchant/shopee');
const ghn = require('../../.shared/delivery/ghn');
const grab_express = require('../../.shared/delivery/grab_express');
const viettel_post = require('../../.shared/delivery/viettel_post');
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const { get_site_order_by_source } = require('../../.shared/cron')
const { json_to_md5 } = require('../../.shared/crypto');
const { get_token_by_site } = require("../../.shared/token_account");
const voucher = require('./voucher');

const _update_order_if_momo_mini = async (order) => {
    if (order.source === 'momo') {
        await momo_mini.update_order_status({
            order_id: order.order_id,
            order_status: order.status,
            site_id: order.site_id,
            driver_name: order.data.driver_name,
            driver_phone: order.data.driver_phone,
        });
    }
}

exports.update_order_status_webhook = async (req, res) => {
    const { partner_id } = req.params;
    const partner_map = {
        ahamove: {
            webhook_name: 'order',
            webhook_func: ahamove.webhook,
        },
        grab_express: {
            webhook_name: 'order',
            webhook_func: grab_express.webhook,
        },
        ghn: {
            webhook_name: 'order',
            webhook_func: ghn.webhook,
        },
        viettel_post: {
            webhook_name: 'order',
            webhook_func: viettel_post.webhook,
        },
    }

    const delivery_merchant = partner_map[partner_id]

    if (delivery_merchant) {
        await VendorCallback.create({
            vendor: partner_id,
            type: delivery_merchant.webhook_name,
            headers: req.headers,
            request_data: req.body,
            url: req.url,
            method: req.method,
        })

        const webhook_data = await delivery_merchant.webhook_func(req.body)

        const order_filter = webhook_data.shipment_id ?
            { 'shipment.shipment_id': webhook_data.shipment_id } :
            { order_id: webhook_data.order_id }

        const order_shipment = await OrderShipment.findOneAndUpdate({ vendor: partner_id, shipment_id: webhook_data.shipment_id }, {}, { upsert: true, })

        order_shipment.status = webhook_data.shipment_status;

        if (webhook_data.tracking_url) {
            order_shipment.tracking_url = webhook_data.tracking_url
        }
        if (webhook_data.driver_name) {
            order_shipment.driver_name = webhook_data.driver_name;
            order_shipment.driver_phone = webhook_data.driver_phone;
        }

        if (webhook_data.shipment_status === 'CANCELLED' && webhook_data.cancel) {
            order_shipment.cancel_by = webhook_data.cancel?.cancel_by;
            order_shipment.cancel_reason = webhook_data.cancel?.cancel_reason;
        }

        order_shipment.webhooks.push(req.body)
        await order_shipment.save();


        const order = await Order.findOne(order_filter)
        if (!order) {
            // Skip old shipment
            return res.status(200).json({
                success: true,
            });
        }


        if (webhook_data.order_status !== '' || order.status !== 'FINISH') {
            order.status = webhook_data.order_status;
        }

        order.shipment.status = webhook_data.shipment_status;

        if (webhook_data.tracking_url) {
            order.shipment.tracking_url = webhook_data.tracking_url
        }
        if (webhook_data.driver_name) {
            order.data.driver_name = order.data_mapping.driver_name = webhook_data.driver_name;
            order.data.driver_phone = order.data_mapping.driver_phone = webhook_data.driver_phone;
        }

        if (order.status === 'PICK') {
            order.data.pick_time = order.data_mapping.pick_time = moment().toISOString();
        }

        if (order.status === 'FINISH') {
            order.data.delivery_time = order.data_mapping.delivery_time = moment().toISOString();
            order.data.delivery_time_unix = order.data_mapping.delivery_time_unix = moment().unix();
        }

        if (webhook_data.shipment_status === 'CANCELLED' && webhook_data.cancel) {
            order.data.cancel_by = order.data_mapping.cancel_by = webhook_data.cancel?.cancel_by;
            order.data.cancel_reason = order.data_mapping.cancel_reason = webhook_data.cancel?.cancel_reason;
        }

        order.markModified('data');
        order.markModified('data_mapping');
        order.markModified('shipment');

        if(order.status === 'FINISH') {
            const result =  await voucher.send_completed_order({
                order_id: order.order_id,
                external_id: order.external_id,
                phone_number: order.data_mapping.customer_phone,
                total: order.data_mapping.total_for_biz,
                discount: order.voucher?.discount || 0,
                delivery_on: order.data_mapping.delivery_time,
                voucher_code: order.voucher?.code,
            }, order.site_id);
            order.dpoint_sync = result;
        }

        await order.save();

        await _update_order_if_momo_mini(order);
        if (webhook_data.cancel) {
            await send_zalo_message_by_order_id({
                order_id: order.order_id,
                message: [
                    `Vận đơn đơn hàng của KH <b>${order.data_mapping.customer_name}</b> đã bị hủy bởi: ${webhook_data.cancel.cancel_by},`,
                    `Lý do hủy: ${webhook_data.cancel.cancel_reason}`,
                    `Vận đơn: ${order.shipment.tracking_url || ''}`,
                    `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
                ].join('\n'),
            })
        }
        
        return res.status(200).json({
            success: true,
        });
    }

    return res.status(200).json({
        success: true,
    });
}