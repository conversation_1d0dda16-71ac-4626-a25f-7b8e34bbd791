const { Site, Order, HubStock, HubStockHistory, CoreProduct, SiteMenuItem } = require('../../.shared/database')
const { build_menu, get_stock_for_cp_item } = require('../common/core-product')
const grab = require('./cp_grab_partner')

const sync_orders_stock = async (req, res) => {
  const sites = await Site.find({ use_core_product: true }, { _id: 1, brand_id: 1 }).lean()

  const orders = await Order.find({
    site_id: sites.map((v) => String(v._id)),
    status: 'FINISH',
    stock_sync: { $exists: false },
  })
    .sort({ 'data_mapping.delivery_time_unix': 1 })
    .limit(50)
    .lean()

  // update to PROCESSING to prevent duplicate
  await Order.updateMany(
    {
      _id: { $in: orders.map((o) => o._id) },
    },
    {
      stock_sync: {
        status: 'PROCESSING',
        detail: { at: new Date() },
      },
    }
  )

  for (const order of orders) {
    const detail = {}

    const dishes_and_options = order.data_mapping.dishes
      .map((d) => {
        const options = d.options?.map((o) => ({ code: o.code, quantity: o.quantity })) || []

        return [{ code: d.code, quantity: d.quantity }, ...options]
      })
      .flat()
      .reduce((acc, d) => {
        acc[d.code] = acc[d.code] || 0
        acc[d.code] += d.quantity
        return acc
      }, {})

    const core_products = await CoreProduct.find({
      code: { $in: Object.keys(dishes_and_options) },
    })

    for (const core_product of core_products) {
      if(core_product.quantity_unlimited) {
        continue
      }

      const ingredients = core_product.ingredients?.length
        ? core_product.ingredients
        : [
            {
              code: core_product.code,
              amount: 1,
            },
          ]

      const hub_stocks = await HubStock.find({
        code: { $in: ingredients.map((i) => i.code) },
        locked_status: 'use_stock',
        hub_id: order.hub_id,
      })

      for (const hub_stock of hub_stocks) {
        const ingredient = ingredients.find((i) => i.code == hub_stock.code)
        const ordered_quantity = dishes_and_options[core_product.code] || 0
        hub_stock.quantity -= ingredient.amount * ordered_quantity
        await hub_stock.save()

        await HubStockHistory.create({
          hub_id: hub_stock.hub_id,
          code: hub_stock.code,
          from_quantity: hub_stock.quantity + ingredient.amount * ordered_quantity,
          to_quantity: hub_stock.quantity,
          updated_type: 'order',
          updated_order_id: order.order_id,
        })
      }
    }

    await Order.findByIdAndUpdate(order._id, {
      stock_sync: {
        status: 'SUCCESS',
        detail: { ...detail, at: new Date() },
      },
    })
  }

  // FIXME: use async job instead
  const hub_ids = orders.map((o) => o.hub_id)
  await Promise.all(hub_ids.map((hub_id) => sync_hub_stock_to_site_menu(hub_id)))

  res.json({
    success: true,
    data: {
      orders: orders.map((o) => o.order_id),
    },
  })
}

const sync_hub_stock_to_site_menu = async (hub_id) => {
  const hub_stocks = await HubStock.find({ hub_id })

  const sites = await Site.find({ hub_id, use_core_product: true })
  for (const site of sites) {
    await sync_stock_to_site_menu(site, hub_stocks)
    await sync_site_stock_to_platform(site)
  }
}

const sync_stock_to_site_menu = async (site, hub_stocks) => {
  const core_products = await CoreProduct.find(
    {
      brand_id: site.brand_id,
      status: 'active',
    },
    { code: 1, quantity_unlimited: 1 }
  )

  const site_menu_items = await SiteMenuItem.find({ site_id: String(site._id) })
  for (const item of site_menu_items) {
    const stock = get_stock_for_cp_item(item, hub_stocks, core_products)
    await SiteMenuItem.updateOne(
      { site_id: String(site._id), code: item.code },
      {
        $set: {
          quantity: stock.quantity,
          quantity_unlimited: stock.quantity_unlimited,
        },
      }
    )
  }
}

const sync_site_stock_to_platform = async (site) => {
  if (site.tokens.find((t) => t.vendor === 'grab_food')) {
    await grab.invalidate_menu(site)
  }
}

module.exports = {
  sync_orders_stock,
  build_menu,
  sync_stock_to_site_menu,
  sync_site_stock_to_platform,
}
