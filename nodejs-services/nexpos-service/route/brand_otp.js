const { Brand, CampaignUser, UserOTP } = require('../../.shared/database')
const moment = require('moment-timezone')
const { flatMap, filter, map, find } = require('lodash')
const PhoneNumber = require('libphonenumber-js');
const crypto = require('crypto');
const zalo = require('../../.shared/merchant/zalo')

// Send or resend OTP to user
exports.send_phone_otp = async (req, res) => {
  const { brand_id } = req.params
  let { receive_method, verify_type, phone } = req.body
  receive_method = receive_method || 'zalo'
  verify_type = verify_type || 'verify_phone'

  const phone_number = PhoneNumber(phone, 'VN')
  const parsed_phone = phone_number.format('E.164') // +84123456789
  const otp = Math.floor(100000 + Math.random() * 900000).toString()

  const user_otp = await UserOTP.findOne({
    receive_method,
    verify_type,
    user_uid: parsed_phone,
  })

  if (user_otp && moment(moment()).diff(user_otp.updated_at, 'minutes') < 1) {
    return res.status(400).json({
      success: false,
      error: 'too_many_otp_sent',
    })
  }
  await UserOTP.findOneAndUpdate({
    receive_method,
    verify_type,
    user_uid: parsed_phone,
  }, {
    otp: otp,
    expired_at: moment().add(15, 'minutes').toDate(),
  }, { upsert: true })


  if (process.env.USE_MERCHANT_APPS === "true") {
    const brand = await Brand.findById(brand_id)
    const zalo_token = brand.getToken('zalo')
    if (!zalo_token?.access_token || !zalo_token?.site_data) {
      return res.status(400).json({
        success: false,
        error: 'zalo_token_not_configured',
      })
    }
    const zalo_otp_zns_id = zalo_token?.site_data?.zalo_otp_zns_id

    await zalo.send_template_message_to_phone(zalo_token, {
      phone: parsed_phone.replace('+', ''),
      template_id: zalo_otp_zns_id,
      template_data: { otp },
    })

    res.json({
      success: true,
    })
  } else {
    res.json({
      success: true,
      data: {
        otp, // For testing
      }
    })
  }

}

exports.verify_phone_otp = async (req, res) => {
  const { brand_id } = req.params
  const { receive_method, verify_type, phone, otp } = req.body;

  const phone_number = PhoneNumber(phone, 'VN');
  const parsed_phone = phone_number.format('E.164'); // +84123456789

  const user_otp = await UserOTP.findOne({
    receive_method: receive_method || 'zalo',
    verify_type: verify_type || 'verify_phone',
    user_uid: parsed_phone,
  });

  if (!user_otp) {
    return res.status(400).json({
      success: false,
      error: 'otp_not_found',
    });
  }

  if (user_otp.otp !== otp) {
    return res.status(400).json({
      success: false,
      error: 'otp_mismatch',
    });
  }

  if (moment(user_otp.expired_at).isBefore(moment())) {
    return res.status(400).json({
      success: false,
      error: 'otp_expired',
    });
  }

  user_otp.verified_hash = crypto
    .createHash('sha256')
    .update(otp + parsed_phone + moment().format())
    .digest('hex');
  user_otp.expired_at = moment().toDate();
  await user_otp.save();
  res.json({
    success: true,
    data: {
      verified_hash: user_otp.verified_hash
    }
  });
}
