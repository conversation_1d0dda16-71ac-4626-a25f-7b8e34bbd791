const moment = require('moment')
const _ = require('lodash')
const { Order, Site, SiteMenuGroup, SiteOrderIndex, Hub, Brand, RetailerSaleConfig } = require('../../.shared/database')
const ahamove = require('../../.shared/delivery/ahamove')
const grab_express = require('../../.shared/delivery/grab_express')
const viettel_post = require('../../.shared/delivery/viettel_post')
const token_account = require('../../.shared/token_account')
const { map_order } = require('../../.shared/merchant/mapping')
const { apply_gifts } = require('../common/gift')
const { gen_external_id } = require('../../.shared/helper')
const { PAY_VIA_WALLET_OR_BANK, SELF_PICKUP, TAKE_AWAY } = require('../../.shared/const')
const { send_order_bill_to_channel } = require('./merchant')
const { get_shipment_slots, get_pickup_slots: order_get_pickup_slots } = require('../common/order')

const _build_menu_items = (menu) => {
  return [
    ...menu.categories.flatMap((cat) => cat.items.map((item) => ({ ...item, category_name: cat.name }))),
    ...menu.categories.flatMap((cat) => cat.sub_categories.flatMap((sub_cat) => sub_cat.items.map((item) => ({ ...item, category_name: cat.name, sub_category_name: sub_cat.name })))),
  ]
}

const _mapping_cart_dishes = (menu_items, cart) => {
  // cart.dishes: [{id: string, quantity: number, options: [], note: string}]
  const dishes = cart.dishes.map((v) => {
    const item = menu_items.find((i) => i._id.toString() === v._id.toString()) || {}

    return {
      ...v,
      unit_price: item.price,
      options: v.options,
      quantity: v.quantity,
      id: item.id,
      code: item.code,
      image: item.image,
      from_brand_id: item.from_brand_id,
      category_name: item.category_name,
      name: item.name,
      description: item.description,
      price: item.price,
      is_gift: item.is_gift,
    }
  })

  return {
    ...cart,
    dishes,
  }
}

const get_available_vouchers = async (req, res) => {
  const { site_id } = req.params
  const { cart } = req.body

  const site = await Site.findOne({ _id: site_id }).lean()
  const menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()
  const menu_items = _build_menu_items(menu)
  const mapping_cart = _mapping_cart_dishes(menu_items, cart)
  let brand_sale_configs = await RetailerSaleConfig.find({
    brand_id: site.brand_id,
    active: true,
    // check voucher_config.voucher_code is not null
    'voucher_config.voucher_code': { $ne: null },
  }).lean()

  const now = new Date()
  brand_sale_configs = brand_sale_configs.filter((v) => {
    if (v.start_date && now < v.start_date) {
      return false
    }
    if (v.end_date && now > v.end_date) {
      return false
    }

    // ignore voucher has quantity = usage_count
    if (v.voucher_config?.quantity && v.voucher_config?.usage_count >= v.voucher_config?.quantity) {
      return false
    }

    return true
  })

  const { gifts, discount, ship_discount } = await apply_gifts({
    user: req.user,
    site,
    menu_items,
    cart: mapping_cart,
    option_categories: menu.option_categories,
    brand_sale_configs,
    ignore_check_site_apply_gift: true,
  })

  const config_ids = [...gifts, ...discount, ...ship_discount]
    .map((v) => v.config_id)
    .filter((v) => v)
    .map((v) => String(v))

  res.json({
    success: true,
    data: brand_sale_configs.map((v) => ({
      _id: v._id,
      name: v.name,
      description: v.description,
      voucher_code: v.voucher_config?.voucher_code,
      apply_with_other: v.voucher_config?.apply_with_other ?? false,
      end_date: v.end_date,
      is_applicable: config_ids.includes(String(v._id)),
    })),
  })
}

const apply_vouchers = async (req, res) => {
  const { site_id } = req.params
  // vouchers: [{code: string }]
  const { cart, vouchers } = req.body

  const site = await Site.findById(site_id)
  const menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()

  const menu_items = _build_menu_items(menu)

  const brand_sale_configs = await RetailerSaleConfig.find({
    brand_id: site.brand_id,
    active: true,
    'voucher_config.voucher_code': { $in: vouchers.map((v) => v.code) },
  }).lean()
  const now = new Date()
  const valid_brand_sale_configs = brand_sale_configs.filter((v) => {
    if (v.start_date && now < v.start_date) {
      return false
    }
    if (v.end_date && now > v.end_date) {
      return false
    }

    // ignore voucher has quantity = usage_count
    if (v.voucher_config?.quantity && v.voucher_config?.usage_count >= v.voucher_config?.quantity) {
      return false
    }
    return true
  })

  const mapping_cart = _mapping_cart_dishes(menu_items, cart)

  const { gifts, discount, ship_discount } = await apply_gifts({
    user: req.user,
    site,
    menu_items,
    cart: mapping_cart,
    option_categories: menu.option_categories,
    brand_sale_configs: valid_brand_sale_configs,
    ignore_check_site_apply_gift: true,
  })

  // return the list, indicate which voucher is applied
  const config_ids = [...gifts, ...discount, ...ship_discount]
    .map((v) => v.config_id)
    .filter((v) => v)
    .map((v) => String(v))

  const applied_configs = valid_brand_sale_configs.filter((v) => config_ids.includes(String(v._id)))
  const applied_vouchers = vouchers.map((v) => {
    const applied = applied_configs.find((c) => c.voucher_config?.voucher_code === v.code)
    const discount_amount = applied ? discount.find((d) => String(d.config_id) === String(applied?._id))?.amount : null
    const ship_discount_amount = applied ? ship_discount.find((d) => String(d.config_id) === String(applied?._id))?.amount : null
    const gift_items = applied ? gifts.filter((d) => String(d.config_id) === String(applied?._id)) : []
    const error = _.isNil(applied) ? 'voucher_not_applicable' : null

    return {
      voucher_code: v.code,
      note: applied?.name,
      applied: !!applied,
      discount: discount_amount || 0,
      ship_discount: ship_discount_amount || 0,
      gifts: gift_items.map((gift) => {
        const item = menu_items.find((i) => i._id.toString() === gift.dish_id.toString())
        return {
          ...gift,
          name: item?.name,
          image: item?.image,
          price: item?.price,
          quantity: gift.quantity,
        }
      }),
      error,
    }
  })

  res.json({
    success: true,
    data: applied_vouchers,
  })
}

const get_shipment = async (req, res) => {
  const { site_id } = req.params
  const { to } = req.body

  if (!to.address || !to.name || !to.phone) {
    return res.json({ success: false, error: 'missing_address' })
  }

  const site = await Site.findById(site_id)
  const hub = await Hub.findOne({ _id: site.hub_id }).lean()

  const result = {
    instant_ship: [],
    same_day_ship: [],
    two_hour_ship: [],
    schedule_ship: [],
    province_ship: [],
  }

  const shipment = {
    from: {
      name: hub.name,
      phone: hub.phone,
      address: hub.address,
    },
    to: {
      name: to.name,
      phone: to.phone,
      address: to.address,
    },
  }

  const [ahamove_shipments, grab_express_shipments, viettel_post_shipments] = await Promise.all([
    ahamove.get_shipments(await token_account.get_token_by_site(site, 'ahamove'), shipment),
    grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express'), shipment),
    viettel_post.get_shipments(await token_account.get_token_by_site(site, 'viettel_post'), shipment),
  ])

  result.instant_ship.push(...grab_express_shipments.filter((v) => ['INSTANT'].includes(v.code)))
  if (result.instant_ship?.length === 0) {
    result.instant_ship.push(...ahamove_shipments.filter((v) => ['SGN-BIKE'].includes(v.code)))
  }

  result.two_hour_ship.push(...ahamove_shipments.filter((v) => ['SGN-BIKE'].includes(v.code)))
  if (result.two_hour_ship?.length === 0) {
    result.two_hour_ship.push(...grab_express_shipments.filter((v) => ['INSTANT'].includes(v.code)))
  }

  result.province_ship.push(...viettel_post_shipments)

  // Sort instant_ship by lowest price and get the first item
  result.instant_ship = result.instant_ship
    .sort((a, b) => a.price - b.price)
    .filter((v) => v.price > 0)
    .slice(0, 1)

  result.two_hour_ship = result.two_hour_ship
    .sort((a, b) => a.price - b.price)
    .filter((v) => v.price > 0)
    .slice(0, 1)

  if (result.two_hour_ship.length > 0) {
    const slots = get_shipment_slots()
    const choice_slot = result.two_hour_ship[0]
    result.schedule_ship = slots.map((v) => ({
      vendor: choice_slot.vendor,
      code: choice_slot.code,
      price: choice_slot.price,
      name: `Khung giờ ${v.from_time} -  ${v.to_time} , ngày ${moment(v.from_date_time).format('DD/MM')} `,
      description: 'Shop sẽ liên hệ và giao hàng theo lịch hẹn của bạn',
      options: choice_slot.options || [],
      raw: { ...choice_slot.raw, schedule: v },
    }))
  }

  delete result.two_hour_ship

  const current_time = moment().utcOffset('+07:00')
  const before_8am = current_time.isBefore(moment().set({ hour: 8, minute: 0, second: 0 }))
  const after_9pm = current_time.isAfter(moment().set({ hour: 21, minute: 0, second: 0 }))
  if (before_8am || after_9pm) {
    result.instant_ship = []
  }

  res.json({
    success: true,
    data: result,
  })
}

const create_order = async (req, res) => {
  const { site_id } = req.params
  const { cart, vouchers = [] } = req.body
  const { shipment: raw_shipment, dishes, note, total_surcharge, payments } = cart

  // dishes: [{id: string, quantity: number, options: [], note: string}]
  // shipment: {service: {code: string, price: number, option: {code: string, price: number}}, to: {name: string, phone: string, address: string}, type: 'self_pickup' | 'take_away' | 'delivery'}
  // note: string
  // total_surcharge: number
  // payments: [{method: string, total: number, note: string}]
  // vouchers: [{code: string, gifts: []}]

  const site = await Site.findById(site_id)

  if (!site) {
    return res.json({ success: false, error: 'invalid_site' })
  }

  // 'self_pickup' | 'take_away' | 'delivery'
  let { type, ...shipment } = raw_shipment

  const self_pickup_token = site.getToken('self_pickup')
  if (type === 'self_pickup') {
    if (!self_pickup_token?.settings?.service_types?.includes('pick_up')) {
      return res.json({ success: false, error: 'invalid_self_pickup' })
    }
    shipment.service = SELF_PICKUP
  } else if (type === 'take_away') {
    if (!self_pickup_token?.settings?.service_types?.includes('take_away')) {
      return res.json({ success: false, error: 'invalid_take_away' })
    }
    shipment.service = TAKE_AWAY
  }

  // vouchers: [{code: string, gifts: [] }]
  // 1. validate if vouchers are valid
  const menu = await SiteMenuGroup.findOne({ site_id: site._id }).lean()
  const menu_items = _build_menu_items(menu)
  let brand_sale_configs = await RetailerSaleConfig.find({
    brand_id: site.brand_id,
    active: true,
    // check voucher_config.voucher_code is not null
    'voucher_config.voucher_code': { $in: vouchers.map((v) => v.code) },
  }).lean()

  const now = new Date()
  brand_sale_configs = brand_sale_configs.filter((v) => {
    if (v.start_date && now < v.start_date) {
      return false
    }
    if (v.end_date && now > v.end_date) {
      return false
    }
    // ignore voucher has quantity = usage_count
    if (v.voucher_config?.quantity && v.voucher_config?.usage_count >= v.voucher_config?.quantity) {
      return false
    }
    return true
  })

  const selected_gifts = vouchers.reduce((acc, v) => {
    const config = brand_sale_configs.find((c) => c.voucher_config?.voucher_code === v.code)
    if (config) {
      acc.push(...(v.gifts || []).map((gift) => ({ ...gift, config_id: config._id })))
    }
    return acc
  }, [])

  const map_cart = _mapping_cart_dishes(menu_items, cart)
  const { gifts, discount, ship_discount } = await apply_gifts({
    user: req.user,
    site,
    menu_items,
    cart: map_cart,
    option_categories: menu.option_categories,
    brand_sale_configs,
    selected_gifts,
    ignore_check_site_apply_gift: true,
  })

  // find the applied sale config
  const applied_configs = [...gifts, ...discount, ...ship_discount]
    .map((v) => v.config_id)
    .filter((v) => v)
    .map((v) => brand_sale_configs.find((c) => String(c._id) === String(v)))
  const coupons = applied_configs.map((v) => ({
    voucher_code: v.voucher_config?.voucher_code,
    vendor: 'nexpos',
  }))

  const final_dishes = map_cart.dishes.concat(gifts).map((v) => {
    const discounts = discount.filter((d) => String(d.dish_id) === String(v._id))
    const dish = menu_items.find((m) => String(m._id) === String(v._id))
    const total_option_price = _.sumBy(_.flatten(v.options), (o) => o.price * o.quantity)
    return {
      options: v.options,
      quantity: v.quantity,
      note: [...discounts.map((d) => d.note), v.note].filter(Boolean).join(', '),
      id: dish.id,
      code: dish.code,
      image: dish.image,
      from_brand_id: dish.from_brand_id,
      category_name: dish.category_name,
      name: dish.name,
      description: dish.description,
      price: (dish.price + total_option_price) * v.quantity,
      discount: _.sumBy(discounts, (d) => _.toNumber(d?.amount || 0)),
      is_gift: dish.is_gift,
      combo: dish.combo,
    }
  })

  const total_discount = _.sumBy(discount, 'amount')
  const sub_total = _.sumBy(final_dishes, (v) => v.price)

  const ship_fee = Math.max(0, _.toNumber(shipment?.service?.price ?? 0) + _.toNumber(shipment?.service?.option?.price ?? 0) - _.sumBy(ship_discount, (v) => v.amount))

  const total_paid = _.sumBy(payments ?? [], 'total')
  const total_for_biz = sub_total + ship_fee + (total_surcharge || 0) - total_discount

  if (total_paid !== total_for_biz) {
    return res.status(400).send({
      success: false,
      error: 'payment_is_not_complete',
      detail: {
        total_paid,
        total_for_biz,
      },
    })
  }

  const order_group = `${site.code?.toUpperCase() ?? 'NEXDOR'}-${moment().format('YYMMDD')}`
  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })

  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  // status
  let status = 'PENDING'
  if (payments.find((v) => PAY_VIA_WALLET_OR_BANK.includes(v.method) && v.total > 0)) {
    status = 'WAITING_PAYMENT'
  }
  const mapping_payments = payments.map((v) => {
    if (PAY_VIA_WALLET_OR_BANK.includes(v.method)) {
      return {
        ...v,
        status: 'PENDING',
      }
    }
    return v
  })

  const created_at = new Date()
  const new_order = {
    source: 'local',
    status,
    site_id,
    hub_id: site.hub_id,
    order_id,
    user_id: undefined,
    data_mapping: {},
    data: {
      id: order_id,
      order_id,
      source: 'local',
      customer_name: shipment?.to?.name,
      customer_phone: shipment?.to?.phone,
      customer_address: shipment?.to?.address,
      customer_address_obj: shipment?.to?.address_obj,
      dishes: final_dishes,
      note,
      total: sub_total + ship_fee,
      total_discount: _.sumBy(discount, 'amount'),
      total_shipment: ship_fee + _.sumBy(ship_discount, 'amount'),
      total_surcharge: total_surcharge ?? 0,
      total_for_biz: total_for_biz,
      ship_discount: _.sumBy(ship_discount, 'amount'),
      shipment_fee: ship_fee,
      order_time: moment(created_at).toISOString(),
      order_time_sort: moment(created_at).unix(),
      delivery_time: moment(created_at).toISOString(),
      delivery_time_unix: moment(created_at).unix(),
      pick_time: moment(created_at).toISOString(),
      payments: mapping_payments,
      raw: {
        ...req.body,
      },
      shipment: { ...shipment, price: Math.max(0, _.toNumber(shipment?.service?.price ?? 0) + _.toNumber(shipment?.service?.option?.price ?? 0)) },
      coupons,
    },
    payments: mapping_payments,
  }

  new_order.data_mapping = map_order(new_order.source, new_order.data)

  const auto_confirm_order = !site.hub_ids?.length
  const brand = await Brand.findById(site.brand_id)
  const cod_token = brand.getToken('cod')
  const max_cod_setting = Number(cod_token?.site_data?.max_cod || 1000000)
  const max_cod_percentage_setting = Number(cod_token?.site_data?.max_cod_percentage || 0)

  // If order has COD and total > 1M, assign to site hub (CS)
  const cash_payments = new_order.payments.filter((v) => v.method === 'CASH')
  for (const payment of cash_payments) {
    payment.status = 'COMPLETED'
  }
  const cod_payments = new_order.payments.filter((v) => v.method === 'COD')
  const total_cod = _.sumBy(cod_payments, 'total') ?? 0
  if (total_cod > 0) {
    if (total_for_biz > max_cod_setting) {
      new_order.hub_id = site.hub_id
      for (const payment of cod_payments) {
        payment.status = 'WAITING_PAYMENT'
        payment.prepaid_required = true
        payment.prepaid_minimum = Math.ceil((total_for_biz * max_cod_percentage_setting) / 1000) * 1000
      }
    } else {
      for (const payment of cod_payments) {
        payment.status = 'COMPLETED'
      }
      if (auto_confirm_order) {
        new_order.status = 'DOING'
      }
    }
  } else {
    new_order.status = 'WAITING_PAYMENT'
  }

  new_order.data_mapping = map_order(new_order.source, new_order.data)
  new_order.external_id = gen_external_id()
  try {
    let total_paid_completed = 0
    for (const payment of payments) {
      if (['CASH', 'COD'].includes(payment.method) || payment.status === 'COMPLETED') {
        total_paid_completed += payment.total
      }
    }
    const order = await Order.create(new_order)

    let delivery_type =
      {
        pick_up: 'pick_up',
        take_away: 'take_away',
      }[shipment?.service?.vendor] || 'delivery'

    const is_delivery = delivery_type === 'delivery'

    if (delivery_type === 'take_away' && total_paid_completed === total_for_biz) {
      await send_order_bill_to_channel(new_order.order_id, {
        bill: true,
        zalo: true,
        bill_template: 'bill_for_kitchen',
        labels: true,
      })
      await send_order_bill_to_channel(new_order.order_id, {
        bill: true,
        zalo: false,
        bill_template: 'bill_for_complete',
        labels: false,
      })

      await Order.findOneAndUpdate({ order_id: new_order.order_id }, { status: 'FINISH' })
      // update sale config to count the usage By 1, by voucher code
      for (const config of applied_configs) {
        await RetailerSaleConfig.findOneAndUpdate({ _id: config._id }, { $inc: { 'voucher_config.usage_count': 1 } })
      }
    }

    res.status(200).json({
      success: true,
      data: order,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error?.message ?? 'Server Error',
    })
  }
}

const get_pickup_slots = async (req, res) => {
  const result = order_get_pickup_slots()
  res.json({
    success: true,
    data: result,
  })
}

module.exports = {
  get_available_vouchers,
  get_shipment,
  create_order,
  apply_vouchers,
  get_pickup_slots,
}
