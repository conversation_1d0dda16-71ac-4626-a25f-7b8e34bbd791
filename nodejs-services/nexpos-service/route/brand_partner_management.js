const { PartnerWithdraw, User } = require('../../.shared/database')


exports.get_list = async (req, res) => {
    const { brand_id } = req.params
    const { status, search } = req.query

    const query = {
        brand_id,
    }

    if (status) {
        query.status = status
    }

    if (search) {
        query['bank_account.account_number'] = { $regex: search, $options: 'i' }
    }

    const result = await PartnerWithdraw.paginate(query, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 docs
        sort: { created_at: 1 },
        customLabels: { docs: 'data' },
        lean: true,
    })

    // populate user
    const user_ids = result.data.map(item => item.user_id)
    const users = await User.find({
        _id: { $in: user_ids },
    }, {
        _id: 1,
        name: 1,
        username: 1,
        email: 1,
    }).then(users => users.reduce((acc, user) => {
        acc[user._id.toString()] = user
        return acc
    }, {}))

    result.data = result.data.map(item => ({
        ...item,
        user: users[item.user_id.toString()],
    }))

    res.json({
        success: true,
        ...result,
    })
}


exports.update = async (req, res) => {
    const { id, brand_id } = req.params
    const { status, attachments, cancelled_reason } = req.body

    const updated = await PartnerWithdraw.findOneAndUpdate({
        _id: id,
        brand_id
    }, {
        status,
        attachments,
        cancelled_reason,
    }, {
        new: true,
    })

    res.json({
        success: true,
        data: updated,
    })
}
