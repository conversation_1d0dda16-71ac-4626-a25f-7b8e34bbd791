require('dotenv').config({ path: './.env' })
const moment = require('moment-timezone')
moment.tz.setDefault('Asia/Bangkok')
const { Order, Site } = require('../../.shared/database')
const { sumBy, intersection } = require('lodash')

exports.get_dashboard_summary = async (req, res) => {
  const { from_date, to_date, site_ids: req_site_ids } = req.query
  if (!from_date || !to_date) {
    return res.status(400).json({
      success: false,
      error: 'from_date_and_to_date_required',
    })
  }
  
  // Check if date range exceeds 1 month
  const days_difference = moment(to_date).diff(moment(from_date), 'days')
  if (days_difference > 31) {
    return res.status(400).json({
      success: false,
      error: 'date_range_exceeds_1_month',
      message: 'Date range cannot exceed 1 month'
    })
  }

  // Get order filter that respects user's site access permissions
  const site_ids = intersection(req_site_ids, req.user.sites)
  
  const from_unix = moment(from_date).unix()
  const to_unix = moment(to_date).unix()

  // Common filter parameters used for both statistics and breakdown
  const site_ids_formatted = site_ids.map(id => typeof id === 'string' ? id : id.toString());
  
  // Filter for completed orders
  const completed_filter = {
    status: 'FINISH',
    'data_mapping.delivery_time_unix': {
      $gte: from_unix,
      $lt: to_unix
    },
    site_id: { $in: site_ids_formatted }
  };
  
  // Filter for canceled orders
  const canceled_filter = {
    status: 'CANCEL',
    'data_mapping.order_time_sort': {
      $gte: from_unix,
      $lt: to_unix
    },
    site_id: { $in: site_ids_formatted }
  };

  // Get all necessary data in parallel for better performance
  const [completed_orders, canceled_count] = await Promise.all([
    // Get completed orders with needed fields for both statistics and breakdown
    Order.find(completed_filter, {
      source: 1,
      'data_mapping.total': 1,
      'data_mapping.total_for_biz': 1, 
      'data_mapping.total_discount': 1,
      'data_mapping.finance_data': 1,
      'data_mapping.delivery_time_unix': 1,
      _id: 0,
    }).lean(),
    
    // Get canceled orders count
    Order.countDocuments(canceled_filter)
  ]);

  // Calculate summary statistics
  const total_completed_orders = completed_orders.length;
  const revenue_after_discount = sumBy(
    completed_orders.map((v) => v.data_mapping),
    'total_for_biz'
  ) || 0;
  const revenue_before_discount = sumBy(
    completed_orders.map((v) => v.data_mapping?.finance_data),
    'original_price'
  ) || 0;

  // Determine grouping granularity for breakdown
  const start_moment = moment(from_date)
  const end_moment = moment(to_date)
  const duration_in_hours = end_moment.diff(start_moment, 'hours')
  const group_by_hours = duration_in_hours <= 24

  // Prepare time slots and initialize result containers
  const time_slots = []
  const order_counts = []
  const revenues = []

  if (group_by_hours) {
    // Group by hours
    for (let m = start_moment.clone(); m.isBefore(end_moment); m.add(1, 'hour')) {
      const hour_label = m.format('HH:00')
      time_slots.push(hour_label)

      const hour_start = m.unix()
      const hour_end = m.clone().add(1, 'hour').unix()

      // Filter orders for this hour
      const hour_orders = completed_orders.filter((order) => {
        const timestamp = order.data_mapping.delivery_time_unix
        return timestamp >= hour_start && timestamp < hour_end
      })

      order_counts.push(hour_orders.length)
      revenues.push(
        sumBy(
          hour_orders.map((v) => v.data_mapping),
          'total_for_biz'
        ) || 0
      )
    }
  } else {
    // Group by days
    for (let m = start_moment.clone().startOf('day'); m.isBefore(end_moment); m.add(1, 'day')) {
      const day_label = m.format('DD/MM')
      time_slots.push(day_label)

      const day_start = m.unix()
      const day_end = m.clone().add(1, 'day').unix()

      // Filter orders for this day
      const day_orders = completed_orders.filter((order) => {
        const timestamp = order.data_mapping.delivery_time_unix
        return timestamp >= day_start && timestamp < day_end
      })

      order_counts.push(day_orders.length)
      revenues.push(
        sumBy(
          day_orders.map((v) => v.data_mapping),
          'total_for_biz'
        ) || 0
      )
    }
  }

  // Combine all data into one response
  res.json({
    success: true,
    data: {
      // Summary statistics
      summary: {
        total_completed_orders,
        total_canceled_orders: canceled_count,
        revenue_before_discount,
        revenue_after_discount,
      },
      // Time-based breakdown
      breakdown: {
        labels: time_slots,
        datasets: {
          orders: order_counts,
          revenue: revenues,
        },
        group_by: group_by_hours ? 'hour' : 'day'
      },
      // Period info
      period: {
        from: moment.unix(from_unix).format('DD/MM/YYYY'),
        to: moment.unix(to_unix).format('DD/MM/YYYY'),
      }
    }
  });
}
