require('dotenv').config({ path: './.env' })
const moment = require('moment')
const xlsx = require('xlsx')
const { Site, Order, Brand, TokenAccount, OrderPayment } = require('../../.shared/database')
const { map_order, map_menu } = require('../../.shared/merchant/mapping')
const { get_site_orders, get_site_ecom_orders, get_site_tokens, get_token_account_health, get_site_orders_by_days, count_site_orders_by_days } = require('../../.shared/cron')
const { refresh_new_token } = require('../../.shared/token_account')
const { send_message_to_topic } = require('../../.shared/event')
const _ = require('lodash');
const { json_to_md5 } = require('../../.shared/crypto');
const { get_token_by_site } = require('../../.shared/token_account')
const { upload_file } = require('../../.shared/storage')
const { send_zalo_message, ZALO_GROUPS } = require('../../.shared/zalo')
const { get_site_order_feedback_summary, get_site_order_feedbacks, get_order_incident_list, get_site_finance_by_source } = require('../../.shared/cron_finance')
const redis = require('../../.shared/redis')
const { get_queue, set_queue, pick_queue, delete_queue } = require('../../.shared/redis_queue')
const { is_token_expired } = require('../../.shared/helper')
const { MERCHANT_INFO } = require('../../.shared/const')

// Google Cloud Auth Credentials
const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode

exports.cron_refresh_token_accounts = async (req, res) => {
  const queue_key = `cron_job:get_site_order_by_source_errors`
  let cached_items = await get_queue(queue_key)
  if (cached_items.length === 0) {
    return res.json({
      success: true,
      data: []
    })
  }
  if (cached_items[0].count < 50) {
    return res.json({
      success: true,
      data: []
    })
  }
  const selected_site_errors = await pick_queue(queue_key, { size: 5, min_duration: 0, delete_queue: true })
  try {
    await Promise.all(selected_site_errors.map(async (selected_site_error) => {
      if (selected_site_error.token_code) {
        const result = await refresh_new_token(selected_site_error.token_code)
        console.log(result)
      }
    })
    )
  } catch (error) {
    console.log(error.message)
  }

  res.json({
    success: true,
    data: selected_site_errors
  })
}

exports.cron_refresh_token_accounts_v2 = async (req, res) => {
  const token = await TokenAccount.findOne({
    source: ['grab_express', 'ahamove', 'be', 'grab', 'grab_mart_official', 'zalo'],
    expired_at: { $not: { $gt: moment().toISOString() } }
  }).sort({ updated_at: 1 }).lean()
  if (!token) {
    return res.json({
      success: true,
    })
  }

  await refresh_new_token(token.token_code).catch(console.error)
  res.json({
    success: true,
    data: token.token_code
  })
}


exports.cron_site_orders = async (req, res) => {
  const queue_key = 'cron_job:site_orders'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      active: true,
      tokens: {
        $elemMatch: {
          source: ['shopee', 'shopee_fresh', 'shopee_ecom', 'grab', 'grab_mart', 'gojek', 'be'],
          token_code: { $exists: true, $ne: null, $ne: '' }
        }
      }
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }
  const selected_sites = await pick_queue(queue_key, { size: 5, min_duration: 120 })

  await Promise.all(selected_sites.map(async process_site => {
    try {
      await get_site_orders(String(process_site._id))
    } catch (err) {
      console.log('get_site_orders error', err.message)
      console.log(err)
    }
  }))

  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};

// this.cron_site_orders(null, { json: console.log })

exports.cron_site_ecom_orders = async (req, res) => {
  const queue_key = 'cron_job:site_ecom_orders'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      active: true,
      last_cron_ecom_order: { $not: { $gt: moment().add(-5, 'minutes').toISOString() } },
      tokens: {
        $elemMatch: {
          source: ['shopee_ecom', 'lazada', 'tiktok'],
          token_code: { $exists: true, $ne: null, $ne: '' }
        }
      }
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }

  const selected_sites = await pick_queue(queue_key, { size: 5, min_duration: 300 })

  await Promise.all(selected_sites.map(async selected_site => {
    try {
      await get_site_ecom_orders(selected_site._id)
    } catch (err) {
      console.eror('get_site_ecom_orders error', err.message)
      console.log(err)
    }
  }))

  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};

exports.cron_site_orders_in_x_days = async (req, res) => {
  const queue_key = 'cron_job:site_orders_in_x_days'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      active: true,
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }

  const selected_sites = await pick_queue(queue_key, { size: 2, min_duration: 4 * 60 * 60 })
  try {
    await Promise.all(selected_sites.map(async site => {
      await get_site_orders_by_days(site._id, {
        from: moment().add(-20, 'days'),
        to: moment(),
      })
    }))
  } catch (error) { console.log(error.message) }
  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};


exports.cron_site_order_feedbacks = async (req, res) => {
  const queue_key = 'cron_job:site_order_feedbacks'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      tokens: {
        $elemMatch: {
          source: ['shopee', 'shopee_fresh', 'grab', 'grab_mart'],
          token_code: { $exists: true, $ne: null, $ne: '' }
        },
      },
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }

  const selected_sites = await pick_queue(queue_key, { size: 1, min_duration: 3600 })
  try {
    await Promise.all(selected_sites.map(async site => {
      await get_site_order_feedbacks(site._id, 100)
    }))
  } catch (error) { console.log(error.message) }
  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};

exports.cron_site_feedback_summary = async (req, res) => {
  const queue_key = 'cron_job:site_feedback_summary'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      tokens: {
        $elemMatch: {
          source: ['shopee', 'shopee_fresh', 'grab', 'grab_mart'],
          token_code: { $exists: true, $ne: null, $ne: '' }
        },
      },
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }

  const selected_sites = await pick_queue(queue_key, { size: 10, min_duration: 0 })
  try {
    await Promise.all(selected_sites.map(async site => {
      await get_site_order_feedback_summary(site._id)
    }))
  } catch (error) { console.log(error.message) }
  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};

exports.cron_site_order_incident_list = async (req, res) => {
  const queue_key = 'cron_job:site_order_incident_list'
  let cached_sites = await get_queue(queue_key)
  if (cached_sites.length === 0) {
    const sites = await Site.find({
      tokens: {
        $elemMatch: {
          source: ['grab', 'grab_mart'],
          token_code: { $exists: true, $ne: null, $ne: '' }
        },
      },
    }, { _id: 1, name: 1 }).lean();
    await set_queue(queue_key, sites)
  }

  const selected_sites = await pick_queue(queue_key, { size: 10, min_duration: 600 })
  // const selected_sites = await pick_queue(queue_key, { size: 20, min_duration: 0 })
  try {
    await Promise.all(selected_sites.map(async site => {
      await get_order_incident_list(site._id)
    }))
  } catch (error) { console.log(error.message) }
  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};

exports.cron_site_finances = async (req, res) => {
  const orders = await Order.find({
    source: ['grab', 'grab_mart'],
    'data.transaction': null,
    created_at: { $gte: moment().add(-60, 'days').toISOString() },
    status: 'FINISH',
    last_cron_transaction: { $not: { $gt: moment().add(-3, 'minutes').toISOString() } },
  }).sort({ created_at: 1 }).limit(3);

  await Order.updateMany({ _id: orders.map((v) => v._id) }, { last_cron_transaction: new Date() })
  try {
    await Promise.all(orders.map(async order => {
      await get_site_finance_by_source(order.order_id)
    }))
  } catch (error) { console.log(error.message) }

  res.json({
    success: true,
    data: orders.map(v => v.order_id)
  });
};


exports.fetch_site_orders = async (req, res) => {
  const { site_id } = req.params
  const { days } = req.body

  await get_site_orders_by_days(site_id, {
    from: moment().add(-Number(days || 10), 'days'),
    to: moment(),
  })


  res.json({
    success: true,
  })
}

exports.cron_token_account_healths = async (req, res) => {
  const token_accounts = await TokenAccount.find({
    source: ['shopee', 'grab', 'gojek', 'grab_express', 'ahamove', 'be'],
    last_updated: { $lte: moment().add(-1, 'hour').toISOString() },
  }).sort({ last_updated: 1 }).limit(5);

  await TokenAccount.updateMany({ _id: token_accounts.map((v) => v._id) }, { $set: { last_updated: new Date() } })

  try {
    await Promise.all(token_accounts.map(async (ta) => {
      await get_token_account_health(ta.token_code);
    }))
  } catch (error) {
    console.log(error);
    console.log(error.message);
  }

  res.json({
    success: true,
  });
}

exports.cron_order_notifications = async (req, res) => {
  let filter = {
    source: { $ne: 'local' },
    status: ['DOING', 'PENDING'],
    pushed_notification: false,
  }

  filter['data_mapping.order_time_sort'] = {}
  filter['data_mapping.order_time_sort'].$gte = moment.tz('Asia/Jakarta').add(-30, 'minute').unix()

  const orders = await Order.find(filter).sort({ 'data_mapping.order_time_sort': -1 })
  if (orders.length > 0) {
    await Order.updateMany({ _id: orders.map((v) => v._id) }, { $set: { pushed_notification: true } })

    const site_ids = orders.map((v) => v.site_id)
    const sites = await Site.find({ _id: site_ids })
    // await send_message_to_topic('topic_new_order', 'Bạn vừa nhận được đơn hàng mới từ ứng dụng', {
    //   new_order: JSON.stringify({
    //     site_ids: site_ids,
    //     brand_ids: sites.map((v) => v.brand_id),
    //     hub_ids: sites.map((v) => v.hub_id),
    //   }),
    // })
    await send_message_to_topic({
      message: `Bạn vừa có ${orders.length} đơn hàng mới từ ứng dụng`,
      data: {
        site_ids: site_ids,
        brand_ids: sites.map((v) => v.brand_id),
        hub_ids: sites.map((v) => v.hub_id),
      }
    })

  }

  res.json({
    success: true,
  })
}

exports.cron_brand_order_checking = async (req, res) => {
  const brands = await Brand.find({
    last_cron_order_checking: { $not: { $gt: moment().add(-1, 'day').endOf('day').toISOString() } }
  }).sort({ last_cron_order_checking: 1 }).limit(1).lean()

  await Brand.updateMany({ _id: brands.map((v) => v._id) }, { $set: { last_cron_order_checking: new Date() } })

  const from = moment().startOf('month').toISOString();
  const to = moment().add(-1, 'day').endOf('day ').toISOString()
  const workbook = new xlsx.utils.book_new()

  let messages = []
  for (const brand of brands) {
    let sites = await Site.find({
      brand_id: brand._id,
      active: true
    }, { name: 1 })

    const missing_orders = []
    for (const site of sites) {
      const site_orders = await count_site_orders_by_days(site._id, { from, to })

      for (const [source, order_ids] of Object.entries(site_orders)) {
        if (order_ids.length === 0) continue
        const db_orders = await Order.find({
          $or: [{
            order_id: order_ids,
          }, {
            'data_mapping.order_id': order_ids,
          }]
        }, { order_id: 1 }).lean()

        for (const order_id of order_ids) {
          if (!db_orders.find(v => v.order_id === order_id)) {
            missing_orders.push({
              site_name: site.name,
              source,
              order_id,
            })
          }
        }

      }
    }

    if (missing_orders.length > 0) {
      messages.push(`Brand: ${brand.name} - Missing Orders: ${missing_orders.length}`)
      const worksheet = xlsx.utils.json_to_sheet(missing_orders)
      xlsx.utils.book_append_sheet(workbook, worksheet, brand.name)
    }
  }

  if (messages.length === 0) {
    return res.json({ success: true })
  }
  const buff = xlsx.write(workbook, { type: 'buffer' })
  const file = await upload_file({ bucket: 'nexpos-files', key: `reports / Order_Checking_${moment(from).format('DD_MM')}_${moment(to).format('DD_MM')}_${moment().unix()}.xlsx`, buff })
  console.log(file)
  await send_zalo_message({
    // group_link: 'https://zalo.me/g/esrctb308', 
    group_link: ZALO_GROUPS.ORDER_SYSNCING_NOTIFICATION,
    message: [
      `Cảnh báo thiếu đơn hàng từ ngày <b> ${moment(from).format('DD/MM/YYYY')}</b> đến <b> ${moment(to).format('DD/MM/YYYY')}</b> `,
      messages.join('\n'),
      `Tải và xem tại: ${file}`
    ].join('\n')
  })
  res.json({
    success: true,
  })
}

exports.cron_bank_transactions = async (req, res) => {
  const queue_key = 'cron_job:bank_transactions'
  console.log(`${moment().format('YYYY-MM-DD HH:mm:ss')} Get Bank Transactions`)
  const resp = await axios.get(`https://pay.nexdor.tech/api/transactions`, {
    headers: {
      'Authorization': `NEXDOR ${process.env.NEXDOR_API_KEY}`
    }
  })
  const transactions = resp.data.data ?? []
  const old_transactions = await redis.getObj(queue_key) ?? []
  if (old_transactions.length === transactions.length) {
    return res.json({ success: true, data: [] })
  }
  await redis.setObj(queue_key, transactions)

  for (const transaction of transactions) {
    if (!transaction.partner_transaction_id) {
      continue
    }
    const order_payment = await OrderPayment.findOne({ transaction_id: transaction.partner_transaction_id })
    if (!order_payment) {
      continue
    }
    order_payment.status = 'COMPLETED'
    await order_payment.save()
  }

  res.json({
    success: true,
    data: selected_sites.map(v => v.name)
  });
};
