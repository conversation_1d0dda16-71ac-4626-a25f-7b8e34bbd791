const crypto = require('crypto');
const axios = require('axios');
const _ = require('lodash');
const moment = require('moment');
const { _textToSlug } = require('../../.shared/merchant/mapping');
// const base64url = require('b64url');
const slugify = require('slugify');

let helper = {};

helper.text_slugify = (text) => slugify(text.toLowerCase(), { replacement: '_', locale: 'vi', trim: true, strict: true })
helper.compose_site_menu_item = (site_menu, categories) => {
    categories.forEach(group_menu_category => {
        const site_menu_category_index = site_menu.categories.findIndex(category => _textToSlug(category.name) === _textToSlug(group_menu_category.name));

        if (site_menu_category_index < 0) {
            group_menu_category.new = true
            site_menu.categories.push(group_menu_category);
        } else {
            const site_menu_category = site_menu.categories[site_menu_category_index];
            site_menu_category.sources = [...new Set([...site_menu_category.sources, ...group_menu_category.sources])];

            (group_menu_category.items || []).forEach(group_menu_item => {
                const site_menu_item_index = site_menu_category.items.findIndex(item => _textToSlug(item.name) === _textToSlug(group_menu_item.name));

                if (site_menu_item_index < 0) {
                    group_menu_item.new = true
                    site_menu_category.items.push(group_menu_item);
                } else {
                    let site_menu_item = site_menu_category.items[site_menu_item_index];
                    Object.assign(site_menu_item, _.pick(group_menu_item, ['name', 'description', 'price', 'active']));
                    if (!site_menu_item.image?.includes('storage.googleapis.com')) {
                        site_menu_item.image = group_menu_item.image;
                    }
                    site_menu_item.sources = [...new Set([...site_menu_item.sources, ...group_menu_item.sources])];
                    site_menu_category.items[site_menu_item_index] = site_menu_item;
                }
            });

            (group_menu_category.sub_categories || []).forEach(group_sub_category => {
                const site_sub_category_index = site_menu_category.sub_categories.findIndex(sub_category => _textToSlug(sub_category.name) === _textToSlug(group_sub_category.name));

                if (site_sub_category_index < 0) {
                    group_sub_category.new = true
                    site_menu_category.sub_categories.push(group_sub_category);
                } else {
                    const site_sub_category = site_menu_category.sub_categories[site_sub_category_index];

                    group_sub_category.items.forEach(group_sub_category_item => {
                        const site_sub_category_item_index = site_sub_category.items.findIndex(item => _textToSlug(item.name) === _textToSlug(group_sub_category_item.name));

                        if (site_sub_category_item_index < 0) {
                            group_sub_category_item.new = true
                            site_sub_category.items.push(group_sub_category_item);
                        } else {
                            const site_sub_category_item = site_sub_category.items[site_sub_category_item_index];
                            Object.assign(site_sub_category_item, _.pick(group_sub_category_item, ['name', 'description', 'price', 'active']));
                            if (!site_sub_category_item.image?.includes('storage.googleapis.com')) {
                                site_sub_category_item.image = group_sub_category_item.image;
                            }
                            site_sub_category_item.sources = [...new Set([...site_sub_category_item.sources, ...group_sub_category_item.sources])];
                            site_sub_category.items[site_sub_category_item_index] = site_sub_category_item;
                        }
                    });

                    site_menu_category.sub_categories[site_sub_category_index] = site_sub_category;
                }
            });

            site_menu.categories[site_menu_category_index] = site_menu_category;
        }
    });

    return site_menu;
}

helper.compose_site_menu_option_item = (site_menu, option_categories) => {
    option_categories.forEach(group_menu_category => {
        const site_menu_category_index = site_menu.option_categories.findIndex(category => _textToSlug(category.name) === _textToSlug(group_menu_category.name));

        if (site_menu_category_index < 0) {
            site_menu.option_categories.push(group_menu_category);
        } else {
            const site_menu_category = site_menu.option_categories[site_menu_category_index];
            site_menu_category.sources = [...new Set([...site_menu_category.sources, ...group_menu_category.sources])];

            if (site_menu_category.category_ids?.length === 0 && group_menu_category.category_ids?.length > 0)
                site_menu_category.category_ids = group_menu_category.category_ids;

            (group_menu_category.options || []).forEach(group_menu_item => {
                const site_menu_item_index = site_menu_category.options.findIndex(item => _textToSlug(item.name) === _textToSlug(group_menu_item.name));

                if (site_menu_item_index < 0) {
                    site_menu_category.options.push(group_menu_item);
                } else {
                    let site_menu_item = site_menu_category.options[site_menu_item_index];
                    Object.assign(site_menu_item, _.pick(group_menu_item, ['name', 'price', 'active']));
                    if (group_menu_item.combo?.length > 0) {
                        site_menu_item.combo = group_menu_item.combo; // Keep config combo
                    }

                    site_menu_item.sources = [...new Set([...site_menu_item.sources, ...group_menu_item.sources])];
                    site_menu_category.options[site_menu_item_index] = site_menu_item;
                }
            });

            site_menu.option_categories[site_menu_category_index] = site_menu_category;
        }
    });

    return site_menu;
}

helper.get_stock_dishes = () => { }

module.exports = helper