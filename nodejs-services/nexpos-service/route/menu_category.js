const _ = require('lodash')
const csvtojson = require('csvtojson');
const xlsx = require('xlsx');
const { v4 } = require('uuid');
const { BrandMenu, Site, SiteMenuGroup, Brand, HubMenuGroup, HubStock } = require('../../.shared/database')
const { map_menu, map_option_categories, _textToSlug } = require('../../.shared/merchant/mapping')
const baemin = require('../../.shared/merchant/baemin');
const shopee = require('../../.shared/merchant/shopee');
const gojek = require('../../.shared/merchant/gojek');
const grab = require('../../.shared/merchant/grab');
const be = require('../../.shared/merchant/be');
const haravan = require('../../.shared/merchant/haravan');
const { upload_file } = require('../../.shared/storage')
const { read_sheet, write_sheet, append_sheet } = require('../../.shared/googlesheet')
const slugify = require('slugify')
const { upload_single } = require('../middlewares/upload_file');
const helper = require('./helper');
const { text_slugify, text_compare } = require('../../.shared/helper');
const { get_token_by_site } = require('../../.shared/token_account');
const crypto = require('crypto');
const path = require('path');
const axios = require('axios');
const redis = require('../../.shared/redis');
const { publisher } = require('../../.shared/pubsub');

const router = {}

router.get_brand_menu_category_list = async (req, res) => {
    const { brand_id } = req.params
    const brand_menu = await BrandMenu.findOneAndUpdate(
        { brand_id },
        { $setOnInsert: { brand_id, categories: [] } },
        { upsert: true, new: true }
    ).lean()
    res.json({
        success: true,
        data: brand_menu,
    });
}


// Create a or update brand menu category
router.create_update_brand_menu_category = async (req, res) => {
    const { brand_id } = req.params;
    const { categories, option_categories } = req.body
    try {
        const brand_menu = await BrandMenu.findOneAndUpdate(
            { brand_id },
            { categories, option_categories },
            { upsert: true, new: true }
        )
        res.json({
            success: true,
            data: brand_menu,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


// Update an existing brand_menu_category
router.update_brand_menu_category = async (req, res) => {
    const { name, items } = req.body;
    const { brand_id } = req.params

    try {
        const brand_menu = await BrandMenu.findOneAndUpdate(
            { brand_id },
            { $setOnInsert: { brand_id, categories: [] } },
            { upsert: true, new: true }
        )

        const index = brand_menu.categories.findIndex(c => String(c._id) === req.params.category_id);
        brand_menu.categories.splice(index, 1, { name, items });

        await brand_menu.save()
        res.json({
            success: true,
            data: brand_menu?.categories,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Delete a brand_menu_category
router.delete_brand_menu_category = async (req, res) => {
    try {
        const brand_menu = await BrandMenu.findOne({ brand_id: req.params.brand_id })
        const index = brand_menu.categories.findIndex(c => String(c._id) === req.params.category_id);
        brand_menu.categories.splice(index, 1);

        await brand_menu.save()

        res.json({
            success: true,
            data: brand_menu?.categories
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.get_brand_menu_site_app_menu = async (req, res) => {
    try {
        const { brand_id } = req.params;
        let { site_ids, sources } = req.body;

        if (!sources) {
            sources = ["grab", "grab_mart", "shopee", "shopee_fresh", "gojek", "be"]
        }
        const brand = await Brand.findById(brand_id)
        const brand_menu = await BrandMenu.findOne({ brand_id }).lean()

        const site_filter = { brand_id }
        if (site_ids) {
            site_filter._id = site_ids
        }

        const sites = await Site.find(site_filter).limit(50)


        // get all menu from merchant apps and from all sites
        let all_site_menu = {
            categories: brand_menu.categories,
            option_categories: brand_menu.option_categories,
        }

        const merchant_functions = {
            grab: grab.get_menu,
            grab_mart: grab.get_mart_menu,
            shopee: shopee.get_menu,
            shopee_fresh: shopee.get_menu,
            // gojek: gojek.get_menu,
            be: be.get_menu,
            haravan: haravan.get_menu,
        }

        await Promise.all(sites.map(async site => {
            for (const source of sources) {
                const merchant_function = merchant_functions[source]
                if (merchant_function) {
                    const token = await get_token_by_site(site, source)
                    const menu = await merchant_function(token)
                    const new_menu = map_menu(source, { categories: menu.categories, option_categories: menu.option_categories })
                    all_site_menu = helper.compose_site_menu_item(all_site_menu, new_menu.categories)
                    all_site_menu = helper.compose_site_menu_option_item(all_site_menu, new_menu.option_categories)
                }
            }
        }))

        all_site_menu.categories = _.unionBy(all_site_menu.categories, 'name')
        all_site_menu.option_categories = _.uniqBy(all_site_menu.option_categories.reverse(), v => v.id)
            .reverse()


        await BrandMenu.updateMany(
            { brand_id },
            {
                categories: all_site_menu.categories,
                option_categories: all_site_menu.option_categories,
            },
            { upsert: true, new: true }
        ).lean()

        // await save_brand_menu_images(brand_id)

        res.json({
            success: true,
            data: {
                categories: all_site_menu.categories,
                option_categories: all_site_menu.option_categories,
            }
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

router.get_brand_menu_all_site_app_menu = async (req, res) => {
    const file_key = `brand_menu/${Date.now()}/all_site_menu_items.xlsx`
    const { brand_id } = req.params;
    res.json({
        success: true,
        data: `https://storage.googleapis.com/nexpos-files/${file_key}`
    })

    await publisher("EXPORT_BRAND_MENU", { file_key, brand_id, user_id: req.user._id })
}


router.post_brand_menu_from_all_site_apps = async (req, res) => {
    try {
        const { brand_id } = req.params;
        const { categories, option_categories } = req.body;
        if (categories?.length === 0) {
            return res.json({
                success: true,
                data: {
                    categories: [],
                    option_categories: [],
                }
            })
        }
        const brand_menu = await BrandMenu.findOne({ brand_id })
        brand_menu.categories = categories
        brand_menu.option_categories = option_categories
        await brand_menu.save()
        // const brand_menu = await BrandMenu.updateMany(
        //     { brand_id },
        //     {
        //         categories: categories,
        //         option_categories: option_categories,
        //     },
        //     { upsert: true, new: true }
        // ).lean()

        res.json({
            success: true,
            data: {
                categories: brand_menu.categories,
                option_categories: brand_menu.option_categories,
            }
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


router.apply_brand_menu_for_all_sites = async (req, res) => {
    try {
        const { brand_id } = req.params;
        const brand_menu = await BrandMenu.findOne({ brand_id })
        const sites = await Site.find({ brand_id })

        await SiteMenuGroup.updateMany(
            { site_id: sites.map(v => String(v._id)) },
            {
                categories: brand_menu.categories,
                option_categories: brand_menu.option_categories,
            }
        )

        await HubMenuGroup.updateMany(
            { hub_id: sites.map(v => v.hub_id) },
            {
                categories: brand_menu.categories,
                option_categories: brand_menu.option_categories,
            }
        )

        const site_id_has_menu = await SiteMenuGroup.find({ site_id: sites.map(v => String(v._id)) }).distinct('site_id')
        const no_menu_sites = _.difference(sites.map(v => String(v._id)), site_id_has_menu)
        for (const site_id of no_menu_sites) {
            await SiteMenuGroup.create({
                site_id: site_id,
                categories: brand_menu.categories,
                option_categories: brand_menu.option_categories,
            })
        }


        const hub_id_has_menu = await HubMenuGroup.find({ hub_id: sites.map(v => v.hub_id) }).distinct('hub_id')
        const no_menu_hubs = _.difference(sites.map(v => v.hub_id), hub_id_has_menu)
        for (const hub_id of no_menu_hubs) {
            await HubMenuGroup.create(
                {
                    hub_id: hub_id,
                    categories: brand_menu.categories,
                    option_categories: brand_menu.option_categories,
                }
            )
        }

        res.json({
            success: true,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

const convert_data_from_json_to_brand_menu = async (brand_id, category_data, option_category_data) => {
    const brand_menu = await BrandMenu.findOne({ brand_id })

    const category_data_map = category_data.reduce((obj, item) => {
        const key = _textToSlug(item["DANH MỤC"]) + '_' + _textToSlug(item["DANH MỤC CON"] || '') + '_' + _textToSlug(item["TÊN MÓN"])
        obj[key] = item;
        return obj;
    }, {})


    for (let c = 0; c < brand_menu.categories.length; c++) {
        for (let i = 0; i < brand_menu.categories[c].items.length; i++) {
            const category_item = brand_menu.categories[c].items[i];
            const config_item = category_data_map[_textToSlug(brand_menu.categories[c].name) + '__' + _textToSlug(category_item.name)]
            if (config_item) {
                let item_combo = []
                for (let m = 0; m < 5; m++) {
                    if (config_item[`[SP ${m + 1}] MÃ MÓN`]) {
                        item_combo.push({
                            name: config_item[`[SP ${m + 1}] TÊN MÓN`],
                            code: config_item[`[SP ${m + 1}] MÃ MÓN`],
                            price: Number(config_item[`[SP ${m + 1}] GIÁ`]),
                            quantity: Number(config_item[`[SP ${m + 1}] SỐ LƯỢNG`]),
                            unit: config_item[`[SP ${m + 1}] ĐƠN VỊ`],
                        })
                    }
                }
                brand_menu.categories[c].items[i].combo = item_combo

                const quantity_minimum = Number(config_item["SỐ LƯỢNG TỐI THIÊU"] || 0)
                brand_menu.categories[c].items[i].quantity_minimum = quantity_minimum > 0 ? quantity_minimum : 0
                brand_menu.categories[c].items[i].quantity_unlimited = quantity_minimum > 0 ? false : true

            } else {
                brand_menu.categories[c].items[i].combo = []
            }
        }

        for (let s = 0; s < brand_menu.categories[c].sub_categories.length; s++) {
            for (let i = 0; i < brand_menu.categories[c].sub_categories[s].items.length; i++) {
                const category_item = brand_menu.categories[c].sub_categories[s].items[i];
                const config_item = category_data_map[_textToSlug(brand_menu.categories[c].name) + '_' + _textToSlug(brand_menu.categories[c].sub_categories[s].name) + '_' + _textToSlug(category_item.name)]
                if (config_item) {
                    let item_combo = []
                    for (let m = 0; m < 5; m++) {
                        if (config_item[`[SP ${m + 1}] MÃ MÓN`]) {
                            item_combo.push({
                                name: config_item[`[SP ${m + 1}] TÊN MÓN`],
                                code: config_item[`[SP ${m + 1}] MÃ MÓN`],
                                price: Number(config_item[`[SP ${m + 1}] GIÁ`]),
                                quantity: Number(config_item[`[SP ${m + 1}] SỐ LƯỢNG`]),
                                unit: config_item[`[SP ${m + 1}] ĐƠN VỊ`],
                            })
                        }
                    }
                    brand_menu.categories[c].sub_categories[s].items[i].combo = item_combo

                    const quantity_minimum = Number(config_item["SỐ LƯỢNG TỐI THIÊU"] || 0)
                    brand_menu.categories[c].sub_categories[s].items[i].quantity_minimum = quantity_minimum > 0 ? quantity_minimum : 0
                    brand_menu.categories[c].sub_categories[s].items[i].quantity_unlimited = quantity_minimum > 0 ? false : true
                } else {
                    brand_menu.categories[c].sub_categories[s].items[i].combo = []
                }
            }
        }

    }

    const option_category_data_map = option_category_data.reduce((obj, item) => {
        const key = _textToSlug(item["DANH MỤC"]) + '_' + _textToSlug(item["TÊN TÙY CHỌN"])
        obj[key] = item;
        return obj;
    }, {})

    for (let c = 0; c < brand_menu.option_categories.length; c++) {
        O: for (let o = 0; o < brand_menu.option_categories[c].options.length; o++) {
            const option = brand_menu.option_categories[c].options[o];
            const key = _textToSlug(brand_menu.option_categories[c].name) + '_' + _textToSlug(option.name)
            const config_item = option_category_data_map[key]
            if (!config_item) continue O
            let item_combo = []
            for (let m = 0; m < 5; m++) {
                if (config_item[`[SP ${m + 1}] MÃ MÓN`]) {
                    item_combo.push({
                        name: config_item[`[SP ${m + 1}] TÊN MÓN`],
                        code: config_item[`[SP ${m + 1}] MÃ MÓN`],
                        price: Number(config_item[`[SP ${m + 1}] GIÁ`]),
                        quantity: Number(config_item[`[SP ${m + 1}] SỐ LƯỢNG`]),
                        unit: config_item[`[SP ${m + 1}] ĐƠN VỊ`],
                    })
                }
            }
            if (item_combo.length > 0) {
                brand_menu.option_categories[c].options[o].combo = item_combo
                const quantity_minimum = Number(config_item["SỐ LƯỢNG TỐI THIÊU"] || 0)
                brand_menu.option_categories[c].options[o].quantity_minimum = quantity_minimum > 0 ? quantity_minimum : 0
                brand_menu.option_categories[c].options[o].quantity_unlimited = quantity_minimum > 0 ? false : true
            } else {
                brand_menu.option_categories[c].options[o].combo = []
            }
        }
    }

    let all_items = [
        ...brand_menu.categories.flatMap(c => c.items),
        ...brand_menu.categories.flatMap(c => c.sub_categories.flatMap(s => s.items)),
        ...brand_menu.option_categories.flatMap(c => c.options),
    ]
    all_items = all_items.filter(v => v.combo?.length > 0).flatMap(v => v.combo)
    const unique_items = _.uniqBy(all_items, v => v.code)

    const sites = await Site.find({ brand_id }, { _id: 1, hub_id: 1 }).lean();
    const hub_ids = _.uniq(sites.map(v => v.hub_id))

    const new_brand_menu = await BrandMenu.findOneAndUpdate(
        { brand_id },
        { categories: brand_menu.categories, option_categories: brand_menu.option_categories },
        { upsert: true, new: true }
    )
    return new_brand_menu
}


router.get_brand_menu_from_excel = [upload_single, async (req, res) => {
    try {
        const { brand_id } = req.params;
        const workbook = xlsx.read(req.file.buffer)
        const sheetNames = workbook.SheetNames;
        const category_data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNames[0]]);
        const option_category_data = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNames[1]]);

        const new_brand_menu = await convert_data_from_json_to_brand_menu(brand_id, category_data, option_category_data)

        res.json({
            success: true,
            data: new_brand_menu,
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}]

router.export_brand_menu_to_excel = async (req, res) => {
    try {
        const { brand_id } = req.params;
        const brand = await Brand.findById(brand_id).lean()
        const brand_menu = await BrandMenu.findOne({ brand_id }).lean()

        const categories_sheet_data = []
        const option_categories_sheet_data = []
        for (const category of brand_menu.categories) {
            for (const item of category.items || []) {
                const row_item = {
                    "DANH MỤC": category.name,
                    "DANH MỤC CON": "",
                    "THƯƠNG HIỆU MÓN": item.brand_name || '',
                    "TÊN MÓN": item.name,
                    "MÃ MÓN": item.code,
                    "GIÁ BÁN": item.price,
                    "SỐ LƯỢNG TỐI THIÊU": item.quantity_unlimited ? 0 : item.quantity_minimum,
                    "DANH SÁCH TÙY CHỌN": brand_menu.option_categories.filter(v => v.category_ids.includes(category.id)).map(v => v.id).join('\n')
                }
                if (item.combo?.length > 0) {
                    for (let i = 0; i < item.combo.length; i++) {
                        row_item[`[SP ${i + 1}] MÃ MÓN`] = item.combo[i].code
                        row_item[`[SP ${i + 1}] TÊN MÓN`] = item.combo[i].name
                        row_item[`[SP ${i + 1}] GIÁ`] = item.combo[i].price
                        row_item[`[SP ${i + 1}] SỐ LƯỢNG`] = item.combo[i].quantity
                        row_item[`[SP ${i + 1}] ĐƠN VỊ`] = item.combo[i].unit
                    }
                } else {
                    row_item[`[SP 1] MÃ MÓN`] = ''
                    row_item[`[SP 1] TÊN MÓN`] = ''
                    row_item[`[SP 1] GIÁ`] = ''
                    row_item[`[SP 1] SỐ LƯỢNG`] = ''
                    row_item[`[SP 1] ĐƠN VỊ`] = ''
                }
                categories_sheet_data.push(row_item)

            }

            for (const sub_category of category.sub_categories || []) {
                for (const item of sub_category.items || []) {
                    const row_item = {
                        "DANH MỤC": category.name,
                        "DANH MỤC CON": sub_category.name,
                        "THƯƠNG HIỆU MÓN": item.brand_name || '',
                        "TÊN MÓN": item.name,
                        "MÃ MÓN": item.code,
                        "GIÁ BÁN": item.price,
                        "SỐ LƯỢNG TỐI THIÊU": item.quantity_unlimited ? 0 : item.quantity_minimum,
                        "DANH SÁCH TÙY CHỌN": brand_menu.option_categories.filter(v => v.category_ids.includes(category.id)).map(v => v.id).join('\n')
                    }
                    if (item.combo?.length > 0) {
                        for (let i = 0; i < item.combo.length; i++) {
                            row_item[`[SP ${i + 1}] MÃ MÓN`] = item.combo[i].code
                            row_item[`[SP ${i + 1}] TÊN MÓN`] = item.combo[i].name
                            row_item[`[SP ${i + 1}] GIÁ`] = item.combo[i].price
                            row_item[`[SP ${i + 1}] SỐ LƯỢNG`] = item.combo[i].quantity
                            row_item[`[SP ${i + 1}] ĐƠN VỊ`] = item.combo[i].unit
                        }
                    } else {
                        row_item[`[SP 1] MÃ MÓN`] = ''
                        row_item[`[SP 1] TÊN MÓN`] = ''
                        row_item[`[SP 1] GIÁ`] = ''
                        row_item[`[SP 1] SỐ LƯỢNG`] = ''
                        row_item[`[SP 1] ĐƠN VỊ`] = ''
                    }
                    categories_sheet_data.push(row_item)
                }
            }
        }

        for (const category of brand_menu.option_categories) {
            for (const item of category.options) {
                const row_item = {
                    "ID": category.id,
                    "DANH MỤC": category.name,
                    "TÊN TÙY CHỌN": item.name,
                    "MÃ MÓN": item.code,
                    "GIÁ TÙY CHỌN": item.price,
                    "SỐ LƯỢNG TỐI THIÊU": item.quantity_unlimited ? 0 : item.quantity_minimum,
                }

                if (item.combo?.length > 0) {
                    for (let i = 0; i < item.combo.length; i++) {
                        row_item[`[SP ${i + 1}] MÃ MÓN`] = item.combo[i].code
                        row_item[`[SP ${i + 1}] TÊN MÓN`] = item.combo[i].name
                        row_item[`[SP ${i + 1}] GIÁ`] = item.combo[i].price
                        row_item[`[SP ${i + 1}] SỐ LƯỢNG`] = item.combo[i].quantity
                        row_item[`[SP ${i + 1}] ĐƠN VỊ`] = item.combo[i].unit
                    }
                } else {
                    row_item[`[SP 1] MÃ MÓN`] = ''
                    row_item[`[SP 1] TÊN MÓN`] = ''
                    row_item[`[SP 1] GIÁ`] = ''
                    row_item[`[SP 1] SỐ LƯỢNG`] = ''
                    row_item[`[SP 1] ĐƠN VỊ`] = ''
                }
                option_categories_sheet_data.push(row_item)
            }
        }

        const categories_worksheet = xlsx.utils.json_to_sheet(categories_sheet_data);
        const option_categories_worksheet = xlsx.utils.json_to_sheet(option_categories_sheet_data);

        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, categories_worksheet, 'Menu items');
        xlsx.utils.book_append_sheet(workbook, option_categories_worksheet, 'Menu options');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const file = await upload_file({ bucket: 'nexpos-files', key: `brand_menu/${Date.now()}/${slugify(brand.name, '_')}.xlsx`, buff })
        res.json({
            success: true,
            data: file
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

module.exports = router