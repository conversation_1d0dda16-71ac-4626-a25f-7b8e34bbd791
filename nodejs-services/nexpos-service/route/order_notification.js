const moment = require('moment');
const { Order, Brand, Site } = require('../../.shared/database')
const { send_zalo_message, send_zalo_message_by_order_id, ZALO_GROUPS } = require('../../.shared/zalo');
const _ = require('lodash');
const shopee = require('../../.shared/merchant/shopee');
const grab = require('../../.shared/merchant/grab');
const gojek = require('../../.shared/merchant/gojek');
const { get_token_by_site } = require('../../.shared/token_account');

const router = {}

router.cron_order_confirmation = async (req, res, next) => {
    const orders = await Order.find({
        source: ['he', 'local'],
        status: ['PENDING'],
        notification_count_order_confirm: { $lt: 3 },
        'shipment.schedule.from_date_time': {
            $exists: true,
            $gte: moment().add(-45, 'minutes').toDate(),
            $lte: moment().add(15, 'minutes').toDate()
        },
        notification_count_order_confirm_at: { $not: { $gt: moment().add(-15, 'minutes').toDate() } },
    })
    if (orders.length > 0) {
        await Order.updateMany({ _id: orders.map(v => v._id) }, { notification_count_order_confirm_at: moment().toDate(), $inc: { notification_count_order_confirm: 1 } })
    }

    for (const order of orders) {
        await send_zalo_message_by_order_id({
            order_id: order.order_id,
            message: [`Bạn vẫn <b>CHƯA</b> xác nhận đơn hàng <bc style="color:#db342e">${order.order_id}</bc>,`,
            `Thời gian người dùng mong muốn lấy hàng: ${order.shipment.schedule.from_time} -  ${order.shipment.schedule.to_time}`,
            `Vui lòng nhanh chóng kiểm tra chi tiết đơn hàng tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`].join('\n'),
        })
    }
    res.json({
        success: true,
    })
}

router.cron_site_order_cancel = async (req, res, next) => {
    const cancel_orders = await Order.find({
        status: 'CANCEL',
        source: ['grab', 'grab_mart', 'gojek', 'shopee', 'shopee_fresh', 'be'],
        'data_mapping.cancel_by': ['merchant'],
        notification_count_order_cancel: { $not: { $gt: 0 } },
        created_at: {
            $gte: moment().startOf('day').toDate()
        }
    })
    const site_orders = _.groupBy(cancel_orders, 'site_id')
    for (const site_id in site_orders) {
        const orders = site_orders[site_id]
        if (orders.length >= 1) {
            await Order.updateMany({ _id: orders.map(v => v._id) }, { $inc: { notification_count_order_cancel: 1 } })
            const site = await Site.findById(site_id)
            const brand = await Brand.findById(site.brand_id)

            if (brand.name.toLowerCase().includes('nutifood')) {
                const merchant_func = {
                    // gojek: {
                    //     source: 'gojek',
                    //     func: gojek,
                    // },
                    grab: {
                        source: 'grab',
                        func: grab,
                    },
                    grab_mart: {
                        source: 'grab_mart',
                        func: grab,
                    },
                    shopee: {
                        source: 'shopee',
                        func: shopee,
                    },
                    shopee_fresh: {
                        source: 'shopee_fresh',
                        func: shopee,
                    }
                }
                for (const merchant in merchant_func) {
                    const { source, func } = merchant_func[merchant]
                    const token = await get_token_by_site(site, source)
                    await func.update_store_status(token, { status: 'close', duration: null }).catch(console.log) // End of date
                }

                await send_zalo_message({
                    group_link: ZALO_GROUPS.INTERNAL_ORDER_NOTIFICATION,
                    message: [
                        `${site.name} đã bị tắt do <bc style="color:#db342e">HỦY LIÊN TIẾP ${orders.length} ĐƠN HÀNG</bc> trong cùng 1 ngày,`,
                        `Vui lòng kiểm tra chi tiết đơn hàng tại:`,
                        orders.map(order => `${process.env.WEB_URL}/?orderId=${order.order_id} . Lý do hủy: ${order.data_mapping.cancel_reason}`).join('\n'),
                        `Site sẽ được tự động mở lại vào ngày hôm sau.`
                    ].join('\n'),
                })
            }
        }

    }
    res.json({
        success: true,
    })
}


module.exports = router