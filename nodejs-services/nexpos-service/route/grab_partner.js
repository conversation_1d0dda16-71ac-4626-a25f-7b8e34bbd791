const { Hub, Site, User, BrandMenu, BrandMasterData, VendorCallback, HubStock, Brand, Order } = require('../../.shared/database')
const { GRAB_CATEGORIES } = require('../../.shared/merchant/grab_mart_official')
const _ = require('lodash')
const moment = require('moment')
const crypto = require('crypto')
const axios = require('../../.shared/axios')
const { text_slugify, name_to_id } = require('../../.shared/helper');
const { read_sheet } = require('../../.shared/googlesheet')
const grab = require('../../.shared/merchant/grab')
const { get_brand_menu_from_google_sheet_for_grab_mart } = require('../../.shared/googlesheet/template')
const nutifood = require('../../.shared/merchant/nutifood')
const { get_token_by_site } = require('../../.shared/token_account')

let router = {};

const ACCESS_TOKEN = "7X9bD-KcJLszYGx2";

router.get_access_token = async (req, res) => {
    const { client_id, client_secret, grant_type, scope } = req.body;
    // grant_type: client_credentials
    // scope: mart.partner_api
    // TODO: check scope

    res.json({
        access_token: ACCESS_TOKEN,
        token_type: "Bearer",
        expires_in: 86400,
    })
}

router.get_access_token = async (req, res) => {
    const { client_id, client_secret, grant_type, scope } = req.body;
    // grant_type: client_credentials
    // scope: mart.partner_api
    // TODO: check scope

    res.json({
        access_token: ACCESS_TOKEN,
        token_type: "Bearer",
        expires_in: 86400,
    })
}

const BRAND_CONFIGS = [{
    brand_name: 'NutiFood NutiMilk',
    source_site_name: 'Cửa Hàng Sữa NutiMilk - Tôn Đản - SA003',
    source: 'grab_mart',
}, {
    brand_name: 'NutiFood GrowPLUS+',
    source_site_name: 'Cửa Hàng Sữa NutiFood GrowPLUS+ - Tôn Đản - SA003',
    source: 'grab_mart',
}, {
    brand_name: 'NutiFood Varna',
    source_site_name: 'Cửa Hàng Sữa Varna - Tôn Đản - SA003',
    source: 'grab_mart',
}, {
    brand_name: 'NutiFood Bliss',
    source_site_name: 'Bliss Premium Gelato - Nguyễn Hữu Cảnh - SA035',
    source: 'grab',
}, {
    brand_name: 'Nutifood NNDD',
    source_site_name: 'NutiFood Ngôi Nhà Dinh Dưỡng - Tôn Đản - QHCM1003',
    source: 'grab_mart',
}];

router.get_mart_menu = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }

    if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
        res.status(400).json({
            success: false,
            message: "invalid_token",
        });
        return;
    }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }
    const brand = await Brand.findOne({ _id: site.brand_id })
    const master_site = await Site.findOne({ name: BRAND_CONFIGS.find(v => v.brand_name === brand.name).source_site_name })
    const token = await get_token_by_site(master_site, 'grab_mart')
    const menu = await grab.get_mart_menu(token)
    const result = {
        merchantID: merchantID,
        partnerMerchantID: "Bearer",
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sellingTimes: [
            {
                "id": "nexpos_sell_time_daily",
                "startTime": moment().add(-1, 'day').format('YYYY-MM-DD') + " 10:00:00",
                "endTime": moment().add(1, 'year').format('YYYY-MM-DD') + " 00:00:00",
                "name": "Best deal",
                "serviceHours": {
                    "mon": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "tue": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "wed": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "thu": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "fri": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sat": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sun": {
                        "openPeriodType": "OpenAllDay",
                    },
                }
            }
        ],
        categories: menu.categories.map(category => {
            const grab_category = GRAB_CATEGORIES.find(v => text_slugify(v.id) === text_slugify(category.itemClassID));
            if (!grab_category) return null;
            return {
                id: grab_category.id,
                sellingTimeID: "nexpos_sell_time_daily",
                name: grab_category.name,
                availableStatus: "AVAILABLE",
                subCategories: category.subDepartments?.map(sub_category => {

                    const grab_sub_category = grab_category.subCategories.find(v => text_slugify(v.id) === text_slugify(sub_category.itemClassID));
                    if (!grab_sub_category) return null;

                    return {
                        id: grab_sub_category.id,
                        sellingTimeID: "nexpos_sell_time_daily",
                        name: grab_sub_category.name,
                        availableStatus: "AVAILABLE",
                        items: sub_category.items?.map(v => {
                            const grabModifyGroups = menu.option_categories.filter(v2 => v2.relatedItemIDs?.includes(v.itemID));
                            return {
                                id: name_to_id(v.itemName),
                                sellingTimeID: "nexpos_sell_time_daily",
                                name: v.itemName,
                                availableStatus: v.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                description: v.description,
                                price: v.priceInMin,
                                taxable: false,
                                photos: v.imageURLs ?? [],
                                weight: v.weight,
                                modifierGroups: grabModifyGroups.map(v2 => {
                                    return {
                                        "id": v2.modifierGroupID,
                                        "name": v2.modifierGroupName,
                                        "nameTranslation": {},
                                        "availableStatus": v2.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                        "selectionRangeMin": v2.selectionRangeMin,
                                        "selectionRangeMax": v2.selectionRangeMax,
                                        "modifiers": v2.modifiers?.map(v3 => {
                                            return {
                                                "id": v3.modifierID,
                                                "name": v3.modifierName,
                                                "nameTranslation": {},
                                                "availableStatus": v3.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                                "price": v3.priceInMin,
                                                "barcode": "",
                                                "advancedPricing": {}
                                            }
                                        })
                                    }
                                }) || [],
                            }
                        }).filter(Boolean)
                    }
                }).filter(Boolean)
            }
        }).filter(Boolean)
    }

    return res.json(result);
}


router.get_mart_menu_v2 = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    // if (!Authorization || !merchantID || !partnerMerchantID) {
    //     res.status(400).json({
    //         success: false,
    //         message: "missing_headers",
    //     });
    //     return;
    // }

    // if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
    //     res.status(400).json({
    //         success: false,
    //         message: "invalid_token",
    //     });
    //     return;
    // }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }


    const hub = await Hub.findOne({ _id: site.hub_id })
    const menu_mapping = await get_brand_menu_from_google_sheet_for_grab_mart(site.brand_id)
    // if (hub.inventory_source === 'nutifood') {
    //     const brand_menu_items = brand_menu?.categories?.flatMap(v => v.items)
    //     const all_menu_items = _.uniqBy(brand_menu_items.filter(v => v.combo?.length > 0).flatMap(v => v.combo), 'code')
    //     const stock_items = await nutifood.get_menu_items({ shop: hub.code, items: all_menu_items.map(v => v.code) })
    //     const hub_stocks = await HubStock.find({ hub_id: site.hub_id });

    //     // Override quantity base on hub stock config
    //     for (const stock_item of stock_items) {
    //         const hub_stock = hub_stocks.find(v => v.code === stock_item.item_code)
    //         if (hub_stock) {
    //             if (hub_stock.locked_status === 'alway_active') {
    //                 stock_item.quantity = 1000000
    //             }
    //             if (hub_stock.locked_status === 'alway_inactive') {
    //                 stock_item.quantity = 0
    //             }
    //         }
    //     }
    //     for (const category of menu_mapping.categories) {
    //         for (const item of category.items) {
    //             const menu_item = find_item_in_menu_by_name(brand_menu.categories, item)
    //             if (menu_item.combo?.length > 0) {
    //                 const min_quantity = calculate_nutifood_item_quantity(menu_item, stock_items)
    //                 if (min_quantity !== null) {
    //                     item.active = min_quantity > menu_item.quantity_minimum
    //                 } else {
    //                     item.active = false
    //                 }
    //             } else {
    //                 item.active = false
    //             }
    //         }
    //     }
    // }
    for (const category of menu_mapping.categories) {
        for (const sub_category of category.sub_categories) {
            for (const item of sub_category.items) {
                item.active = true
            }
        }
    }


    const result = {
        merchantID: merchantID,
        partnerMerchantID: "Bearer",
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sellingTimes: [
            {
                "id": "nexpos_sell_time_daily",
                "startTime": moment().add(-1, 'day').format('YYYY-MM-DD') + " 10:00:00",
                "endTime": moment().add(1, 'year').format('YYYY-MM-DD') + " 00:00:00",
                "name": "Best deal",
                "serviceHours": {
                    "mon": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "tue": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "wed": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "thu": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "fri": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sat": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sun": {
                        "openPeriodType": "OpenAllDay",
                    },
                }
            }
        ],
        categories: menu_mapping.categories.map(category => {
            const grab_category = GRAB_CATEGORIES.find(v => text_slugify(v.name) === text_slugify(category.name));
            if (!grab_category) return null;
            return {
                id: grab_category.id,
                sellingTimeID: "nexpos_sell_time_daily",
                name: grab_category.name,
                availableStatus: "AVAILABLE",
                subCategories: category.sub_categories?.map(sub_category => {
                    const grab_sub_category = grab_category.subCategories.find(v => text_slugify(v.name) === text_slugify(sub_category.name));
                    if (!grab_sub_category) return null;

                    return {
                        id: grab_sub_category.id,
                        sellingTimeID: "nexpos_sell_time_daily",
                        name: grab_sub_category.name,
                        availableStatus: "AVAILABLE",
                        items: sub_category.items?.map(v => {
                            // const grabModifyGroups = menu.modifierGroups.filter(v2 => v2.relatedItemIDs?.includes(v.itemID));
                            return {
                                id: v.id,
                                sellingTimeID: "nexpos_sell_time_daily",
                                name: v.name,
                                availableStatus: v.active ? "AVAILABLE" : "UNAVAILABLE",
                                description: v.description,
                                price: v.price,
                                taxable: false,
                                photos: v.images ?? [],
                                maxCount: v.max_item_per_order ?? 1000,
                                weight: null,
                                // weight: {
                                //     unit: "g",
                                //     value: 50,
                                //     count: 1,
                                //     originalValue: 0
                                // },
                                modifierGroups: [],
                                // modifierGroups: grabModifyGroups.map(v2 => {
                                //     return {
                                //         "id": v2.modifierGroupID,
                                //         "name": v2.modifierGroupName,
                                //         "nameTranslation": {},
                                //         "availableStatus": v2.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                //         "selectionRangeMin": v2.selectionRangeMin,
                                //         "selectionRangeMax": v2.selectionRangeMax,
                                //         "modifiers": v2.modifiers?.map(v3 => {
                                //             return {
                                //                 "id": v3.modifierID,
                                //                 "name": v3.modifierName,
                                //                 "nameTranslation": {},
                                //                 "availableStatus": v3.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                //                 "price": v3.priceInMin,
                                //                 "barcode": "",
                                //                 "advancedPricing": {}
                                //             }
                                //         })
                                //     }
                                // }) || [],
                            }
                        }).filter(Boolean)
                    }
                }).filter(Boolean)
            }
        }).filter(Boolean)
    }

    return res.json(result);
}


router.clone_site_campaign = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }


}

router.menu_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'grab',
        type: 'menu',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

router.order_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'grab',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    const { orderID, state, code, partnerMerchantID } = req.body

    // DRIVER_ALLOCATED - Driver has been allocated
    // DRIVER_ARRIVED - Driver has reached your store to collect the order
    // COLLECTED - Driver has collected the order from your store
    // DELIVERED - Driver has delivered the order to the consumer location
    // CANCELLED - Order has been cancelled by the consumer, merchant, or driver for some reason, Refer to message for more information.
    // FAILED - The order might fail because of unallocation, reallocation, system issues, etc. If there is a system issue, GrabMart will flag the order, and escalate if the issue is on your side. Refer to message for more information.

    const order_status = {
        'DRIVER_ALLOCATED': 'PENDING',
        'DRIVER_ARRIVED': 'DOING',
        'COLLECTED': 'PICK',
        'DELIVERED': 'FINISH',
        'CANCELLED': 'CANCEL',
        'FAILED': 'CANCEL',
    }[state]

    await Order.findOneAndUpdate({ order_id: orderID }, {
        status: order_status,
    }, { new: true })

    res.json({ success: true });
}

router.get_access_token = async (req, res) => {
    const { client_id, client_secret, grant_type, scope } = req.body;
    // grant_type: client_credentials
    // scope: mart.partner_api
    // TODO: check scope

    res.json({
        access_token: ACCESS_TOKEN,
        token_type: "Bearer",
        expires_in: 86400,
    })
}

router.get_food_menu = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }

    if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
        res.status(400).json({
            success: false,
            message: "invalid_token",
        });
        return;
    }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }

    const master_data = await BrandMasterData.findOne({ brand_id: site.brand_id, key: 'grab_food_menu' }).lean();
    if (!master_data) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }

    const menu = master_data.value
    const result = {
        merchantID: merchantID,
        partnerMerchantID: partnerMerchantID,
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sellingTimes: [
            {
                "id": "nexpos_sell_time_daily",
                "startTime": "2023-10-01 10:00:00",
                "endTime": "2025-01-01 00:00:00",
                "name": "Best deal",
                "serviceHours": {
                    "mon": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "tue": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "wed": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "thu": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "fri": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sat": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sun": {
                        "openPeriodType": "OpenAllDay",
                    },
                }
            }
        ],
        categories: menu.categories.map(category => {
            return {
                id: category.categoryID,
                sellingTimeID: "nexpos_sell_time_daily",
                name: category.categoryName,
                availableStatus: category.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                items: category.items?.map(v => {
                    const grabModifyGroups = menu.modifierGroups.filter(v2 => v2.relatedItemIDs?.includes(v.itemID));
                    return {
                        id: v.itemID + '_' + moment().format('DD'),
                        sellingTimeID: "nexpos_sell_time_daily",
                        name: v.itemName,
                        availableStatus: v.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                        description: v.description,
                        price: v.priceInMin,
                        taxable: false,
                        photos: v.imageURL ? [v.imageURL] : [],
                        weight: v.weight,
                        modifierGroups: grabModifyGroups.map(v2 => {
                            return {
                                "id": v2.modifierGroupID,
                                "name": v2.modifierGroupName,
                                "nameTranslation": {},
                                "availableStatus": v2.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                "selectionRangeMin": v2.selectionRangeMin,
                                "selectionRangeMax": v2.selectionRangeMax,
                                "modifiers": v2.modifiers?.map(v3 => {
                                    return {
                                        "id": v3.modifierID,
                                        "name": v3.modifierName,
                                        "nameTranslation": {},
                                        "availableStatus": v3.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                        "price": v3.priceInMin,
                                        "barcode": "",
                                        "advancedPricing": {}
                                    }
                                })
                            }
                        }) || [],
                    }
                }).filter(Boolean)
            }
        }).filter(Boolean).filter(v => v.items.length > 0)
    }

    return res.json(result);
}

router.get_food_menu_v2 = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }

    if (Authorization !== `Bearer ${ACCESS_TOKEN}`) {
        res.status(400).json({
            success: false,
            message: "invalid_token",
        });
        return;
    }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }
    const brand = await Brand.findOne({ _id: site.brand_id })
    const brand_config = await read_sheet(brand.menu_sheet_file_id, 1, ['Brand'])
    const select_brand_config = brand_config['Brand'].find(v => v.brand_name === brand.name)
    if (!select_brand_config) {
        return res.status(400).json({
            success: false,
            message: "brand_not_found",
        });
    }
    const menu_mapping = await google_sheet_to_menu(brand.menu_sheet_file_id, {
        menu_name: JSON.parse(select_brand_config.shopee_fresh)?.menu,
        option_name: JSON.parse(select_brand_config.shopee_fresh)?.option,
    })


    const master_data = await BrandMasterData.findOne({ brand_id: site.brand_id, key: 'grab_food_menu' }).lean();
    if (!master_data) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }

    const menu = master_data.value
    const result = {
        merchantID: merchantID,
        partnerMerchantID: partnerMerchantID,
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sellingTimes: [
            {
                "id": "nexpos_sell_time_daily",
                "startTime": moment().add(-1, 'day').format('YYYY-MM-DD') + " 10:00:00",
                "endTime": moment().add(1, 'year').format('YYYY-MM-DD') + " 00:00:00",
                "name": "Best deal",
                "serviceHours": {
                    "mon": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "tue": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "wed": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "thu": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "fri": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sat": {
                        "openPeriodType": "OpenAllDay",
                    },
                    "sun": {
                        "openPeriodType": "OpenAllDay",
                    },
                }
            }
        ],
        categories: menu.categories.map(category => {
            return {
                id: category.categoryID,
                sellingTimeID: "nexpos_sell_time_daily",
                name: category.categoryName,
                availableStatus: category.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                items: category.items?.map(v => {
                    const grabModifyGroups = menu.modifierGroups.filter(v2 => v2.relatedItemIDs?.includes(v.itemID));
                    return {
                        id: v.itemID + '_' + moment().format('DD'),
                        sellingTimeID: "nexpos_sell_time_daily",
                        name: v.itemName,
                        availableStatus: v.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                        description: v.description,
                        price: v.priceInMin,
                        taxable: false,
                        photos: v.imageURL ? [v.imageURL] : [],
                        weight: v.weight,
                        modifierGroups: grabModifyGroups.map(v2 => {
                            return {
                                "id": v2.modifierGroupID,
                                "name": v2.modifierGroupName,
                                "nameTranslation": {},
                                "availableStatus": v2.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                "selectionRangeMin": v2.selectionRangeMin,
                                "selectionRangeMax": v2.selectionRangeMax,
                                "modifiers": v2.modifiers?.map(v3 => {
                                    return {
                                        "id": v3.modifierID,
                                        "name": v3.modifierName,
                                        "nameTranslation": {},
                                        "availableStatus": v3.availableStatus === 1 ? "AVAILABLE" : "UNAVAILABLE",
                                        "price": v3.priceInMin,
                                        "barcode": "",
                                        "advancedPricing": {}
                                    }
                                })
                            }
                        }) || [],
                    }
                }).filter(Boolean)
            }
        }).filter(Boolean).filter(v => v.items.length > 0)
    }

    return res.json(result);
}

router.grab_food_menu_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'grab_food',
        type: 'menu',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

router.grab_food_order_webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'grab_food',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        success: true,
        method: req.method,
    })
    res.json({ success: true });
}

module.exports = router;