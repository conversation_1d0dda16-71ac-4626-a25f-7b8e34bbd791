const { Campaign, BrandMenu } = require('../../.shared/database')
const moment = require('moment-timezone')
const { flatMap, filter, map, find } = require('lodash')

const bundle_campaign_status = (campaign) => {
  const now = moment()
  const start_date = moment(campaign.start_date)
  const end_date = moment(campaign.end_date)

  let status = 'pending'
  if (now.isBetween(start_date, end_date)) {
    status = 'active'
  }

  if (now.isAfter(end_date)) {
    status = 'inactive'
  }

  return {
    ...campaign,
    status,
  }
}

exports.get_campaign_list = async (req, res) => {
  const is_system = req.permissions?.includes('system')
  const brands = req.user.brands
  const query = is_system ? {} : { brand_id: { $in: brands } }

  const result = await Campaign.paginate(
    query,
    {
      page: Number(req.query.page || 1),
      limit: Number(req.query.limit || 100), // Max 100 docs
      sort: { created_at: -1 },
      customLabels: { docs: 'data' },
      lean: true,
    }
  )

  res.json({
    success: true,
    ...result,
    data: result.data.map(bundle_campaign_status),
  })
}

exports.create_campaign = async (req, res) => {
  const user_id = req.user._id
  const brand_id = req.params.brand_id
  const { name, description, start_date, end_date, items } = req.body

  const created = await Campaign.create({
    brand_id,
    name,
    description,
    start_date,
    end_date,
    created_by: user_id,
    items,
  })

  res.json({
    success: true,
    data: bundle_campaign_status(created),
  })
}

exports.update_campaign = async (req, res) => {
  const { brand_id, id } = req.params
  const { name, description, start_date, end_date, items } = req.body

  const campaign = await Campaign.findOne({ _id: id, brand_id })
  if (!campaign) {
    return res.status(404).json({
      success: false,
      error: 'campaign_not_found',
    })
  }

  await Campaign.updateOne(
    { _id: id },
    {
      name,
      description,
      start_date,
      end_date,
      items,
    },
    {
      runValidators: true,
    }
  )

  res.json({
    success: true,
    data: bundle_campaign_status({ ...campaign.toObject(), ...req.body })
  })
}

exports.delete_campaign = async (req, res) => {
  const { brand_id, id } = req.params

  const campaign = await Campaign.findOne({ _id: id, brand_id })
  if (!campaign) {
    return res.status(404).json({
      success: false,
      error: 'campaign_not_found',
    })
  }

  await Campaign.deleteOne({ _id: id })

  res.json({
    success: true,
  })
}

exports.partner_get_campaign_list = async (req, res) => {
  const { _id, brands } = req.user
  const { status } = req.query

  let query = {
    brand_id: { $in: brands },
  }

  if (status === 'pending') {
    query = {
      ...query,
      end_date: { $gte: moment().toISOString() },
      partners: { $not: { $elemMatch: { user_id: String(_id) } } },
    }
  } else if (status === 'joined') {
    query = {
      ...query,
      end_date: { $gte: moment().toISOString() },
      partners: { $elemMatch: { user_id: String(_id) } },
    }
  } else if (status === 'ended') {
    query = {
      ...query,
      end_date: { $lte: moment().toISOString() },
      partners: { $elemMatch: { user_id: String(_id) } },
    }
  }

  const result = await Campaign.paginate(
    query,
    {
      page: Number(req.query.page || 1),
      limit: Number(req.query.limit || 100), // Max 100 docs
      sort: { created_at: 1 },
      customLabels: { docs: 'data' },
      lean: true,
    }
  )

  const categoryResults = await BrandMenu.find()
  const categories = flatMap(categoryResults, (x) => x?.categories || [])

  const allItems = flatMap(categories, (category) => [
    ...category.items,
    ...flatMap(category.sub_categories, (subCategory) => subCategory.items),
  ])

  const productIds = flatMap(result.data, (x) => x?.items || []).map((x) => x?.id)
  const filteredProducts = filter(allItems, (x) => productIds?.includes(String(x?.id)))

  const mappingData = map(result.data, (x) => {
    const joined = x.partners.find((p) => String(p?.user_id) === String(_id))
    return {
      ...bundle_campaign_status(x),
      joined: !!joined,
      joined_date: joined?.joined_date,
      items: map(x?.items, (i) => {
        return {
          ...i,
          product: find(filteredProducts, (x) => String(x?.id) === i?.id),
        }
      }),
    }
  })

  res.json({
    success: true,
    ...result,
    data: mappingData,
  })
}

exports.partner_join_campaign = async (req, res) => {
  const { _id, brands } = req.user
  const { campaign_id } = req.body

  const campaign = await Campaign.findOne({
    brand_id: { $in: brands },
    _id: campaign_id,
  })

  if (!campaign) {
    return res.status(404).json({
      success: false,
      error: 'campaign_not_found',
    })
  }

  const joined = campaign.partners.find((p) => String(p?.user_id) === String(_id))
  if (joined) {
    return res.status(400).json({
      success: false,
      error: 'partner_already_joined',
    })
  }

  await Campaign.updateOne({ _id: campaign_id }, {
    $push: {
      partners: {
        user_id: String(_id),
        joined_date: moment().toISOString(),
      },
    },
  }, { runValidators: true })

  res.json({
    success: true,
  })
}

exports.partner_get_campaign_detail = async (req, res) => {
  const { id } = req.params

  const campaign = await Campaign.findOne({
    _id: id,
  })

  if (!campaign) {
    return res.status(404).json({
      success: false,
      error: 'campaign_not_found',
    })
  }

  res.json({
    success: true,
    campaign,
  })
}
