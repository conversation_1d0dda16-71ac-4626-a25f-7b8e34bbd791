const crypto = require('crypto')
const path = require('path')
const axios = require('axios')
const Jimp = require('jimp')
const { Storage } = require('@google-cloud/storage');
const { upload_single, upload_array } = require('../middlewares/upload_file');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY)
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n') // Fix bug when encode
const storage = new Storage({ credentials });

exports.upload_image = [upload_single, async (req, res) => {
    try {
        if (!req.file) {
            throw Error('Invalid file')
        }
        const file_name_base = path.basename(req.file.originalname || req.file.name)
        const md5_hash = crypto.createHash('md5').update(req.file.buffer).digest('hex')
        const new_file_name = `${file_name_base}-${md5_hash}.jpg`

        const optimizedImage = await Jimp.read(req.file.buffer).then((image) => {
            return image
                .resize(1080, Jimp.AUTO) // resize to a maximum width of 1080 pixels
                .quality(80) // set quality to 80%
                .getBufferAsync(Jimp.MIME_JPEG) // convert to WebP format
        })

        const bucket = storage.bucket(`nexpos-images`);
        const file = bucket.file(`images/${new_file_name}`);
        await file.save(optimizedImage);

        // Return the URL of the uploaded file
        res.json({
            success: true,
            data: file.publicUrl(),
        })
    } catch (err) {
        console.error(err)
        res.status(500).json({
            success: false,
            message: err?.message || 'Error uploading file',
        })
    }
}]

exports.upload_multiple_images = [upload_array, async (req, res) => {
    try {
        console.log(req.files)
        if (!req.files) {
            return res.status(400).json({
                success: false,
                error: 'no_files_uploaded_or_invalid'
            })
        }

        const promises = req.files.map(async (file) => {
            const file_name_base = path.basename(file.originalname || file.name)
            const md5_hash = crypto.createHash('md5').update(file.buffer).digest('hex')
            const new_file_name = `${file_name_base}-${md5_hash}.jpg`
            try {
                const optimizedImage = await Jimp.read(file.buffer).then((image) => {
                    return image
                        .resize(1080, Jimp.AUTO) // resize to a maximum width of 1080 pixels
                        .quality(80) // set quality to 80%
                        .getBufferAsync(Jimp.MIME_JPEG) // convert to WebP format
                })

                const bucket = storage.bucket(`nexpos-images`);
                const bucketFile = bucket.file(`images/${new_file_name}`);
                await bucketFile.save(optimizedImage);
                console.log({ bucketFile })
                return Promise.resolve(bucketFile.publicUrl())
            } catch (error) {
                return Promise.reject(error)
            }
        })
        const data = await Promise.all(promises);

        res.json({ success: true, data })
    } catch (err) {
        console.error(err)
        res.status(500).json({
            success: false,
            message: err?.message || 'Error uploading file',
        })
    }
}]

exports.upload_file = [upload_single, async (req, res) => {
    try {
        if (!req.file) {
            throw Error('Invalid file')
        }
        const file_name = req.file.originalname || req.file.name
        const md5_hash = crypto.createHash('md5').update(req.file.buffer).digest('hex')

        const bucket = storage.bucket(`nexpos-files`);
        const file = bucket.file(`uploads/${md5_hash}_${file_name}`);
        await file.save(req.file.buffer);

        // Return the URL of the uploaded file
        res.json({
            success: true,
            data: file.publicUrl(),
        })
    } catch (err) {
        console.error(err)
        res.status(500).json({
            success: false,
            message: err?.message || 'Error uploading file',
        })
    }
}]

exports.get_file_headers = async (req, res) => {
    try {
        if (!req.body.file) {
            throw Error('Invalid file')
        }
        const resp = await axios({
            method: 'head',
            maxBodyLength: Infinity,
            url: req.body.file,
            headers: {}
        })
        res.json({
            success: true,
            data: resp.headers,
        })
    } catch (err) {
        console.log(err.message)
        res.json({
            success: true,
            data: {},
        })
    }
}