const { default: axios } = require('axios')
const { RetailerSaleConfig, Brand, Voucher, Order } = require('../../.shared/database')
const xlsx = require('xlsx')
const { upload_file } = require('../../.shared/storage')
const moment = require('moment')
const _ = require('lodash')
const nanoid = require('nanoid')

const _create_voucher = async (voucher_config, brand_id) => {
  if (!voucher_config) return null

  const { voucher_code, prefix } = voucher_config
  const code = voucher_code ?? prefix + nanoid.nanoid(6).toUpperCase()
  const created = await Voucher.create({
    code,
    vendor: 'nexdor',
    brand_id,
  })
  return created
}

exports.brand_create_config = async (req, res) => {
  const { brand_id } = req.params
  const { config, type, name, description, active, start_date, end_date, order, voucher_config } = req.body

  if (voucher_config && voucher_config.voucher_code) {
    // check if code is existed
    const existed = await Voucher.findOne({ code: voucher_config.voucher_code })
    if (existed) {
      return res.status(400).json({
        success: false,
        error: 'voucher_code_existed',
      })
    }
  }

  if (config && config.url) {
    const response = await axios.get(config.url, { responseType: 'arraybuffer' })
    const workbook = xlsx.read(response.data, { type: 'buffer' })
    const file = workbook.Sheets[workbook.SheetNames[0]]
    const json = xlsx.utils.sheet_to_json(file)

    const items = json.map((item) => {
      return {
        item_name: item['Name'],
        category_name: item['Category Name'],
        quantity: item['Quantity'],
        discount: item['Discount'],
        min_value: item['Min Value'],
        free_ship: item['Is Free Ship'],
      }
    })

    const created = await RetailerSaleConfig.create({
      brand_id,
      config: { ...config, items },
      name,
      type,
      description,
      active,
      start_date,
      end_date,
      order: order || new Date().getTime(),
      voucher_config,
    })
    const voucher = await _create_voucher(voucher_config, brand_id)
    if (voucher_config && !voucher_config.voucher_code) {
      created.voucher_config = {
        ...created.voucher_config,
        voucher_code: voucher.code,
      }
      await created.save()
    }

    return res.json({
      success: true,
      data: created,
    })
  }

  const created = await RetailerSaleConfig.create({
    brand_id,
    config,
    name,
    type,
    description,
    active,
    start_date,
    end_date,
    order: order || new Date().getTime(),
    voucher_config,
  })
  const voucher = await _create_voucher(voucher_config, brand_id)
  if (voucher_config && !voucher_config.voucher_code) {
    created.voucher_config = {
      ...created.voucher_config,
      voucher_code: voucher.code,
    }
    await created.save()
  }

  return res.json({
    success: true,
    data: created,
  })
}

exports.download_config_template = async (req, res) => {
  const { type } = req.query

  let template = {}

  if (type === 'percent_discount' || !type) {
    template = {
      Name: '',
      'Category Name': '',
      Quantity: '',
    }
  }

  if (type === 'fixed_discount') {
    template = {
      Name: '',
      'Category Name': '',
      Discount: '',
    }
  }

  const config_worksheet = xlsx.utils.json_to_sheet([template])

  const workbook = xlsx.utils.book_new()
  xlsx.utils.book_append_sheet(workbook, config_worksheet, 'Config')
  const buff = xlsx.write(workbook, { type: 'buffer' })

  const timestamp = moment().format('YYMMDDHHmm')
  const file = await upload_file({ bucket: 'nexpos-files', key: `Config_Sale_${timestamp}.xlsx`, buff })

  res.json({
    success: true,
    data: file,
  })
}

exports.brand_update_config = async (req, res) => {
  const { brand_id, config_id } = req.params
  const { config, type, name, description, active, start_date, end_date, order, voucher_config } = req.body

  const existed = await RetailerSaleConfig.findOne({ _id: config_id, brand_id })
  if (!existed) {
    return res.status(404).json({
      success: false,
      error: 'config_not_found',
    })
  }

  if (config && config.url) {
    const response = await axios.get(config.url, { responseType: 'arraybuffer' })
    const workbook = xlsx.read(response.data, { type: 'buffer' })
    const file = workbook.Sheets[workbook.SheetNames[0]]
    const json = xlsx.utils.sheet_to_json(file)

    const items = json.map((item) => {
      return {
        item_name: item['Name'],
        category_name: item['Category Name'],
        quantity: item['Quantity'],
        discount: item['Discount'],
        min_value: item['Min Value'],
        free_ship: item['Is Free Ship'],
      }
    })

    const voucher_config = {
      ...existed.voucher_config,
      ..._.omit(voucher_config || {}, ['voucher_code', 'prefix']),
    }

    await RetailerSaleConfig.updateOne(
      { _id: config_id, brand_id },
      {
        config: { ...config, items },
        name,
        type,
        description,
        active,
        start_date,
        end_date,
        order,
        voucher_config,
      }
    )

    return res.json({
      success: true,
    })
  }

  const voucher_config_payload = {
    ...existed.voucher_config,
    ..._.omit(voucher_config || {}, ['voucher_code', 'prefix']),
  }

  await RetailerSaleConfig.updateOne(
    { _id: config_id, brand_id },
    {
      config,
      name,
      type,
      description,
      active,
      start_date,
      end_date,
      order,
      voucher_config: voucher_config_payload,
    }
  )

  res.json({
    success: true,
  })
}

exports.brand_delete_sale_config = async (req, res) => {
  const { brand_id, config_id } = req.params

  await RetailerSaleConfig.deleteOne({ _id: config_id, brand_id })

  res.json({
    success: true,
  })
}

exports.get_sale_config = async (req, res) => {
  const brands = req.user.brands
  const { has_voucher } = req.query

  const filter = { brand_id: { $in: brands } }

  if (has_voucher === 'false') {
    // check filter with voucher_config -> voucher_code is nil or empty
    filter.$and = [
      ...(filter.$and || []), // Preserve existing $and conditions if any
      {
        $or: [
          { 'voucher_config.voucher_code': { $exists: false } }, // Field doesn't exist
          { 'voucher_config.voucher_code': null }, // Field is null
          { 'voucher_config.voucher_code': '' }, // Field is an empty string
        ],
      },
    ]
  }

  const configs = await RetailerSaleConfig.find(filter).sort({ order: 1 })

  // populate brands s
  for (let i = 0; i < configs.length; i++) {
    const brand = await Brand.findOne(
      { _id: configs[i].brand_id },
      {
        name: 1,
        _id: 1,
      }
    )

    configs[i].brand = brand
  }

  res.json({
    success: true,
    data: configs,
  })
}

exports.get_sale_config_by_brand = async (req, res) => {
  const { brand_id } = req.params
  const { status, has_voucher } = req.query // running, upcoming, complete
  const filter = { brand_id }
  if (status === 'running') {
    filter.$or = [
      { start_date: { $lte: moment().toISOString() }, end_date: { $gte: moment().toISOString() } },
      { start_date: undefined, end_date: undefined },
    ]
  } else if (status === 'upcoming') {
    filter.start_date = { $gt: moment().toISOString() }
  } else if (status === 'complete') {
    filter.end_date = { $lt: moment().toISOString() }
  }

  if (has_voucher === 'false') {
    // check filter with voucher_config -> voucher_code is nil or empty
    filter.$and = [
      ...(filter.$and || []), // Preserve existing $and conditions if any
      {
        $or: [
          { 'voucher_config.voucher_code': { $exists: false } }, // Field doesn't exist
          { 'voucher_config.voucher_code': null }, // Field is null
          { 'voucher_config.voucher_code': '' }, // Field is an empty string
        ],
      },
    ]
  }

  const config = await RetailerSaleConfig.find(filter).sort({ order: 1 })

  res.json({
    success: true,
    data: config,
  })
}

exports.get_voucher_usage = async (req, res) => {
  const { config_id } = req.params
  const { page = 0, limit = 10 } = req.query

  // Get the sale config to extract voucher code
  const saleConfig = await RetailerSaleConfig.findOne({ _id: config_id }).select({
    voucher_config: 1,
  })
  if (!saleConfig) {
    return res.status(404).json({
      success: false,
      error: 'config_not_found',
    })
  }

  if (!saleConfig.voucher_config || !saleConfig.voucher_config.voucher_code) {
    return res.json({
      success: true,
      data: {
        voucher_code: null,
        orders: [],
        usage_count: 0,
        quantity: 0,
      },
    })
  }

  const voucherCode = saleConfig.voucher_config.voucher_code

  // Query orders that used this voucher code
  const orders = await Order.find({
    'data_mapping.coupons.code': voucherCode,
  })
    .skip(page * limit)
    .limit(limit)
    .sort({ 'data_mapping.order_time_sort': -1 })
    .select({
      order_id: 1,
      'data_mapping.order_time': 1,
      'data_mapping.customer_name': 1,
      'data_mapping.total': 1,  
      status: 1,
    })
  
  const orderData = orders.map((order) => ({
    order_id: order.order_id,
    order_time: order.data_mapping.order_time,
    customer_name: order.data_mapping?.customer_name,
    total: order.data_mapping?.total,
    status: order.status,
  }))

  const count = await Order.countDocuments({
    'data_mapping.coupons.code': voucherCode,
    status: { $ne: 'CANCEL' },
  })

  // double check usage quantity
  if(saleConfig.voucher_config.usage_count !== count) {
    saleConfig.voucher_config.usage_count = count
    await saleConfig.save()
  }

  return res.json({
    success: true,
    data: {
      voucher_code: voucherCode,
      usage_count: count,
      orders: orderData,
      quantity: saleConfig.voucher_config.quantity,
    },
  })
}
