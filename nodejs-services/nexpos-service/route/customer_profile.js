const { CustomerPro<PERSON>le, Brand, CustomerSegment } = require('../../.shared/database')
const xlsx = require('xlsx')
const { upload_file } = require('../../.shared/storage')

const build_filters = (brand_id, filters = []) => {
  let filter = {
    brand_id,
  }
  try {
    for (const f of filters) {
      const { field, operator, value } = f

      switch (operator) {
        case 'bw': {
          const [start, end] = value
          const formatted_start = field === 'last_order_at' ? new Date(start) : start
          const formatted_end = field === 'last_order_at' ? new Date(end) : end
          filter[field] = { $gte: formatted_start, $lte: formatted_end }

          continue
        }

        case 'lt':
        case 'lte':
        case 'gt':
        case 'gte': {
          const formatted_value = field === 'last_order_at' ? new Date(value) : value
          filter[field] = { [`$${operator}`]: formatted_value }
          continue
        }

        case 'in': {
          filter[field] = { $all: value }
          continue
        }

        default: {
          filter[field] = value
        }
      }
    }

    return { brands: { $elemMatch: filter } }
  } catch (error) {
    throw new Error('invalid_filters')
  }
}

const get_profiles = async (req, res) => {
  const { brand_id, filters } = req.body
  if (!brand_id) {
    return res.json({ success: false, message: 'brand_id_required' })
  }

  const filter = build_filters(brand_id, filters)
  // console.log(filter, JSON.stringify(filter))
  const profile_paginate = await CustomerProfile.paginate(filter, {
    page: Number(req.body.page || 1),
    limit: Number(req.body.limit || 20), // Max 100 docs
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
  })

  const brand_segments = await CustomerSegment.find({ brand_id }, { name: 1 }).lean()

  const data = profile_paginate.data.map((profile) => {
    const { brands, ...rest } = profile.toJSON()
    const info = brands.find((b) => b.brand_id === brand_id) || {}

    const segments = (info.segments || []).filter((s) => brand_segments.find((bs) => bs.name === s))

    return { ...rest, ...info, segments }
  })

  const max_orders = await CustomerProfile.aggregate([
    { $unwind: '$brands' },
    { $match: { 'brands.brand_id': brand_id } },
    { $group: { _id: null, total_orders: { $max: '$brands.total_orders' } } },
  ]).then((r) => r[0])

  const max_amount = await CustomerProfile.aggregate([
    { $unwind: '$brands' },
    { $match: { 'brands.brand_id': brand_id } },
    { $group: { _id: null, total_order_amount: { $max: '$brands.total_order_amount' } } },
  ]).then((r) => r[0])

  res.json({
    success: true,
    ...profile_paginate,
    data,
    filters: {
      max_orders: max_orders?.total_orders,
      max_amount: max_amount?.total_order_amount,
    },
  })
}

const export_profiles = async (req, res) => {
  const { brand_id, filters } = req.body
  if (!brand_id) {
    return res.json({ success: false, message: 'brand_id_required' })
  }

  const filter = build_filters(brand_id, filters)
  const profiles = await CustomerProfile.find(filter)

  const data = []
  profiles.forEach((profile) => {
    const brand_orders = profile.brands.find((b) => b.brand_id === brand_id) || {}

    const d = {
      'Số điện thoại': profile.phone,
      Tên: profile.names.join(' - '),
      'Địa chỉ': profile.addresses.join(' - '),
      'Tổng số Đơn hàng': brand_orders.total_orders,
      'Tổng giá trị đơn hàng': brand_orders.total_order_amount,
      'Phân loại': brand_orders.segments.join(', '),
    }
    data.push(d)
  })

  const workbook = new xlsx.utils.book_new()
  const worksheet = xlsx.utils.json_to_sheet(data)
  xlsx.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  xlsx.writeFile(workbook, 'profiles.xlsx')

  const buff = xlsx.write(workbook, { type: 'buffer' })
  const file = await upload_file({ bucket: 'nexpos-files', key: `reports/customer_profile${Date.now()}.xlsx`, buff })

  res.json({
    success: true,
    file,
  })
}

// periodically sync segment for updated profiles
const sync_segment_for_updated_profiles = async (req, res) => {
  const segments = await CustomerSegment.find({}).lean()

  for (const segment of segments) {
    const query = build_filters(segment.brand_id, segment.filters)

    await CustomerProfile.updateMany(
      // { segment_synced: false, ...query },
      query,
      {
        $addToSet: { 'brands.$.segments': segment.name },
        // $set: { segment_synced: true }
      }
    )
  }

  res.json({ success: true })
}

const sync_brand_segment = async (brand_id, segment, old_segment) => {
  if (old_segment) {
    await CustomerProfile.updateMany({
      brands: { $elemMatch: { brand_id, segments: old_segment.name } }
    }, { $pull: { 'brands.$.segments': old_segment.name } })
  }

  if (segment) {
    const query = build_filters(brand_id, segment.filters)
    await CustomerProfile.updateMany(
      query,
      { $addToSet: { 'brands.$[elem].segments': segment.name } },
      { arrayFilters: [{ 'elem.brand_id': brand_id }] }
    )
  }
}

const create_segment = async (req, res) => {
  const { name, filters, description, brand_id } = req.body

  const segment = await CustomerSegment.create({ name, filters, description, brand_id })
  await sync_brand_segment(brand_id, segment, null)
  res.json({ success: true, data: segment })
}

const update_segment = async (req, res) => {
  const { description, name, filters } = req.body

  const id = req.params.id
  const existed = await CustomerSegment.findOne({ _id: id, name, brand_id: { $in: req.user.brands } }).lean()

  if (!existed) {
    return res.json({ success: false, message: 'segment_not_found' })
  }

  const updated = await CustomerSegment.findByIdAndUpdate(req.params.id, { filters, description, name })
  if (updated.name !== existed.name || updated.filters !== existed.filters) {
    await sync_brand_segment(updated.brand_id, updated, existed)
  }

  res.json({ success: true, data: updated })
}

const get_segments = async (req, res) => {
  const brand_id = req.query.brand_id

  const segments = await CustomerSegment.find({ brand_id }).sort({ created_at: -1 }).lean()
  res.json({ success: true, data: segments })
}

const delete_segment = async (req, res) => {
  const id = req.params.id
  const segment = await CustomerSegment.findOne({ _id: id }).lean()
  if (!segment) {
    return res.json({ success: false, message: 'segment_not_found' })
  }

  await CustomerSegment.deleteOne({ _id: id })
  await sync_brand_segment(segment.brand_id, null, segment)
  res.json({ success: true })
}

module.exports = {
  get_profiles,
  export_profiles,
  sync_segment_for_updated_profiles,
  create_segment,
  get_segments,
  update_segment,
  delete_segment,
}
