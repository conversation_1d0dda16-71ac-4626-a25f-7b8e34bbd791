const axios = require('axios');
const moment = require('moment');
const _ = require('lodash');
const { GoogleSheetFile, Brand, BrandMenu, Site, Hub, TokenAccount, SiteMenu } = require("../../.shared/database")
const { read_sheet } = require("../../.shared/googlesheet")
const { get_brand_menu_from_google_sheet } = require("../../.shared/googlesheet/template");

const validate_brand_menu = async (req, res) => {
    const { sheet_id } = req.query
    const sheet_data = await read_sheet(sheet_id, 1, ['MD_menu', 'MD_option', 'MD_promotion', 'MD_site_selection'])
    const brand_names = _.uniq(sheet_data.MD_menu.map(v => v.brand))
    const brands = await Brand.find({ name: brand_names })
    const errors = []
    for (const brand of brands) {
        const brand_menu_items = sheet_data.MD_menu.filter(v => v.brand === brand.name)
        for (const brand_menu_item of brand_menu_items) {
            if (brand_menu_item.id?.length < 10) {
                errors.push(`${brand.name}, ${brand_menu_item.name} has id is required`)
            }
            if (brand_menu_item.grab_mart === 'TRUE' && (!brand_menu_item.grab_category || !brand_menu_item.grab_sub_category)) {
                errors.push(`${brand.name}, ${brand_menu_item.name} has grab_mart is TRUE, grab_category and grab_sub_category is required`)
            }
            if (brand_menu_item.description?.length > 500) {
                errors.push(`${brand.name}, ${brand_menu_item.name} description is too long (max 500 characters)`)
            }

            if (_.isNumber(brand_menu_item.stock_price)) {
                errors.push(`${brand.name}, ${brand_menu_item.name} stock_price is not a number`)
            }

            const check_item_codes = brand_menu_items.filter(v =>
                v.category === brand_menu_item.category &&
                v.item_code === brand_menu_item.item_code &&
                ['grab_mart', 'shopee_fresh', 'be', 'grab', 'shopee'].some(s => v[s] === 'TRUE')
            )
            if (check_item_codes?.length > 1) {
                errors.push(`${brand.name}, Item code: ${brand_menu_item.item_code} is duplicated ${check_item_codes.length} times`)
            }

            let index = 1
            while (brand_menu_item[`item${index} _code`]) {
                if (isNaN(Number(brand_menu_item[`item${index} _price`]))) {
                    errors.push(`${brand.name}, ${brand_menu_item.name} item${index}_price is not a number`)
                }
                if (isNaN(Number(brand_menu_item[`item${index} _quantity`]))) {
                    errors.push(`${brand.name}, ${brand_menu_item.name} item${index}_quantity is not a number`)
                }
                index++
            }
        }
        const brand_promotions = sheet_data.MD_promotion.filter(v => v.brand === brand.name)
        for (const promotion of brand_promotions) {
            const MD_menu_item = brand_menu_items.find(v => v.name === promotion.name)
            if (!MD_menu_item) {
                errors.push(`${brand.name}, ${promotion.name} not found in menu`)
                continue
            }
            if (Number(promotion.price) !== Number(MD_menu_item.price)) {
                errors.push(`${brand.name}, ${promotion.name} price mismatch ${promotion.price}, in menu: ${MD_menu_item.price} `)
            }
            if (Number(promotion.price) < Number(promotion.sell_price)) {
                errors.push(`${brand.name}, ${promotion.name} sell price too high ${promotion.sell_price}, price: ${promotion.price} `)
            }
            if (moment(promotion.start_time, 'YYYY/MM/DD').isAfter(moment(promotion.end_time, 'YYYY/MM/DD'))) {
                errors.push(`${brand.name}, ${promotion.name} start time is after end time`)
            }
            if (moment(promotion.start_time, 'YYYY/MM/DD').isBefore(moment().startOf('day'))) {
                errors.push(`${brand.name}, ${promotion.name} start time is in the past`)
            }
        }
    }
    res.json({
        success: true,
        errors: _.uniq(errors)
    })
}

const set_brand_menu = async (req, res) => {
    const { sheet_id } = req.query
    const sheet_data = await read_sheet(sheet_id, 1, ['MD_menu', 'MD_option', 'MD_promotion', 'MD_site_selection'])
    await GoogleSheetFile.findOneAndUpdate({
        file_id: sheet_id,
    }, { sheet_data }, { upsert: true })

    const brand_names = _.uniq(sheet_data.MD_menu.map(v => v.brand))
    const brands = await Brand.find({ name: brand_names })
    for (const brand of brands) {
        const menu_mapping = await get_brand_menu_from_google_sheet(brand._id)
        if (!menu_mapping) continue
        await BrandMenu.findOneAndUpdate({ brand_id: brand._id }, menu_mapping, { upsert: true, runValidators: true })
        const sites = await Site.find({ brand_id: brand._id })
        for (const site of sites) {
            await SiteMenu.findOneAndUpdate({ site_id: site._id }, menu_mapping, { upsert: true })
        }
    }


    res.json({
        success: true,
    })
}
// set_brand_menu({ query: { sheet_id: '1CHH1Cdu4hqibE2JbXgJY8EvTFWDcjssHeO2ekJ3tf6k' } }, { json: console.log })

const set_hub_site_info = async (req, res) => {
    const { sheet_id } = req.query
    const sheet_data = await read_sheet(sheet_id, 1, ['MD_site_requests'])
    const rows = sheet_data.MD_site_requests.filter(v => v.status === '')
    for (const row of rows) {
        const brand = await Brand.findOne({ name: row.brand })

        const address_resp = await axios.get('https://api.nexpos.io/api/map/v2/suggest_addresses', { params: { address: row.address } })
        const hub = await Hub.findOneAndUpdate({
            code: row.hub_code,
        }, {
            $setOnInsert: {
                name: row.hub_name,
                phone: row.hub_phone,
                address: address_resp?.data?.data[0].formatted_address,
                address_obj: address_resp?.data?.data[0],
                inventory_source: 'manual'
            }
        }, { upsert: true, new: true })
        // await Site.deleteMany({ code: row.site_code })
        const site = await Site.findOneAndUpdate({
            code: row.site_code,
        }, {
            $setOnInsert: {
                type: 'mart',
                name: row.site_name,
                brand_id: brand._id,
                hub_id: hub._id,
                address: address_resp?.data?.data[0].formatted_address,
                address_obj: address_resp?.data?.data[0],
                active: true,
                auto_confirm: true,
                auto_print: true,
                tokens: [],
            }
        }, { upsert: true, new: true })
        // if (row.be_token_name) {
        //     await TokenAccount.findOneAndUpdate({
        //         token_code: row.be_token_name,
        //         source: 'be'
        //     }, {
        //         $setOnInsert: {
        //             username: row.be_user,
        //             password: row.be_password,
        //         }
        //     }, { upsert: true, new: true })
        //     const be_token = site.tokens.find(v => v.source === 'be')
        //     if (be_token) {
        //         be_token.token_code = row.be_token_name
        //     } else {
        //         site.tokens.push({
        //             source: 'be',
        //             token_code: row.be_token_name
        //         })
        //     }
        //     await site.save()
        // }
        // if (row.grab_food_token_name) {
        //     await TokenAccount.findOneAndUpdate({
        //         token_code: row.grab_food_token_name,
        //         source: 'grab'
        //     }, {
        //         $setOnInsert: {
        //             username: row.grab_food_user,
        //             password: row.grab_food_password,
        //         }
        //     }, { upsert: true, new: true })
        //     const grab_food_token = site.tokens.find(v => v.source === 'grab')
        //     if (grab_food_token) {
        //         grab_food_token.token_code = row.grab_food_token_name
        //     } else {
        //         site.tokens.push({
        //             source: 'grab',
        //             token_code: row.grab_food_token_name
        //         })
        //     }
        //     await site.save()
        // }

    }
    res.json({
        success: true,
    })
}
// set_hub_site_info({ query: { sheet_id: '15sJPu1TNTVzsS-LEraV95jyockeZgfhaXoeL19Ndwv8' } }, { json: console.log })

module.exports = { validate_brand_menu, set_brand_menu }