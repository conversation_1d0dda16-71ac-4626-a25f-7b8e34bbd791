const {
  Site,
  CoreProduct,
  SiteMenuOptionItem,
  SiteMenu,
  BrandMenuCategoryTemplate,
  BrandMenuOptionTemplate,
  Brand,
  CpBrandMenuTemplate,
  BrandMenuItemTemplate,
  BrandMenuOptionItemTemplate,
  SiteMenuCategory,
  SiteMenuItem,
  SiteMenuOption,
} = require('../../.shared/database')

const build_menu_template = async function (template_id, { active } = {}) {
  const active_query = active != null ? { active } : {}

  const categories = await BrandMenuCategoryTemplate.find({ template_id, ...active_query }).lean()
  const items = await BrandMenuItemTemplate.find({ template_id, ...active_query }).lean()
  const options = await BrandMenuOptionTemplate.find({ template_id, ...active_query }).lean()
  const option_items = await BrandMenuOptionItemTemplate.find({ template_id, ...active_query }).lean()

  const main_categories = categories.filter((c) => !c.parent_id)
  const itemCategoryMap = items
    .filter((i) => i.category_id)
    .reduce((acc, i) => {
      acc[i.category_id] = acc[i.category_id] || []
      acc[i.category_id].push(i)
      return acc
    }, {})

  const menu = {
    categories: main_categories.map((c) => {
      const sub_categories = categories.filter((sc) => sc.parent_id === String(c._id))
      return {
        ...c,
        items:
          itemCategoryMap[c._id]?.map((i) => {
            return i
          }) || [],
        sub_categories: sub_categories.map((sc) => {
          const items =
            itemCategoryMap[sc._id].map((i) => {
              return i
            }) || []

          return {
            ...sc,
            items,
          }
        }),
      }
    }),
    option_categories: options.map((o) => {
      const opt_items = option_items.filter((oi) => oi.option_id === String(o._id)).map((oi) => {
        return oi
      })

      const linked_items = o.item_ids
        .map((item_id) => {
          const item = items.find((i) => i._id.toString() === item_id.toString())
          if (!item) {
            return null
          }
          return {
            _id: item._id,
            images: item.images,
            name: item.name,
            price: item.price,
            code: item.code,
            order: item.order,
          }
        })
        .filter(Boolean)

      return {
        ...o,
        option_items: opt_items,
        linked_items,
      }
    }),
  }

  return menu
}

exports.get_menu_templates = async function (req, res) {
  const brand_id = req.params.brand_id
  const templates = await CpBrandMenuTemplate.find({ brand_id }).sort({ created_at: 1 })
  res.json({
    success: true,
    data: templates,
  })
}

exports.create_template = async function (req, res) {
  const brand_id = req.params.brand_id

  const brand = await Brand.findById(brand_id)
  if (!brand) {
    return res.json({
      success: false,
      error: 'brand_not_found',
    })
  }

  const { name, description } = req.body

  const template = await CpBrandMenuTemplate.create({ brand_id, name, description })
  res.json({
    success: true,
    data: template,
  })
}

exports.get_template = async function (req, res) {
  const { template_id } = req.params
  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }
  const menu = await build_menu_template(template_id)
  res.json({
    success: true,
    data: { ...template.toJSON(), menu },
  })
}

exports.update_template = async function (req, res) {
  const { template_id } = req.params
  const { name, description } = req.body

  const template = await CpBrandMenuTemplate.findOneAndUpdate({ _id: template_id }, { name, description }, { new: true })
  res.json({
    success: true,
    data: template,
  })
}

exports.delete_template = async function (req, res) {
  const { template_id } = req.params
  await CpBrandMenuTemplate.deleteOne({ _id: template_id })
  res.json({
    success: true,
  })
}

exports.create_category = async function (req, res) {
  const template_id = req.params.template_id

  const { name, parent_id, order, active } = req.body
  if (parent_id) {
    const parent = await BrandMenuCategoryTemplate.findOne({ _id: parent_id })
    if (!parent) {
      return res.json({
        success: false,
        error: 'parent_category_not_found',
      })
    }
  }

  const cat = await BrandMenuCategoryTemplate.create({ template_id, name, parent_id, order: order || new Date().getTime(), active })

  res.json({
    success: true,
    data: cat,
  })
}

exports.get_categories = async function (req, res) {
  const { template_id } = req.params
  const cats = await BrandMenuCategoryTemplate.find({ template_id }).sort({ order: 1 })
  res.json({
    success: true,
    data: cats,
  })
}

exports.update_category = async function (req, res) {
  const { template_id, category_id } = req.params

  let cat = await BrandMenuCategoryTemplate.findOne({ _id: category_id })
  if (!cat) {
    return res.json({
      success: false,
      error: 'category_not_found',
    })
  }

  const { name, parent_id, order, active } = req.body
  cat.name = name || cat.name
  cat.parent_id = parent_id || cat.parent_id
  cat.order = order || cat.order
  cat.active = active || cat.active

  let sub_categories = []
  if (!parent_id) {
    sub_categories = await BrandMenuCategoryTemplate.find({ parent_id: cat._id })
  }

  await cat.save()

  res.json({
    success: true,
    data: { ...cat.toJSON(), sub_categories },
  })
}

exports.delete_category = async function (req, res) {
  const { template_id, category_id } = req.params

  await BrandMenuCategoryTemplate.deleteOne({ _id: category_id, template_id })

  res.json({
    success: true,
  })
}

// items in categories
exports.create_item = async function (req, res) {
  const { template_id, category_id } = req.params

  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }

  const { name, code, order, unit, sources, description, images, image_preview, price, active } = req.body

  const cp = await CoreProduct.findOne({ brand_id: template.brand_id, code })
  if (!cp) {
    return res.json({
      success: false,
      error: 'core_product_not_found',
    })
  }

  if (['nguyen_lieu', 'ban_thanh_pham'].includes(cp.type)) {
    return res.json({
      success: false,
      error: 'site_menu_item_type_not_allowed',
    })
  }

  const item = await BrandMenuItemTemplate.create({ template_id, category_id, name, code, unit, sources, description, images, image_preview, price, active, order: order || new Date().getTime() })

  res.json({
    success: true,
    data: item,
  })
}

exports.update_item = async function (req, res) {
  const { template_id, category_id, item_id } = req.params

  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }

  let existed = await BrandMenuItemTemplate.findOne({ _id: item_id, category_id, template_id })
  if (!existed) {
    return res.json({
      success: false,
      error: 'item_not_found',
    })
  }

  const { name, order, code, unit, sources, description, images, image_preview, price, active } = req.body

  const updated = await BrandMenuItemTemplate.findOneAndUpdate(
    {
      _id: item_id,
      category_id,
      template_id,
    },
    {
      name,
      unit,
      sources,
      code,
      description,
      images,
      image_preview,
      price,
      active,
      order,
    },
    {
      new: true,
    }
  )

  res.json({
    success: true,
    data: updated,
  })
}

exports.delete_item = async function (req, res) {
  const { template_id, category_id, item_id } = req.params

  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }
  await BrandMenuItemTemplate.deleteOne({ _id: item_id, category_id, template_id })

  res.json({
    success: true,
  })
}

exports.create_option_category = async function (req, res) {
  const { template_id } = req.params

  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }

  const { name, order, active, rule, item_ids, option_items } = req.body

  const option_category = await BrandMenuOptionTemplate.create({ template_id, name, active, rule, item_ids, option_items, order: order || new Date().getTime() })

  if (!option_items?.length) {
    return res.json({
      success: false,
      error: 'option_items_required',
    })
  }
  if (!item_ids?.length) {
    return res.json({
      success: false,
      error: 'item_ids_required',
    })
  }
  const core_products = await CoreProduct.find({ brand_id: template.brand_id, code: { $in: option_items.map((o) => o.code) } })

  const items = option_items.map((o) => {
    const product = core_products.find((p) => p.code === o.code)
    return {
      code: product.code,
      name: product.name,
      price: o.price,
      template_id,
      order: o.order || new Date().getTime(),
      option_id: option_category._id,
    }
  })

  await BrandMenuOptionItemTemplate.insertMany(items)

  res.json({
    success: true,
    data: option_category,
  })
}

exports.update_option_category = async function (req, res) {
  const { template_id, id } = req.params

  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }

  const { name, order, active, rule, item_ids, option_items } = req.body

  if (!option_items) {
    return res.json({
      success: false,
      error: 'option_items_required',
    })
  }
  if (!item_ids) {
    return res.json({
      success: false,
      error: 'item_ids_required',
    })
  }

  const option = await BrandMenuOptionTemplate.findOneAndUpdate({ _id: id, template_id }, { name, order, active, rule, item_ids, option_items })
  if (!option) {
    return res.json({
      success: false,
      error: 'option_category_not_found',
    })
  }

  // clean up old items
  await BrandMenuOptionItemTemplate.deleteMany({ option_id: option._id, template_id })

  // create new items
  const core_products = await CoreProduct.find({ brand_id: template.brand_id, code: { $in: option_items.map((o) => o.code) } })

  const items = option_items.map((o) => {
    const product = core_products.find((p) => p.code === o.code)
    return {
      name: product.name,
      code: product.code,
      price: o.price,
      unit: product.unit,
      sources: product.sources,
      template_id,
      order: o.order || new Date().getTime(),
      option_id: option._id,
    }
  })
  const menu_items = await BrandMenuItemTemplate.find({ template_id }).lean()

  const newItems = await BrandMenuOptionItemTemplate.insertMany(items)

  const linked_items = item_ids
    .map((item_id) => {
      const item = menu_items.find((i) => i._id.toString() === item_id.toString())
      if (!item) {
        return null
      }
      return {
        _id: item._id,
        images: item.images,
        name: item.name,
        price: item.price,
        code: item.code,
        order: item.order,
      }
    })
    .filter(Boolean)

  const data = {
    ...option.toJSON(),
    option_items: newItems,
    linked_items,
  }

  res.json({
    success: true,
    data,
  })
}

exports.delete_option_category = async function (req, res) {
  const { template_id, id } = req.params

  await BrandMenuOptionTemplate.deleteOne({ _id: id, template_id })

  res.json({
    success: true,
  })
}

exports.apply_template_to_site = async function (req, res) {
  const { template_id } = req.params
  const { site_ids } = req.body

  // apply template to site
  const template = await CpBrandMenuTemplate.findById(template_id)
  if (!template) {
    return res.json({
      success: false,
      error: 'template_not_found',
    })
  }

  const sites = await Site.find({ _id: { $in: site_ids }, use_core_product: true, brand_id: template.brand_id })
  if (sites.length !== site_ids.length) {
    return res.json({
      success: false,
      error: 'some_sites_not_found',
    })
  }

  // get data from template
  const categories = await BrandMenuCategoryTemplate.find({ template_id })
  const items = await BrandMenuItemTemplate.find({ template_id })
  const option_categories = await BrandMenuOptionTemplate.find({ template_id })
  const option_items = await BrandMenuOptionItemTemplate.find({ template_id })

  for (const site of sites) {
    const old_categories = await SiteMenuCategory.find({ site_id: site._id })
    const old_items = await SiteMenuItem.find({ site_id: site._id })
    const old_options = await SiteMenuOption.find({ site_id: site._id })
    const old_option_items = await SiteMenuOptionItem.find({ site_id: site._id })
    const site_menu = await SiteMenu.findOne({ site_id: site._id }, {
      channels: 1,
    })

    // console.log('delete all categories and items in site', old_categories)
    // delete all categories and items in site
    await SiteMenuCategory.deleteMany({ site_id: site._id })
    await SiteMenuItem.deleteMany({ site_id: site._id })
    await SiteMenuOptionItem.deleteMany({ site_id: site._id })
    await SiteMenuOption.deleteMany({ site_id: site._id })

    // create categories
    const cat_map = {}
    const root_cats = categories.filter((c) => !c.parent_id)

    const root_cat_docs = root_cats.map((c) => ({ ...c.toJSON(), _id: undefined, site_id: site._id }))
    const new_root_cats = await SiteMenuCategory.insertMany(root_cat_docs)
    root_cats.forEach((cat, index) => {
      cat_map[cat._id.toString()] = new_root_cats[index]._id.toString()
    })

    // create sub categories
    const sub_cats = categories.filter((c) => c.parent_id)
    const sub_cat_docs = sub_cats.map((c) => ({ ...c.toJSON(), _id: undefined, site_id: site._id, parent_id: cat_map[c.parent_id.toString()] }))
    const new_sub_cats = await SiteMenuCategory.insertMany(sub_cat_docs)
    sub_cats.forEach((cat, index) => {
      cat_map[cat._id.toString()] = new_sub_cats[index]._id.toString()
    })

    // create items
    const item_docs = items.map((i) => ({
      ...i.toJSON(),
      _id: undefined,
      site_id: site._id,
      category_id: cat_map[i.category_id.toString()],
      ref_id: i._id.toString(),
      channels: old_items.find((old_item) => old_item.ref_id === i._id.toString())?.channels,
    }))
    const new_items = await SiteMenuItem.insertMany(item_docs)
    const item_map = new_items.reduce((acc, item) => {
      acc[item.ref_id] = item._id.toString()
      return acc
    }, {})

    
    // create options
    const option_docs = option_categories.map((o) => ({ ...o.toJSON(), _id: undefined, site_id: site._id, item_ids: o.item_ids.map((item_id) => item_map[item_id]) }))
    const new_options = await SiteMenuOption.insertMany(option_docs)
    const option_map = {}
    option_categories.forEach((option, index) => {
      option_map[option._id.toString()] = new_options[index]._id.toString()
    })

    console.log('option_map', option_map, option_items)

    // console.log('option_map', option_map, option_categories)

    // create option items
    const option_item_docs = option_items.map((o) => ({ ...o.toJSON(), _id: undefined, site_id: site._id, option_id: option_map[o.option_id] }))
    // console.log('option_item_docs',JSON.stringify(option_items, null, 2))
    await SiteMenuOptionItem.insertMany(option_item_docs)

    // update site menu
    await SiteMenu.findOneAndUpdate(
      { site_id: site._id },
      {
        $set: {
          template_id,
          channels: site_menu.channels.map((c) => ({ ...c, modified_on: new Date(), synced: false })),
          modified_on: new Date(),
          histories: [{ template_id, applied_on: new Date(), old_categories, old_items, old_options, old_option_items }],
        },
      },
      { upsert: true }
    )
  }

  res.json({
    success: true,
  })
}
