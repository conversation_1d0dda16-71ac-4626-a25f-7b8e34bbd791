// eslint.config.js
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';

const compat = new FlatCompat({
  baseDirectory: import.meta.url,
});

export default [
  js.configs.recommended,
  {
    files: ['**/*.js'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
    },
    rules: {
      'no-unused-vars': 'off',
      'no-empty': 'off',
      'no-inner-declarations': 'off',
      'no-unsafe-optional-chaining': 'off',
    },
  },
  ...compat.config({
    env: {
      node: true,
      commonjs: true,
      es2021: true,
    },
    ignorePatterns: ['node_modules/'],
  }),
];