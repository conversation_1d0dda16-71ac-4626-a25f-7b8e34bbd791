const mockOrder = async () => {
    const mockMessages = [
        {
            entry: [{
                id: '489133967615178',
                messaging: [{
                    sender: { id: '123456' },
                    recipient: { id: '489133967615178' },
                    message: { text: 'Chào shop' }
                }]
            }]
        },
        {
            entry: [{
                id: '489133967615178',
                messaging: [{
                    sender: { id: '123456' },
                    recipient: { id: '489133967615178' },
                    message: { text: 'Cho mình đặt 1 phở bò tái' }
                }]
            }]
        },
        {
            entry: [{
                id: '489133967615178',
                messaging: [{
                    sender: { id: '123456' },
                    recipient: { id: '489133967615178' },
                    message: { text: 'Size M' }
                }]
            }]
        },
        {
            entry: [{
                id: '489133967615178',
                messaging: [{
                    sender: { id: '123456' },
                    recipient: { id: '489133967615178' },
                    message: { text: '<PERSON><PERSON> điện thoại của mình là 0987654321, đ<PERSON><PERSON> chỉ 123 <PERSON>uyễn <PERSON> Q1' }
                }]
            }]
        }
    ];

    const { webhook } = require('./facebook');
    await new Promise(resolve => setTimeout(resolve, 10000));
    for (const message of mockMessages) {
        console.log('\nSending message:', message.entry[0].messaging[0].message.text);
        await webhook(message);
        // Wait 1s between messages
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
};

mockOrder().catch(console.error);