const { PageConversation } = require("../../.shared/database");
const { read_sheet } = require("../../.shared/googlesheet");
const ChatHandler = require("./chat_handler");
const axios = require('axios');
const moment = require('moment');
const _ = require('lodash');
const facebook = require('../../.shared/messaging/facebook');

const PAGE_ACCESS_TOKEN = `EAATEFterXZCcBOZBa8NBOzCvyR1ZBLfa0dc6vPJ42lke9d8FZCXZAmq7iNHsxFzZAwCzBPZAJcCZAmwTmQkMAZBtwgz6mUjZBbrIZA6RRYQxitxIlsKlR5SKTKt6Tct8l6mXJfasHJzltymRY4ZC8hyjI8xdu4VI9q6PFBGfNwZCqgAGiiOdUtwlW9BIXBkaTuZBZCDeKlyfpWaZBfaH8QN8CfLV`;
const webhook = async (body) => {
    const sheet_data = await read_sheet(`1XWFx9aH9WlkMXbDN7Ps3I60fb3HnZZTicb8y2QoXGhs`, 1, ['MD_store', 'MD_menu', 'MD_option', 'MD_promotion'], true);
    const chatHandler = new ChatHandler({ sheet_data });
    for (const dish of sheet_data.MD_menu) {
        dish.options = _.groupBy(sheet_data.MD_option.filter(v => dish.option_ids.split('\n').includes(v.option_id)), 'option_id')
    }
    const page_id = body.entry[0].id
    // NexDor Chat Test Page 
    if (page_id === '489133967615178') {
        const fb_message = body.entry[0].messaging[0]
        const message = fb_message?.message?.text
        if (!message) {
            return
        }
        const is_user_message = fb_message.sender.id !== page_id

        const exist_page_conversation = await PageConversation.findOne({
            channel: 'facebook',
            page_id: page_id,
            uid: fb_message.sender.id === page_id ? fb_message.recipient.id : fb_message.sender.id,
            status: 'open',
        })

        let need_create_new = false
        if (exist_page_conversation) {
            if (moment(exist_page_conversation.updated_at).isBefore(moment().add(-10, 'minutes'))) {
                exist_page_conversation.status = 'closed'
                await exist_page_conversation.save()
                need_create_new = true
            } else {
                exist_page_conversation.messages.push({
                    role: is_user_message ? 'user' : 'bot',
                    message: message,
                    created_at: new Date()
                })
                await exist_page_conversation.save()
            }
        } else {
            need_create_new = true
        }

        let page_conversation = exist_page_conversation
        if (need_create_new) {
            page_conversation = await PageConversation.create({
                channel: 'facebook',
                page_id: page_id,
                uid: fb_message.sender.id === page_id ? fb_message.recipient.id : fb_message.sender.id,
                status: 'open',
                messages: [{
                    role: is_user_message ? 'user' : 'bot',
                    message: message,
                    created_at: new Date()
                }]
            });
        }

        if (!is_user_message) {
            return
        }

        if (need_create_new) {
            chatHandler.handleFirstMessage(page_conversation).then(response => {
                // console.log(response)
                facebook.send_images(PAGE_ACCESS_TOKEN, page_id, fb_message.sender.id, response.images).then(() => {
                    facebook.send_message(PAGE_ACCESS_TOKEN, page_id, fb_message.sender.id, response.message)
                })
            });
        } else {
            chatHandler.handleMessage(page_conversation).then(response => {
                // console.log(response)
                facebook.send_images(PAGE_ACCESS_TOKEN, page_id, fb_message.sender.id, response.images).then(() => {
                    facebook.send_message(PAGE_ACCESS_TOKEN, page_id, fb_message.sender.id, response.message)
                })
                if (response.status === 'COMPLETED') {
                    page_conversation.status = 'closed'
                    page_conversation.save()
                }
            });

        }
    }
}

module.exports = {
    webhook
}