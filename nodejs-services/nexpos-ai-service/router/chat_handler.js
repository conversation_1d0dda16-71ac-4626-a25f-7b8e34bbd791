const axios = require('axios');
const uuid = require('uuid');
const _ = require('lodash');
const slugify = require('slugify');
const text_slugify = (text) => slugify(text?.toLowerCase(), { remove: /[^0-9a-zA-Z\s]/g, replacement: '_', locale: 'vi', trim: true })
const extract = require('extract-json-from-string');
const {
    FunctionDeclarationSchemaType,
    HarmBlockThreshold,
    HarmCategory,
    VertexAI
} = require('@google-cloud/vertexai');

// Import Google Cloud credentials
const credentials = require('../certification.json');

const project = 'friendly-idea-384714';
const location = 'asia-southeast1';  // Singapore region
const textModel = 'gemini-1.5-flash-002';

// Initialize VertexAI with credentials
const vertexAI = new VertexAI({
    project: project,
    location: location,  // Using Singapore region
    googleAuthOptions: {
        credentials: {
            client_email: credentials.client_email,
            private_key: credentials.private_key,
            project_id: credentials.project_id,
        }
    }

});

class ChatHandler {
    constructor({ sheet_data }) {
        this.sheet_data = sheet_data;
        this.base_message = `
        Vai trò: Bạn là tư vấn viên của ${this.sheet_data.MD_store.name}
        
        Quy tắc chung:
        - Trả lời ngắn gọn (tối đa 200 từ)
        - Phát hiện intent từ tin nhắn khách hàng
        - Gợi ý menu và hình ảnh món ăn phù hợp với ngữ cảnh
        - Không gửi lại hình ảnh đã dùng trước đó trừ khi được yêu cầu
        - Lần đầu trả lời: giới thiệu menu và khuyến mãi
        
        Intent và xử lý:
        1. "hỏi giá": Trả lời giá các món được hỏi
        2. "đặt đơn": 
           - Xác nhận món ăn, tùy chọn đi kèm (nếu có), số lượng
           - Tính tổng giá trị đơn hàng
        3. "xác nhận đơn hàng":
           - Yêu cầu thông tin liên hệ: SĐT và địa chỉ giao hàng
           - Gợi ý và xác nhận địa chỉ chi tiết (tên đường, quận/huyện)
        4. "đã cung cấp thông tin giao hàng":
           - Kiểm tra đủ cả SĐT và địa chỉ
           - Tóm tắt thông tin đơn hàng
        
        Dữ liệu:
        - Cửa hàng: ${JSON.stringify(this.sheet_data.MD_store)}
        - Menu: ${JSON.stringify(this.sheet_data.MD_menu)}
        - Tùy chọn món (theo option_ids): ${JSON.stringify(this.sheet_data.MD_option)}
        - Khuyến mãi: ${JSON.stringify(this.sheet_data.MD_promotion)}
        
        Yêu cầu xử lý tên món:
        - Tên món phải khớp chính xác với menu (dishes.name)
        - Nếu yêu cầu không rõ: xác nhận lại tên món và tùy chọn
        Nguyên tắc trả về thông tin:
        - Bắt buộc  phải theo format JSON như sau:
        Format kết quả JSON:
        {
          "detectedIntent": "intent_here",
          "dishes": [
            {
              "name": "tên_món",
              "quantity": số_lượng,
              "price": giá_tiền,
              "options": [
                {
                  "option_id": "id_tùy_chọn",
                  "option": "tên_tùy_chọn"
                }
              ]
            }
          ],
          "phone": "số_điện_thoại",
          "address": "địa_chỉ",
          "suggest_images": ["image_url_1", "image_url_2"],
          "response": "nội_dung_trả_lời"
        }
        `;

        this.generativeModel = vertexAI.getGenerativeModel({
            model: textModel,
            safetySettings: [{
                category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            }],
            generationConfig: { maxOutputTokens: 5000 },
            systemInstruction: {
                role: 'system',
                parts: [{
                    "text": this.base_message
                }]
            },
        });
    }


    async getAIResponseMessage(messages) {
        const last_message = messages[messages.length - 1]
        last_message.message = `${last_message.message} (trả về cấu trúc JSON như đã đề câp ở trên)`
        const request = {
            contents: messages.map(v => ({
                role: v.role === 'bot' ? 'model' : 'user',
                parts: [{ text: v.message }]
            }))
        };

        const streamingResult = await this.generativeModel.generateContentStream(request);
        // for await (const item of streamingResult.stream) {

        // }
        const aggregatedResponse = await streamingResult.response;
        return {
            role: 'bot',
            message: aggregatedResponse.candidates[0].content.parts[0].text,
            created_at: new Date(),
        }
    }

    async handleFirstMessage(page_conversation) {
        try {
            page_conversation.messages = [{ role: 'user', message: this.base_message }, ...page_conversation.messages]
            const new_message = await this.getAIResponseMessage(page_conversation.messages)

            if (new_message) {
                let matchJSONs = extract(new_message.message);
                const message_json = matchJSONs[0]
                console.log(JSON.stringify(message_json, null, 2))
                // await this.handleIntentV2(message_json);
                page_conversation.messages.push(new_message)
                return {
                    type: 'text',
                    message: message_json.response,
                    images: message_json.suggest_images
                };
            }

            return {
                type: 'error',
                message: 'Bạn ơi, mình chưa hiểu rõ ý của bạn. Bạn giải thích thêm giúp mình nhé?'
            };

        } catch (error) {
            console.error('Error handling message:', error);
            return {
                type: 'error',
                message: 'Bạn ơi, mình chưa hiểu rõ ý của bạn. Bạn giải thích thêm giúp mình nhé?'
            };
        }
    }

    async handleMessage(page_conversation, retry = 0) {
        try {
            const new_message = await this.getAIResponseMessage(page_conversation.messages)

            if (new_message) {
                let matchJSONs = extract(new_message.message);
                if (matchJSONs.length === 0) {
                    if (retry > 3) {
                        return {
                            type: 'error',
                            message: 'Xin lỗi, tôi không hiểu ý bạn. Vui lòng thử lại.',
                            images: [],
                        };
                    }
                    this.handleMessage(page_conversation, retry + 1)
                }
                const message_json = matchJSONs[0]
                console.log(JSON.stringify(message_json, null, 2))
                const intent = await this.handleIntentV2(page_conversation, message_json);
                page_conversation.messages.push(new_message)
                return {
                    type: 'text',
                    intent: intent.intent,
                    status: 'COMPLETED',
                    message: message_json.response,
                    images: message_json.suggest_images
                };
            }

            return {
                type: 'error',
                message: 'Xin lỗi, tôi không hiểu ý bạn. Vui lòng thử lại.'
            };

        } catch (error) {
            console.error('Error handling message:', error);
            return {
                type: 'error',
                message: 'Có lỗi xảy ra khi xử lý tin nhắn. Vui lòng thử lại.'
            };
        }
    }


    async handleIntentV2(page_conversation, detectedIntent) {
        const intent = detectedIntent?.detectedIntent || 'un_clear_intent';
        const intent_slug = text_slugify(intent)
        console.log('Intent:', intent_slug);
        if (['da_cung_cap_thong_tin_giao_hang'].includes(intent_slug)) {
            if (!detectedIntent.phone || !detectedIntent.address) {
                return {
                    intent: intent_slug
                }
            }
            console.log(JSON.stringify(this.messages, null, 2))
            console.log(JSON.stringify(detectedIntent, null, 2))
            const resp = await axios.post(`https://api.nexpos.io/api/customer-support/orders`, {
                items: detectedIntent.dishes,
                name: detectedIntent.phone,
                phone: detectedIntent.phone,
                address: detectedIntent.address,
                shippingFee: 0,
                grandTotal: detectedIntent.dishes.reduce((acc, item) => acc + item.price * item.quantity, 0),
                messages: page_conversation.messages,
            })
            console.log('Order response:', resp.data);
        } else {
            console.log('Intent not handled:', intent_slug);
        }
        return {
            intent: intent_slug
        }
    }

    async handleOrderIntent(message) {

    }

    async handleOrderConfirmation() {

    }
}

module.exports = ChatHandler; 