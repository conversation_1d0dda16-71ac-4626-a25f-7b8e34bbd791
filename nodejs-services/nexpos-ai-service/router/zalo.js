const { PageConversation, Site } = require("../../.shared/database");
const { read_sheet } = require("../../.shared/googlesheet");
const ChatHandler = require("./chat_handler");
const axios = require('axios');
const moment = require('moment');
const { send_message } = require('../../.shared/messaging/zalo');
const { get_token_by_site } = require('../../.shared/token_account')
const webhook = async (body) => {
    const site = await Site.findOne({ code: 'NEXDOR.TEST' });
    const zalo_token = await get_token_by_site(site, 'zalo')
    const sheet_data = await read_sheet(`1XWFx9aH9WlkMXbDN7Ps3I60fb3HnZZTicb8y2QoXGhs`, 1, ['MD_store', 'MD_menu', 'MD_option', 'MD_promotion'], true);
    const chatHandler = new ChatHandler({ sheet_data });

    const page_id = body.recipient.id
    const message = body.message.text
    const is_user_message = body.sender.id !== page_id
    // NexDor OA
    if (page_id !== '1916884326199305460') {
        return
    }

    if (!message) {
        return
    }

    const exist_page_conversation = await PageConversation.findOne({
        channel: 'zalo',
        page_id: page_id,
        uid: body.sender.id,
        status: 'open',
    })

    let need_create_new = false
    if (exist_page_conversation) {
        if (moment(exist_page_conversation.updated_at).isBefore(moment().add(-10, 'minutes'))) {
            exist_page_conversation.status = 'closed'
            await exist_page_conversation.save()
            need_create_new = true
        } else {
            exist_page_conversation.messages.push({
                role: 'user',
                message: message,
                created_at: new Date()
            })
            await exist_page_conversation.save()
        }
    } else {
        need_create_new = true
    }

    let page_conversation = exist_page_conversation
    if (need_create_new) {
        page_conversation = await PageConversation.create({
            channel: 'zalo',
            page_id: page_id,
            uid: body.sender.id,
            status: 'open',
            messages: [{
                role: is_user_message ? 'user' : 'bot',
                message: message,
                created_at: new Date()
            }]
        });
    }

    if (!is_user_message) {
        return
    }

    if (need_create_new) {
        chatHandler.handleFirstMessage(page_conversation).then(response => {
            // console.log(response)
            send_message(zalo_token.access_token, body.sender.id, response.message, response.images)
        });
    } else {
        chatHandler.handleMessage(page_conversation).then(response => {
            // console.log(response)
            send_message(zalo_token.access_token, body.sender.id, response.message, response.images)
        });

    }
}

module.exports = {
    webhook
}