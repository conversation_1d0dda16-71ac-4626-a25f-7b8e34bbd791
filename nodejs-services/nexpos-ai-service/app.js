const express = require('express');
const app = express();
const http = require('http');
const axios = require('axios');
const path = require('path');
const facebook = require('./router/facebook');
const zalo = require('./router/zalo');
const { PageConversation } = require('../.shared/database');

const port = process.env.PORT || 3000;
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

app.get('/api/health', (req, res) => {
    res.json({ status: 'ok' });
});

(async () => {
    await PageConversation.deleteMany({})
})();

app.all('/api/facebook/webhook', async (req, res) => {
    try {
        if (req.method === 'GET') {
            // Handle webhook verification
            const mode = req.query['hub.mode'];
            const token = req.query['hub.verify_token'];
            const challenge = req.query['hub.challenge'];

            if (mode && token) {
                console.log('WEBHOOK_VERIFIED');
                return res.status(200).send(challenge);
            }
        } else if (req.method === 'POST') {
            // Log the incoming webhook event
            // console.log('Webhook event received!');
            // console.log('Headers:', JSON.stringify(req.headers, null, 2));
            // console.log('Body:', JSON.stringify(req.body, null, 2));
            facebook.webhook(req.body);
            return res.json({ success: true });
            // Forward to Chatwoot
            try {
                const response = await axios.post('https://chat.nexpos.io/bot', req.body, {
                    headers: {
                        'Content-Type': 'application/json',
                        'x-hub-signature-256': req.headers['x-hub-signature-256'],
                        'x-hub-signature': req.headers['x-hub-signature'],
                    },
                    timeout: 5000 // 5 second timeout
                });

                if (response.status === 200) {
                    console.log('Successfully forwarded to Chatwoot');
                } else {
                    console.error('Unexpected response from Chatwoot:', response.status);
                }
            } catch (error) {
                console.error('Error forwarding to Chatwoot:', {
                    message: error.message,
                    status: error.response?.status,
                    data: error.response?.data
                });
                // We still return 200 to Facebook to acknowledge receipt
            }

            // Always return 200 to Facebook to acknowledge receipt of the webhook
            return res.status(200).json({ success: true });
        } else {
            return res.sendStatus(405);
        }
    } catch (error) {
        console.error('Webhook processing error:', error);
        // Always return 200 to Facebook even if we have internal errors
        return res.status(200).json({ success: true });
    }
});

app.all('/api/zalo/webhook', async (req, res) => {
    console.log('Webhook event received!');
    console.log(JSON.stringify(req.body, null, 2));
    zalo.webhook(req.body);
    res.json({ success: true });
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
});