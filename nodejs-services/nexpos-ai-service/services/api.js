const getStoreInfo = () => {
    return {
        name: "<PERSON><PERSON><PERSON>a NutiMilk - 1008 Tỉnh Lộ 43 - SA116",
        address: "1008 Tỉnh lộ 43, <PERSON><PERSON> 1, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ho <PERSON> Minh City, Vietnam",
        phone: "0906123456"
    };
};

const calculateShipping = async (address) => {
    // Mock API call to calculate shipping
    return 30000; // Example shipping cost in VND
};

const createOrder = async (orderData) => {
    // Mock API call to create order
    return {
        orderId: Math.random().toString(36).substr(2, 9),
        ...orderData
    };
};

module.exports = {
    getStoreInfo,
    calculateShipping,
    createOrder
}; 