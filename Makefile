print:
	cd pos-printer-service && nodemon app.js

# IMAGE_TAG=$(shell git rev-parse --short HEAD)
IMAGE_TAG := $(shell date +'%y%m%d%H%M%S')

build-nexpos-image:
	@docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:$(IMAGE_TAG) -f .docker/nexpos.dockerfile
	@docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:$(IMAGE_TAG)
	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.nexpos.yaml
	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.nexpos-pub-service.yaml

# browser-image:
# 	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/browser-service:$(IMAGE_TAG) -f .docker/browser-service.dockerfile
# 	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/browser-service:$(IMAGE_TAG)
# 	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.browser-service.yaml

# shopee-otp-image:
# 	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/shopee-otp-service:$(IMAGE_TAG) -f .docker/shopee-otp-service.dockerfile
# 	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/shopee-otp-service:$(IMAGE_TAG)
# 	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.shopee-otp-service.yaml

# build-app-order-service-image:
# 	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/app-order-service:$(IMAGE_TAG) -f .docker/app-order-service.dockerfile
# 	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/app-order-service:$(IMAGE_TAG)
# 	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.app-order-service.yaml

# build-cron-image:
# 	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/cron-service:$(IMAGE_TAG) -f .docker/cron-service.dockerfile
# 	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/cron-service:$(IMAGE_TAG)
# 	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.cron-service.yaml

build-%-image:
	@SERVIVE=$(subst build-,,$(subst -image,,$@)); \
	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) -f .docker/$$SERVIVE-service.dockerfile ;\
	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) ;\
	cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.$$SERVIVE-service.yaml

deploy-%-image:
	@SERVIVE=$(subst deploy-,,$(subst -image,,$@)); \
	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) -f .docker/$$SERVIVE-service.dockerfile ;\
	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) ;\
	cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.$$SERVIVE-service.yaml;\
	kubectl apply -f deployment.$$SERVIVE-service.yaml ;\
	# kubectl rollout restart deploy $$SERVIVE-service

deploy-%-image-dev:
	@SERVIVE=$(subst deploy-,,$(subst -image-dev,,$@)); \
	docker build . -t asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) -f .docker/$$SERVIVE-service.dockerfile ;\
	docker push asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/$$SERVIVE-service:$(IMAGE_TAG) ;\
	cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g" deployment.$$SERVIVE-dev-service.yaml;\
	kubectl apply -f deployment.$$SERVIVE-dev-service.yaml ;\
	kubectl rollout restart deploy $$SERVIVE-dev-service

deploy-dev: build-nexpos-image
	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g"  deployment.nexpos-dev-service.yaml
	@cd .k8s && kubectl apply -f deployment.nexpos-dev-service.yaml
	@kubectl rollout restart deploy nexpos-dev-service

deploy-stag: build-nexpos-image
	@cd .k8s && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g"  deployment.nexpos-stag.yaml
	@cd .k8s && kubectl apply -f deployment.nexpos-stag.yaml
	@kubectl rollout restart deploy nexpos-stag-service

deploy-uat: build-nexpos-image
	@cd .k8s/uat && sed -i "/image:/s/:[0-9a-z].*/:${IMAGE_TAG}/g"  deployment.nexpos.yaml
	@cd .k8s/uat && kubectl apply -f deployment.nexpos.yaml

deploy-prod: build-nexpos-image
	@cd .k8s && kubectl apply -f deployment.nexpos.yaml
	@cd .k8s && kubectl apply -f deployment.nexpos-pub-service.yaml
	# @kubectl rollout restart deploy nexpos-service
	# @kubectl rollout restart deploy nexpos-pub-service

dump_prod_to_dev:
	mongodump --uri="mongodb+srv://nexdor:<EMAIL>/food-app" --archive=dump
	mongorestore --uri="mongodb://admin:CyberNeposHackme2023***@34.124.166.35:27017/food-app-dev?retryWrites=true&w=majority&authMechanism=SCRAM-SHA-1" --archive=dump --nsFrom "food-app.*" --nsTo "food-app-dev.*"  --nsInclude="*" --drop
	# mongorestore --uri="mongodb+srv://nexdor:<EMAIL>/food-app?retryWrites=true&w=majority" --archive=dump --nsFrom "food-app.*" --nsTo "food-app.*"  --nsInclude="*" --drop
	rm dump

deploy-odoo-dev:
	rsync --rsync-path="sudo rsync" -av  nodejs-services/odoo-service/extra-addons/* root@66.29.131.103:~/odoo_dev_addons/
	ssh root@66.29.131.103 "docker restart odoo_dev"

deploy-odoo-prod:
	rsync --rsync-path="sudo rsync" -av  nodejs-services/odoo-service/extra-addons/* root@66.29.131.103:~/odoo_prod_addons/
	ssh root@66.29.131.103 "docker restart odoo_prod"

copy-backend-to-dev:
	rsync --rsync-path="sudo rsync" -av --exclude="node_modules" nodejs-services root@66.29.131.103:~/odoo/nexpos-backend-dev

copy-backend-to-prod:
	rsync --rsync-path="sudo rsync" -av --exclude="node_modules" nodejs-services root@66.29.131.103:~/odoo/nexpos-backend-prod

prod-profile:
	gcloud container clusters get-credentials cluster-nexdor --zone asia-southeast1-a --project friendly-idea-384714
uat-profile:
	gcloud container clusters get-credentials cluster-nexdor-stag --zone asia-southeast1-a --project friendly-idea-384714

vet:
	@echo "Running go vet on all golang-services..."
	@find golang-services -type d -name "*-service" | while read dir; do \
		echo "Vetting $$dir"; \
		(cd $$dir && go vet ./... || echo "Vetting failed in $$dir") \
	done
	@echo "Go vet completed"

