# Terms of Service

## Free and Open-Source Software (FOSS) License Terms
NexDor Chat is based on Chatwoot, which is licensed under the MIT License. You can find a copy of the FOSS License Terms in Chatwoot's Github Repository.

## NexDor Chat Subscription Terms
These terms apply to any of our Cloud or Self-Hosted plans provided through chat.nexpos.io

### 1. License and support
#### 1.1
Subject to these terms, NexDor grants Customer a limited, non-exclusive, non-transferable license to:
1. Internally use and modify the NexDor software at the paid tier level
2. Use documentation and training materials
3. Publish patches to the Software at the paid usage level

NexDor retains all rights to the Software. "User" means each end-user (person or machine) with access to the Licensed Materials.

#### 1.2
NexDor will provide reasonable support as specified on the 'Features' page for the paid support plan. Support may be terminated with 15 days notice if Customer doesn't follow support instructions.

#### 1.2.1
NexDor will respond to unlimited support questions via Discord, email or in-app chat.

### 2. Restrictions and responsibilities
#### 2.1
Customer will not:
- Use Licensed Materials beyond authorization
- Use for timesharing or service bureau purposes
- Use for high-risk activities
- Violate laws or regulations
- Use in harmful, fraudulent or malicious ways

#### 2.2
Customer will:
- Cooperate with NexDor
- Maintain accurate usage records
- Allow audits with 30 days notice
- Pay any underage fees discovered

#### 2.3
Customer is responsible for account security, passwords and access control.

### 3. Confidentiality
#### 3.1
"Proprietary Information" includes technology and business information. Licensed Materials are NexDor Proprietary Information.

#### 3.2
Receiving Party must:
- Protect confidential information
- Limit access to necessary employees
- Take reasonable security precautions

Exceptions apply for public information, prior knowledge, third party disclosure, or independent development.

#### 3.3
Parties may seek injunctive relief for breaches.

#### 3.4
Relationship existence may be disclosed, specific terms may not without approval.

### 4. Intellectual property rights
#### 4.1
NexDor retains all IP rights to Licensed Materials and enhancements.

#### 4.2
Customer must maintain copyright notices and attributions.

#### 4.3
Customer retains rights to their Content and will indemnify NexDor against infringement claims.

#### 4.4
NexDor will indemnify Customer against IP claims except for modified/combined materials or unauthorized use.

### 5. Payment of fees
#### 5.1
Customer pays fees per Order Form/Quote. NexDor may change fees with 30 days notice. Billing disputes must be raised within 60 days.

#### 5.2
Invoices due per terms. Late fees: 1.5% monthly. Customer pays applicable taxes.

#### 5.3
Fees exclude taxes. Customer responsible for all taxes except NexDor income tax.

#### 5.4
Auto-renews for same term unless terminated with 30 days notice.

### 6. Termination
#### 6.1
Either party may terminate with 30 days notice if no active subscription.

#### 6.2
Customer may terminate anytime. Either party may terminate for material breach with 30 days cure period.

#### 6.3
Immediate termination for insolvency or dissolution.

#### 6.4
Rights terminate upon agreement end. Pro-rated refund available for customer termination under 6.2.

### 7. Warranty
NexDor warrants:
- It has necessary rights
- Software won't contain intentionally harmful code
Customer may terminate if warranty breached and not cured within 30 days.

### 8. Warranty Disclaimer
LICENSED MATERIALS PROVIDED "AS-IS" WITHOUT WARRANTIES EXCEPT AS STATED.

### 9. Limitation of liability
LIABILITY LIMITED TO GREATER OF $1,000 OR ONE YEAR OF FEES, EXCEPT FOR LICENSE/RESTRICTION BREACHES. NO CONSEQUENTIAL DAMAGES.

### 10. U.S. Government Matters
Export restrictions apply. No use in embargoed countries or by restricted parties. Government use subject to commercial terms.

### 11. Miscellaneous
- California law governs
- San Francisco courts have jurisdiction
- No assignment without consent
- Complete agreement
- No agency relationship created
- Force majeure applies
- Notices must be written
- Modifications must be written

### 12. Data privacy
Customer must:
- Comply with data protection laws
- Obtain necessary consents
- Maintain security measures
- Report security incidents
- Enter GDPR agreements if required

"Applicable Data Protection Laws" includes GDPR and related regulations.

### GDPR Data Processing Agreement
Available for enterprise clients. Contact <EMAIL>

### Attribution
NexDor Chat is powered by [Chatwoot](https://www.chatwoot.com), an open source customer engagement platform. While we have modified and customized the software for our needs, we maintain attribution to the original creators as required by the MIT License.

### Contact Information
For any questions about these terms, please contact us at:  
<EMAIL>