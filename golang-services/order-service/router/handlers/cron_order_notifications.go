package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/spf13/cast"
)

// TestOrderNotificationsHandler test sending notifications about new orders
func TestOrderNotificationsHandler(c *gin.Context) {
	redisClient := middlewares.GetRedis(c)

	// Send notification using Redis pub/sub
	message := "Bạn vừa có 1 đơn hàng mới từ ứng dụng"
	data := map[string]any{
		"site_ids": []string{c.Query("site_id")},
	}
	if err := utils.SendMessageToTopic(redisClient, "saas_notifications", message, data); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to send notification: " + err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
	})
}

// CronOrderNotificationsHandler handles the API endpoint for sending notifications about new orders
func CronOrderNotificationsHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)

	// Find orders that need notifications
	var orders []models.Order

	if err := db.
		Where("pushed_notification = ?", false).
		// Where("status IN ?", []string{"DOING", "PENDING"}).
		// Where("data_mapping->'order_time_sort' >= ?", time.Now().Add(-30*time.Minute).Unix()).
		Find(&orders).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to fetch orders: " + err.Error(),
		})
		return
	}

	if len(orders) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	// Update orders to mark them as notified
	var orderIDs []string
	var siteIDs []string
	for _, order := range orders {
		orderIDs = append(orderIDs, string(order.ID))
		siteIDs = append(siteIDs, order.SiteID)
	}

	// Mark orders as notified
	if err := db.Model(&models.Order{}).
		Where("id IN ?", orderIDs).
		Update("pushed_notification", true).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update orders: " + err.Error(),
		})
		return
	}

	// Get sites information
	var sites []models.Site
	if err := db.Where("id IN ?", siteIDs).Find(&sites).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to fetch sites: " + err.Error(),
		})
		return
	}

	// Extract brand IDs and hub IDs
	var brandIDs []string
	var hubIDs []string
	for _, site := range sites {
		brandIDs = append(brandIDs, site.BrandID)
		hubIDs = append(hubIDs, site.HubID)
	}

	// Send notification using Redis pub/sub
	message := "Bạn vừa có " + cast.ToString(len(orders)) + " đơn hàng mới từ ứng dụng"
	data := map[string]any{
		"order_ids": orderIDs,
		"site_ids":  siteIDs,
		"brand_ids": brandIDs,
		"hub_ids":   hubIDs,
	}

	if err := utils.SendMessageToTopic(redisClient, "saas_notifications", message, data); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to send notification: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
