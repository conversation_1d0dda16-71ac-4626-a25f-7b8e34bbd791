package router

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"

	"sort"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

func GetCart(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	// Find site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Find or create cart
	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get site menu
	var siteMenu models.SiteMenuGroup
	if err := db.Where("site_id = ?", site.ID).First(&siteMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	menuItems := buildMenuItems(&siteMenu)
	validateCartItems(&cart, menuItems)

	if err := populateCartGifts(db, &site, user, &cart, nil); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cart,
	})
}

func validateCartItems(cart *models.UserCart, menuItems []models.MenuItem) {
	for i, dish := range cart.Dishes.Data {
		menuItem := funk.Find(menuItems, func(item models.MenuItem) bool {
			return item.Active && item.ID == dish.ItemID
		}).(models.MenuItem)

		if menuItem.ID == "" {
			cart.Dishes.Data[i].Error = "NOT_FOUND"
			continue
		}

		if menuItem.Price != dish.UnitPrice {
			cart.Dishes.Data[i].Error = "PRICE_CHANGED"
			cart.Dishes.Data[i].UnitPrice = menuItem.Price
			cart.Dishes.Data[i].Price = menuItem.Price * float64(dish.Quantity)
		} else {
			cart.Dishes.Data[i].Error = ""
		}
	}
}

func buildMenuItems(menu *models.SiteMenuGroup) []models.MenuItem {
	var items []models.MenuItem

	// Add main category items
	for _, cat := range menu.Categories {
		for _, item := range cat.Items {
			// Set category name and code
			item.CategoryName = cat.Name
			item.CategoryCode = cat.Code
			items = append(items, item)
		}
	}

	// Add subcategory items
	for _, cat := range menu.Categories {
		for _, subCat := range cat.SubCategories {
			for _, item := range subCat.Items {
				// Set category and subcategory names and code
				item.CategoryName = cat.Name
				item.CategoryCode = cat.Code
				items = append(items, item)
			}
		}
	}

	return items
}

func populateCartGifts(db *gorm.DB, site *models.Site, user *models.User, cart *models.UserCart, selectedGifts []models.CartDish) error {
	// Get site menu
	var siteMenu models.SiteMenuGroup
	if err := db.Where("site_id = ?", site.ID).First(&siteMenu).Error; err != nil {
		return err
	}

	menuItems := buildMenuItems(&siteMenu)

	// Find DPoint voucher
	var dpointVoucher *models.Voucher
	for _, v := range cart.Vouchers.Data {
		if v.Vendor == "dpoint" {
			dpointVoucher = &v
			break
		}
	}

	// Get Nexpos vouchers
	var nexposVoucherCodes []string
	for _, v := range cart.Vouchers.Data {
		if v.Vendor == "nexpos" {
			nexposVoucherCodes = append(nexposVoucherCodes, v.Code)
		}
	}

	saleConfigs, err := GetSaleConfigs(db, site, nexposVoucherCodes)
	if err != nil {
		return err
	}

	gifts, discount, shipDiscount, err := ApplyGifts(db, &ApplyGiftsParams{
		User:             user,
		Site:             site,
		MenuItems:        menuItems,
		Cart:             cart,
		SelectedGifts:    selectedGifts,
		OptionCategories: siteMenu.OptionCategories,
		DPointVoucher:    dpointVoucher,
		SaleConfigs:      saleConfigs,
	})
	if err != nil {
		return err
	}

	// Update cart with gifts and discounts
	cart.Gifts = models.JSONField[[]models.CartDish]{Data: gifts}
	cart.Discount = models.JSONField[[]models.CartDiscount]{Data: discount}
	cart.ShipDiscount = models.JSONField[[]models.CartDiscount]{Data: shipDiscount}

	// Calculate totals
	var subTotal float64
	for _, dish := range cart.Dishes.Data {
		subTotal += dish.Price
	}
	for _, gift := range cart.Gifts.Data {
		if gift.IsApplicable {
			subTotal += gift.Price
		}
	}

	var totalItems int64
	for _, dish := range cart.Dishes.Data {
		totalItems += dish.Quantity
	}
	for _, gift := range cart.Gifts.Data {
		if gift.IsApplicable {
			totalItems += gift.Quantity
		}
	}

	var shippingDiscount float64
	for _, discount := range cart.ShipDiscount.Data {
		if discount.IsApplicable {
			shippingDiscount += discount.Amount
		}
	}

	servicePrice := cart.Shipment.Service.Price
	optionPrice := cart.Shipment.Service.Option.Price
	shippingFee := math.Max(servicePrice+optionPrice-shippingDiscount, 0)

	var totalDiscount float64
	for _, discount := range cart.Discount.Data {
		if discount.IsApplicable {
			totalDiscount += discount.Amount
		}
	}

	cart.SubTotal = subTotal
	cart.TotalItems = totalItems
	cart.Total = subTotal + shippingFee - totalDiscount
	cart.ShippingFee = shippingFee

	return nil
}

// GetSaleConfigs retrieves valid sale configurations for a site
func GetSaleConfigs(db *gorm.DB, site *models.Site, nexposVouchers []string) ([]models.RetailerSaleConfig, error) {
	var configs []models.RetailerSaleConfig
	now := time.Now()

	// Build base query
	query := db.Where("brand_id = ? AND active = ?", site.BrandID, true)

	// Add site-specific filter - config either has no site IDs (applies to all) or includes this site
	query = query.Where("site_ids IS NULL OR ? = ANY(site_ids)", site.ID)

	// Add ordering
	query = query.Order("\"order\" asc")

	if err := query.Find(&configs).Error; err != nil {
		return nil, err
	}

	var validConfigs []models.RetailerSaleConfig
	for _, config := range configs {
		// Check date validity
		if config.StartDate != nil && config.StartDate.After(now) {
			continue
		}
		if config.EndDate != nil && config.EndDate.Before(now) {
			continue
		}

		// Check voucher validity
		if len(nexposVouchers) > 0 {
			if config.VoucherConfig.Data.VoucherCode == "" || !funk.ContainsString(nexposVouchers, config.VoucherConfig.Data.VoucherCode) {
				continue
			}
		} else if config.VoucherConfig.Data.VoucherCode != "" {
			continue
		}

		// Check usage limits if applicable
		if config.VoucherConfig.Data.Quantity > 0 && config.VoucherConfig.Data.UsageCount >= config.VoucherConfig.Data.Quantity {
			continue
		}

		validConfigs = append(validConfigs, config)
	}

	return validConfigs, nil
}

type ApplyGiftsParams struct {
	User             *models.User
	Site             *models.Site
	MenuItems        []models.MenuItem
	Cart             *models.UserCart
	SelectedGifts    []models.CartDish
	OptionCategories []models.OptionCategory
	DPointVoucher    *models.Voucher
	SaleConfigs      []models.RetailerSaleConfig
}

type GiftResult struct {
	Gifts        []models.CartDish
	Discount     []models.CartDiscount
	ShipDiscount []models.CartDiscount
}

func ApplyGifts(db *gorm.DB, params *ApplyGiftsParams) ([]models.CartDish, []models.CartDiscount, []models.CartDiscount, error) {
	result := GiftResult{}

	// Check if site is eligible for gifts
	if !(params.Site.Type == "partner" && params.Site.ApplyGift) || len(params.Cart.Dishes.Data) == 0 {
		return result.Gifts, result.Discount, result.ShipDiscount, nil
	}

	// Check for first order
	isFirstOrder := false
	if params.User != nil {
		var orderCount int64
		db.Model(&models.Order{}).Where("user_id = ? AND status IN ?", params.User.ID, []string{"PENDING", "DOING", "PICK", "FINISH"}).Count(&orderCount)
		isFirstOrder = orderCount == 0
	}

	now := time.Now()

	for _, saleConfig := range params.SaleConfigs {
		// Check date validity
		if saleConfig.StartDate != nil && saleConfig.StartDate.After(now) {
			continue
		}
		if saleConfig.EndDate != nil && saleConfig.EndDate.Before(now) {
			continue
		}

		isApplicable := params.User != nil && isFirstOrder

		switch saleConfig.Type {
		case "order_bonus":
			handleOrderBonus(&result, &saleConfig, params, isApplicable)
		case "buy_x_get_y":
			handleBuyXGetY(&result, &saleConfig, params, isApplicable)
		case "buy_x_discount_y":
			handleBuyXDiscountY(&result, &saleConfig, params, isApplicable)
		case "discount":
			handleDiscount(&result, &saleConfig, params, isApplicable)
		// case "percent_discount":
		// 	handlePercentDiscount(&result, &saleConfig, params, isApplicable)
		case "order_discount":
			handleOrderDiscount(&result, &saleConfig, params, isApplicable)
		case "fixed_discount":
			handleFixedDiscount(&result, &saleConfig, params, isApplicable)
		case "ship_discount":
			handleShipDiscount(&result, &saleConfig, params, isApplicable)
		}
	}

	// Filter duplicate discounts, keep highest
	result.Discount = filterDuplicateDiscounts(result.Discount)

	return result.Gifts, result.Discount, result.ShipDiscount, nil
}

func handleOrderBonus(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	subTotal := calculateSubTotal(params.Cart.Dishes.Data)

	// Find applicable config based on minimum order amount
	var appliedConfig *models.SaleItem
	for _, item := range config.Config.Data.Items {
		if subTotal >= item.MinOrderAmount {
			appliedConfig = &item
			break
		}
	}

	if appliedConfig == nil {
		return
	}

	// Find gift item in menu using item code instead of name/category
	giftItem := funk.Find(params.MenuItems, func(item models.MenuItem) bool {
		return item.Code == appliedConfig.GiftCode
	}).(models.MenuItem)

	if giftItem.ID == "" {
		return
	}

	// Find selected or existing gift
	var appliedGift *models.CartDish
	for _, g := range params.SelectedGifts {
		if g.ConfigID == string(config.ID) {
			appliedGift = &g
			break
		}
	}
	if appliedGift == nil {
		for _, g := range params.Cart.Gifts.Data {
			if g.Code == appliedConfig.Code && g.ConfigID == string(config.ID) {
				appliedGift = &g
				break
			}
		}
	}

	if appliedGift != nil {
		totalOptionPrice := calculateTotalOptionPrice(appliedGift.Options)
		giftQuantity := int64(math.Max(1, float64(appliedConfig.GiftQuantity)))

		gift := models.CartDish{
			ID:           giftItem.ID,
			ItemID:       giftItem.ID,
			Name:         giftItem.Name,
			CategoryName: giftItem.CategoryName,
			Code:         appliedConfig.Code,
			Quantity:     giftQuantity,
			UnitPrice:    0,
			Price:        float64(giftQuantity) * totalOptionPrice,
			IsGift:       true,
			IsApplicable: isApplicable,
			ConfigID:     string(config.ID),
			Note:         config.Name,
		}
		result.Gifts = append(result.Gifts, gift)
	}
}

func handleBuyXGetY(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	conf := config.Config.Data
	var cartItemCount int64 = 0

	// Count matching items in cart using item code instead of name/category
	for _, dish := range params.Cart.Dishes.Data {
		if dish.Code == conf.ItemCode {
			cartItemCount += dish.Quantity
		}
	}

	noOfGifts := (cartItemCount / conf.Quantity) * conf.GiftQuantity
	if noOfGifts <= 0 {
		return
	}

	// Find gift item in menu using gift code
	giftItem := funk.Find(params.MenuItems, func(item models.MenuItem) bool {
		return item.Code == conf.GiftCode
	}).(models.MenuItem)

	if giftItem.ID == "" {
		return
	}

	// Find applied gift
	var appliedGift *models.CartDish
	for _, g := range params.SelectedGifts {
		if g.ConfigID == string(config.ID) {
			appliedGift = &g
			break
		}
	}
	if appliedGift == nil {
		for _, g := range params.Cart.Gifts.Data {
			if g.Code == conf.GiftCode && g.ConfigID == string(config.ID) {
				appliedGift = &g
				break
			}
		}
	}

	if appliedGift != nil {
		totalOptionPrice := calculateTotalOptionPrice(appliedGift.Options)
		result.Gifts = append(result.Gifts, models.CartDish{
			ID:           giftItem.ID,
			ItemID:       giftItem.ID,
			Name:         giftItem.Name,
			CategoryName: giftItem.CategoryName,
			Code:         conf.GiftCode,
			Quantity:     noOfGifts,
			Price:        float64(noOfGifts) * (conf.GiftPrice + totalOptionPrice),
			IsGift:       true,
			IsApplicable: isApplicable,
			ConfigID:     string(config.ID),
			Note:         config.Name,
		})
	}
}

func handleBuyXDiscountY(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	conf := config.Config.Data

	switch conf.ItemType {
	case "category", "dish":
		var configItemsInMenu []models.MenuItem
		if conf.ItemType == "dish" {
			// Find menu items using code from the config items
			configItemsInMenu = funk.Filter(params.MenuItems, func(item models.MenuItem) bool {
				return funk.Some(conf.Items, func(configItem models.SaleItem) bool {
					return configItem.Code == item.Code
				})
			}).([]models.MenuItem)
		} else {
			// For category type, still use category name since categories don't have codes
			configItemsInMenu = funk.Filter(params.MenuItems, func(item models.MenuItem) bool {
				return funk.Some(conf.Items, func(configItem models.SaleItem) bool {
					return configItem.CategoryCode == item.CategoryCode
				})
			}).([]models.MenuItem)
		}

		// Find applicable cart items matching the menu items
		applicableItems := funk.Filter(params.Cart.Dishes.Data, func(dish models.CartDish) bool {
			return funk.Some(configItemsInMenu, func(item models.MenuItem) bool {
				if conf.ItemType == "dish" {
					return item.Code == dish.Code
				}
				return item.CategoryName == dish.CategoryName
			})
		}).([]models.CartDish)

		totalQuantity := funk.Reduce(applicableItems, func(acc int64, dish models.CartDish) int64 {
			return acc + dish.Quantity
		}, 0).(int64)

		if totalQuantity >= conf.Quantity {
			maxGifts := (totalQuantity / conf.Quantity) * conf.GiftQuantity

			// Find gift items by code
			applicableGifts := funk.Filter(params.Cart.Dishes.Data, func(dish models.CartDish) bool {
				return funk.Some(conf.GiftItems, func(item models.GiftItem) bool {
					return item.Code == dish.Code
				})
			}).([]models.CartDish)

			sort.Slice(applicableGifts, func(i, j int) bool {
				return applicableGifts[i].UnitPrice < applicableGifts[j].UnitPrice
			})
			sortedGifts := applicableGifts

			remainingGifts := maxGifts
			for _, gift := range sortedGifts {
				if remainingGifts <= 0 {
					break
				}

				discountQuantity := int64(math.Min(float64(gift.Quantity), float64(remainingGifts)))
				discountedPrice := calculateDiscountedPrice(gift.UnitPrice, conf.DiscountType, conf.DiscountValue)
				totalDiscount := float64(discountQuantity) * (gift.UnitPrice - discountedPrice)

				result.Discount = append(result.Discount, models.CartDiscount{
					DishID:       gift.ID,
					Code:         gift.Code,
					Amount:       math.Floor(totalDiscount),
					Note:         config.Name,
					ConfigID:     string(config.ID),
					IsApplicable: isApplicable,
					Type:         "item_discount",
				})

				remainingGifts -= discountQuantity
			}
		}
	}
}

func handleDiscount(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	// Group cart dishes by code
	cartDishesUnion := make(map[string]models.CartDish)
	for _, dish := range params.Cart.Dishes.Data {
		if existing, ok := cartDishesUnion[dish.Code]; ok {
			existing.Quantity += dish.Quantity
			existing.Price += dish.Price
			cartDishesUnion[dish.Code] = existing
		} else {
			cartDishesUnion[dish.Code] = dish
		}
	}

	conf := config.Config.Data
	// Check for matching items by code instead of name/category
	for _, unionDish := range cartDishesUnion {
		if unionDish.Code == conf.ItemCode && unionDish.Quantity >= conf.Quantity {
			result.Discount = append(result.Discount, models.CartDiscount{
				Code:         unionDish.Code,
				Amount:       conf.DiscountValue,
				Note:         config.Name,
				ConfigID:     string(config.ID),
				IsApplicable: isApplicable,
			})
			break
		}
	}
}

// func handlePercentDiscount(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
// 	conf := config.Config.Data
// 	for _, dish := range params.Cart.Dishes.Data {
// 		for _, configItem := range conf.Items {
// 			if (dish.Name == configItem.ItemName && dish.CategoryName == configItem.CategoryName) ||
// 				(dish.Name == configItem.ItemName && dish.CategoryName == dish.SubCategoryName) {
// 				discount := (configItem.PercentDiscount * dish.Price) / 100
// 				result.Discount = append(result.Discount, models.CartDiscount{
// 					DishID:       dish.ID,
// 					Code:         dish.Code,
// 					CategoryName: dish.CategoryName,
// 					ItemName:     dish.Name,
// 					Amount:       math.Floor(discount),
// 					Note:         config.Name,
// 					ConfigID:    string( config.ID),
// 					IsApplicable: isApplicable,
// 					Type:         "item_discount",
// 				})
// 			}
// 		}
// 	}
// }

func handleOrderDiscount(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	subTotal := calculateSubTotal(params.Cart.Dishes.Data)
	var totalDiscount float64
	for _, discount := range result.Discount {
		if discount.IsApplicable {
			totalDiscount += discount.Amount
		}
	}

	if params.DPointVoucher != nil {
		totalDiscount += params.DPointVoucher.Discount
	}

	totalAfterDiscount := subTotal - totalDiscount

	var finalDiscount float64
	for _, item := range config.Config.Data.Items {
		if totalAfterDiscount > item.MinOrderAmount {
			var itemDiscount float64
			if item.DiscountType == "fixed" {
				itemDiscount = item.DiscountValue
			} else if item.DiscountType == "percent" {
				itemDiscount = (item.DiscountValue * totalAfterDiscount) / 100
				if item.MaxDiscount > 0 {
					itemDiscount = math.Min(itemDiscount, item.MaxDiscount)
				}
			}
			finalDiscount = math.Max(finalDiscount, itemDiscount)
		}
	}

	if finalDiscount > 0 {
		result.Discount = append(result.Discount, models.CartDiscount{
			Amount:       math.Floor(finalDiscount),
			Note:         config.Name,
			ConfigID:     string(config.ID),
			IsApplicable: isApplicable,
		})
	}
}

func handleFixedDiscount(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	for _, dish := range params.Cart.Dishes.Data {
		for _, configItem := range config.Config.Data.Items {
			// Match items by code instead of name and category
			if dish.Code == configItem.Code {
				discount := math.Min(dish.Price, configItem.DiscountValue*float64(dish.Quantity))
				result.Discount = append(result.Discount, models.CartDiscount{
					DishID:       dish.ID,
					Code:         dish.Code,
					CategoryName: dish.CategoryName,
					ItemName:     dish.Name,
					Amount:       math.Floor(discount),
					Note:         config.Name,
					ConfigID:     string(config.ID),
					IsApplicable: isApplicable,
					Type:         "item_discount",
				})
			}
		}
	}
}

func handleShipDiscount(result *GiftResult, config *models.RetailerSaleConfig, params *ApplyGiftsParams, isApplicable bool) {
	if !isApplicable {
		return
	}

	subTotal := calculateSubTotal(params.Cart.Dishes.Data)
	var totalDiscount float64
	for _, d := range result.Discount {
		if d.IsApplicable {
			totalDiscount += d.Amount
		}
	}
	if params.DPointVoucher != nil {
		totalDiscount += params.DPointVoucher.Discount
	}

	totalAfterDiscount := subTotal - totalDiscount
	shippingFee := params.Cart.Shipment.Service.Price

	var maxDiscount float64
	for _, item := range config.Config.Data.Items {
		if totalAfterDiscount >= item.MinOrderAmount {
			var itemDiscount float64
			switch item.Type {
			case "discount":
				itemDiscount = item.Value
			case "free_ship":
				itemDiscount = shippingFee
			case "flat_rate":
				if shippingFee > item.Value {
					itemDiscount = shippingFee - item.Value
				}
			}
			maxDiscount = math.Max(maxDiscount, itemDiscount)
		}
	}

	finalDiscount := math.Min(maxDiscount, shippingFee)
	if finalDiscount > 0 {
		result.ShipDiscount = append(result.ShipDiscount, models.CartDiscount{
			Amount:       finalDiscount,
			Note:         config.Name,
			ConfigID:     string(config.ID),
			IsApplicable: true,
		})
	}
}

func calculateDiscountedPrice(price float64, discountType string, discountValue float64) float64 {
	switch discountType {
	case "fixed":
		return math.Max(0, price-discountValue)
	case "percent":
		return price * (1 - discountValue/100)
	case "flat":
		return discountValue
	default:
		return price
	}
}

func calculateSubTotal(dishes []models.CartDish) float64 {
	var total float64
	for _, dish := range dishes {
		total += dish.Price
	}
	return total
}

func calculateTotalOptionPrice(options [][]models.DishOption) float64 {
	var total float64
	for _, optionGroup := range options {
		for _, option := range optionGroup {
			total += option.Price
		}
	}
	return total
}

func filterDuplicateDiscounts(discounts []models.CartDiscount) []models.CartDiscount {
	discountMap := make(map[string]models.CartDiscount)
	for _, discount := range discounts {
		if discount.Type == "item_discount" {
			existingDiscount, exists := discountMap[discount.DishID]
			if !exists || existingDiscount.Amount < discount.Amount {
				discountMap[discount.DishID] = discount
			}
		} else {
			discountMap[discount.DishID] = discount
		}
	}

	var result []models.CartDiscount
	for _, discount := range discountMap {
		result = append(result, discount)
	}
	return result
}

func UpdateCart(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	var request struct {
		Note string `json:"note"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if siteID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_id_is_empty",
		})
		return
	}

	// Find or create cart
	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: user.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	cart.Note = request.Note

	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cart,
	})
}

func GetCartGifts(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	var siteMenu models.SiteMenuGroup
	if err := db.Where("site_id = ?", siteID).First(&siteMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	menuItems := buildMenuItems(&siteMenu)

	var dpointVoucher *models.Voucher
	for _, v := range cart.Vouchers.Data {
		if v.Vendor == "dpoint" {
			dpointVoucher = &v
			break
		}
	}

	var saleConfigs []models.RetailerSaleConfig
	if err := db.Where("brand_id = ? AND active = ? AND voucher_config IS NOT NULL", site.BrandID, true).Find(&saleConfigs).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	gifts, discount, shipDiscount, err := ApplyGifts(db, &ApplyGiftsParams{
		User:             user,
		Site:             &site,
		MenuItems:        menuItems,
		Cart:             &cart,
		OptionCategories: siteMenu.OptionCategories,
		DPointVoucher:    dpointVoucher,
		SaleConfigs:      saleConfigs,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	configIDs := make(map[string]bool)
	for _, gift := range gifts {
		if gift.ConfigID != "" {
			configIDs[gift.ConfigID] = true
		}
	}
	for _, d := range discount {
		if d.ConfigID != "" {
			configIDs[d.ConfigID] = true
		}
	}
	for _, d := range shipDiscount {
		if d.ConfigID != "" {
			configIDs[d.ConfigID] = true
		}
	}

	type ResponseConfig struct {
		ID             string     `json:"_id"`
		Name           string     `json:"name"`
		Description    string     `json:"description"`
		VoucherCode    string     `json:"voucher_code"`
		ApplyWithOther bool       `json:"apply_with_other"`
		EndDate        *time.Time `json:"end_date"`
		IsApplicable   bool       `json:"is_applicable"`
	}

	response := make([]ResponseConfig, 0)
	for _, config := range saleConfigs {
		response = append(response, ResponseConfig{
			ID:             string(config.ID),
			Name:           config.Name,
			Description:    config.Description,
			VoucherCode:    config.VoucherConfig.Data.VoucherCode,
			ApplyWithOther: config.VoucherConfig.Data.ApplyWithOther,
			EndDate:        config.EndDate,
			IsApplicable:   configIDs[string(config.ID)],
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

func ResetCart(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	if err := db.Where("user_id = ? AND site_id = ? AND status = ?",
		user.ID, siteID, "created").Delete(&models.UserCart{}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true})
}

func UpdateCartAddress(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	var request struct {
		To struct {
			Address string `json:"address" binding:"required"`
			Phone   string `json:"phone" binding:"required"`
			Name    string `json:"name" binding:"required"`
		} `json:"to" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	var site models.Site
	if err := db.First(&site, siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", site.BrandID).First(&brandMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	hubIDs := []string{string(site.HubID)}
	hubWithStocks, err := getHubsHasStocks(db, getHubsParams{
		Categories: brandMenu.Categories,
		Items:      cart.Dishes.Data,
		Address:    request.To.Address,
		HubIDs:     hubIDs,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	var hubToAssign *models.Hub
	for _, hub := range hubWithStocks {
		if hub.HasStock {
			hubToAssign = &hub.Hub
			break
		}
	}

	if hubToAssign == nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "hub_not_found"})
		return
	}

	if utils.GetEnv("ENV", "") != "prod" && hubToAssign.Phone == "" {
		hubToAssign.Phone = "0932071312"
	}

	if hubToAssign.Address == "" || hubToAssign.Name == "" || hubToAssign.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "hub_address_or_name_or_phone_is_missing"})
		return
	}

	cart.Shipment = models.CartShipment{
		From: models.ShipmentParty{
			Address: hubToAssign.Address,
			Phone:   hubToAssign.Phone,
			Name:    hubToAssign.Name,
		},
		To: models.ShipmentParty{
			Address: request.To.Address,
			Phone:   request.To.Phone,
			Name:    request.To.Name,
			UserID:  user.ID,
		},
	}

	if err := populateCartGifts(db, &site, user, &cart, nil); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "data": cart})
}

func GetCartShipment(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	var cart models.UserCart
	if err := db.Where("user_id = ? AND site_id = ? AND status = ?",
		user.ID, siteID, "created").First(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	if cart.Shipment.From.Address == "" || cart.Shipment.From.Name == "" || cart.Shipment.From.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "from_address_is_missing"})
		return
	}

	skipShipment := cart.Shipment.To.Address == "" || cart.Shipment.To.Name == "" || cart.Shipment.To.Phone == ""

	var site models.Site
	if err := db.First(&site, siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	result := struct {
		InstantShip  []models.ShipmentService `json:"instant_ship"`
		SameDayShip  []models.ShipmentService `json:"same_day_ship"`
		TwoHourShip  []models.ShipmentService `json:"two_hour_ship"`
		ScheduleShip []models.ShipmentService `json:"schedule_ship"`
		ProvinceShip []models.ShipmentService `json:"province_ship"`
	}{
		InstantShip:  []models.ShipmentService{},
		SameDayShip:  []models.ShipmentService{},
		TwoHourShip:  []models.ShipmentService{},
		ScheduleShip: []models.ShipmentService{},
		ProvinceShip: []models.ShipmentService{},
	}

	if !skipShipment {
		// TODO: Get shipments from delivery services
		// shipments, err := getShipments(db, &site, &cart)
		// if err != nil {
		// 	c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		// 	return
		// }
		shipments := []models.ShipmentService{}
		cart.Shipments = models.JSONField[[]models.ShipmentService]{Data: shipments}

		// Add self pickup options if available
		selfPickupToken := token.GetToken(site, "self_pickup")
		if selfPickupToken != nil {
			if selfPickupToken.SiteData != nil {
				var settings struct {
					PickUp   bool `json:"pick_up"`
					TakeAway bool `json:"take_away"`
				}
				json.Unmarshal(selfPickupToken.SiteData, &settings)

				if settings.PickUp {
					cart.Shipments.Data = append(cart.Shipments.Data, models.ShipmentService{
						Vendor:      "pick_up",
						Code:        "pick_up",
						Price:       0,
						Name:        "Khách đến cửa hàng lấy hàng (Pick up)",
						Description: "Khách đến cửa hàng lấy hàng (Pick up)",
					})
				}

				if settings.TakeAway {
					cart.Shipments.Data = append(cart.Shipments.Data, models.ShipmentService{
						Vendor:      "take_away",
						Code:        "take_away",
						Price:       0,
						Name:        "Bán cho khách mang đi (Take away)",
						Description: "Bán cho khách mang đi (Take away)",
					})
				}
			}
		}

		if err := db.Save(&cart).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
			return
		}

		// Categorize shipments
		for _, s := range cart.Shipments.Data {
			switch {
			case s.Vendor == "grab_express" && s.Code == "INSTANT":
				result.InstantShip = append(result.InstantShip, s)
			case s.Vendor == "ahamove" && s.Code == "SGN-BIKE":
				result.TwoHourShip = append(result.TwoHourShip, s)
			case s.Vendor == "viettel_post":
				result.ProvinceShip = append(result.ProvinceShip, s)
			}
		}

		// Sort and filter shipments
		result.InstantShip = filterShipments(result.InstantShip, 1)
		result.TwoHourShip = filterShipments(result.TwoHourShip, 1)

		// Generate schedule slots if available
		if len(result.TwoHourShip) > 0 {
			slots := getShipmentSlots()
			choiceSlot := result.TwoHourShip[0]

			for _, slot := range slots {
				result.ScheduleShip = append(result.ScheduleShip, models.ShipmentService{
					Vendor: choiceSlot.Vendor,
					Code:   choiceSlot.Code,
					Price:  choiceSlot.Price,
					Name: fmt.Sprintf("Khung giờ %s - %s, ngày %s",
						slot.FromTime, slot.ToTime,
						slot.FromDateTime),
					Description: "Shop sẽ liên hệ và giao hàng theo lịch hẹn của bạn",
					Options:     choiceSlot.Options,
					Raw:         choiceSlot.Raw,
				})
			}
		}

		result.TwoHourShip = nil

		// Check delivery time restrictions
		now := time.Now()
		if now.Hour() < 8 || now.Hour() >= 21 {
			result.InstantShip = nil
		}
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "data": result})
}

func filterShipments(shipments []models.ShipmentService, limit int) []models.ShipmentService {
	sort.Slice(shipments, func(i, j int) bool {
		return shipments[i].Price < shipments[j].Price
	})
	filtered := funk.Filter(shipments, func(s models.ShipmentService) bool {
		return s.Price > 0
	}).([]models.ShipmentService)
	if len(filtered) > limit {
		filtered = filtered[:limit]
	}
	return filtered
}

func getShipmentSlots() []models.ShipmentSlot {
	maxDays := 3
	slots := make([]models.ShipmentSlot, 0)

	// Get current time plus 1 hour
	currentTime := time.Now().Add(time.Hour)

	for i := 0; i < maxDays; i++ {
		// Get current date plus i days
		currentDate := time.Now().AddDate(0, 0, i)

		// Set start time to 9 AM on current date
		startTime := time.Date(
			currentDate.Year(),
			currentDate.Month(),
			currentDate.Day(),
			9, 0, 0, 0,
			currentDate.Location(),
		)

		// Set end time to 9 PM on current date
		endTime := time.Date(
			currentDate.Year(),
			currentDate.Month(),
			currentDate.Day(),
			21, 0, 0, 0,
			currentDate.Location(),
		)

		// If start time is before current time, start from next hour
		if startTime.Before(currentTime) {
			startTime = currentTime.Truncate(time.Hour)
		}

		// Generate hourly slots
		for startTime.Before(endTime) {
			slot := models.ShipmentSlot{
				Date:         startTime.Format("2006-01-02"),
				FromTime:     startTime.Format("15:04"),
				FromDateTime: startTime.Format(time.RFC3339),
				ToTime:       startTime.Add(time.Hour).Format("15:04"),
				ToDateTime:   startTime.Add(time.Hour).Format(time.RFC3339),
				IsActive:     true,
			}
			slots = append(slots, slot)
			startTime = startTime.Add(time.Hour)
		}
	}

	return slots
}

// UpdateCartShipment updates the shipping method for a cart
func UpdateCartShipment(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	// Parse request body
	var request struct {
		Vendor      string                `json:"vendor"`
		Code        string                `json:"code"`
		Price       float64               `json:"price"`
		Name        string                `json:"name"`
		Description string                `json:"description"`
		Raw         json.RawMessage       `json:"raw"`
		To          *models.ShipmentParty `json:"to"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Find site
	var site models.Site
	if err := db.First(&site, siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Find or create cart
	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Handle null vendor (clear shipment service)
	if request.Vendor == "" {
		// Create new shipment with null vendor
		newShipment := cart.Shipment
		newShipment.Service = models.ShipmentService{
			Vendor: "",
		}
		cart.Shipment = newShipment
		cart.ShippingFee = 0

		if err := populateCartGifts(db, &site, user, &cart, nil); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
			return
		}

		if err := db.Save(&cart).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"success": true, "data": cart})
		return
	}

	// Handle pickup and takeaway
	if request.Vendor == "pick_up" {
		// Create self pickup shipment
		selfPickupShipment := models.ShipmentService{
			Vendor:      request.Vendor,
			Code:        request.Code,
			Price:       request.Price,
			Name:        request.Name,
			Description: request.Description,
			Raw:         request.Raw,
		}
		cart.Shipments.Data = append(cart.Shipments.Data, selfPickupShipment)
	} else if request.Vendor == "take_away" {
		// Create take away shipment
		takeAwayShipment := models.ShipmentService{
			Vendor:      request.Vendor,
			Code:        request.Code,
			Price:       request.Price,
			Name:        request.Name,
			Description: request.Description,
			Raw:         request.Raw,
		}
		cart.Shipments.Data = append(cart.Shipments.Data, takeAwayShipment)
	} else {
		// Validate regular shipping details
		if cart.Shipment.From.Address == "" || cart.Shipment.From.Name == "" || cart.Shipment.From.Phone == "" {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "from_address_is_missing"})
			return
		}

		if cart.Shipment.To.Address == "" || cart.Shipment.To.Name == "" || cart.Shipment.To.Phone == "" {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "to_address_is_missing"})
			return
		}

		// Check if shipment service exists in available shipments
		var shipmentExists bool
		for _, s := range cart.Shipments.Data {
			if s.Code == request.Code && s.Vendor == request.Vendor && s.Price == request.Price {
				shipmentExists = true
				break
			}
		}

		if !shipmentExists {
			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "shipment_service_is_incorrect"})
			return
		}
	}

	// Update cart.shipment.service with request body
	cart.Shipment.Service = models.ShipmentService{
		Vendor:      request.Vendor,
		Code:        request.Code,
		Price:       request.Price,
		Name:        request.Name,
		Description: request.Description,
		Raw:         request.Raw,
	}

	// Update destination if provided
	if request.To != nil {
		cart.Shipment.To = *request.To
	}

	// Update schedule if provided in raw.schedule
	if request.Raw != nil {
		var rawData map[string]any
		if err := json.Unmarshal(request.Raw, &rawData); err == nil {
			if schedule, ok := rawData["schedule"].(map[string]any); ok {
				scheduleJSON, err := json.Marshal(schedule)
				if err == nil {
					var shipmentSchedule models.ShipmentSchedule
					if err := json.Unmarshal(scheduleJSON, &shipmentSchedule); err == nil {
						cart.Shipment.Schedule = &shipmentSchedule
					}
				}
			}
		}
	}

	// Update shipping fee
	cart.ShippingFee = request.Price

	// Calculate gifts and update cart totals
	if err := populateCartGifts(db, &site, user, &cart, nil); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Save cart
	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "data": cart})
}
