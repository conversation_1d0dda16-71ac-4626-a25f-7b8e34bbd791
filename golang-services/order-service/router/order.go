package router

import (
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// OrderDetailResponse defines the response structure for order details
type OrderDetailResponse struct {
	Success bool   `json:"success"`
	Data    any    `json:"data,omitempty"`
	Error   string `json:"error,omitempty"`
}

// GetOrderDetail retrieves detailed information about an order
func GetOrderDetail(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	db := middlewares.GetDB(c)

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
		}
		return
	}

	// Check if user has permission to view this order
	hasPermission := funk.ContainsString(accessibleSiteIDs, order.SiteID)

	// Get site information to check permissions
	// var permSite models.Site
	// if err := db.Where("id = ?", order.SiteID).First(&permSite).Error; err == nil {
	// 	// Check brand permission
	// 	if _, exists := brandPermissions[permSite.BrandID]; exists {
	// 		hasPermission = true
	// 	}

	// 	// Check hub permission
	// 	if _, exists := hubPermissions[string(permSite.HubID)]; exists {
	// 		hasPermission = true
	// 	}
	// }

	// // If user doesn't have permission, return forbidden
	if !hasPermission {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "permission_denied",
		})
		return
	}

	// Get print information
	var printQueue models.PrintQueue
	if err := db.Where("order_id = ?", orderID).Order("created_at DESC").First(&printQueue).Error; err != nil && err != gorm.ErrRecordNotFound {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Get site, hub, and brand information
	var site models.Site
	var hub models.Hub
	var brand models.Brand

	siteName := ""
	siteType := ""
	hubName := ""
	brandName := ""
	brandID := ""
	brandType := ""

	// Get site information
	if order.SiteID != "" {
		if err := db.Where("id = ?", order.SiteID).First(&site).Error; err == nil {
			siteName = site.Name
			siteType = site.Type
			brandID = site.BrandID

			// Get brand information
			if site.BrandID != "" {
				if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err == nil {
					brandName = brand.Name
					brandType = string(brand.BrandType)
				}
			}
		}
	}

	// Get hub information
	if order.HubID != "" {
		if err := db.Where("id = ?", order.HubID).First(&hub).Error; err == nil {
			hubName = hub.Name
		}
	}

	// Build the order detail response
	orderDetail := map[string]any{
		"order_id":           order.OrderID,
		"short_order_id":     order.ShortOrderID,
		"source":             order.Source,
		"order_time_unix":    order.DataMapping.Data.OrderTimeSort,
		"pick_time_unix":     order.DataMapping.Data.OrderTimeSort, // Using OrderTimeSort as a fallback
		"delivery_time_unix": order.DataMapping.Data.DeliveryTimeUnix,
		"driver_name":        order.DataMapping.Data.DriverName,
		"driver_phone":       order.DataMapping.Data.DriverPhone,
		"dish_changed":       order.DataMapping.Data.DishChanged,
		"total":              order.DataMapping.Data.Total,
		"commission":         order.DataMapping.Data.Commission,
		"total_surcharge":    order.DataMapping.Data.TotalSurcharge,
		"total_discount":     order.DataMapping.Data.TotalDiscount,
		"total_for_biz":      order.DataMapping.Data.TotalForBiz,
		"total_shipment":     order.DataMapping.Data.TotalShipment,
		"shipment_discount":  order.DataMapping.Data.ShipmentDiscount,
		"shipment_fee":       order.DataMapping.Data.ShipmentFee,
		"transaction_fee":    order.DataMapping.Data.TransactionFee,
		"note":               order.DataMapping.Data.Note,
		"cancel_type":        order.DataMapping.Data.CancelType,
		"cancel_reason":      order.DataMapping.Data.CancelReason,
		"cancel_by":          order.DataMapping.Data.CancelBy,
		"created_at":         time.Now(), // This would be replaced with actual created_at if available
		"printed_at":         printQueue.CreatedAt,
		"dishes":             order.DataMapping.Data.Dishes,
		"coupons":            order.DataMapping.Data.Coupons,
		"payments":           order.DataMapping.Data.Payments,
		"stock_dishes":       order.DataMapping.Data.StockDishes,
		"customer_data":      order.DataMapping.Data.CustomerData,
		"finance_data":       order.DataMapping.Data.FinanceData,
		"status":             order.Status,
		"shipment":           order.Shipment,
		"order_time":         order.DataMapping.Data.OrderTime,
		"pick_time":          order.DataMapping.Data.PickTime,
		"delivery_time":      order.DataMapping.Data.DeliveryTime,

		// "raw":                order.DataMapping.Data.Raw,

		// Add site, hub, and brand information
		"site_id":    order.SiteID,
		"site_name":  siteName,
		"site_type":  siteType,
		"hub_id":     order.HubID,
		"hub_name":   hubName,
		"brand_id":   brandID,
		"brand_name": brandName,
		"brand_type": brandType,
	}

	// Show customer info based on permissions
	// Always allow viewing info for now
	canViewInfo := true

	if canViewInfo {
		orderDetail["customer_name"] = order.DataMapping.Data.CustomerName
		orderDetail["customer_address"] = order.DataMapping.Data.CustomerAddress
		orderDetail["customer_phone"] = order.DataMapping.Data.CustomerPhone
	} else {
		orderDetail["customer_name"] = order.DataMapping.Data.CustomerName
		orderDetail["customer_address"] = ""
		orderDetail["customer_phone"] = ""
	}

	// Add transactions if available - this would require additional models
	orderDetail["transactions"] = []any{}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    orderDetail,
	})
}

// UpdateOrderRequest defines the request structure for updating an order
type UpdateOrderRequest struct {
	Status string `json:"status" binding:"required"`
}

// UpdateOrder updates an existing order with new status or other fields
func UpdateOrder(c *gin.Context) {
	orderID := c.Param("order_id")

	var req UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "invalid_request",
		})
		return
	}

	// Validate status value is in enum
	validStatuses := map[string]bool{
		"DRAFT": true, "PRE_ORDER": true, "WAITING_PAYMENT": true,
		"PENDING": true, "DOING": true, "PICK": true,
		"FINISH": true, "CANCEL": true, "RETURNING": true, "RETURNED": true,
	}
	if !validStatuses[req.Status] {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "invalid_status",
		})
		return
	}

	db := middlewares.GetDB(c)

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	if (order.Status == "PICK" || order.Status == "FINISH" || order.Status == "CANCEL") && req.Status == "CANCEL" {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "order_not_cancel",
		})
		return
	}

	// Create a map for updates
	updates := make(map[string]any)

	// Apply status if provided
	if req.Status != "" {
		updates["status"] = req.Status // Using Status field directly
	}

	// Handle status-specific logic
	if req.Status == "DOING" {
		now := time.Now().Format(time.RFC3339)
		updates["data_mapping.pick_time"] = now
	}

	if req.Status == "FINISH" {
		now := time.Now()
		nowStr := now.Format(time.RFC3339)
		nowUnix := now.Unix()

		// Check if this is user's first order
		var isFirstOrder bool
		if order.ID != "" {
			var count int64
			db.Model(&models.Order{}).Where("id = ? AND status = ?", order.ID, "FINISH").Count(&count)
			isFirstOrder = count == 0
		}

		updates["data.pick_time"] = nowStr
		updates["data_mapping.pick_time"] = nowStr
		updates["data.delivery_time"] = nowStr
		updates["data_mapping.delivery_time"] = nowStr
		updates["data.delivery_time_unix"] = nowUnix
		updates["data_mapping.delivery_time_unix"] = nowUnix
		updates["is_first_order"] = isFirstOrder

		// Handle voucher logic - this would be implemented in a separate service
		// For now, we'll skip this part and just note it
	}

	// Handle special source-specific logic for 'momo'
	if (req.Status == "DOING" || req.Status == "FINISH" || req.Status == "PICK" || req.Status == "CANCEL") &&
		order.Source == "momo" {
		// In a real implementation, you would call the momo integration service here
		// For example: momoService.UpdateOrderStatus(...)
		// For now, we'll skip this part since it's an external service call
	}

	// Update the order
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    order,
	})
}

// OrderListResponse defines the response structure for order list
type OrderListResponse struct {
	Success bool   `json:"success"`
	Data    any    `json:"data,omitempty"`
	Total   int64  `json:"total,omitempty"`
	Page    int    `json:"page,omitempty"`
	Limit   int    `json:"limit,omitempty"`
	Error   string `json:"error,omitempty"`
}

// Note: This should be replaced with models.PaginationResponse in the future

// OrderCountResponse defines the response structure for order count
type OrderCountResponse struct {
	Success bool   `json:"success"`
	Data    any    `json:"data,omitempty"`
	Error   string `json:"error,omitempty"`
}

// OrderQueryParams defines the common query parameters for order list and count endpoints
type OrderQueryParams struct {
	Status       string `form:"status"`
	From         string `form:"from"`
	To           string `form:"to"`
	FilterType   string `form:"filter_type"`
	HubIDs       string `form:"hub_ids"`
	BrandIDs     string `form:"brand_ids"`
	SiteIDs      string `form:"site_ids"`
	Page         int    `form:"page" binding:"min=1"`
	ItemsPerPage int    `form:"items_per_page" binding:"min=1,max=100"`

	// Table parameters using flat format
	TablePage         int `form:"table[page]" binding:"min=0"`
	TableItemsPerPage int `form:"table[items_per_page]" binding:"min=0,max=100"`

	// Table filter parameters
	TableFilterSiteName   string   `form:"table[filter][site_name]"`
	TableFilterOrderID    string   `form:"table[filter][order_id]"`
	TableFilterSource     []string `form:"table[filter][source]"`
	TableFilterVendorSync string   `form:"table[filter][_vendor_sync]"`

	// Table sorter parameters
	TableSorterColumn string `form:"table[sorter][column]"`
	TableSorterState  string `form:"table[sorter][state]"`
}

// QueryBuilderResult contains the query and other data needed for order list/count operations
type QueryBuilderResult struct {
	Query             *gorm.DB
	AccessibleSiteIDs []string
	FromUnix          int64
	ToUnix            int64
	Page              int
	ItemsPerPage      int
	Status            string
}

// buildOrderQuery creates a common query for both order list and count endpoints
func buildOrderQuery(c *gin.Context, db *gorm.DB, params OrderQueryParams) (*QueryBuilderResult, error) {
	// Get user's brand and hub permissions
	// brandPermissions := middlewares.GetBrandPermissions(c)
	// hubPermissions := middlewares.GetHubPermissions(c)

	// Extract values from the struct
	status := params.Status
	from := params.From
	to := params.To
	filterType := params.FilterType
	hubIDsParam := params.HubIDs
	brandIDsParam := params.BrandIDs
	siteIDsParam := params.SiteIDs

	// Set pagination parameters, prioritizing flat table parameters if provided
	page := params.Page
	itemsPerPage := params.ItemsPerPage

	// Use table parameters if provided
	if params.TablePage > 0 {
		page = params.TablePage
	}

	if params.TableItemsPerPage > 0 {
		itemsPerPage = params.TableItemsPerPage
	}

	// Apply date range filter
	var fromTime, toTime time.Time
	var fromUnix, toUnix int64

	if from != "" {
		fromTime, _ = time.Parse(time.RFC3339, from)
		fromUnix = fromTime.Unix()
	} else {
		fromTime = time.Now().Truncate(24 * time.Hour) // Start of today
		fromUnix = fromTime.Unix()
	}

	if to != "" {
		toTime, _ = time.Parse(time.RFC3339, to)
		toUnix = toTime.Unix()
	} else {
		toTime = time.Now() // Current time
		toUnix = toTime.Unix()
	}

	// Get all sites and hubs the user has access to
	var accessibleSiteIDs []string
	// var accessibleHubIDs []string

	// Get sites from brand permissions
	// if len(brandPermissions) > 0 {
	// 	var brandIDs []string
	// 	for brandID := range brandPermissions {
	// 		brandIDs = append(brandIDs, brandID)
	// 	}

	// 	var brandSites []models.Site
	// 	if err := db.Where("brand_id IN (?)", brandIDs).Find(&brandSites).Error; err == nil {
	// 		for _, site := range brandSites {
	// 			accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
	// 		}
	// 	}
	// }

	// // Get sites from hub permissions
	// if len(hubPermissions) > 0 {
	// 	for hubID := range hubPermissions {
	// 		accessibleHubIDs = append(accessibleHubIDs, hubID)
	// 	}

	// 	var hubSites []models.Site
	// 	if err := db.Where("hub_id IN (?)", accessibleHubIDs).Find(&hubSites).Error; err == nil {
	// 		for _, site := range hubSites {
	// 			accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
	// 		}
	// 	}
	// }

	// Remove duplicates
	accessibleSiteIDs = middlewares.GetUserAccessibleSites(c)
	user := middlewares.GetUser(c)
	// If user has no accessible sites, return early
	if len(accessibleSiteIDs) == 0 {
		return &QueryBuilderResult{
			Query:             db.Model(&models.Order{}),
			AccessibleSiteIDs: accessibleSiteIDs,
			FromUnix:          fromUnix,
			ToUnix:            toUnix,
			Page:              page,
			ItemsPerPage:      itemsPerPage,
			Status:            status,
		}, nil
	}

	// Build base query with common filters
	query := db.Model(&models.Order{})

	// Apply status filter
	if status != "" && status != "ALL" {
		query = query.Where("status = ?", status)
	}

	// Apply time filter based on status
	query = query.Where("(data_mapping->>'order_time_sort')::int >= ? AND (data_mapping->>'order_time_sort')::int <= ?", fromUnix, toUnix)

	// Apply base permission filter - user can only see orders from sites they have access to
	query = query.Where("site_id IN (?)", accessibleSiteIDs)

	// Apply additional filter by type (hub, brand, site)
	if filterType == "hub" && hubIDsParam != "" {
		hubIDList := strings.Split(hubIDsParam, ",")
		// Filter by intersection of accessible hubs and requested hubs
		var filteredHubIDs []string
		for _, hubID := range hubIDList {

			if funk.ContainsString(user.Hubs, hubID) {
				filteredHubIDs = append(filteredHubIDs, hubID)
			}
		}

		if len(filteredHubIDs) > 0 {
			query = query.Where("hub_id IN (?)", filteredHubIDs)
		} else {
			// No accessible hubs found in the requested list
			return &QueryBuilderResult{
				Query:             query,
				AccessibleSiteIDs: accessibleSiteIDs,
				FromUnix:          fromUnix,
				ToUnix:            toUnix,
				Page:              page,
				ItemsPerPage:      itemsPerPage,
				Status:            status,
			}, nil
		}
	} else if filterType == "brand" && brandIDsParam != "" {
		// For brand filter, filter by intersection of accessible brands and requested brands
		brandIDList := strings.Split(brandIDsParam, ",")
		var filteredBrandIDs []string
		for _, brandID := range brandIDList {

			if funk.ContainsString(user.Brands, brandID) {
				filteredBrandIDs = append(filteredBrandIDs, brandID)
			}
		}

		if len(filteredBrandIDs) > 0 {
			// Get all sites belonging to the filtered brands
			var sites []models.Site
			db.Where("brand_id IN (?)", filteredBrandIDs).Find(&sites)

			if len(sites) > 0 {
				siteIDList := make([]string, len(sites))
				for i, site := range sites {
					siteIDList[i] = site.ID
				}
				// Filter by intersection of accessible sites and sites from filtered brands
				query = query.Where("site_id IN (?)", siteIDList)
			} else {
				// No sites found for the filtered brands
				return &QueryBuilderResult{
					Query:             query,
					AccessibleSiteIDs: accessibleSiteIDs,
					FromUnix:          fromUnix,
					ToUnix:            toUnix,
					Page:              page,
					ItemsPerPage:      itemsPerPage,
					Status:            status,
				}, nil
			}
		} else {
			// No accessible brands found in the requested list
			return &QueryBuilderResult{
				Query:             query,
				AccessibleSiteIDs: accessibleSiteIDs,
				FromUnix:          fromUnix,
				ToUnix:            toUnix,
				Page:              page,
				ItemsPerPage:      itemsPerPage,
				Status:            status,
			}, nil
		}
	} else if filterType == "site" && siteIDsParam != "" {
		// Filter by intersection of accessible sites and requested sites
		siteIDList := strings.Split(siteIDsParam, ",")
		var filteredSiteIDs []string
		for _, siteID := range siteIDList {
			if funk.ContainsString(accessibleSiteIDs, siteID) {
				filteredSiteIDs = append(filteredSiteIDs, siteID)
			}
		}

		if len(filteredSiteIDs) > 0 {
			query = query.Where("site_id IN (?)", filteredSiteIDs)
		} else {
			// No accessible sites found in the requested list
			return &QueryBuilderResult{
				Query:             query,
				AccessibleSiteIDs: accessibleSiteIDs,
				FromUnix:          fromUnix,
				ToUnix:            toUnix,
				Page:              page,
				ItemsPerPage:      itemsPerPage,
				Status:            status,
			}, nil
		}
	}

	// Apply table filters if provided
	// Apply site name filter
	siteName := params.TableFilterSiteName
	if siteName != "" {
		var sites []models.Site
		db.Where("name ILIKE ?", "%"+siteName+"%").Find(&sites)
		if len(sites) > 0 {
			siteIDs := make([]string, len(sites))
			for i, site := range sites {
				siteIDs[i] = site.ID
			}
			query = query.Where("site_id IN (?)", siteIDs)
		}
	}

	// Apply order ID filter
	orderID := params.TableFilterOrderID
	if orderID != "" {
		query = query.Where("data_mapping->>'order_id' ILIKE ?", "%"+orderID+"%")
	}

	// Apply sources filter
	sources := params.TableFilterSource
	if len(sources) > 0 {
		query = query.Where("source IN (?)", sources)
	}

	// Apply vendor sync filter
	vendorSyncStr := params.TableFilterVendorSync
	if vendorSyncStr != "" {
		vendorSync := vendorSyncStr == "true"
		if vendorSync {
			query = query.Where("vendor_sync->>'success' = 'true'")
		} else {
			query = query.Where("vendor_sync->>'success' = 'false' OR vendor_sync IS NULL")
		}
	}

	return &QueryBuilderResult{
		Query:             query,
		AccessibleSiteIDs: accessibleSiteIDs,
		FromUnix:          fromUnix,
		ToUnix:            toUnix,
		Page:              page,
		ItemsPerPage:      itemsPerPage,
		Status:            status,
	}, nil
}

// GetMultiSiteOrderListCount retrieves counts of orders across multiple sites by status
func GetMultiSiteOrderListCount(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderCountResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Set default values and bind query parameters
	params := OrderQueryParams{
		Page:         1,
		ItemsPerPage: 20,
	}

	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, OrderCountResponse{
			Success: false,
			Error:   "invalid_query_parameters: " + err.Error(),
		})
		return
	}

	params.Status = ""

	// Initialize counts for all statuses
	counts := map[string]int64{
		"ALL":             0,
		"DRAFT":           0,
		"PRE_ORDER":       0,
		"WAITING_PAYMENT": 0,
		"PENDING":         0,
		"DOING":           0,
		"FINISH":          0,
		"CANCEL":          0,
		"RETURNING":       0,
		"RETURNED":        0,
		"PICK":            0,
	}

	// Build the query using the common helper function
	result, err := buildOrderQuery(c, db, params)
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderCountResponse{
			Success: false,
			Error:   "query_build_error: " + err.Error(),
		})
		return
	}

	// If no accessible sites, return empty result
	if len(result.AccessibleSiteIDs) == 0 {
		c.JSON(http.StatusOK, OrderCountResponse{
			Success: true,
			Data:    counts,
		})
		return
	}

	// Get the base query
	baseQuery := result.Query
	fromUnix := result.FromUnix
	toUnix := result.ToUnix

	// Count orders for each status
	for status := range counts {
		query := baseQuery.Session(&gorm.Session{}) // Create a new session to avoid modifying the base query

		query = query.Where("(data_mapping->>'order_time_sort')::int >= ? AND (data_mapping->>'order_time_sort')::int <= ?", fromUnix, toUnix)
		if status != "ALL" {
			query = query.Where("status = ?", status)
		} else {
			// query = query.Where(
			// 	"(data_mapping->>'order_time_sort')::int >= ? AND (data_mapping->>'order_time_sort')::int <= ? OR "+
			// 		"(status IN ('DOING', 'PICK') AND (data_mapping->>'order_time_sort')::int <= ?) OR "+
			// 		"(status IN ('PENDING', 'RETURNING', 'RETURNED') AND (data_mapping->>'order_time_sort')::int >= ? AND (data_mapping->>'order_time_sort')::int <= ?)",
			// 	fromUnix, toUnix, toUnix, time.Now().AddDate(0, 0, -30).Unix(), toUnix)
		}

		// Count orders for this status
		var count int64
		if err := query.Count(&count).Error; err != nil {
			c.JSON(http.StatusBadRequest, OrderCountResponse{
				Success: false,
				Error:   "database_error",
			})
			return
		}

		counts[status] = count
	}

	c.JSON(http.StatusOK, OrderCountResponse{
		Success: true,
		Data:    counts,
	})
}

// GetMultiSiteOrderList retrieves a list of orders across multiple sites
func GetMultiSiteOrderList(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderListResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Set default values
	params := OrderQueryParams{
		Page:         1,
		ItemsPerPage: 20,
	}

	// Bind query parameters to struct
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, OrderListResponse{
			Success: false,
			Error:   "invalid_query_parameters: " + err.Error(),
		})
		return
	}

	// Build the query using the common helper function
	result, err := buildOrderQuery(c, db, params)
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderListResponse{
			Success: false,
			Error:   "query_build_error: " + err.Error(),
		})
		return
	}

	// If no accessible sites, return empty result
	if len(result.AccessibleSiteIDs) == 0 {
		c.JSON(http.StatusOK, models.PaginationResponse{
			Success:    true,
			Data:       []any{},
			Total:      0,
			Page:       result.Page,
			PageSize:   result.ItemsPerPage,
			TotalPages: 0,
			HasNext:    false,
			HasPrev:    false,
		})
		return
	}

	// Get the base query
	query := result.Query
	page := result.Page
	itemsPerPage := result.ItemsPerPage

	// Apply sorting
	sorterColumn := params.TableSorterColumn
	sorterState := params.TableSorterState

	if sorterColumn != "" {
		var sortDirection string
		if sorterState == "descend" {
			sortDirection = "DESC"
		} else {
			sortDirection = "ASC"
		}

		switch sorterColumn {
		case "order_id":
			query = query.Order("order_id " + sortDirection)
		case "source":
			query = query.Order("source " + sortDirection)
		default:
			// Default sorting
			query = query.Order("created_at DESC")
		}
	}

	// Default sorting if not specified
	if !strings.Contains(query.Statement.SQL.String(), "ORDER BY") {
		query = query.Order("created_at DESC")
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderListResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Apply pagination
	offset := (page - 1) * itemsPerPage
	var orders []models.Order
	if err := query.Offset(offset).Limit(itemsPerPage).Find(&orders).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderListResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Get site and hub information for the orders
	orderSiteIDs := make([]string, 0, len(orders))
	orderHubIDs := make([]string, 0, len(orders))
	for _, order := range orders {
		orderSiteIDs = append(orderSiteIDs, order.SiteID)
		orderHubIDs = append(orderHubIDs, order.HubID)
	}

	// Remove duplicates
	orderSiteIDs = funk.UniqString(orderSiteIDs)
	orderHubIDs = funk.UniqString(orderHubIDs)

	// Get sites and hubs
	var sites []models.Site
	var hubs []models.Hub
	db.Where("id IN (?)", orderSiteIDs).Find(&sites)
	db.Where("id IN (?)", orderHubIDs).Find(&hubs)

	// Get brand information for sites
	brandIDs := make([]string, 0, len(sites))
	for _, site := range sites {
		if site.BrandID != "" {
			brandIDs = append(brandIDs, site.BrandID)
		}
	}
	brandIDs = funk.UniqString(brandIDs)

	var brands []models.Brand
	db.Where("id IN (?)", brandIDs).Find(&brands)

	// Create maps for quick lookup
	siteMap := make(map[string]models.Site)
	hubMap := make(map[string]models.Hub)
	brandMap := make(map[string]models.Brand)

	for _, site := range sites {
		siteMap[site.ID] = site
	}

	for _, hub := range hubs {
		hubMap[hub.ID] = hub
	}

	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	// Transform orders for response
	responseData := make([]map[string]any, len(orders))
	for i, order := range orders {
		// Get site, hub, and brand information
		site, siteExists := siteMap[order.SiteID]
		hub, hubExists := hubMap[order.HubID]

		siteName := ""
		siteType := ""
		hubName := ""
		brandName := ""
		brandID := ""
		brandType := ""

		if siteExists {
			siteName = site.Name
			siteType = site.Type
			brandID = site.BrandID

			// Get brand name if available
			if brand, brandExists := brandMap[site.BrandID]; brandExists {
				brandName = brand.Name
				brandType = string(brand.BrandType)
			}
		}

		if hubExists {
			hubName = hub.Name
		}

		// Create response data
		orderData := map[string]any{
			// Include data_mapping fields
			"id":                order.DataMapping.Data.ID,
			"order_id":          order.DataMapping.Data.OrderID,
			"source":            order.Source,
			"order_time":        order.DataMapping.Data.OrderTime,
			"pick_time":         order.DataMapping.Data.PickTime,
			"delivery_time":     order.DataMapping.Data.DeliveryTime,
			"order_time_sort":   order.DataMapping.Data.OrderTimeSort,
			"driver_name":       order.DataMapping.Data.DriverName,
			"driver_phone":      order.DataMapping.Data.DriverPhone,
			"customer_name":     order.DataMapping.Data.CustomerName,
			"total":             order.DataMapping.Data.Total,
			"commission":        order.DataMapping.Data.Commission,
			"total_discount":    order.DataMapping.Data.TotalDiscount,
			"total_for_biz":     order.DataMapping.Data.TotalForBiz,
			"total_shipment":    order.DataMapping.Data.TotalShipment,
			"shipment_discount": order.DataMapping.Data.ShipmentDiscount,
			"shipment_fee":      order.DataMapping.Data.ShipmentFee,
			"transaction_fee":   order.DataMapping.Data.TransactionFee,
			"note":              order.DataMapping.Data.Note,

			// Include order fields
			"site_id":        order.SiteID,
			"shipment":       order.Shipment,
			"vendor_sync":    order.VendorSync,
			"status":         order.Status,
			"auto_confirmed": order.AutoConfirmed,

			// Add site, hub, and brand information
			"site_name":  siteName,
			"site_type":  siteType,
			"hub_id":     order.HubID,
			"hub_name":   hubName,
			"brand_id":   brandID,
			"brand_name": brandName,
			"brand_type": brandType,
		}

		// Handle customer info based on permissions
		// For now, we'll show customer info for all orders except those with sensitive statuses
		showCustomerInfo := true
		if order.Status == "CANCEL" || order.Status == "RETURNED" {
			showCustomerInfo = false
		}

		if showCustomerInfo {
			orderData["customer_address"] = order.DataMapping.Data.CustomerAddress
			orderData["customer_phone"] = order.DataMapping.Data.CustomerPhone
		} else {
			orderData["customer_address"] = ""
			orderData["customer_phone"] = ""
		}

		responseData[i] = orderData
	}

	totalPages := int(math.Ceil(float64(total) / float64(itemsPerPage)))
	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       responseData,
		Total:      total,
		Page:       page,
		PageSize:   itemsPerPage,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// GetMultiSiteNewOrders checks for new orders in the last few minutes
func GetMultiSiteNewOrders(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "user_not_authenticated",
		})
		return
	}

	// Get user's brand and hub permissions
	// brandPermissions := middlewares.GetBrandPermissions(c)
	// hubPermissions := middlewares.GetHubPermissions(c)

	// Get query parameters
	filterType := c.Query("filter_type")
	hubIDsParam := c.Query("hub_ids")
	brandIDsParam := c.Query("brand_ids")
	siteIDsParam := c.Query("site_ids")

	// Build the query
	query := db.Model(&models.Order{})

	// Filter by source (exclude 'local')
	query = query.Where("source != ?", "local")

	// Filter by status (only DOING and PENDING)
	query = query.Where("status IN (?)", []string{"DOING", "PENDING"})

	// Filter by time (last 3 minutes)
	threeMinutesAgo := time.Now().Add(-3 * time.Minute).Unix()
	query = query.Where("(data_mapping->>'order_time_sort')::int >= ?", threeMinutesAgo)

	// Get all sites and hubs the user has access to
	var accessibleSiteIDs []string
	// var accessibleHubIDs []string

	// Get sites from brand permissions
	// if len(brandPermissions) > 0 {
	// 	var brandIDs []string
	// 	for brandID := range brandPermissions {
	// 		brandIDs = append(brandIDs, brandID)
	// 	}

	// 	var brandSites []models.Site
	// 	if err := db.Where("brand_id IN (?)", brandIDs).Find(&brandSites).Error; err == nil {
	// 		for _, site := range brandSites {
	// 			accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
	// 		}
	// 	}
	// }

	// Get sites from hub permissions
	// if len(hubPermissions) > 0 {
	// 	for hubID := range hubPermissions {
	// 		accessibleHubIDs = append(accessibleHubIDs, hubID)
	// 	}

	// 	var hubSites []models.Site
	// 	if err := db.Where("hub_id IN (?)", accessibleHubIDs).Find(&hubSites).Error; err == nil {
	// 		for _, site := range hubSites {
	// 			accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
	// 		}
	// 	}
	// }

	// Remove duplicates
	accessibleSiteIDs = middlewares.GetUserAccessibleSites(c)

	// If user has no accessible sites, return empty result
	if len(accessibleSiteIDs) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"has_new_order": false,
			},
		})
		return
	}

	// Apply base permission filter - user can only see orders from sites they have access to
	query = query.Where("site_id IN (?)", accessibleSiteIDs)

	// Apply additional filter by type (hub, brand, site)
	if filterType == "hub" && hubIDsParam != "" {
		hubIDList := strings.Split(hubIDsParam, ",")
		// Filter by intersection of accessible hubs and requested hubs
		var filteredHubIDs []string
		for _, hubID := range hubIDList {
			if funk.ContainsString(user.Hubs, hubID) {
				filteredHubIDs = append(filteredHubIDs, hubID)
			}
		}

		if len(filteredHubIDs) > 0 {
			query = query.Where("hub_id IN (?)", filteredHubIDs)
		} else {
			// No accessible hubs found in the requested list
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data": gin.H{
					"has_new_order": false,
				},
			})
			return
		}
	} else if filterType == "brand" && brandIDsParam != "" {
		// For brand filter, filter by intersection of accessible brands and requested brands
		brandIDList := strings.Split(brandIDsParam, ",")
		var filteredBrandIDs []string
		for _, brandID := range brandIDList {
			if funk.ContainsString(user.Brands, brandID) {
				filteredBrandIDs = append(filteredBrandIDs, brandID)
			}
		}

		if len(filteredBrandIDs) > 0 {
			// Get all sites belonging to the filtered brands
			var sites []models.Site
			db.Where("brand_id IN (?)", filteredBrandIDs).Find(&sites)

			if len(sites) > 0 {
				siteIDList := make([]string, len(sites))
				for i, site := range sites {
					siteIDList[i] = site.ID
				}
				// Filter by intersection of accessible sites and sites from filtered brands
				query = query.Where("site_id IN (?)", siteIDList)
			} else {
				// No sites found for the filtered brands
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"has_new_order": false,
					},
				})
				return
			}
		} else {
			// No accessible brands found in the requested list
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data": gin.H{
					"has_new_order": false,
				},
			})
			return
		}
	} else if filterType == "site" && siteIDsParam != "" {
		// Filter by intersection of accessible sites and requested sites
		siteIDList := strings.Split(siteIDsParam, ",")
		var filteredSiteIDs []string
		for _, siteID := range siteIDList {
			if funk.ContainsString(accessibleSiteIDs, siteID) {
				filteredSiteIDs = append(filteredSiteIDs, siteID)
			}
		}

		if len(filteredSiteIDs) > 0 {
			query = query.Where("site_id IN (?)", filteredSiteIDs)
		} else {
			// No accessible sites found in the requested list
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data": gin.H{
					"has_new_order": false,
				},
			})
			return
		}
	}

	// Get the most recent order
	var order models.Order
	if err := query.Order("(data_mapping->>'order_time_sort')::int DESC").First(&order).Error; err != nil {
		// No new orders found
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"has_new_order": false,
			},
		})
		return
	}

	// Get site, hub, and brand information
	var site models.Site
	var hub models.Hub
	var brand models.Brand
	db.Where("id = ?", order.SiteID).First(&site)
	db.Where("id = ?", order.HubID).First(&hub)

	// Get brand information if site has a brand ID
	brandName := ""
	if site.BrandID != "" {
		if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err == nil {
			brandName = brand.Name
		}
	}

	// Create response data
	orderData := map[string]any{
		// Include data_mapping fields
		"id":                order.DataMapping.Data.ID,
		"order_id":          order.DataMapping.Data.OrderID,
		"order_time":        order.DataMapping.Data.OrderTime,
		"pick_time":         order.DataMapping.Data.PickTime,
		"delivery_time":     order.DataMapping.Data.DeliveryTime,
		"order_time_sort":   order.DataMapping.Data.OrderTimeSort,
		"driver_name":       order.DataMapping.Data.DriverName,
		"driver_phone":      order.DataMapping.Data.DriverPhone,
		"customer_name":     order.DataMapping.Data.CustomerName,
		"customer_address":  order.DataMapping.Data.CustomerAddress,
		"customer_phone":    order.DataMapping.Data.CustomerPhone,
		"total":             order.DataMapping.Data.Total,
		"commission":        order.DataMapping.Data.Commission,
		"total_discount":    order.DataMapping.Data.TotalDiscount,
		"total_for_biz":     order.DataMapping.Data.TotalForBiz,
		"total_shipment":    order.DataMapping.Data.TotalShipment,
		"shipment_discount": order.DataMapping.Data.ShipmentDiscount,
		"shipment_fee":      order.DataMapping.Data.ShipmentFee,
		"transaction_fee":   order.DataMapping.Data.TransactionFee,
		"note":              order.DataMapping.Data.Note,

		// Include order fields
		"site_id":        order.SiteID,
		"site_name":      site.Name,
		"site_type":      site.Type,
		"hub_id":         order.HubID,
		"hub_name":       hub.Name,
		"brand_id":       site.BrandID,
		"brand_name":     brandName,
		"status":         order.Status,
		"auto_confirmed": order.AutoConfirmed,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"has_new_order":   true,
			"last_order_time": order.DataMapping.Data.OrderTimeSort,
			"order":           orderData,
		},
	})
}

// ConfirmOrder confirms an order
func ConfirmOrder(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Get user's brand and hub permissions
	// brandPermissions := middlewares.GetBrandPermissions(c)
	// hubPermissions := middlewares.GetHubPermissions(c)

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Check if user has permission to confirm this order
	accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)
	// check if the order's site_id is in the user's accessible sites
	hasPermission := funk.ContainsString(accessibleSiteIDs, order.SiteID)

	// Get site information to check permissions
	var site models.Site

	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err == nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "site_not_found",
		})
		return
	}

	// If user doesn't have permission, return forbidden
	if !hasPermission {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "permission_denied",
		})
		return
	}

	// Check if order can be confirmed (only PENDING orders can be confirmed)
	if order.Status != "PENDING" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_not_confirmable",
		})
		return
	}

	// Get hub information
	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "hub_not_found",
		})
		return
	}

	// Process confirmation based on order source
	var confirmErr error

	// Get token for the order source
	tokenAccount, err := models.GetTokenBySite(db, &site, order.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "token_error: " + err.Error(),
		})
		return
	}

	// Call the appropriate merchant service to confirm the order
	if tokenAccount != nil && tokenAccount.AccessToken != "" {
		// Create a token object for the merchant service
		token := &models.Token{
			SiteID:      tokenAccount.SiteID,
			AccessToken: tokenAccount.AccessToken,
			Username:    tokenAccount.Username,
		}

		// Call the appropriate merchant service based on the order source
		switch order.Source {
		case "shopee_food", "shopee_fresh":
			// Import the shopee package
			shopeeMerchant := merchant.NewMerchant("shopee")
			confirmErr = shopeeMerchant.ConfirmOrder(token, orderID)
		case "grab", "grab_mart":
			// Import the grab package
			grabMerchant := merchant.NewMerchant("grab")
			confirmErr = grabMerchant.ConfirmOrder(token, orderID)
		case "be":
			// Import the be package
			beMerchant := merchant.NewMerchant("be")
			confirmErr = beMerchant.ConfirmOrder(token, orderID)
		case "gojek":
			// Import the gojek package
			gojekMerchant := merchant.NewMerchant("gojek")
			confirmErr = gojekMerchant.ConfirmOrder(token, orderID)
		}

		if confirmErr != nil {
			c.JSON(http.StatusBadRequest, OrderDetailResponse{
				Success: false,
				Error:   "confirm_error: " + confirmErr.Error(),
			})
			return
		}
	}

	// Update order status and auto_confirmed flag
	updates := map[string]any{
		"status":         "DOING",
		"auto_confirmed": true,
	}

	// Update the order
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    map[string]any{"message": "Order confirmed successfully"},
	})
}

// CancelOrderRequest defines the request structure for canceling an order
type CancelOrderRequest struct {
	CancelType   string `json:"cancel_type"`
	CancelReason string `json:"cancel_reason"`
}

// CancelOrder cancels an order
func CancelOrder(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Parse request body
	var req CancelOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Default values if not provided
		req.CancelType = "out_stock"
		req.CancelReason = "Out of stock"
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Get user's brand and hub permissions
	// brandPermissions := middlewares.GetBrandPermissions(c)
	// hubPermissions := middlewares.GetHubPermissions(c)

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Check if user has permission to cancel this order
	accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)
	hasPermission := funk.ContainsString(accessibleSiteIDs, order.SiteID)

	// Get site information from db
	var site models.Site = models.Site{}
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "site_not_found",
		})
		return
	}

	// if err := db.Where("id = ?", order.SiteID).First(&site).Error; err == nil {
	// 	// Check brand permission
	// 	if _, exists := brandPermissions[site.BrandID]; exists {
	// 		hasPermission = true
	// 	}

	// 	// Check hub permission
	// 	if _, exists := hubPermissions[string(site.HubID)]; exists {
	// 		hasPermission = true
	// 	}
	// }

	// If user doesn't have permission, return forbidden
	if !hasPermission {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "permission_denied",
		})
		return
	}

	// Check if order can be canceled
	// Only allow cancellation of orders with certain statuses
	if order.Status == "PICK" || order.Status == "FINISH" || order.Status == "CANCEL" {
		// Special permission check for customer service or system users
		isCustomerService := false
		// This would be implemented with proper permission checks
		// For now, we'll just check if the user has specific permissions

		if !isCustomerService {
			c.JSON(http.StatusForbidden, OrderDetailResponse{
				Success: false,
				Error:   "order_not_cancel",
			})
			return
		}
	}

	// Get hub information
	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "hub_not_found",
		})
		return
	}

	// Process cancellation based on order source
	var cancelErr error

	// Get token for the order source
	tokenAccount, err := models.GetTokenBySite(db, &site, order.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "token_error: " + err.Error(),
		})
		return
	}

	// Call the appropriate merchant service to cancel the order
	if tokenAccount != nil && tokenAccount.AccessToken != "" {
		// Create a token object for the merchant service
		token := &models.Token{
			SiteID:      tokenAccount.SiteID,
			AccessToken: tokenAccount.AccessToken,
			Username:    tokenAccount.Username,
		}

		// Call the appropriate merchant service based on the order source
		switch order.Source {
		case "shopee_food", "shopee_fresh":
			// Import the shopee package
			shopeeMerchant := merchant.NewMerchant("shopee")
			cancelErr = shopeeMerchant.CancelOrder(token, orderID, req.CancelType)
		case "grab", "grab_mart":
			// Import the grab package
			grabMerchant := merchant.NewMerchant("grab")
			cancelErr = grabMerchant.CancelOrder(token, orderID, req.CancelType)
		case "be":
			// Import the be package
			beMerchant := merchant.NewMerchant("be")
			cancelErr = beMerchant.CancelOrder(token, orderID, req.CancelType)
		case "gojek":
			// Import the gojek package
			gojekMerchant := merchant.NewMerchant("gojek")
			cancelErr = gojekMerchant.CancelOrder(token, orderID, req.CancelType)
		}

		if cancelErr != nil {
			c.JSON(http.StatusBadRequest, OrderDetailResponse{
				Success: false,
				Error:   "cancel_error: " + cancelErr.Error(),
			})
			return
		}
	}

	// Handle special logic for 'momo' source
	if order.Source == "momo" {
		// In a real implementation, you would call the momo integration service here
		// For example: momoService.UpdateOrderStatus(...)
		// For now, we'll skip this part since it's an external service call
	}

	// Update order status and cancel information
	updates := map[string]any{
		"status":                     "CANCEL",
		"data_mapping.cancel_type":   req.CancelType,
		"data_mapping.cancel_reason": req.CancelReason,
		"data_mapping.cancel_by":     "merchant",
	}

	// Update the order
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    map[string]any{"message": "Order canceled successfully"},
	})
}

// DeleteOrder deletes an order by ID
func DeleteOrder(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Get user's brand and hub permissions
	// brandPermissions := middlewares.GetBrandPermissions(c)
	// hubPermissions := middlewares.GetHubPermissions(c)

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Check if user has permission to delete this order
	accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)
	hasPermission := funk.ContainsString(accessibleSiteIDs, order.SiteID)

	// Get site information to check permissions
	// var permSite models.Site
	// if err := db.Where("id = ?", order.SiteID).First(&permSite).Error; err == nil {
	// 	// Check brand permission
	// 	if _, exists := brandPermissions[permSite.BrandID]; exists {
	// 		hasPermission = true
	// 	}

	// 	// Check hub permission
	// 	if _, exists := hubPermissions[string(permSite.HubID)]; exists {
	// 		hasPermission = true
	// 	}
	// }

	// If user doesn't have permission, return forbidden
	if !hasPermission {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "permission_denied",
		})
		return
	}

	// Check if order can be deleted
	// Only allow deletion of orders with certain statuses
	allowedStatuses := map[string]bool{
		"DRAFT": true, "PRE_ORDER": true, "WAITING_PAYMENT": true,
		"PENDING": true, "CANCEL": true, "RETURNED": true,
	}

	if !allowedStatuses[order.Status] {
		c.JSON(http.StatusForbidden, OrderDetailResponse{
			Success: false,
			Error:   "order_not_deletable",
		})
		return
	}

	// Delete the order
	if err := db.Delete(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "delete_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    map[string]any{"message": "Order deleted successfully"},
	})
}
