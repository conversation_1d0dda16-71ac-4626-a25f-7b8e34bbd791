package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ConfirmPaymentRequest defines the request structure for confirming a payment
type ConfirmPaymentRequest struct {
	Note          string  `json:"note"`
	ImageURL      string  `json:"image_url"`
	Total         float64 `json:"total" binding:"required"`
	PaymentMethod string  `json:"payment_method" binding:"required"`
	Index         int     `json:"index"`
}

// ConfirmPayment confirms a payment for an order
func ConfirmPayment(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Parse request body
	var req ConfirmPaymentRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "invalid_request: " + err.Error(),
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Create a new payment
	newPayment := models.Payment{
		Method: req.PaymentMethod,
		Total:  req.Total,
		Status: "COMPLETED",
		Note:   req.Note,
	}

	// Get current payments from data_mapping
	var payments []models.Payment
	if order.DataMapping.Data.Payments != nil {
		payments = order.DataMapping.Data.Payments
	}

	// Add the new payment
	if req.Index >= 0 && req.Index < len(payments) {
		// Update existing payment at index
		payments[req.Index] = newPayment
	} else {
		// Add new payment
		payments = append(payments, newPayment)
	}

	// Update the order's data_mapping and data
	updates := map[string]any{
		"data_mapping.payments": payments,
		"data.payments":         payments,
	}

	// Update the order
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    payments,
	})
}

// DeletePaymentRequest defines the request structure for deleting a payment
type DeletePaymentRequest struct {
	Index int `json:"index"`
}

// DeletePayment deletes a payment from an order
func DeletePayment(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Parse request body
	var req DeletePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Default to index 0 if not provided
		req.Index = 0
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Get current payments from data_mapping
	var payments []models.Payment
	if order.DataMapping.Data.Payments != nil {
		payments = order.DataMapping.Data.Payments
	}

	// Check if index is valid
	if req.Index < 0 || req.Index >= len(payments) {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "invalid_payment_index",
		})
		return
	}

	// Remove the payment at the specified index
	payments = append(payments[:req.Index], payments[req.Index+1:]...)

	// Update the order's data_mapping and data
	updates := map[string]any{
		"data_mapping.payments": payments,
		"data.payments":         payments,
	}

	// Update the order
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    payments,
	})
}
