package router

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/bill"

	"github.com/gin-gonic/gin"
)

// PosCreateOrderRequest defines the request structure for creating a POS order
type PosCreateOrderRequest struct {
	CustomerPhone   string   `json:"customer_phone"`
	CustomerName    string   `json:"customer_name"`
	DeliveryType    string   `json:"delivery_type"` // pickup or delivery
	PaymentMethod   string   `json:"payment_method"`
	VoucherCodes    []string `json:"voucher_codes,omitempty"`
	AppliedDiscount float64  `json:"applied_discount,omitempty"`
	OrderItems      []struct {
		Code     string  `json:"code"`
		Quantity int     `json:"quantity"`
		Price    float64 `json:"price"`
	} `json:"order_items"`
	CustomerAddress *struct {
		Lat  float64 `json:"lat"`
		Long float64 `json:"long"`
		Text string  `json:"text"`
	} `json:"customer_address,omitempty"`
	PickupTime *string `json:"pickup_time,omitempty"`
	Note       string  `json:"note,omitempty"`
}

// CreatePosOrder handles the request to create a new order through POS
func CreatePosOrder(c *gin.Context) {
	// Get site ID from URL parameter
	siteID := c.Param("site_id")
	siteIDInt, err := strconv.Atoi(siteID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid site ID",
		})
		return
	}

	var req PosCreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if req.CustomerPhone == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Customer phone is required",
		})
		return
	}

	if req.DeliveryType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Delivery type is required",
		})
		return
	}

	if req.PaymentMethod == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Payment method is required",
		})
		return
	}

	if len(req.OrderItems) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Order items are required",
		})
		return
	}

	// If delivery type is delivery, validate customer address
	if req.DeliveryType == "delivery" && req.CustomerAddress == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Customer address is required for delivery orders",
		})
		return
	}

	// If delivery type is pickup, validate pickup time
	if req.DeliveryType == "pickup" && req.PickupTime == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Pickup time is required for pickup orders",
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)

	// Fetch site data
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Site not found",
		})
		return
	}

	// Calculate subtotal and total
	var subtotal float64
	for _, item := range req.OrderItems {
		subtotal += item.Price * float64(item.Quantity)
	}

	total := subtotal - req.AppliedDiscount

	// Generate order ID
	// Format: {site_code}-{YYMMDD}-{index}
	now := time.Now()
	orderGroup := fmt.Sprintf("%s-%s", strings.ToUpper(site.Code), now.Format("060102"))

	// Get or create order index
	var siteOrderIndex models.SiteOrderIndex
	if err := db.Where("site_id = ? AND group_name = ?", siteID, orderGroup).
		FirstOrCreate(&siteOrderIndex, models.SiteOrderIndex{
			SiteID:    siteID,
			GroupName: orderGroup,
			Index:     0,
		}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to generate order ID",
		})
		return
	}

	// Increment index
	siteOrderIndex.Index++
	if err := db.Save(&siteOrderIndex).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to update order index",
		})
		return
	}

	orderID := fmt.Sprintf("%s-%03d", orderGroup, siteOrderIndex.Index)

	// Create order dishes
	dishes := make([]models.OrderDish, len(req.OrderItems))
	for i, item := range req.OrderItems {
		dishes[i] = models.OrderDish{
			Name:          item.Code, // Using code as name temporarily
			Quantity:      int64(item.Quantity),
			Price:         item.Price * float64(item.Quantity),
			DiscountPrice: item.Price,
			Note:          "",
		}
	}

	// Create payments array
	payments := []models.Payment{
		{
			Method: req.PaymentMethod,
			Total:  total,
			Status: "COMPLETED", // For POS orders, payment is typically completed at creation
		},
	}

	// Create shipping data
	var shipment models.OrderShipment
	if req.DeliveryType == "delivery" {
		shipment = models.OrderShipment{
			COD: 0, // No COD for POS orders
			// PaymentMethod: req.PaymentMethod,
			Price:     0, // No shipping fee for POS orders
			ToAddress: req.CustomerAddress.Text,
			ToName:    req.CustomerName,
			ToPhone:   req.CustomerPhone,
		}
	}

	// Determine status based on payment method
	status := "PENDING"
	payViaWalletOrBank := []string{"BANK_TRANSFER", "WALLET"}
	for _, method := range payViaWalletOrBank {
		if req.PaymentMethod == method {
			status = "WAITING_PAYMENT"
			break
		}
	}

	// Create order data mapping
	dataMapping := models.DataMapping{
		ID:               orderID,
		OrderID:          orderID,
		Source:           "pos",
		OrderTime:        now.Format(time.RFC3339),
		OrderTimeSort:    now.Unix(),
		DeliveryTimeUnix: now.Unix(),
		CustomerName:     req.CustomerName,
		CustomerPhone:    req.CustomerPhone,
		Dishes:           dishes,
		Total:            subtotal,
		TotalDiscount:    req.AppliedDiscount,
		TotalForBiz:      total,
		Note:             req.Note,
		Payments:         payments,
	}

	if req.CustomerAddress != nil {
		dataMapping.CustomerAddress = req.CustomerAddress.Text
	}

	// Create the order object
	order := models.Order{
		OrderID:      orderID,
		ShortOrderID: orderID,
		Source:       "pos",
		SiteID:       siteID,
		HubID:        fmt.Sprintf("%v", site.HubID),
		Status:       status,
		Shipment: models.JSONField[models.OrderShipment]{
			Data: shipment,
		},
		DataMapping: models.JSONField[models.DataMapping]{
			Data: dataMapping,
		},
		Data: models.JSONField[map[string]any]{
			Data: map[string]any{
				"id":                 orderID,
				"order_id":           orderID,
				"source":             "pos",
				"customer_name":      req.CustomerName,
				"customer_phone":     req.CustomerPhone,
				"customer_address":   req.CustomerAddress,
				"dishes":             dishes,
				"note":               req.Note,
				"total":              subtotal,
				"total_discount":     req.AppliedDiscount,
				"total_for_biz":      total,
				"order_time":         now.Format(time.RFC3339),
				"order_time_sort":    now.Unix(),
				"delivery_time":      now.Format(time.RFC3339),
				"delivery_time_unix": now.Unix(),
				"pick_time":          now.Format(time.RFC3339),
				"payments":           payments,
				"raw":                req,
				"shipment":           shipment,
			},
		},
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// Process vouchers if provided
	if len(req.VoucherCodes) > 0 {
		coupons := make([]models.OrderCoupon, len(req.VoucherCodes))
		for i, code := range req.VoucherCodes {
			coupons[i] = models.OrderCoupon{
				Code:  code,
				Name:  "POS Voucher",
				Total: 0, // We don't have voucher amount details here
			}
		}
		dataMapping.Coupons = coupons
		order.Data.Data["coupons"] = coupons
	}

	// Save the order to database
	if err := db.Create(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create order: " + err.Error(),
		})
		return
	}

	// For take-away orders that are already paid, send bill to printer
	if req.DeliveryType == "take_away" {
		totalPaidCompleted := total // In this simplified version, we assume all is paid

		// Send bills to printer and Zalo if applicable
		if totalPaidCompleted == total {
			// Generate kitchen bill and send to channel
			_, err := bill.GenerateOrderBills(db, orderID, bill.BillOptions{
				BillType:     "bill_for_kitchen",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
					BillTemplate: "bill_for_kitchen",
					Bill:         true,
					Zalo:         true, // Send kitchen bills to Zalo
				})
			}

			// Generate complete bill and send to channel
			_, err = bill.GenerateOrderBills(db, orderID, bill.BillOptions{
				BillType:     "bill_for_complete",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
					BillTemplate: "bill_for_complete",
					Bill:         true,
				})
			}

			// Mark order as finished for take-away orders
			order.Status = "FINISH"
			db.Save(&order)
		}
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"order_id":    orderID,
		"site_id":     siteIDInt,
		"subtotal":    subtotal,
		"discount":    req.AppliedDiscount,
		"total":       total,
		"order_items": req.OrderItems,
		"message":     "Order created successfully",
	})
}
