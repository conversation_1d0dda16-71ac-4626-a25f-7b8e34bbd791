// Package router provides HTTP routing for the order service
package router

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// generateRandomCode creates a random alphanumeric string of specified length
// This replaces nanoid from the NodeJS implementation
func generateRandomCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// createVoucher generates a new voucher based on the provided configuration
func createVoucher(db *gorm.DB, voucherConfig *models.VoucherConfig, brandID string) (*models.VoucherPromotion, error) {
	// Skip if no voucher config is provided
	if voucherConfig == nil {
		return nil, nil
	}

	// Generate a code if not provided
	code := voucherConfig.VoucherCode
	if code == "" && voucherConfig.Prefix != "" {
		// Generate a 6-character random code and append to prefix
		randomCode := generateRandomCode(6)
		code = fmt.Sprintf("%s%s", voucherConfig.Prefix, randomCode)
	}

	if code == "" {
		return nil, nil
	}

	// Check if code already exists
	var existingVoucher models.VoucherPromotion
	result := db.Where("code = ?", code).First(&existingVoucher)
	if result.RowsAffected > 0 {
		return nil, fmt.Errorf("voucher code already exists")
	}

	// Use a map to create the voucher to avoid field name conflicts
	// This is more reliable than direct struct assignment when the struct fields might change
	now := time.Now()
	voucherData := map[string]any{
		"id":         uuid.NewString(), // Using lowercase field names as they appear in the database
		"code":       code,
		"vendor":     "nexdor",
		"brand_id":   brandID,
		"created_at": now,
		"updated_at": now,
	}

	// Create the voucher using the map data
	if err := db.Table("vouchers").Create(voucherData).Error; err != nil {
		return nil, fmt.Errorf("failed to create voucher: %w", err)
	}

	// Fetch the created voucher to return
	var voucher models.VoucherPromotion
	if err := db.Where("code = ?", code).First(&voucher).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve created voucher: %w", err)
	}

	return &voucher, nil
}

// GetSaleConfig returns sale configurations accessible to the current user
func GetSaleConfig(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	// Get current user from context
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Invalid user session",
		})
		return
	}

	// Check for has_voucher query param
	hasVoucher := c.Query("has_voucher")

	// Build query based on user's accessible brands
	query := db.Model(&models.RetailerSaleConfig{}).Where("brand_id IN ?", user.Brands).Order("`order` ASC")

	// Apply voucher filter if specified
	if hasVoucher == "false" {
		query = query.Where("voucher_config IS NULL OR voucher_config->'$.voucher_code' IS NULL OR voucher_config->'$.voucher_code' = ''")
	}

	// Execute query
	var configs []models.RetailerSaleConfig
	if err := query.Find(&configs).Error; err != nil {
		println(err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve sale configurations. Please check data format.",
		})
		return
	}

	// Populate brand information for each config
	for i := range configs {
		var brand models.Brand
		db.Select("id, name").Where("id = ?", configs[i].BrandID).First(&brand)
		configs[i].Brand = &brand
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// GetSaleConfigByBrand returns sale configurations for a specific brand
func GetSaleConfigByBrand(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	brandID := c.Param("brand_id")

	// Get filter parameters
	status := c.Query("status")
	hasVoucher := c.Query("has_voucher")

	// Start building the query
	query := db.Model(&models.RetailerSaleConfig{}).Where("brand_id = ?", brandID).Order("\"order\" ASC")

	// Apply status filter
	now := time.Now()

	if status == "running" {
		query = query.Where(
			"(start_date <= ? AND end_date >= ?) OR (start_date IS NULL AND end_date IS NULL)",
			now, now,
		)
	} else if status == "upcoming" {
		query = query.Where("start_date > ?", now)
	} else if status == "complete" {
		query = query.Where("end_date < ?", now)
	}

	// Apply voucher filter
	if hasVoucher == "false" {
		query = query.Where("voucher_config IS NULL OR voucher_config->'$.voucher_code' IS NULL OR voucher_config->'$.voucher_code' = ''")
	}

	// Execute query
	var configs []models.RetailerSaleConfig
	if err := query.Find(&configs).Error; err != nil {
		println(err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve sale configurations",
		})
		return
	}

	// Return successful response with configs
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// BrandCreateConfig creates a new sale configuration for a brand
func BrandCreateConfig(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	brandID := c.Param("brand_id")

	// Parse request body
	var req struct {
		Config        *models.SaleConfig    `json:"config" binding:"omitempty"` // Removed the 'dive' validator
		Type          string                `json:"type" binding:"omitempty,max=50"`
		Name          string                `json:"name" binding:"omitempty,max=100"`
		Description   string                `json:"description" binding:"omitempty"`
		Active        bool                  `json:"active" binding:"omitempty"`
		StartDate     *time.Time            `json:"start_date" binding:"omitempty"`
		EndDate       *time.Time            `json:"end_date" binding:"omitempty,gtfield=StartDate"`
		Order         float64               `json:"order" binding:"omitempty,gte=0"`
		VoucherConfig *models.VoucherConfig `json:"voucher_config" binding:"omitempty"`
		SiteIDs       []string              `json:"site_ids" binding:"omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		// Return more detailed error information instead of a generic message
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Request validation failed: %v", err.Error()),
		})
		return
	}

	// Validate that all sites belong to the brand if site IDs are provided
	if len(req.SiteIDs) > 0 {
		var count int64
		if err := db.Model(&models.Site{}).Where("id IN ? AND brand_id = ?", req.SiteIDs, brandID).Count(&count).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to validate sites",
			})
			return
		}
		if int(count) != len(req.SiteIDs) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Some sites do not belong to this brand",
			})
			return
		}
	}

	// Check if voucher code already exists
	if req.VoucherConfig != nil && req.VoucherConfig.VoucherCode != "" {
		var existingVoucher models.VoucherPromotion
		result := db.Where("code = ?", req.VoucherConfig.VoucherCode).First(&existingVoucher)
		if result.RowsAffected > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "voucher_code_existed",
			})
			return
		}
	}

	// Set current time for timestamps
	now := time.Now()

	// Create config JSON field
	configJSON := models.JSONField[models.SaleConfig]{}
	if req.Config != nil {
		configBytes, _ := json.Marshal(req.Config)
		json.Unmarshal(configBytes, &configJSON)
	}

	// Create voucher config JSON field
	var voucherConfigJSON models.JSONField[models.VoucherConfig]
	if req.VoucherConfig != nil {
		voucherConfigBytes, _ := json.Marshal(req.VoucherConfig)
		json.Unmarshal(voucherConfigBytes, &voucherConfigJSON)
	}

	// Create the config record with manually generated ID and timestamps
	saleConfig := models.RetailerSaleConfig{
		ID:            uuid.NewString(),
		BrandID:       brandID,
		Type:          req.Type,
		Name:          req.Name,
		Description:   req.Description,
		Active:        req.Active,
		StartDate:     req.StartDate,
		EndDate:       req.EndDate,
		Order:         req.Order,
		Config:        configJSON,
		VoucherConfig: voucherConfigJSON,
		SiteIDs: models.JSONField[[]string]{
			Data: req.SiteIDs,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Set default order if not provided
	if saleConfig.Order == 0 {
		saleConfig.Order = float64(time.Now().UnixNano() / int64(time.Millisecond))
	}

	// Use a transaction for creating config and voucher together
	tx := db.Begin()

	if err := tx.Create(&saleConfig).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to create sale config: %v", err),
		})
		return
	}

	// Create voucher if needed
	voucher, err := createVoucher(db, req.VoucherConfig, brandID)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update voucher code in config if auto-generated
	if voucher != nil && req.VoucherConfig != nil && req.VoucherConfig.VoucherCode == "" {
		// Create a copy of the voucher config with the updated code
		updatedVoucherConfig := *req.VoucherConfig
		updatedVoucherConfig.VoucherCode = voucher.Code

		// Marshal to JSON bytes
		voucherConfigBytes, _ := json.Marshal(updatedVoucherConfig)

		// Use raw JSON update to avoid type issues
		if err := tx.Exec("UPDATE retailer_sale_configs SET voucher_config = ? WHERE id = ?",
			string(voucherConfigBytes), saleConfig.ID).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to update sale config with voucher code",
			})
			return
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to complete the transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    saleConfig,
	})
}

// BrandUpdateConfig updates an existing sale configuration
func BrandUpdateConfig(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	brandID := c.Param("brand_id")
	configID := c.Param("config_id")

	// Check if config exists
	var existingConfig models.RetailerSaleConfig
	if result := db.Where("id = ? AND brand_id = ?", configID, brandID).First(&existingConfig); result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "config_not_found",
		})
		return
	}

	// Parse request body
	var req struct {
		Config        *models.SaleConfig    `json:"config" binding:"omitempty"` // Removed the dive validator
		Type          string                `json:"type" binding:"omitempty,max=50"`
		Name          string                `json:"name" binding:"omitempty,max=100"`
		Description   string                `json:"description" binding:"omitempty"`
		Active        bool                  `json:"active" binding:"omitempty"`
		StartDate     *time.Time            `json:"start_date" binding:"omitempty"`
		EndDate       *time.Time            `json:"end_date" binding:"omitempty,gtfield=StartDate"`
		Order         float64               `json:"order" binding:"omitempty,gte=0"`
		VoucherConfig *models.VoucherConfig `json:"voucher_config" binding:"omitempty"`
		SiteIDs       []string              `json:"site_ids" binding:"omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		// Return more detailed error information instead of a generic message
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Request validation failed: %v", err.Error()),
		})
		return
	}

	// Validate that all sites belong to the brand if site IDs are provided
	if len(req.SiteIDs) > 0 {
		var count int64
		if err := db.Model(&models.Site{}).Where("id IN ? AND brand_id = ?", req.SiteIDs, brandID).Count(&count).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to validate sites",
			})
			return
		}
		if int(count) != len(req.SiteIDs) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Some sites do not belong to this brand",
			})
			return
		}
	}

	// Create updated sale config
	updates := map[string]any{
		"type":        req.Type,
		"name":        req.Name,
		"description": req.Description,
		"active":      req.Active,
		"start_date":  req.StartDate,
		"end_date":    req.EndDate,
		"order":       req.Order,
		"updated_at":  time.Now(),
	}

	// Update site IDs if provided
	if req.SiteIDs != nil {
		updates["site_ids"] = models.JSONField[[]string]{Data: req.SiteIDs}
	}

	// Update config if provided
	if req.Config != nil {
		configBytes, _ := json.Marshal(req.Config)
		updates["config"] = string(configBytes)
	}

	// Handle voucher config update
	if req.VoucherConfig != nil {
		// Preserve existing voucher code and prefix
		var existingVoucherConfig models.VoucherConfig
		existingVoucherConfigBytes, _ := json.Marshal(existingConfig.VoucherConfig)
		json.Unmarshal(existingVoucherConfigBytes, &existingVoucherConfig)

		if existingVoucherConfig.VoucherCode != "" || existingVoucherConfig.Prefix != "" {
			req.VoucherConfig.VoucherCode = existingVoucherConfig.VoucherCode
			req.VoucherConfig.Prefix = existingVoucherConfig.Prefix
		}

		voucherConfigBytes, _ := json.Marshal(req.VoucherConfig)
		updates["voucher_config"] = string(voucherConfigBytes)
	}

	// Update the config
	if err := db.Model(&models.RetailerSaleConfig{}).Where("id = ? AND brand_id = ?", configID, brandID).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to update sale config: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// BrandDeleteSaleConfig deletes a sale configuration
func BrandDeleteSaleConfig(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	brandID := c.Param("brand_id")
	configID := c.Param("config_id")

	if err := db.Where("id = ? AND brand_id = ?", configID, brandID).Delete(&models.RetailerSaleConfig{}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to delete sale config: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetVoucherUsage returns usage statistics for a voucher
func GetVoucherUsage(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Database connection failed",
		})
		return
	}

	configID := c.Param("config_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "0"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Find the sale config to get the voucher code
	var saleConfig models.RetailerSaleConfig
	if result := db.Select("voucher_config").Where("id = ?", configID).First(&saleConfig); result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "config_not_found",
		})
		return
	}

	// Extract and unmarshal the voucher config from JSONField
	var voucherConfigValue models.VoucherConfig
	voucherConfigBytes, _ := json.Marshal(saleConfig.VoucherConfig)
	json.Unmarshal(voucherConfigBytes, &voucherConfigValue)

	// Check if voucher code exists
	if voucherConfigValue.VoucherCode == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"voucher_code": nil,
				"orders":       []any{},
				"usage_count":  0,
				"quantity":     0,
			},
		})
		return
	}

	voucherCode := voucherConfigValue.VoucherCode

	// Query orders that used this voucher
	var orderSummaries []struct {
		OrderID      string  `json:"order_id"`
		OrderTime    string  `json:"order_time"`
		CustomerName string  `json:"customer_name"`
		Total        float64 `json:"total"`
		Status       string  `json:"status"`
	}

	// This query needs to be adapted to your specific database schema
	query := `
		SELECT 
			o.order_id, 
			o.data_mapping->>'$.order_time' as order_time, 
			o.data_mapping->>'$.customer_name' as customer_name',
			CAST(o.data_mapping->>'$.total' AS DECIMAL(10,2)) as total,
			o.status
		FROM orders o
		WHERE JSON_CONTAINS(o.data_mapping->'$.coupons', JSON_OBJECT('code', ?))
		ORDER BY o.data_mapping->>'$.order_time_sort' DESC
		LIMIT ? OFFSET ?
	`

	// Execute the query
	if err := db.Raw(query, voucherCode, limit, page*limit).Scan(&orderSummaries).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to query orders",
		})
		return
	}

	// Count total orders using this voucher (excluding cancelled orders)
	var count int64
	countQuery := `
		SELECT COUNT(*)
		FROM orders o
		WHERE JSON_CONTAINS(o.data_mapping->'$.coupons', JSON_OBJECT('code', ?))
		AND o.status != 'CANCEL'
	`
	if err := db.Raw(countQuery, voucherCode).Count(&count).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to count orders",
		})
		return
	}

	// Update usage count if it's different
	if voucherConfigValue.UsageCount != int(count) {
		// Create an updated voucher config with new usage count
		updatedVoucherConfig := voucherConfigValue
		updatedVoucherConfig.UsageCount = int(count)

		// Marshal to JSON bytes
		updatedVoucherConfigBytes, _ := json.Marshal(updatedVoucherConfig)

		// Use raw SQL update to avoid type issues
		if err := db.Exec("UPDATE retailer_sale_configs SET voucher_config = ? WHERE id = ?",
			string(updatedVoucherConfigBytes), configID).Error; err != nil {
			// Log the error but continue
			fmt.Printf("Failed to update voucher usage count: %v\n", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"voucher_code": voucherCode,
			"usage_count":  count,
			"orders":       orderSummaries,
			"quantity":     voucherConfigValue.Quantity,
		},
	})
}
