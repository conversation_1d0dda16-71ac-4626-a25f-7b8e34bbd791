package router

func partner() {}

// GetMembersList handles retrieving members list for a partner
// func GetMembersList(c *gin.Context) {
// 	user := c.Must<PERSON>et("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
// 	limit, _ := strconv.Atoi(c.<PERSON>("limit", "100"))
// 	search := c.Query("search")

// 	var members []models.User
// 	var total int64

// 	query := db.Model(&models.User{}).
// 		Where("he_info->>'referrer_id' = ?", user.ID).
// 		Where("he_info->>'status' = ?", "active")

// 	if search != "" {
// 		query = query.Where("name ILIKE ? OR username ILIKE ? OR email ILIKE ?",
// 			"%"+search+"%", "%"+search+"%", "%"+search+"%")
// 	}

// 	// Count total records
// 	query.Count(&total)

// 	// Paginate
// 	query = query.Offset((page - 1) * limit).Limit(limit).Order("created_at ASC")

// 	// Execute query
// 	if err := query.Find(&members).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Get current month and year for commissions
// 	currentTime := time.Now()
// 	currentMonth := int(currentTime.Month())
// 	currentYear := currentTime.Year()

// 	// Calculate commissions
// 	commissions, err := calculateCommissions(db, members, currentMonth, currentYear,
// 		user.Brands[0]) // Assuming first brand in array
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Transform data
// 	var data []gin.H
// 	for _, member := range members {
// 		memberID := member.ID
// 		commission := gin.H{
// 			"total_sale":  0,
// 			"commission":  0,
// 			"total_order": 0,
// 			"percentage":  0,
// 		}

// 		if comm, ok := commissions[memberID]; ok {
// 			commission = comm
// 		}

// 		data = append(data, gin.H{
// 			"user":                  member,
// 			"status":                member.HEInfo.Data.Status,
// 			"total_sale":            commission["total_sale"],
// 			"commission":            commission["commission"],
// 			"total_order":           commission["total_order"],
// 			"commission_percentage": commission["percentage"],
// 		})
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    data,
// 		"page":    page,
// 		"limit":   limit,
// 		"total":   total,
// 	})
// }

// // InviteMember handles inviting a new member
// func InviteMember(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	var request struct {
// 		Type  string `json:"type"`
// 		Email string `json:"email" binding:"required"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invalid_request_data",
// 		})
// 		return
// 	}

// 	// Check if user with this email already exists
// 	var existingUser models.User
// 	if err := db.Where("email = ?", request.Email).First(&existingUser).Error; err == nil {
// 		c.JSON(http.StatusConflict, gin.H{
// 			"success": false,
// 			"error":   "member_is_existed",
// 		})
// 		return
// 	}

// 	// Create pending member
// 	pendingMember := models.User{
// 		Username: request.Email,
// 		Email:    request.Email,
// 		Password: "",
// 		Name:     request.Email,
// 		IsActive: false,
// 		Status:   models.UserStatusPending,
// 		HEInfo: models.JSONField[models.HeAccountInfo]{
// 			Data: models.HeAccountInfo{
// 				ReferrerID: user.ID,
// 				Status:     "pending",
// 			},
// 		},
// 	}

// 	if err := db.Create(&pendingMember).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_create_member",
// 		})
// 		return
// 	}

// 	// Send invitation email
// 	// webURL := utils.GetEnv("WEB_URL", "http://localhost:3000")
// 	// invitationLink := fmt.Sprintf("%s/invitation?id=%d&email=%s&type=%s",
// 	// 	webURL, pendingMember.ID, request.Email, request.Type)

// 	// err := utils.SendEmail(models.EmailTemplate{},
// 	// 	models.EmailRecipient{Email: request.Email, Name: request.Email},
// 	// 	"member_invitation",
// 	// 	map[string]any{
// 	// 		"name":    request.Email,
// 	// 		"link":    invitationLink,
// 	// 		"inviter": user.Name,
// 	// 	})

// 	// if err != nil {
// 	// 	// Log error but continue
// 	// 	fmt.Printf("Failed to send invitation email: %v\n", err)
// 	// }

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    pendingMember,
// 	})
// }

// // CheckInvitation validates an invitation
// func CheckInvitation(c *gin.Context) {
// 	db := c.MustGet("db").(*gorm.DB)
// 	invitationID := c.Query("invitation_id")

// 	if invitationID == "" {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invalid_invitation",
// 		})
// 		return
// 	}

// 	var pendingPartner models.User
// 	id, _ := strconv.ParseInt(invitationID, 10, 64)
// 	if err := db.First(&pendingPartner, id).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invalid_invitation",
// 		})
// 		return
// 	}

// 	if pendingPartner.HEInfo.Data.Status != "pending" {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invitation_already_accepted",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

// // AcceptInvitation handles accepting a partner invitation
// func AcceptInvitation(c *gin.Context) {
// 	db := c.MustGet("db").(*gorm.DB)

// 	var request struct {
// 		Password     string `json:"password" binding:"required"`
// 		Name         string `json:"name" binding:"required"`
// 		InvitationID string `json:"invitation_id" binding:"required"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invalid_request_data",
// 		})
// 		return
// 	}

// 	invitationID, _ := strconv.ParseInt(request.InvitationID, 10, 64)
// 	var partner models.User
// 	if err := db.First(&partner, invitationID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "invitation_not_found",
// 		})
// 		return
// 	}

// 	if partner.HEInfo.Data.Status != "pending" {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invitation_already_accepted",
// 		})
// 		return
// 	}

// 	// Hash password
// 	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.Password), bcrypt.DefaultCost)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "password_hashing_failed",
// 		})
// 		return
// 	}

// 	// Find parent
// 	var parent models.User
// 	referrerID, _ := strconv.ParseInt(partner.HEInfo.Data.ReferrerID, 10, 64)
// 	if err := db.First(&parent, referrerID).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "parent_not_found",
// 		})
// 		return
// 	}

// 	// Update partner
// 	now := time.Now()
// 	partner.Password = string(hashedPassword)
// 	partner.Name = request.Name
// 		partner.Status = models.UserStatusActive
// 	partner.HEInfo.Data.Status = string(models.UserStatusActive)
// 	partner.HEInfo.Data.ApprovedAt = now

// 	if err := db.Save(&partner).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_update_partner",
// 		})
// 		return
// 	}

// 	// Clone parent sites
// 	clonedSiteIDs := []string{}
// 	for _, siteID := range parent.Sites {
// 		// Find parent site
// 		var parentSite models.Site
// 		if err := db.Model(&models.Site{}).
// 			Where("id = ? AND type = ? AND apply_commission = ?", siteID, "partner", true).
// 			First(&parentSite).Error; err != nil {
// 			continue
// 		}

// 		// Generate unique code
// 		generator, _ := shortid.New(1, shortid.DefaultABC, 2342)
// 		generatedCode, _ := generator.Generate()

// 		// Clone site
// 		clonedSite := parentSite
// 		clonedSite.ID = ""
// 		clonedSite.Name = fmt.Sprintf("%s - %s", parentSite.Name, request.Name)
// 		clonedSite.Code = fmt.Sprintf("%s-%s", parentSite.Code, generatedCode)
// 		// Initialize empty tokens array instead of copying tokens
// 		clonedSite.Tokens = []models.SiteToken{}
// 		// clonedSite.HEID = fmt.Sprintf("%d", partner.ID)

// 		// Example of how to use GetToken if needed
// 		// If parentSite has a specific token we want to check
// 		// if grabToken := parentSite.GetToken("grab"); grabToken != nil {
// 		//     // Do something with the grab token
// 		//     fmt.Printf("Found grab token: %s\n", grabToken.TokenCode)
// 		// }

// 		if err := db.Create(&clonedSite).Error; err != nil {
// 			continue
// 		}

// 		// Clone parent site menu
// 		var parentSiteMenu models.SiteMenuGroup
// 		if err := db.Where("site_id = ?", parentSite.ID).First(&parentSiteMenu).Error; err == nil {
// 			clonedSiteMenu := parentSiteMenu
// 			clonedSiteMenu.ID = ""
// 			clonedSiteMenu.SiteID = clonedSite.ID

// 			db.Create(&clonedSiteMenu)
// 		}

// 		clonedSiteIDs = append(clonedSiteIDs, clonedSite.ID)
// 	}

// 	// Update partner sites, hubs, brands
// 	partner.Sites = clonedSiteIDs
// 	partner.Hubs = parent.Hubs
// 	partner.Brands = parent.Brands

// 	if err := db.Save(&partner).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_update_partner_sites",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    partner,
// 	})
// }

// // CreateCustomer handles creating a customer by a partner
// func CreateCustomer(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	var request struct {
// 		Phone    string         `json:"phone" binding:"required"`
// 		Password string         `json:"password"`
// 		Name     string         `json:"name" binding:"required"`
// 		Address  map[string]any `json:"address" binding:"required"`
// 		Email    string         `json:"email"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "missing_required_fields",
// 		})
// 		return
// 	}

// 	// Check if user exists by phone
// 	var existingUser models.User
// 	if err := db.Where("phone = ?", request.Phone).First(&existingUser).Error; err == nil {
// 		c.JSON(http.StatusConflict, gin.H{
// 			"success": false,
// 			"error":   "phone_is_existed",
// 		})
// 		return
// 	}

// 	// Set email if not provided
// 	email := request.Email
// 	if email == "" {
// 		email = request.Phone + "@nexdor.tech"
// 	}

// 	// Hash password
// 	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.Password), bcrypt.DefaultCost)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "password_hashing_failed",
// 		})
// 		return
// 	}

// 	// Find Individual role
// 	var userRole models.Role
// 	if err := db.Where("name = ?", "Individual").First(&userRole).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "role_not_found",
// 		})
// 		return
// 	}

// 	// Create user
// 	createdUser := models.User{
// 		Username:  request.Phone,
// 		Email:     email,
// 		Phone:     request.Phone,
// 		Password:  string(hashedPassword),
// 		Name:      request.Name,
// 		IsActive:  true,
// 		CreatedBy: &user.ID,
// 	}

// 	if err := db.Create(&createdUser).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_create_user",
// 		})
// 		return
// 	}

// 	// Create address
// 	addressObj := models.AddressObj{}
// 	utils.MapToStruct(request.Address, &addressObj)

// 	createdAddress := models.UserAddress{
// 		UserID:     createdUser.ID,
// 		Address:    addressObj.FormattedAddress,
// 		AddressObj: models.JSONField[models.AddressObj]{Data: addressObj},
// 		Phone:      request.Phone,
// 		Name:       request.Name,
// 		IsDefault:  true,
// 	}

// 	if err := db.Create(&createdAddress).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_create_address",
// 		})
// 		return
// 	}

// 	// Prepare response
// 	createdUser.Password = ""
// 	response := gin.H{
// 		"success": true,
// 		"data": gin.H{
// 			"id":              createdUser.ID,
// 			"username":        createdUser.Username,
// 			"email":           createdUser.Email,
// 			"phone":           createdUser.Phone,
// 			"name":            createdUser.Name,
// 			"is_active":       createdUser.IsActive,
// 			"created_by":      createdUser.CreatedBy,
// 			"default_address": createdAddress,
// 		},
// 	}

// 	c.JSON(http.StatusOK, response)
// }

// // UpdateCustomer handles updating a customer's details
// func UpdateCustomer(c *gin.Context) {
// 	db := c.MustGet("db").(*gorm.DB)
// 	customerID := c.Param("customer_id")

// 	var request struct {
// 		Name string `json:"name"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "invalid_request_data",
// 		})
// 		return
// 	}

// 	id, _ := strconv.ParseInt(customerID, 10, 64)
// 	result := db.Model(&models.User{}).Where("id = ?", id).Update("name", request.Name)
// 	if result.Error != nil || result.RowsAffected == 0 {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "customer_not_found",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    true,
// 	})
// }

// // GetCustomer retrieves a customer by ID
// func GetCustomer(c *gin.Context) {
// 	db := c.MustGet("db").(*gorm.DB)
// 	customerID := c.Param("customer_id")

// 	var customer models.User
// 	id, _ := strconv.ParseInt(customerID, 10, 64)
// 	if err := db.First(&customer, id).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "customer_not_found",
// 		})
// 		return
// 	}

// 	// Clear sensitive data
// 	customer.Password = ""
// 	customer.LastLoginDevice = ""
// 	customer.LastLoginDevices = nil

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    customer,
// 	})
// }

// // GetCustomers retrieves all customers for a partner
// func GetCustomers(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	search := c.Query("search")
// 	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
// 	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))

// 	// Get orders for this partner
// 	var orders []models.Order
// 	if err := db.Where("he_id = ?", user.ID).Find(&orders).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_orders",
// 		})
// 		return
// 	}

// 	// Get unique user IDs from orders
// 	userIDs := make(map[string]bool)
// 	for _, order := range orders {
// 		userIDs[order.UserID] = true
// 	}

// 	// Convert map to slice
// 	userIDList := []string{}
// 	for id := range userIDs {
// 		userIDList = append(userIDList, id)
// 	}

// 	// Build query
// 	query := db.Model(&models.User{})

// 	// Apply filters
// 	filterConditions := []string{}
// 	filterValues := []any{}

// 	// Add user IDs from orders
// 	if len(userIDList) > 0 {
// 		idPlaceholders := strings.Repeat("?,", len(userIDList))
// 		idPlaceholders = idPlaceholders[:len(idPlaceholders)-1] // Remove trailing comma
// 		filterConditions = append(filterConditions, fmt.Sprintf("id IN (%s)", idPlaceholders))
// 		for _, id := range userIDList {
// 			filterValues = append(filterValues, id)
// 		}
// 	}

// 	// Add created_by filter
// 	filterConditions = append(filterConditions, "created_by = ?")
// 	filterValues = append(filterValues, user.ID)

// 	// Add HE IDs filter
// 	filterConditions = append(filterConditions, "? = ANY(he_ids)")
// 	filterValues = append(filterValues, user.ID)

// 	// Apply search
// 	if search != "" {
// 		filterConditions = append(filterConditions, "(username ILIKE ? OR name ILIKE ?)")
// 		filterValues = append(filterValues, "%"+search+"%", "%"+search+"%")
// 	}

// 	// Combine conditions
// 	query = query.Where("("+strings.Join(filterConditions, " OR ")+") AND is_guest = ?", append(filterValues, false)...)

// 	// Get total count
// 	var total int64
// 	query.Count(&total)

// 	// Apply pagination
// 	query = query.Offset((page - 1) * limit).Limit(limit).Order("updated_at DESC")

// 	// Execute query
// 	var users []models.User
// 	if err := query.Find(&users).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Get addresses for users
// 	var addresses []models.UserAddress
// 	userIDsForAddresses := []string{}
// 	for _, user := range users {
// 		userIDsForAddresses = append(userIDsForAddresses, user.ID)
// 	}

// 	db.Where("user_id IN ?", userIDsForAddresses).Find(&addresses)

// 	// Create map of user ID to default address
// 	addressMap := make(map[string]models.UserAddress)
// 	for _, userID := range userIDsForAddresses {
// 		// Find addresses for this user
// 		userAddresses := []models.UserAddress{}
// 		for _, addr := range addresses {
// 			if addr.UserID == userID {
// 				userAddresses = append(userAddresses, addr)
// 			}
// 		}

// 		// Sort by IsDefault (desc) and UpdatedAt (desc)
// 		if len(userAddresses) > 0 {
// 			// Find default address (simple approach)
// 			var defaultAddr models.UserAddress
// 			for _, addr := range userAddresses {
// 				if addr.IsDefault {
// 					defaultAddr = addr
// 					break
// 				}
// 			}

// 			// If no default address, use the first one
// 			if defaultAddr.ID == "" && len(userAddresses) > 0 {
// 				defaultAddr = userAddresses[0]
// 			}

// 			addressMap[userID] = defaultAddr
// 		}
// 	}

// 	// Prepare response data
// 	responseData := []gin.H{}
// 	for _, user := range users {
// 		userID := user.ID

// 		// Get orders for this user
// 		userOrders := []models.Order{}
// 		for _, order := range orders {
// 			if order.UserID == userID {
// 				userOrders = append(userOrders, order)
// 			}
// 		}

// 		// Create response object
// 		userData := gin.H{
// 			"id":              user.ID,
// 			"username":        user.Username,
// 			"email":           user.Email,
// 			"phone":           user.Phone,
// 			"name":            user.Name,
// 			"default_address": addressMap[userID],
// 			"orders":          userOrders,
// 		}

// 		responseData = append(responseData, userData)
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    responseData,
// 		"page":    page,
// 		"limit":   limit,
// 		"total":   total,
// 	})
// }

// // GetCustomerOrders retrieves orders for a specific customer
// func GetCustomerOrders(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)
// 	customerID := c.Param("customer_id")

// 	if customerID == "" {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "missing_required_fields",
// 		})
// 		return
// 	}

// 	var orders []models.Order
// 	if err := db.Where("he_id = ? AND user_id = ?", user.ID, customerID).Find(&orders).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_orders",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    orders,
// 	})
// }

// // GetBanners retrieves banners for brands
// func GetPartnerBanners(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)
// 	brandIDs := c.QueryArray("brand_ids")

// 	userBrands := user.Brands

// 	// Validate brand IDs if provided
// 	if len(brandIDs) > 0 {
// 		validBrands := true
// 		for _, brandID := range brandIDs {
// 			found := false

// 			for _, userBrandID := range userBrands {
// 				if userBrandID == brandID {
// 					found = true
// 					break
// 				}
// 			}

// 			if !found {
// 				validBrands = false
// 				break
// 			}
// 		}

// 		if !validBrands {
// 			c.JSON(http.StatusNotFound, gin.H{
// 				"success": false,
// 				"error":   "brand_not_found",
// 			})
// 			return
// 		}
// 	}

// 	// Apply brand filter
// 	var brands []models.Brand
// 	query := db.Model(&models.Brand{})

// 	if len(brandIDs) > 0 {
// 		// Convert string IDs to int64
// 		var brandIDInts []int64
// 		for _, id := range brandIDs {
// 			idInt, _ := strconv.ParseInt(id, 10, 64)
// 			brandIDInts = append(brandIDInts, idInt)
// 		}
// 		query = query.Where("id IN ?", brandIDInts)
// 	} else {
// 		query = query.Where("id IN ?", userBrands)
// 	}

// 	// Select only needed fields
// 	query = query.Select("id, name, banners")

// 	if err := query.Find(&brands).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_brands",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    brands,
// 	})
// }

// // GetSavedBankAccounts retrieves saved bank accounts for a partner
// func GetSavedBankAccounts(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    user.HEInfo.Data.SavedAccounts,
// 	})
// }

// // DeactivateSelf handles a partner deactivating their own account
// func DeactivateSelf(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	// Verify user exists
// 	var partner models.User
// 	if err := db.First(&partner, user.ID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "partner_not_found",
// 		})
// 		return
// 	}

// 	// Update user to inactive
// 	if err := db.Model(&models.User{}).Where("id = ?", user.ID).Update("is_active", false).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_deactivate",
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

// // GetPartnerCommissions retrieves the commission details for a partner
// func GetPartnerCommissions(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	// Get query parameters
// 	month, _ := strconv.Atoi(c.DefaultQuery("month", fmt.Sprintf("%d", time.Now().Month())))
// 	year, _ := strconv.Atoi(c.DefaultQuery("year", fmt.Sprintf("%d", time.Now().Year())))
// 	brandID, _ := strconv.ParseInt(c.DefaultQuery("brand_id", user.Brands[0]), 10, 64)

// 	// Get commission summary from database
// 	var commissionSummary models.CommissionSummary
// 	err := db.Where("user_id = ? AND brand_id = ? AND month = ? AND year = ?",
// 		user.ID, brandID, month, year).First(&commissionSummary).Error

// 	if err != nil {
// 		// If not found, return empty data
// 		if err == gorm.ErrRecordNotFound {
// 			c.JSON(http.StatusOK, gin.H{
// 				"success": true,
// 				"data": gin.H{
// 					"balance": 0,
// 					"details": []any{},
// 				},
// 				"month": month,
// 				"year":  year,
// 			})
// 			return
// 		}

// 		// For other errors
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_commissions",
// 		})
// 		return
// 	}

// 	// Return commission data
// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data": gin.H{
// 			"balance": commissionSummary.Balance,
// 			"details": commissionSummary.Details,
// 		},
// 		"month": month,
// 		"year":  year,
// 	})
// }

// // GetPartnerReport retrieves the sales report for a partner
// func GetPartnerReport(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	// Get query parameters
// 	month, _ := strconv.Atoi(c.DefaultQuery("month", fmt.Sprintf("%d", time.Now().Month())))
// 	year, _ := strconv.Atoi(c.DefaultQuery("year", fmt.Sprintf("%d", time.Now().Year())))
// 	brandID := c.DefaultQuery("brand_id", "0")

// 	// Validate brand access
// 	if brandID != "" {
// 		hasAccess := false
// 		for _, userBrandID := range user.Brands {
// 			if userBrandID == brandID {
// 				hasAccess = true
// 				break
// 			}
// 		}

// 		if !hasAccess {
// 			c.JSON(http.StatusForbidden, gin.H{
// 				"success": false,
// 				"error":   "invalid_brand_access",
// 			})
// 			return
// 		}
// 	} else if len(user.Brands) > 0 {
// 		// Use first brand if not specified
// 		brandID = user.Brands[0]
// 	}

// 	// Get start and end dates for the month
// 	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
// 	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

// 	// Prepare response data structure
// 	report := gin.H{
// 		"total_sales":      0,
// 		"total_orders":     0,
// 		"total_customers":  0,
// 		"average_order":    0,
// 		"sales_by_day":     []gin.H{},
// 		"top_customers":    []gin.H{},
// 		"sales_by_source":  []gin.H{},
// 		"orders_by_source": []gin.H{},
// 	}

// 	// Get orders for this partner within the date range
// 	var orders []models.Order
// 	err := db.Where("he_id = ? AND created_at BETWEEN ? AND ?",
// 		user.ID, startDate, endDate).Find(&orders).Error

// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_orders",
// 		})
// 		return
// 	}

// 	// Process orders to generate report
// 	if len(orders) > 0 {
// 		// Calculate total sales and orders
// 		totalSales := 0.0
// 		customerSet := make(map[string]bool)
// 		salesByDay := make(map[int]float64)
// 		salesBySource := make(map[string]float64)
// 		ordersBySource := make(map[string]int)
// 		customerSales := make(map[string]float64)

// 		for _, order := range orders {
// 			// Skip cancelled orders
// 			if order.Status == "CANCEL" || order.Status == "RETURNED" || order.Status == "RETURNING" {
// 				continue
// 			}

// 			// Get order value
// 			var orderValue float64

// 			orderValue = order.DataMapping.Data.Total

// 			totalSales += orderValue
// 			customerSet[order.UserID] = true

// 			// Track sales by day
// 			orderDay := time.Unix(order.DataMapping.Data.OrderTimeSort, 0).Day()
// 			salesByDay[orderDay] += orderValue

// 			// Track sales by source
// 			source := order.Source
// 			if source == "" {
// 				source = "unknown"
// 			}
// 			salesBySource[source] += orderValue
// 			ordersBySource[source]++

// 			// Track customer sales
// 			customerSales[order.UserID] += orderValue
// 		}

// 		// Calculate average order value
// 		averageOrder := 0.0
// 		if len(orders) > 0 {
// 			averageOrder = totalSales / float64(len(orders))
// 		}

// 		// Prepare sales by day data
// 		salesByDaySlice := []gin.H{}
// 		for day := 1; day <= endDate.Day(); day++ {
// 			salesByDaySlice = append(salesByDaySlice, gin.H{
// 				"day":   day,
// 				"sales": salesByDay[day],
// 			})
// 		}

// 		// Prepare sales by source data
// 		salesBySourceSlice := []gin.H{}
// 		for source, sales := range salesBySource {
// 			salesBySourceSlice = append(salesBySourceSlice, gin.H{
// 				"source": source,
// 				"sales":  sales,
// 			})
// 		}

// 		// Prepare orders by source data
// 		ordersBySourceSlice := []gin.H{}
// 		for source, count := range ordersBySource {
// 			ordersBySourceSlice = append(ordersBySourceSlice, gin.H{
// 				"source": source,
// 				"count":  count,
// 			})
// 		}

// 		// Get top customers
// 		type customerInfo struct {
// 			ID    string
// 			Sales float64
// 		}

// 		var topCustomers []customerInfo
// 		for customerID, sales := range customerSales {
// 			topCustomers = append(topCustomers, customerInfo{
// 				ID:    customerID,
// 				Sales: sales,
// 			})
// 		}

// 		// Sort customers by sales (descending)
// 		sort.Slice(topCustomers, func(i, j int) bool {
// 			return topCustomers[i].Sales > topCustomers[j].Sales
// 		})

// 		// Get customer details for top 5
// 		topCustomersSlice := []gin.H{}
// 		if len(topCustomers) > 0 {
// 			// Get top 5 or less
// 			limit := 5
// 			if len(topCustomers) < limit {
// 				limit = len(topCustomers)
// 			}

// 			// Extract customer IDs
// 			customerIDs := []string{}
// 			for i := 0; i < limit; i++ {
// 				customerIDs = append(customerIDs, topCustomers[i].ID)
// 			}

// 			// Get customer details
// 			var customers []models.User
// 			if err := db.Where("id IN ?", customerIDs).Find(&customers).Error; err == nil {
// 				// Create a map of customer ID to customer
// 				customerMap := make(map[string]models.User)
// 				for _, customer := range customers {
// 					customerMap[customer.ID] = customer
// 				}

// 				// Create top customers data
// 				for i := 0; i < limit; i++ {
// 					customerID := topCustomers[i].ID
// 					if customer, ok := customerMap[customerID]; ok {
// 						topCustomersSlice = append(topCustomersSlice, gin.H{
// 							"id":    customerID,
// 							"name":  customer.Name,
// 							"sales": topCustomers[i].Sales,
// 						})
// 					} else {
// 						// Customer not found, use ID only
// 						topCustomersSlice = append(topCustomersSlice, gin.H{
// 							"id":    customerID,
// 							"name":  "Unknown Customer",
// 							"sales": topCustomers[i].Sales,
// 						})
// 					}
// 				}
// 			}
// 		}

// 		// Update report with calculated values
// 		report["total_sales"] = totalSales
// 		report["total_orders"] = len(orders)
// 		report["total_customers"] = len(customerSet)
// 		report["average_order"] = averageOrder
// 		report["sales_by_day"] = salesByDaySlice
// 		report["top_customers"] = topCustomersSlice
// 		report["sales_by_source"] = salesBySourceSlice
// 		report["orders_by_source"] = ordersBySourceSlice
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    report,
// 		"month":   month,
// 		"year":    year,
// 	})
// }

// // calculateCommissions computes commission values for a list of members
// func calculateCommissions(db *gorm.DB, members []models.User, month, year int, brandID string) (map[string]gin.H, error) {
// 	result := make(map[string]gin.H)

// 	// Get the start and end dates for the given month and year
// 	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
// 	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

// 	// Get brand commission configuration
// 	var brandCommission models.BrandCommission
// 	if err := db.Where("brand_id = ? AND type = ? AND status = ?",
// 		brandID, "personal", "active").First(&brandCommission).Error; err != nil {
// 		// If no commission configuration exists, return empty result
// 		if err == gorm.ErrRecordNotFound {
// 			return result, nil
// 		}
// 		return nil, err
// 	}

// 	// Process each member
// 	for _, member := range members {
// 		memberIDStr := member.ID

// 		// Get orders for this member within the date range
// 		var orders []models.Order
// 		if err := db.Where("he_id = ? AND created_at BETWEEN ? AND ? AND status NOT IN (?, ?, ?)",
// 			memberIDStr, startDate, endDate, "CANCEL", "RETURNED", "RETURNING").Find(&orders).Error; err != nil {
// 			return nil, err
// 		}

// 		// Calculate total sales and commission
// 		totalSale := 0.0
// 		for _, order := range orders {
// 			totalSale += order.DataMapping.Data.Total
// 		}

// 		// Apply progressive commission tiers from the brand commission config
// 		commissionAmount := 0.0
// 		commissionPercentage := 0.0

// 		if len(brandCommission.Personal.Data) > 0 {
// 			// Find the applicable tier based on total sales
// 			for _, tier := range brandCommission.Personal.Data {
// 				if totalSale >= float64(tier.MinSale) {
// 					commissionPercentage = float64(tier.Bonus)
// 					commissionAmount = totalSale * (commissionPercentage / 100.0)
// 				}
// 			}
// 		}

// 		// Add result for this member
// 		result[memberIDStr] = gin.H{
// 			"total_sale":  totalSale,
// 			"commission":  commissionAmount,
// 			"total_order": len(orders),
// 			"percentage":  commissionPercentage,
// 		}
// 	}

// 	return result, nil
// }

// // GetTopSaleItems retrieves the top selling items for a partner
// func GetTopSaleItems(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	// Get query parameters
// 	month, _ := strconv.Atoi(c.DefaultQuery("month", fmt.Sprintf("%d", time.Now().Month())))
// 	year, _ := strconv.Atoi(c.DefaultQuery("year", fmt.Sprintf("%d", time.Now().Year())))
// 	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

// 	// Calculate date range for the query
// 	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
// 	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

// 	type TopItem struct {
// 		Name      string  `json:"name"`
// 		ItemID    string  `json:"item_id"`
// 		Quantity  int     `json:"quantity"`
// 		TotalSale float64 `json:"total_sale"`
// 	}

// 	// Get all orders for this partner within the date range
// 	var orders []models.Order
// 	if err := db.Where("he_id = ? AND created_at BETWEEN ? AND ? AND status NOT IN (?)",
// 		user.ID, startDate, endDate, []string{"CANCEL", "RETURNED", "RETURNING"}).
// 		Find(&orders).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_orders",
// 		})
// 		return
// 	}

// 	// Process orders to extract items and calculate totals
// 	topItemsMap := make(map[string]*TopItem)

// 	for _, order := range orders {
// 		// Access dishes properly based on Order structure
// 		for _, dishData := range order.DataMapping.Data.Dishes {
// 			// Each dish is an OrderDish type
// 			name := dishData.Name
// 			itemID := ""

// 			// Use name as ID if no specific itemID is provided
// 			if itemID == "" {
// 				itemID = name
// 			}

// 			quantity := int(dishData.Quantity)
// 			price := dishData.DiscountPrice
// 			if price == 0 {
// 				// Fall back to original price if discount price is not set
// 				price = dishData.Price
// 			}

// 			// Add to map or update existing
// 			key := itemID
// 			if item, exists := topItemsMap[key]; exists {
// 				item.Quantity += quantity
// 				item.TotalSale += price
// 			} else {
// 				topItemsMap[key] = &TopItem{
// 					Name:      name,
// 					ItemID:    itemID,
// 					Quantity:  quantity,
// 					TotalSale: price,
// 				}
// 			}
// 		}
// 	}

// 	// Convert map to slice for sorting
// 	var topItems []*TopItem
// 	for _, item := range topItemsMap {
// 		topItems = append(topItems, item)
// 	}

// 	// Sort by total sale (descending)
// 	sort.Slice(topItems, func(i, j int) bool {
// 		return topItems[i].TotalSale > topItems[j].TotalSale
// 	})

// 	// Apply limit
// 	if len(topItems) > limit {
// 		topItems = topItems[:limit]
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    topItems,
// 		"month":   month,
// 		"year":    year,
// 	})
// }

// // GetPartnerWithdraws retrieves withdrawal records for a partner
// func GetPartnerWithdraws(c *gin.Context) {
// 	user := c.MustGet("user").(*models.User)
// 	db := c.MustGet("db").(*gorm.DB)

// 	// Get pagination parameters
// 	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
// 	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

// 	// Define PartnerWithdraw structure (adjust based on your actual model)
// 	type PartnerWithdraw struct {
// 		ID          int64   `json:"id"`
// 		UserID      string  `json:"user_id"`
// 		Amount      float64 `json:"amount"`
// 		BrandID     string  `json:"brand_id"`
// 		Status      string  `json:"status"`
// 		Note        string  `json:"note"`
// 		BankAccount struct {
// 			BankName      string `json:"bank_name"`
// 			AccountNumber string `json:"account_number"`
// 			AccountName   string `json:"account_name"`
// 		} `json:"bank_account"`
// 		Attachments     []string  `json:"attachments"`
// 		CancelledReason string    `json:"cancelled_reason"`
// 		CreatedAt       time.Time `json:"created_at"`
// 		UpdatedAt       time.Time `json:"updated_at"`
// 	}

// 	var withdraws []PartnerWithdraw
// 	var total int64

// 	// Count total records
// 	if err := db.Model(&PartnerWithdraw{}).Where("user_id = ?", user.ID).Count(&total).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_count_withdraws",
// 		})
// 		return
// 	}

// 	// Get paginated records
// 	if err := db.Where("user_id = ?", user.ID).
// 		Order("created_at DESC").
// 		Offset((page - 1) * limit).
// 		Limit(limit).
// 		Find(&withdraws).Error; err != nil {

// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "failed_to_get_withdraws",
// 		})
// 		return
// 	}

// 	// Get brand info for each withdraw
// 	var brands []models.Brand
// 	brandIDs := make([]string, 0)
// 	for _, withdraw := range withdraws {
// 		brandIDs = append(brandIDs, withdraw.BrandID)
// 	}

// 	if len(brandIDs) > 0 {
// 		db.Where("id IN ?", brandIDs).Find(&brands)
// 	}

// 	// Create a map of brand ID to brand name
// 	brandMap := make(map[string]string)
// 	for _, brand := range brands {
// 		brandMap[brand.ID] = brand.Name
// 	}

// 	// Add brand names to response
// 	response := []gin.H{}
// 	for _, withdraw := range withdraws {
// 		brandName, ok := brandMap[withdraw.BrandID]
// 		if !ok {
// 			brandName = "Unknown Brand"
// 		}

// 		response = append(response, gin.H{
// 			"id":               withdraw.ID,
// 			"amount":           withdraw.Amount,
// 			"status":           withdraw.Status,
// 			"note":             withdraw.Note,
// 			"bank_account":     withdraw.BankAccount,
// 			"attachments":      withdraw.Attachments,
// 			"cancelled_reason": withdraw.CancelledReason,
// 			"created_at":       withdraw.CreatedAt,
// 			"updated_at":       withdraw.UpdatedAt,
// 			"brand": gin.H{
// 				"id":   withdraw.BrandID,
// 				"name": brandName,
// 			},
// 		})
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    response,
// 		"page":    page,
// 		"limit":   limit,
// 		"total":   total,
// 	})
// }
