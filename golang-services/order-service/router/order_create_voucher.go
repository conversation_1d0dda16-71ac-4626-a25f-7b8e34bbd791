package router

import (
	"math"
	"net/http"
	"sort"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PosVoucherRequest defines the request structure for getting available vouchers
type PosVoucherRequest struct {
	SiteID        string `json:"site_id"`
	CustomerPhone string `json:"customer_phone"`
	OrderItems    []struct {
		Code     string  `json:"code"`
		Quantity int     `json:"quantity"`
		Price    float64 `json:"price"`
	} `json:"order_items"`
}

// PosApplyVoucherRequest defines the request structure for applying vouchers
type PosApplyVoucherRequest struct {
	SiteID        string   `json:"site_id"`
	CustomerPhone string   `json:"customer_phone"`
	VoucherCodes  []string `json:"voucher_codes"`
	OrderItems    []struct {
		Code     string  `json:"code"`
		Quantity int     `json:"quantity"`
		Price    float64 `json:"price"`
	} `json:"order_items"`
}

// VoucherResponse represents a voucher that can be applied to an order in the POS system
type VoucherResponse struct {
	Code         string     `json:"code"`
	Discount     float64    `json:"discount"`
	DiscountType string     `json:"discount_type"` // percentage, fixed, or shipping
	Description  string     `json:"description"`
	EndDate      *time.Time `json:"end_date,omitempty"`
	ApplyWith    bool       `json:"apply_with_other"` // Whether this voucher can be applied with others
}

// ApplyGiftParams defines the parameters needed for the applyGifts function
// This struct improves organization of parameters and makes function signature cleaner
type ApplyGiftParams struct {
	Site             models.Site
	MenuItems        []models.MenuItem
	CartDishes       []models.CartDish
	OptionCategories []models.OptionCategory
	SaleConfigs      []models.RetailerSaleConfig
	IgnoreSiteCheck  bool
}

// GiftsResult holds the processing results for voucher application
// This helps organize the processing results more effectively
type GiftsResult struct {
	Gifts         []GiftItem
	Discounts     []DiscountItem
	ShipDiscounts []DiscountItem
}

// GetAvailableVouchers handles the request to get available vouchers for a POS order
func GetAvailableVouchers(c *gin.Context) {
	// Get database connection from middleware context
	db := middlewares.GetDB(c)

	// Bind and validate request body
	var req PosVoucherRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate site_id
	if req.SiteID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_id is required",
		})
		return
	}

	// Find site by ID
	var site models.Site
	if err := db.First(&site, "id = ?", req.SiteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Site not found",
		})
		return
	}

	// Calculate subtotal from order items for minimum order validation
	var subtotal float64
	for _, item := range req.OrderItems {
		subtotal += item.Price * float64(item.Quantity)
	}

	// Create slice for storing voucher responses
	vouchers := make([]VoucherResponse, 0)

	// Find all available vouchers under these categories:
	// 1. Customer-specific vouchers (by phone number)
	// 2. Brand-wide promotions for this site's brand
	// 3. Site-specific promotions

	// 1. Check customer-specific vouchers if phone provided
	if req.CustomerPhone != "" && len(req.CustomerPhone) >= 8 {
		// Find vouchers assigned to this customer's phone number
		var customerVouchers []models.VoucherPromotion
		if err := db.Where(
			"owner_phone = ? AND brand_id = ? AND (expired_at IS NULL OR expired_at > ?) AND is_used = ?",
			req.CustomerPhone,
			site.BrandID,
			time.Now(),
			false,
		).Find(&customerVouchers).Error; err == nil && len(customerVouchers) > 0 {
			// Convert customer vouchers to response format
			for _, v := range customerVouchers {
				vouchers = append(vouchers, VoucherResponse{
					Code:         v.Code,
					Discount:     10000, // Hardcoded example discount, should come from a mapping table
					DiscountType: "fixed",
					Description:  "Customer loyalty voucher",
					EndDate:      v.ExpiredAt,
					ApplyWith:    true,
				})
			}
		}
	}

	// 2. Find active brand-wide promotions
	// Query retailer sale configurations that have vouchers
	var saleConfigs []models.RetailerSaleConfig
	now := time.Now()

	// Build query for active promotions with valid date ranges
	query := db.Where("brand_id = ? AND active = ? AND voucher_config IS NOT NULL", site.BrandID, true).
		Where("(start_date IS NULL OR start_date <= ?)", now).
		Where("(end_date IS NULL OR end_date >= ?)", now)

	if err := query.Find(&saleConfigs).Error; err == nil {
		// Process each valid sale config
		for _, config := range saleConfigs {
			// Skip configs without a voucher code
			if config.VoucherConfig.Data.VoucherCode == "" {
				continue
			}

			// Check minimum order amount if specified
			var minOrderValue float64
			for _, item := range config.Config.Data.Items {
				if item.MinOrderAmount > 0 {
					minOrderValue = item.MinOrderAmount
					break
				}
			}

			if minOrderValue > 0 && subtotal < minOrderValue {
				// Skip if order total is less than minimum required
				continue
			}

			// Determine discount type and value
			discountType := "percentage"
			var discountValue float64

			switch config.Type {
			case "percent_discount", "order_discount":
				discountType = "percentage"
				// Find percentage discount from items
				for _, item := range config.Config.Data.Items {
					if item.DiscountType == "percent" {
						discountValue = item.DiscountValue
						break
					}
				}
			case "fixed_discount":
				discountType = "fixed"
				// Find fixed amount discount
				for _, item := range config.Config.Data.Items {
					if item.DiscountValue > 0 {
						discountValue = item.DiscountValue
						break
					}
				}
			case "ship_discount":
				discountType = "shipping"
				// Find shipping discount amount
				for _, item := range config.Config.Data.Items {
					if item.DiscountValue > 0 {
						discountValue = item.DiscountValue
						break
					}
				}
			}

			// Add voucher to result if it has a valid discount
			if discountValue > 0 {
				vouchers = append(vouchers, VoucherResponse{
					Code:         config.VoucherConfig.Data.VoucherCode,
					Discount:     discountValue,
					DiscountType: discountType,
					Description:  config.Description,
					EndDate:      config.EndDate,
					ApplyWith:    config.VoucherConfig.Data.ApplyWithOther,
				})
			}
		}
	}

	// Return the combined list of vouchers
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"vouchers": vouchers,
	})
}

// ApplyVouchers handles the request to apply voucher codes to an order
func ApplyVouchers(c *gin.Context) {
	// Get database connection from middleware context
	db := middlewares.GetDB(c)

	// Bind and validate request body
	var req PosApplyVoucherRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate site_id
	if req.SiteID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_id is required",
		})
		return
	}

	// Validate voucher codes
	if len(req.VoucherCodes) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "At least one voucher code is required",
		})
		return
	}

	// Find site by ID
	var site models.Site
	if err := db.First(&site, "id = ?", req.SiteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Site not found",
		})
		return
	}

	// Find menu for this site
	var menu models.SiteMenuGroup
	if err := db.Where("site_id = ?", req.SiteID).First(&menu).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Menu not found for this site",
		})
		return
	}

	// Build menu items from categories and subcategories
	menuItems := buildMenuItems1(menu)

	// Map cart dishes with menu data
	mappedCart := mapCartDishes(menuItems, req.OrderItems)

	// Find sale configs (vouchers) by brand ID and voucher codes
	// Use SQL JSON path syntax for accessing the voucher_code field inside voucher_config JSONB column
	var brandSaleConfigs []models.RetailerSaleConfig
	if err := db.Where("brand_id = ? AND active = ?", site.BrandID, true).
		Where("voucher_config->>'voucher_code' IN (?)", req.VoucherCodes).
		Find(&brandSaleConfigs).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Error finding vouchers: " + err.Error(),
		})
		return
	}

	// Filter valid vouchers based on date and usage count
	now := time.Now()
	var validSaleConfigs []models.RetailerSaleConfig
	for _, config := range brandSaleConfigs {
		// Skip if start date is in the future
		if config.StartDate != nil && now.Before(*config.StartDate) {
			continue
		}

		// Skip if end date is in the past
		if config.EndDate != nil && now.After(*config.EndDate) {
			continue
		}

		// Skip if voucher usage limit reached
		// Access fields through the nested Data property of JSONField
		voucherConfig := config.VoucherConfig.Data
		if voucherConfig.Quantity > 0 && voucherConfig.UsageCount >= voucherConfig.Quantity {
			continue
		}

		validSaleConfigs = append(validSaleConfigs, config)
	}

	// Calculate subtotal from order items
	var subtotal float64
	for _, item := range req.OrderItems {
		subtotal += item.Price * float64(item.Quantity)
	}

	// Apply gifts and discounts
	gifts, discounts, shipDiscounts, err := applyGifts(db, site, menuItems, mappedCart, menu.OptionCategories, validSaleConfigs, true)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Error applying vouchers: " + err.Error(),
		})
		return
	}

	// Create map of config IDs that were applied
	configIDs := make(map[string]bool)
	for _, gift := range gifts {
		if gift.ConfigID != "" {
			configIDs[gift.ConfigID] = true
		}
	}
	for _, discount := range discounts {
		if discount.ConfigID != "" {
			configIDs[discount.ConfigID] = true
		}
	}
	for _, shipDiscount := range shipDiscounts {
		if shipDiscount.ConfigID != "" {
			configIDs[shipDiscount.ConfigID] = true
		}
	}

	// Map applied configs to their corresponding voucher codes
	appliedVoucherMap := make(map[string]*models.RetailerSaleConfig)
	for _, config := range validSaleConfigs {
		if _, ok := configIDs[config.ID]; ok {
			// Access the voucher code through the Data field
			voucherCode := config.VoucherConfig.Data.VoucherCode
			if voucherCode != "" {
				appliedVoucherMap[voucherCode] = &config
			}
		}
	}

	// Create response with applied voucher details
	appliedVouchers := make([]map[string]interface{}, 0, len(req.VoucherCodes))

	for _, voucherCode := range req.VoucherCodes {
		// Create voucher response object with default values
		voucher := map[string]interface{}{
			"voucher_code":  voucherCode,
			"applied":       false,
			"discount":      0.0,
			"ship_discount": 0.0,
			"gifts":         []interface{}{},
		}

		// Check if this voucher was applied
		if config, ok := appliedVoucherMap[voucherCode]; ok {
			voucher["applied"] = true
			voucher["note"] = config.Name

			// Find discount amount for this config
			for _, d := range discounts {
				if d.ConfigID == config.ID {
					voucher["discount"] = d.Amount
					break
				}
			}

			// Find shipping discount for this config
			for _, d := range shipDiscounts {
				if d.ConfigID == config.ID {
					voucher["ship_discount"] = d.Amount
					break
				}
			}

			// Find gifts for this config
			configGifts := make([]map[string]interface{}, 0)
			for _, g := range gifts {
				if g.ConfigID == config.ID {
					// Find menu item details for this gift
					var item *models.MenuItem
					for _, mi := range menuItems {
						if mi.ID == g.DishID {
							item = &mi
							break
						}
					}

					// Add gift to response if menu item found
					if item != nil {
						configGifts = append(configGifts, map[string]interface{}{
							"dish_id":  g.DishID,
							"name":     item.Name,
							"image":    item.Image,
							"price":    item.Price,
							"quantity": g.Quantity,
						})
					}
				}
			}
			voucher["gifts"] = configGifts
		} else {
			// Set error for vouchers not applicable
			voucher["error"] = "voucher_not_applicable"
		}

		appliedVouchers = append(appliedVouchers, voucher)
	}

	// Return the list of applied vouchers
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    appliedVouchers,
	})
}

// Helper functions to support ApplyVouchers

// buildMenuItems extracts all menu items from categories and subcategories
func buildMenuItems1(menu models.SiteMenuGroup) []models.MenuItem {
	// Pre-allocate capacity based on rough estimate
	items := make([]models.MenuItem, 0, len(menu.Categories)*10)

	// Add items from main categories
	for _, cat := range menu.Categories {
		for _, item := range cat.Items {
			// Set category name and code
			item.CategoryName = cat.Name
			item.CategoryCode = cat.Code
			items = append(items, item)
		}

		// Add items from subcategories
		for _, subCat := range cat.SubCategories {
			for _, item := range subCat.Items {
				// Set category and subcategory names and code
				item.CategoryName = cat.Name
				item.CategoryCode = cat.Code
				items = append(items, item)
			}
		}
	}

	return items
}

// mapCartDishes maps order items to their corresponding menu items
func mapCartDishes(menuItems []models.MenuItem, orderItems []struct {
	Code     string  `json:"code"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
}) []models.CartDish {
	// Pre-allocate capacity based on the number of order items
	dishes := make([]models.CartDish, 0, len(orderItems))

	// Find corresponding menu item for each order item
	for _, item := range orderItems {
		// Look for matching menu item by code
		var menuItem models.MenuItem
		found := false
		for _, mi := range menuItems {
			if mi.Code == item.Code {
				menuItem = mi
				found = true
				break
			}
		}

		// Skip if no matching menu item found
		if !found {
			continue
		}

		// Create cart dish with information from menu item
		dish := models.CartDish{
			Code:         item.Code,
			Quantity:     int64(item.Quantity),
			UnitPrice:    menuItem.Price,
			Price:        item.Price,
			ID:           menuItem.ID,
			Name:         menuItem.Name,
			Description:  menuItem.Description,
			Image:        menuItem.Image,
			CategoryName: menuItem.CategoryName,
			CategoryCode: menuItem.CategoryCode,
			Options:      [][]models.DishOption{},
		}

		dishes = append(dishes, dish)
	}

	return dishes
}

// GiftItem represents a gift item applied to an order
type GiftItem struct {
	DishID   string `json:"dish_id"`
	Quantity int    `json:"quantity"`
	ConfigID string `json:"config_id"`
}

// DiscountItem represents a discount applied to an order
type DiscountItem struct {
	Amount   float64 `json:"amount"`
	ConfigID string  `json:"config_id"`
	DishID   string  `json:"dish_id,omitempty"`
	Note     string  `json:"note,omitempty"`
}

// applyGifts calculates applicable gifts and discounts for a cart
// This function applies promotion rules based on the retailer sale configs
func applyGifts(db *gorm.DB, site models.Site, menuItems []models.MenuItem,
	cartDishes []models.CartDish, optionCategories []models.OptionCategory,
	saleConfigs []models.RetailerSaleConfig, ignoreCheckSiteApplyGift bool) (
	[]GiftItem, []DiscountItem, []DiscountItem, error) {

	// Create structured parameters to improve organization
	params := ApplyGiftParams{
		Site:             site,
		MenuItems:        menuItems,
		CartDishes:       cartDishes,
		OptionCategories: optionCategories,
		SaleConfigs:      saleConfigs,
		IgnoreSiteCheck:  ignoreCheckSiteApplyGift,
	}

	// Initialize result container
	result := GiftsResult{
		Gifts:         make([]GiftItem, 0),
		Discounts:     make([]DiscountItem, 0),
		ShipDiscounts: make([]DiscountItem, 0),
	}

	// Check if site is eligible for gifts (early return if not eligible)
	if !(ignoreCheckSiteApplyGift || (site.Type == "partner" && site.ApplyGift)) || len(cartDishes) == 0 {
		return result.Gifts, result.Discounts, result.ShipDiscounts, nil
	}

	// Calculate the subtotal of the cart for discount calculations
	subtotal := handleCalculateSubTotal(cartDishes)

	// Process each sale configuration
	now := time.Now()
	for _, saleConfig := range saleConfigs {
		// Skip configurations with invalid date ranges
		if saleConfig.StartDate != nil && now.Before(*saleConfig.StartDate) {
			continue
		}
		if saleConfig.EndDate != nil && now.After(*saleConfig.EndDate) {
			continue
		}

		// Process each promotion type with a dedicated function
		switch saleConfig.Type {
		case "order_bonus":
			processOrderBonus(&result, &saleConfig, &params, subtotal)
		case "buy_x_get_y":
			processBuyXGetY(&result, &saleConfig, &params)
		case "buy_x_discount_y":
			processBuyXDiscountY(&result, &saleConfig, &params)
		case "fixed_discount":
			processFixedDiscount(&result, &saleConfig, &params)
		case "order_discount":
			processOrderDiscount(&result, &saleConfig, &params, subtotal)
		case "ship_discount":
			processShipDiscount(&result, &saleConfig, &params, subtotal)
		}
	}

	// Return the processed results
	return result.Gifts, result.Discounts, result.ShipDiscounts, nil
}

// Helper function to calculate the total sum of cart items
func handleCalculateSubTotal(dishes []models.CartDish) float64 {
	var total float64
	for _, dish := range dishes {
		total += dish.Price
	}
	return total
}

// processOrderBonus processes order bonus promotions (free gifts with order)
func processOrderBonus(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams, subtotal float64) {
	// Find applicable config based on minimum order amount
	var appliedConfig *models.SaleItem
	for _, item := range config.Config.Data.Items {
		if subtotal >= item.MinOrderAmount {
			appliedConfig = &item
			break
		}
	}

	if appliedConfig == nil {
		return
	}

	// Find gift item in menu using item code
	var giftItem models.MenuItem
	giftFound := false
	for _, item := range params.MenuItems {
		if item.Code == appliedConfig.Code {
			giftItem = item
			giftFound = true
			break
		}
	}

	if !giftFound || giftItem.ID == "" {
		return
	}

	// Create gift item with appropriate quantity
	giftQuantity := int(appliedConfig.Quantity)
	if giftQuantity < 1 {
		giftQuantity = 1
	}

	// Add gift to results
	result.Gifts = append(result.Gifts, GiftItem{
		DishID:   giftItem.ID,
		Quantity: giftQuantity,
		ConfigID: string(config.ID),
	})
}

// processBuyXGetY processes "buy X get Y" promotions
func processBuyXGetY(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams) {
	conf := config.Config.Data
	var cartItemCount int64 = 0

	// Count matching items in cart by code
	for _, dish := range params.CartDishes {
		if dish.Code == conf.ItemCode {
			cartItemCount += dish.Quantity
		}
	}

	// Calculate number of gifts to provide
	noOfGifts := (cartItemCount / conf.Quantity) * conf.GiftQuantity
	if noOfGifts <= 0 {
		return
	}

	// Find gift item in menu
	var giftItem models.MenuItem
	giftFound := false
	for _, item := range params.MenuItems {
		if item.Code == conf.GiftCode {
			giftItem = item
			giftFound = true
			break
		}
	}

	if !giftFound || giftItem.ID == "" {
		return
	}

	// Add gift to results
	result.Gifts = append(result.Gifts, GiftItem{
		DishID:   giftItem.ID,
		Quantity: int(noOfGifts),
		ConfigID: string(config.ID),
	})
}

// processBuyXDiscountY processes "buy X discount Y" promotions
func processBuyXDiscountY(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams) {
	conf := config.Config.Data

	var applicableItems []models.CartDish
	switch conf.ItemType {
	case "dish":
		// Find items in cart that match dish codes from config
		for _, dish := range params.CartDishes {
			for _, configItem := range conf.Items {
				if dish.Code == configItem.Code {
					applicableItems = append(applicableItems, dish)
					break
				}
			}
		}
	case "category":
		// Find items in cart that match category codes from config
		for _, dish := range params.CartDishes {
			for _, configItem := range conf.Items {
				if dish.CategoryCode == configItem.CategoryCode {
					applicableItems = append(applicableItems, dish)
					break
				}
			}
		}
	}

	// Count total applicable quantity
	var totalQuantity int64
	for _, dish := range applicableItems {
		totalQuantity += dish.Quantity
	}

	// Continue only if we have enough items
	if totalQuantity < conf.Quantity {
		return
	}

	// Find eligible items for discount (gift items)
	var discountableItems []models.CartDish
	for _, dish := range params.CartDishes {
		for _, giftItem := range conf.GiftItems {
			if dish.Code == giftItem.Code {
				discountableItems = append(discountableItems, dish)
				break
			}
		}
	}

	// Sort discountable items by price (lowest first)
	sort.Slice(discountableItems, func(i, j int) bool {
		return discountableItems[i].UnitPrice < discountableItems[j].UnitPrice
	})

	// Calculate max number of items to discount
	maxGifts := (totalQuantity / conf.Quantity) * conf.GiftQuantity
	remainingGifts := maxGifts

	// Apply discounts to eligible items
	for _, dish := range discountableItems {
		if remainingGifts <= 0 {
			break
		}

		// Calculate how many units of this item to discount
		discountQuantity := dish.Quantity
		if discountQuantity > remainingGifts {
			discountQuantity = remainingGifts
		}

		// Calculate discounted price based on discount type
		var discountedPrice float64
		switch conf.DiscountType {
		case "fixed":
			discountedPrice = math.Max(0, dish.UnitPrice-conf.DiscountValue)
		case "percent":
			discountedPrice = dish.UnitPrice * (1 - conf.DiscountValue/100)
		case "flat":
			discountedPrice = conf.DiscountValue
		default:
			discountedPrice = dish.UnitPrice
		}

		// Calculate total discount amount
		totalDiscount := float64(discountQuantity) * (dish.UnitPrice - discountedPrice)

		// Add discount to results if it's a meaningful amount
		if totalDiscount > 0 {
			result.Discounts = append(result.Discounts, DiscountItem{
				DishID:   dish.ID,
				Amount:   math.Floor(totalDiscount),
				ConfigID: string(config.ID),
				Note:     config.Name,
			})
		}

		remainingGifts -= discountQuantity
	}
}

// processFixedDiscount processes fixed amount discount promotions
func processFixedDiscount(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams) {
	for _, dish := range params.CartDishes {
		for _, configItem := range config.Config.Data.Items {
			// Match items by code
			if dish.Code == configItem.Code {
				// Calculate discount amount (either configItem value * quantity or dish price, whichever is smaller)
				discount := math.Min(dish.Price, configItem.DiscountValue*float64(dish.Quantity))

				// Add to results if discount amount is positive
				if discount > 0 {
					result.Discounts = append(result.Discounts, DiscountItem{
						DishID:   dish.ID,
						Amount:   math.Floor(discount),
						ConfigID: string(config.ID),
						Note:     config.Name,
					})
				}
			}
		}
	}
}

// processOrderDiscount processes percentage or fixed discounts on the entire order
func processOrderDiscount(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams, subtotal float64) {
	// Find applicable discount based on minimum order amount
	var finalDiscount float64

	for _, item := range config.Config.Data.Items {
		if subtotal >= item.MinOrderAmount {
			var itemDiscount float64

			// Calculate discount based on type
			switch item.DiscountType {
			case "fixed":
				itemDiscount = item.DiscountValue
			case "percent":
				itemDiscount = (item.DiscountValue * subtotal) / 100
				// Apply maximum discount cap if specified
				if item.MaxDiscount > 0 {
					itemDiscount = math.Min(itemDiscount, item.MaxDiscount)
				}
			}

			// Keep track of highest applicable discount
			finalDiscount = math.Max(finalDiscount, itemDiscount)
		}
	}

	// Add discount to results if it's meaningful
	if finalDiscount > 0 {
		result.Discounts = append(result.Discounts, DiscountItem{
			Amount:   math.Floor(finalDiscount),
			ConfigID: string(config.ID),
			Note:     config.Name,
		})
	}
}

// processShipDiscount processes shipping discount promotions
func processShipDiscount(result *GiftsResult, config *models.RetailerSaleConfig, params *ApplyGiftParams, subtotal float64) {
	// Default shipping fee for calculation purposes
	// In a real implementation, this would come from the actual shipment data
	shippingFee := 30000.0 // Example value

	// Find applicable discount based on minimum order amount
	var maxDiscount float64

	for _, item := range config.Config.Data.Items {
		if subtotal >= item.MinOrderAmount {
			var itemDiscount float64

			// Calculate discount based on type
			switch item.Type {
			case "discount":
				itemDiscount = item.Value
			case "free_ship":
				itemDiscount = shippingFee
			case "flat_rate":
				if shippingFee > item.Value {
					itemDiscount = shippingFee - item.Value
				}
			}

			// Keep track of highest applicable discount
			maxDiscount = math.Max(maxDiscount, itemDiscount)
		}
	}

	// Cap discount at shipping fee amount
	finalDiscount := math.Min(maxDiscount, shippingFee)

	// Add shipping discount to results if it's meaningful
	if finalDiscount > 0 {
		result.ShipDiscounts = append(result.ShipDiscounts, DiscountItem{
			Amount:   finalDiscount,
			ConfigID: string(config.ID),
			Note:     config.Name,
		})
	}
}
