package router

// CreateOrder creates an order from a user's cart
// func CreateOrder(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	user := middlewares.GetUser(c)
// 	siteID := c.<PERSON>m("site_id")

// 	// Parse request body
// 	var request struct {
// 		Dishes        []models.CartDish `json:"dishes"`
// 		Note          string            `json:"note"`
// 		PaymentMethod string            `json:"payment_method" binding:"required"`
// 		PromoCode     string            `json:"promo_code"`
// 		VoucherCode   string            `json:"voucher_code"`
// 		ShipVoucher   string            `json:"ship_voucher"`
// 		HubID         int64             `json:"hub_id"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	// Validate payment method
// 	if request.PaymentMethod == "" {
// 		c.<PERSON>(http.StatusBadRequest, gin.H{"success": false, "error": "payment_method_is_empty"})
// 		return
// 	}

// 	// Find site, brand, and menu data
// 	var site models.Site
// 	if err := db.First(&site, siteID).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
// 		return
// 	}

// 	var brand models.Brand
// 	if err := db.First(&brand, site.BrandID).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	var siteMenu models.SiteMenuGroup
// 	if err := db.Where("site_id = ?", siteID).First(&siteMenu).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	var brandMenu models.BrandMenu
// 	if err := db.Where("brand_id = ?", site.BrandID).First(&brandMenu).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	menuItems := buildMenuItems(&siteMenu)

// 	// Find user's cart
// 	var cart models.UserCart
// 	if err := db.Where("user_id = ? AND site_id = ? AND status = ?", user.ID, siteID, "created").First(&cart).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	// Validate cart shipment
// 	isPickup := cart.Shipment.Service.Vendor == "pick_up"
// 	if (!isPickup && cart.Shipment.To.Address == "") || cart.Shipment.To.Name == "" || cart.Shipment.To.Phone == "" {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "to_address_is_missing"})
// 		return
// 	}

// 	if cart.Shipment.Service.Vendor == "" {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "cart_shipment_service_is_missing"})
// 		return
// 	}

// 	// Check if the shipment is in the last shipments
// 	vendor := cart.Shipment.Service.Vendor
// 	code := cart.Shipment.Service.Code
// 	price := cart.Shipment.Service.Price
// 	var lastShipment *models.ShipmentService

// 	for _, s := range cart.Shipments.Data {
// 		if s.Code == code && s.Vendor == vendor && s.Price == price {
// 			shipment := s
// 			lastShipment = &shipment
// 			break
// 		}
// 	}

// 	if lastShipment == nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "shipment_service_is_incorrect"})
// 		return
// 	}

// 	// Handle dish items
// 	if len(request.Dishes) > 0 {
// 		dishItems := handleCreateOrderItem(menuItems, request.Dishes)
// 		if len(dishItems) > 0 {
// 			cart.Dishes.Data = dishItems
// 		}
// 	}

// 	// Validate cart items
// 	validateCartItems(&cart, menuItems)
// 	for _, dish := range cart.Dishes.Data {
// 		if dish.Error != "" {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   "cart_item_error",
// 				"detail":   dish.Error,
// 			})
// 			return
// 		}
// 	}

// 	// Handle shipping promo
// 	shippingPromo := &models.ShippingPromo{
// 		Code:     "",
// 		Error:    "",
// 		Discount: 0,
// 	}

// 	if request.ShipVoucher != "" {
// 		service := cart.Shipment.Service
// 		if service.Vendor == "" {
// 			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "shipment_service_is_missing"})
// 			return
// 		}

// 		// TODO: Implement shipping promo validation for different vendors
// 		// This would require implementing the shipping service clients (GHN, AhaMove, etc.)
// 		// For now, we'll just acknowledge the placeholder
// 		shippingPromo = &models.ShippingPromo{
// 			Code:     request.ShipVoucher,
// 			Discount: 0, // This should be populated from the shipping service response
// 			Error:    "",
// 		}
// 	}

// 	cart.ShippingPromo = shippingPromo

// 	// Apply gifts and discounts
// 	vouchers := cart.Vouchers.Data
// 	var dpointVoucher *models.Voucher
// 	for _, v := range vouchers {
// 		if v.Vendor == "dpoint" {
// 			dpointVoucher = &v
// 			break
// 		}
// 	}

// 	nexposVouchers := funk.Filter(vouchers, func(v models.Voucher) bool {
// 		return v.Vendor == "nexpos"
// 	}).([]models.Voucher)

// 	var nexposVoucherCodes []string
// 	for _, v := range nexposVouchers {
// 		nexposVoucherCodes = append(nexposVoucherCodes, v.Code)
// 	}

// 	saleConfigs, err := GetSaleConfigs(db, &site, nexposVoucherCodes)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	gifts, discount, shipDiscount, err := ApplyGifts(db, &ApplyGiftsParams{
// 		User:             user,
// 		Site:             &site,
// 		MenuItems:        menuItems,
// 		Cart:             &cart,
// 		OptionCategories: siteMenu.OptionCategories,
// 		DPointVoucher:    dpointVoucher,
// 		SaleConfigs:      saleConfigs,
// 	})
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	// Filter out inapplicable gifts and discounts
// 	applicableGifts := funk.Filter(gifts, func(g models.CartDish) bool {
// 		return g.IsApplicable
// 	}).([]models.CartDish)

// 	applicableDiscount := funk.Filter(discount, func(d models.CartDiscount) bool {
// 		return d.IsApplicable
// 	}).([]models.CartDiscount)

// 	applicableShipDiscount := funk.Filter(shipDiscount, func(d models.CartDiscount) bool {
// 		return d.IsApplicable
// 	}).([]models.CartDiscount)

// 	// Generate order ID
// 	orderGroup := fmt.Sprintf("%s-%s", strings.ToUpper(site.Code), time.Now().Format("060102"))

// 	var siteNextIndex models.SiteOrderIndex
// 	result := db.Where("site_id = ? AND group_name = ?", site.ID, orderGroup).
// 		FirstOrCreate(&siteNextIndex, models.SiteOrderIndex{
// 			SiteID:      site.ID,
// 			GroupName:   orderGroup,
// 			CurrentIndex: 0,
// 		})

// 	if result.Error != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": result.Error.Error()})
// 		return
// 	}

// 	siteNextIndex.CurrentIndex++
// 	if err := db.Save(&siteNextIndex).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	orderID := fmt.Sprintf("%s-%03d", orderGroup, siteNextIndex.CurrentIndex)

// 	// Calculate total amounts
// 	var total float64
// 	for _, dish := range cart.Dishes.Data {
// 		total += dish.Price
// 	}

// 	var totalDiscount float64
// 	for _, d := range applicableDiscount {
// 		totalDiscount += d.Amount
// 	}

// 	if dpointVoucher != nil {
// 		totalDiscount += dpointVoucher.Discount
// 	}

// 	total = math.Max(total-totalDiscount, 0)
// 	shippingFee := cart.ShippingFee

// 	// Get assigned hub
// 	var assignedHub models.Hub
// 	if request.HubID != 0 {
// 		if err := db.First(&assignedHub, request.HubID).Error; err != nil {
// 			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "hub_not_found"})
// 			return
// 		}
// 	} else {
// 		if err := db.First(&assignedHub, site.HubID).Error; err != nil {
// 			c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "hub_not_found"})
// 			return
// 		}
// 	}

// 	// Prepare order dishes
// 	var cartDishes []models.CartDish
// 	cartDishes = append(cartDishes, cart.Dishes.Data...)
// 	cartDishes = append(cartDishes, applicableGifts...)

// 	orderDishes := make([]models.OrderDish, 0, len(cartDishes))
// 	for _, dish := range cartDishes {
// 		// Find applicable discounts for this dish
// 		dishDiscounts := funk.Filter(applicableDiscount, func(d models.CartDiscount) bool {
// 			return d.DishID == dish.ID
// 		}).([]models.CartDiscount)

// 		// Build discount notes
// 		var discountNotes []string
// 		for _, d := range dishDiscounts {
// 			if d.Note != "" {
// 				discountNotes = append(discountNotes, d.Note)
// 			}
// 		}

// 		// Calculate total discount
// 		var dishDiscount float64
// 		for _, d := range dishDiscounts {
// 			dishDiscount += d.Amount
// 		}

// 		// Combine notes
// 		note := dish.Note
// 		if len(discountNotes) > 0 {
// 			if note != "" {
// 				note += ", "
// 			}
// 			note += strings.Join(discountNotes, ", ")
// 		}

// 		orderDishes = append(orderDishes, models.OrderDish{
// 			ID:          dish.ID,
// 			Code:        dish.Code,
// 			Image:       dish.Image,
// 			FromBrandID: 0, // This might need to be populated from somewhere
// 			CategoryName: dish.CategoryName,
// 			Name:        dish.Name,
// 			Description: dish.Description,
// 			Options:     dish.Options,
// 			Quantity:    dish.Quantity,
// 			Price:       dish.Price,
// 			Note:        note,
// 			Discount:    dishDiscount,
// 			IsGift:      dish.IsGift,
// 			Combo:       dish.Combo,
// 		})
// 	}

// 	// Build the order notes
// 	orderNotes := []string{cart.Note}
// 	if request.Note != "" {
// 		orderNotes = append(orderNotes, request.Note)
// 	}

// 	// Create the order
// 	var userID int64
// 	if user != nil {
// 		userID = user.ID
// 	}

// 	orderTime := time.Now().UTC()

// 	order := models.Order{
// 		Source:  "he",
// 		SiteID:  site.ID,
// 		Status:  "PENDING",
// 		OrderID: orderID,
// 		HubID:   assignedHub.ID,
// 		HEID:    site.HEID,
// 		Vouchers: cart.Vouchers.Data,
// 		// Shipment: models.OrderShipment{
// 		// 	COD:           total,
// 		// 	PaymentMethod: request.PaymentMethod,
// 		// 	Price:         shippingFee,
// 		// 	Note:          "",
// 		// 	Promo:         cart.ShippingPromo,
// 		// 	ShipmentID:    "",
// 		// 	TrackingURL:   "",
// 		// 	Discount:      applicableShipDiscount,
// 		// 	Service:       cart.Shipment.Service,
// 		// 	Schedule:      cart.Shipment.Schedule,
// 		// 	Vendor:        cart.Shipment.Service.Vendor,
// 		// 	From: models.ShipmentParty{
// 		// 		Address:    assignedHub.Address,
// 		// 		Phone:      assignedHub.Phone,
// 		// 		Name:       assignedHub.Name,
// 		// 	},
// 		// 	To:            cart.Shipment.To,
// 		// },
// 		Data: models.OrderData{
// 			ID:                orderID,
// 			OrderID:           orderID,
// 			Source:            "he",
// 			OrderTime:         orderTime.Format(time.RFC3339),
// 			PickTime:          "",
// 			DeliveryTime:      "",
// 			OrderTimeSort:     orderTime.Unix(),
// 			DriverName:        "",
// 			DriverPhone:       "",
// 			Dishes:            orderDishes,
// 			Commission:        0,
// 			Total:             total,
// 			TotalForBiz:       total + shippingFee,
// 			TotalShipment:     shippingFee,
// 			PromoCode:         request.PromoCode,
// 			TotalDiscount:     0,
// 			TotalDisplay:      helper.FormatCurrency(total, "vi-VN", "VND"),
// 			AffiliateSource:   "",
// 			Note:              strings.Join(orderNotes, ". "),
// 			CancelReason:      "",
// 			Payments:          []models.Payment{{Method: request.PaymentMethod, Total: total + shippingFee, Status: "PENDING"}},
// 			Discount:          applicableDiscount,
// 			ShipDiscount:      applicableShipDiscount,
// 			ShipmentFee:       cart.ShippingFee,
// 			PaymentMethod:     request.PaymentMethod,
// 			CustomerAddress:   cart.Shipment.To.Address,
// 			CustomerName:      cart.Shipment.To.Name,
// 			CustomerPhone:     cart.Shipment.To.Phone,
// 		},
// 		UserID:     userID,
// 		ExternalID: helper.GenerateExternalID(),
// 	}

// 	// Add coupons data
// 	coupons := make([]models.OrderCoupon, 0)
// 	for _, v := range cart.Vouchers.Data {
// 		coupons = append(coupons, models.OrderCoupon{
// 			Code:  v.Code,
// 			Total: v.Discount,
// 			Name:  v.Vendor,
// 		})
// 	}
// 	order.Data.Coupons = coupons

// 	// Set order status based on payment method
// 	if codToken := token.GetToken(brand, "cod"); codToken != nil {
// 		var codSettings struct {
// 			MaxCOD          float64 `json:"max_cod"`
// 			MaxCODPercentage float64 `json:"max_cod_percentage"`
// 		}

// 		if codToken.SiteData != nil {
// 			// Parse settings from token
// 			// This is simplified, actual implementation might need more robust parsing
// 			if data, ok := codToken.SiteData.(map[string]any); ok {
// 				if val, exists := data["max_cod"]; exists {
// 					codSettings.MaxCOD, _ = strconv.ParseFloat(fmt.Sprintf("%v", val), 64)
// 				}
// 				if val, exists := data["max_cod_percentage"]; exists {
// 					codSettings.MaxCODPercentage, _ = strconv.ParseFloat(fmt.Sprintf("%v", val), 64)
// 				}
// 			}
// 		}

// 		// Default values if not set
// 		if codSettings.MaxCOD == 0 {
// 			codSettings.MaxCOD = 1000000
// 		}

// 		// Adjust payment status based on COD settings
// 		if request.PaymentMethod == "COD" {
// 			for i := range order.Data.Payments {
// 				if total+shippingFee >= codSettings.MaxCOD {
// 					order.Data.Payments[i].Status = "WAITING_PAYMENT"
// 					order.Data.Payments[i].PrepaidRequired = true
// 					order.Data.Payments[i].PrepaidMinimum = math.Ceil(((total + shippingFee) * codSettings.MaxCODPercentage) / 1000) * 1000
// 				} else {
// 					order.Status = "DOING"
// 					order.Data.Payments[i].Status = "COMPLETED"
// 				}
// 			}
// 		} else if request.PaymentMethod == "CASH" {
// 			for i := range order.Data.Payments {
// 				order.Data.Payments[i].Status = "COMPLETED"
// 			}
// 			order.Status = "DOING"
// 		} else {
// 			order.Status = "WAITING_PAYMENT"
// 		}
// 	}

// 	// Map order for data consistency
// 	order.DataMapping = mapping.MapOrder(&models.MerchantOrder{
// 		LongOrderID:  order.OrderID,
// 		ShortOrderID: order.OrderID,
// 		Source:       "he",
// 		DataInDetail: order.Data,
// 	})

// 	// Create the order in database
// 	if err := db.Create(&order).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	// Mark the cart as completed
// 	cart.Status = "completed"
// 	if err := db.Save(&cart).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
// 		return
// 	}

// 	// Handle voucher usage
// 	if dpointVoucher != nil {
// 		if err := voucher.UseVoucher(dpointVoucher, &site); err != nil {
// 			// Log error but don't fail the request
// 			// In production we'd want to handle this more carefully
// 			fmt.Printf("Error using voucher: %v\n", err)
// 		}
// 	}

// 	// Create a new empty cart for the user
// 	if err := db.Create(&models.UserCart{
// 		UserID: user.ID,
// 		SiteID: site.ID,
// 		Status: "created",
// 	}).Error; err != nil {
// 		// Log error but don't fail the request
// 		fmt.Printf("Error creating new cart: %v\n", err)
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    order,
// 	})
// }

// handleCreateOrderItem processes menu items for order creation
// func handleCreateOrderItem(menuItems []models.MenuItem, dishes []models.CartDish) []models.CartDish {
// 	if len(menuItems) == 0 {
// 		return nil
// 	}

// 	result := make([]models.CartDish, 0, len(dishes))

// 	// Filter menu items that match the dishes
// 	for _, menuItem := range menuItems {
// 		for _, dish := range dishes {
// 			if menuItem.ID == dish.ID {
// 				quantity := dish.Quantity
// 				if quantity == 0 {
// 					quantity = 1
// 				}

// 				unitPrice := menuItem.Price
// 				price := float64(quantity) * unitPrice

// 				item := menuItem

// 				// Create a CartDish with the menu item data
// 				result = append(result, models.CartDish{
// 					ID:              item.ID,
// 					ItemID:          item.ID,
// 					Name:            item.Name,
// 					CategoryName:    item.CategoryName,
// 					SubCategoryName: item.SubCategoryName,
// 					Unit:            item.Unit,
// 					Code:            item.Code,
// 					Image:           item.Image,
// 					Description:     item.Description,
// 					Price:           price,
// 					UnitPrice:       unitPrice,
// 					Quantity:        quantity,
// 					Note:            dish.Note,
// 					Options:         dish.Options,
// 					Combo:           item.Combo,
// 				})

// 				break
// 			}
// 		}
// 	}

// 	return result
// }
