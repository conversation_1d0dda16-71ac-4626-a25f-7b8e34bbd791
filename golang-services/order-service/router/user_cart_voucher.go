package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/voucher"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
)

// ApplyVoucher applies a voucher to the user's cart
func ApplyVoucher(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	// Parse request body
	var request struct {
		VoucherCode string `json:"voucher_code"`
		Phone       string `json:"phone"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Validate request
	if request.VoucherCode == "" || request.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "voucher_code_or_phone_is_missing"})
		return
	}

	// Find site
	var site models.Site
	if err := db.First(&site, siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Find cart
	var cart models.UserCart
	if err := db.Where("user_id = ? AND site_id = ? AND status = ?", user.ID, siteID, "created").First(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Initialize result struct
	result := struct {
		Error        string  `json:"error"`
		VoucherCode  string  `json:"voucher_code"`
		VoucherValue float64 `json:"voucher_value"`
		VoucherType  string  `json:"voucher_type"`
	}{
		Error:        "",
		VoucherCode:  "",
		VoucherValue: 0,
		VoucherType:  "",
	}

	// Check if voucher is already applied
	existingVoucher := funk.Find(cart.Vouchers.Data, func(v models.Voucher) bool {
		return v.Code == request.VoucherCode
	})

	if existingVoucher != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "voucher_already_applied",
		})
		return
	}

	// Validate voucher
	dpointVoucherResult, err := voucher.ValidateVoucher(&site, request.VoucherCode, request.Phone)
	if err != nil {
		result.Error = err.Error()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"data":    cart,
		})
		return
	}

	if dpointVoucherResult.Error != "" {
		result.Error = dpointVoucherResult.Error
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"data":    cart,
		})
		return
	}

	// Check if site applies vouchers
	appliesVoucher := voucher.IsSiteApplyVoucher(&site)
	if !appliesVoucher {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "voucher_not_configured",
		})
		return
	}

	// Add voucher to cart
	result.VoucherCode = request.VoucherCode
	result.VoucherValue = dpointVoucherResult.Voucher.Discount
	result.VoucherType = "dpoint"

	cart.Vouchers.Data = append(cart.Vouchers.Data, models.Voucher{
		Code:     request.VoucherCode,
		Discount: dpointVoucherResult.Voucher.Discount,
		Vendor:   dpointVoucherResult.Voucher.Vendor,
	})

	// Update cart totals
	if err := populateCartGifts(db, &site, user, &cart, nil); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Save updated cart
	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cart,
	})
}

// CheckVoucher validates a voucher without applying it to the cart
func CheckVoucher(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	// Parse request body
	var request struct {
		VoucherCode string `json:"voucher_code"`
		Phone       string `json:"phone"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Validate request
	if request.VoucherCode == "" || request.Phone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "voucher_code_or_phone_is_missing"})
		return
	}

	// Find site
	var site models.Site
	if err := db.First(&site, siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Find cart (not actually used, but keeping consistent with JS implementation)
	var cart models.UserCart
	if err := db.Where("user_id = ? AND site_id = ? AND status = ?", user.ID, siteID, "created").First(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Initialize result struct
	result := struct {
		Error        string  `json:"error"`
		VoucherCode  string  `json:"voucher_code"`
		VoucherValue float64 `json:"voucher_value"`
		VoucherType  string  `json:"voucher_type"`
	}{
		Error:        "",
		VoucherCode:  "",
		VoucherValue: 0,
		VoucherType:  "",
	}

	// Validate voucher
	dpointVoucherResult, err := voucher.ValidateVoucher(&site, request.VoucherCode, request.Phone)
	if err != nil {
		result.Error = err.Error()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"data":    result,
		})
		return
	}

	if dpointVoucherResult.Error != "" {
		result.Error = dpointVoucherResult.Error
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"data":    result,
		})
		return
	}

	// Check if site applies vouchers
	appliesVoucher := voucher.IsSiteApplyVoucher(&site)
	if !appliesVoucher {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "voucher_not_configured",
		})
		return
	}

	// Return voucher details
	result.VoucherCode = request.VoucherCode
	result.VoucherValue = dpointVoucherResult.Voucher.Discount
	result.VoucherType = "dpoint"

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}
