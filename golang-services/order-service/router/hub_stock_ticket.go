package router

import (
	"bytes"
	"fmt"
	"net/http"

	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/ticket"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetHubStockTicketList(c *gin.Context) {
	db := middlewares.GetDB(c)
	hubID := c.Param("hub_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	ticketType := c.Query("ticket_type")
	status := c.Query("status")

	query := db.Model(&models.HubStockTicket{}).
		Preload("Hub").
		Preload("TargetHub").
		Preload("Creator").
		Preload("Approver").
		Preload("Rejecter").
		Where("hub_id = ? OR target_hub_id = ?", hubID, hubID)

	if ticketType != "" {
		query = query.Where("ticket_type = ?", ticketType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	offset := (page - 1) * limit
	var tickets []models.HubStockTicket
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&tickets).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tickets,
		"page":    page,
		"limit":   limit,
		"total":   total,
	})
}

func CreateHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	hubID := c.Param("hub_id")
	user := middlewares.GetUser(c)

	var request struct {
		TicketType    string                      `json:"ticket_type" binding:"required"`
		TargetHubID   *string                     `json:"target_hub_id"`
		TargetHubDate *time.Time                  `json:"target_hub_date"`
		Items         []models.HubStockTicketItem `json:"items" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Get hub for ticket number generation
	var hub models.Hub
	if err := db.First(&hub, hubID).Error; err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Error:   "Hub not found",
		})
		return
	}

	// Generate ticket number
	ticketNumber, err := ticket.GenerateTicketCode(db, &hub, request.TicketType)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	ticket := models.HubStockTicket{
		TicketNumber:  ticketNumber,
		TicketType:    request.TicketType,
		HubID:         hub.ID,
		TargetHubID:   request.TargetHubID,
		TargetHubDate: request.TargetHubDate,
		Status:        "submitted",
		Items:         models.JSONField[[]models.HubStockTicketItem]{Data: request.Items},
		CreatedBy:     user.ID,
	}

	if err := db.Create(&ticket).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    ticket,
	})
}

func UpdateHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	ticketID := c.Param("ticket_id")

	var request struct {
		TargetHubID   *int64                      `json:"target_hub_id"`
		TargetHubDate *time.Time                  `json:"target_hub_date"`
		Items         []models.HubStockTicketItem `json:"items"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	updates := map[string]any{
		"target_hub_id":   request.TargetHubID,
		"target_hub_date": request.TargetHubDate,
		"items":           models.JSONField[[]models.HubStockTicketItem]{Data: request.Items},
	}

	if err := db.Model(&models.HubStockTicket{}).
		Where("id = ?", ticketID).
		Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	var ticket models.HubStockTicket
	if err := db.First(&ticket, ticketID).Error; err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Error:   "Ticket not found",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    ticket,
	})
}

func ApproveHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	ticketID := c.Param("ticket_id")
	user := middlewares.GetUser(c)

	var request struct {
		Items []models.HubStockTicketItem `json:"items" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	result := approveTicket(db, ticketID, request.Items, user.ID)
	c.JSON(http.StatusOK, result)
}

// Helper function to approve ticket and update stock
func approveTicket(db *gorm.DB, ticketID string, items []models.HubStockTicketItem, approverID string) Response {
	var response Response

	err := db.Transaction(func(tx *gorm.DB) error {
		var ticket models.HubStockTicket
		if err := tx.Preload("Hub").Preload("TargetHub").First(&ticket, ticketID).Error; err != nil {
			response = Response{Success: false, Error: "Ticket not found"}
			return err
		}

		if ticket.Status != "submitted" {
			response = Response{Success: false, Error: "Ticket cannot be approved"}
			return fmt.Errorf("ticket cannot be approved")
		}

		// Update ticket items and status
		ticket.Status = "approved"
		ticket.ApprovedBy = &approverID

		// Process each item and update stock
		for i := range ticket.Items.Data {
			if matchItem := findMatchingItem(items, ticket.Items.Data[i].Code); matchItem != nil {
				ticket.Items.Data[i].ExchangedQuantity = matchItem.ExchangedQuantity
				ticket.Items.Data[i].StockReportTypes = matchItem.StockReportTypes
				ticket.Items.Data[i].StockReportNote = matchItem.StockReportNote

				if err := updateStock(tx, &ticket, &ticket.Items.Data[i]); err != nil {
					response = Response{Success: false, Error: err.Error()}
					return err
				}
			}
		}

		if err := tx.Save(&ticket).Error; err != nil {
			response = Response{Success: false, Error: err.Error()}
			return err
		}

		response = Response{Success: true, Data: ticket}
		return nil
	})

	if err != nil {
		return response
	}

	return response
}

func RejectHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	ticketID := c.Param("ticket_id")
	user := middlewares.GetUser(c)

	var request struct {
		ReportTypes []string `json:"report_types"`
		ReportNote  string   `json:"report_note"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	var ticket models.HubStockTicket
	if err := db.First(&ticket, ticketID).Error; err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Error:   "Ticket not found",
		})
		return
	}

	if ticket.Status != "submitted" {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Ticket cannot be rejected",
		})
		return
	}

	updates := map[string]any{
		"status":      "rejected",
		"rejected_by": user.ID,
	}

	if err := db.Model(&ticket).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    ticket,
	})
}

func DeleteHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	ticketID := c.Param("ticket_id")

	if err := db.Delete(&models.HubStockTicket{}, ticketID).Error; err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
	})
}

func PrintHubStockTicket(c *gin.Context) {
	db := middlewares.GetDB(c)
	ticketID := c.Param("ticket_id")

	var ticket models.HubStockTicket
	if err := db.Preload("Hub").
		Preload("TargetHub").
		First(&ticket, ticketID).Error; err != nil {
		c.JSON(http.StatusNotFound, Response{
			Success: false,
			Error:   "Ticket not found",
		})
		return
	}

	// Create Excel workbook
	f := excelize.NewFile()

	// Add headers
	headers := []string{
		"Loại phiếu",
		"Kho yêu cầu",
		"Kho nhận hàng",
		"Mã hàng",
		"Tên hàng",
		"Đơn vị",
		"Số lượng tồn",
		"Số lượng yêu cầu",
		"Số lượng đã trao đổi",
		"Lý do",
		"Ghi chú",
	}

	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
	}

	// Add data rows
	localizeText := map[string]string{
		"stock_incorrect": "Sai số lượng",
		"stock_outdated":  "Hàng hết hạn",
		"stock_missed":    "Hàng thiếu",
		"export":          "Xuất kho",
		"import":          "Nhập kho",
		"trash":           "Hủy hàng",
	}

	for i, item := range ticket.Items.Data {
		row := i + 2
		rowData := []any{
			localizeText[ticket.TicketType],
			ticket.Hub.Name,
			ticket.TargetHub.Name,
			item.Code,
			item.Name,
			item.Unit,
			item.StockQuantity,
			item.ExchangeQuantity,
			item.ExchangedQuantity,
			translateReportTypes(item.StockReportTypes, localizeText),
			item.StockReportNote,
		}

		for j, value := range rowData {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			f.SetCellValue("Sheet1", cell, value)
		}
	}

	// Write to buffer
	var buf bytes.Buffer
	if err := f.Write(&buf); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// Upload file
	timestamp := time.Now().Format("060102150405")
	filename := fmt.Sprintf("hub_stock_tickets/%s_Ticket_%s_%s.xlsx",
		utils.SlugifyText(ticket.Hub.Name),
		ticket.TicketNumber,
		timestamp)

	fileURL, err := utils.UploadFile("nexpos-files", filename, buf.Bytes())
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    fileURL,
	})
}

// Helper functions

func translateReportTypes(types []string, translations map[string]string) string {
	var translated []string
	for _, t := range types {
		if trans, ok := translations[t]; ok {
			translated = append(translated, trans)
		} else {
			translated = append(translated, t)
		}
	}
	return strings.Join(translated, ", ")
}

func updateStock(tx *gorm.DB, ticket *models.HubStockTicket, item *models.HubStockTicketItem) error {
	switch ticket.TicketType {
	case "export":
		return handleExportStock(tx, ticket, item)
	case "import":
		return handleImportStock(tx, ticket, item)
	case "trash":
		return handleTrashStock(tx, ticket, item)
	default:
		return fmt.Errorf("invalid ticket type")
	}
}

func findMatchingItem(items []models.HubStockTicketItem, code string) *models.HubStockTicketItem {
	for _, item := range items {
		if item.Code == code {
			return &item
		}
	}
	return nil
}

func handleExportStock(tx *gorm.DB, ticket *models.HubStockTicket, item *models.HubStockTicketItem) error {
	// Update source hub stock
	var sourceStock models.HubStock
	err := tx.Where("hub_id = ? AND code = ?", ticket.HubID, item.Code).First(&sourceStock).Error
	if err == gorm.ErrRecordNotFound {
		sourceStock = models.HubStock{
			HubID:    ticket.HubID,
			Code:     item.Code,
			Name:     item.Name,
			Unit:     item.Unit,
			Quantity: 0,
		}
		if err := tx.Create(&sourceStock).Error; err != nil {
			return err
		}
	} else if err != nil {
		return err
	}

	// Create source hub stock history
	oldSourceQty := sourceStock.Quantity
	sourceStock.Quantity -= item.ExchangedQuantity
	if err := tx.Save(&sourceStock).Error; err != nil {
		return err
	}

	if err := tx.Create(&models.HubStockHistory{
		HubID:               models.ObjectID(ticket.HubID),
		Code:                item.Code,
		FromQuantity:        oldSourceQty,
		ToQuantity:          sourceStock.Quantity,
		UpdatedType:         "ticket",
		UpdatedTicketNumber: &ticket.TicketNumber,
		CreatedAt:           time.Now(),
	}).Error; err != nil {
		return err
	}

	// Update target hub stock
	if ticket.TargetHubID != nil {
		var targetStock models.HubStock
		err := tx.Where("hub_id = ? AND code = ?", *ticket.TargetHubID, item.Code).First(&targetStock).Error
		if err == gorm.ErrRecordNotFound {
			targetStock = models.HubStock{
				HubID:    *ticket.TargetHubID,
				Code:     item.Code,
				Name:     item.Name,
				Unit:     item.Unit,
				Quantity: 0,
			}
			if err := tx.Create(&targetStock).Error; err != nil {
				return err
			}
		} else if err != nil {
			return err
		}

		oldTargetQty := targetStock.Quantity
		targetStock.Quantity += item.ExchangedQuantity
		if err := tx.Save(&targetStock).Error; err != nil {
			return err
		}

		if err := tx.Create(&models.HubStockHistory{
			HubID:               models.ObjectID(*ticket.TargetHubID),
			Code:                item.Code,
			FromQuantity:        oldTargetQty,
			ToQuantity:          targetStock.Quantity,
			UpdatedType:         "ticket",
			UpdatedTicketNumber: &ticket.TicketNumber,
			CreatedAt:           time.Now(),
		}).Error; err != nil {
			return err
		}
	}

	return nil
}

func handleImportStock(tx *gorm.DB, ticket *models.HubStockTicket, item *models.HubStockTicketItem) error {
	// Update target hub stock (the receiving hub)
	var targetStock models.HubStock
	err := tx.Where("hub_id = ? AND code = ?", ticket.HubID, item.Code).First(&targetStock).Error
	if err == gorm.ErrRecordNotFound {
		targetStock = models.HubStock{
			HubID:    ticket.HubID,
			Code:     item.Code,
			Name:     item.Name,
			Unit:     item.Unit,
			Quantity: 0,
		}
		if err := tx.Create(&targetStock).Error; err != nil {
			return err
		}
	} else if err != nil {
		return err
	}

	oldTargetQty := targetStock.Quantity
	targetStock.Quantity += item.ExchangedQuantity
	if err := tx.Save(&targetStock).Error; err != nil {
		return err
	}

	if err := tx.Create(&models.HubStockHistory{
		HubID:               models.ObjectID(ticket.HubID),
		Code:                item.Code,
		FromQuantity:        oldTargetQty,
		ToQuantity:          targetStock.Quantity,
		UpdatedType:         "ticket",
		UpdatedTicketNumber: &ticket.TicketNumber,
		CreatedAt:           time.Now(),
	}).Error; err != nil {
		return err
	}

	// Update source hub stock if specified
	if ticket.TargetHubID != nil {
		var sourceStock models.HubStock
		err := tx.Where("hub_id = ? AND code = ?", *ticket.TargetHubID, item.Code).First(&sourceStock).Error
		if err == gorm.ErrRecordNotFound {
			sourceStock = models.HubStock{
				HubID:    *ticket.TargetHubID,
				Code:     item.Code,
				Name:     item.Name,
				Unit:     item.Unit,
				Quantity: 0,
			}
			if err := tx.Create(&sourceStock).Error; err != nil {
				return err
			}
		} else if err != nil {
			return err
		}

		oldSourceQty := sourceStock.Quantity
		sourceStock.Quantity -= item.ExchangedQuantity
		if err := tx.Save(&sourceStock).Error; err != nil {
			return err
		}

		if err := tx.Create(&models.HubStockHistory{
			HubID:               models.ObjectID(*ticket.TargetHubID),
			Code:                item.Code,
			FromQuantity:        oldSourceQty,
			ToQuantity:          sourceStock.Quantity,
			UpdatedType:         "ticket",
			UpdatedTicketNumber: &ticket.TicketNumber,
			CreatedAt:           time.Now(),
		}).Error; err != nil {
			return err
		}
	}

	return nil
}

func handleTrashStock(tx *gorm.DB, ticket *models.HubStockTicket, item *models.HubStockTicketItem) error {
	// Update hub stock
	var stock models.HubStock
	err := tx.Where("hub_id = ? AND code = ?", ticket.HubID, item.Code).First(&stock).Error
	if err == gorm.ErrRecordNotFound {
		stock = models.HubStock{
			HubID:    ticket.HubID,
			Code:     item.Code,
			Name:     item.Name,
			Unit:     item.Unit,
			Quantity: 0,
		}
		if err := tx.Create(&stock).Error; err != nil {
			return err
		}
	} else if err != nil {
		return err
	}

	oldQty := stock.Quantity
	stock.Quantity -= item.ExchangedQuantity
	if err := tx.Save(&stock).Error; err != nil {
		return err
	}

	// Create stock history
	return tx.Create(&models.HubStockHistory{
		HubID:               models.ObjectID(ticket.HubID),
		Code:                item.Code,
		FromQuantity:        oldQty,
		ToQuantity:          stock.Quantity,
		UpdatedType:         "ticket",
		UpdatedTicketNumber: &ticket.TicketNumber,
		CreatedAt:           time.Now(),
	}).Error
}
