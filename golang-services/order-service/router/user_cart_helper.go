package router

import (
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"os"
	"sort"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

func findItemInMenuByName(categories []models.Category, dish models.CartDish) *models.MenuItem {
	// Find by code
	if dish.Code != "" {
		for _, category := range categories {
			for _, item := range category.Items {
				if item.Code == dish.Code {
					return &item
				}
			}

			for _, subCategory := range category.SubCategories {
				for _, item := range subCategory.Items {
					if item.Code == dish.Code {
						return &item
					}
				}
			}
		}
	}

	// Find by name
	for _, category := range categories {
		for _, item := range category.Items {
			if utils.SlugifyText(item.Name) == utils.SlugifyText(dish.Name) {
				return &item
			}
		}

		for _, subCategory := range category.SubCategories {
			for _, item := range subCategory.Items {
				if utils.SlugifyText(item.Name) == utils.SlugifyText(dish.Name) {
					return &item
				}
			}
		}
	}

	return nil
}

type getHubsParams struct {
	Categories []models.Category
	Items      []models.CartDish
	Address    string
	HubIDs     []string
}

type HubStockInfo struct {
	HubID        string            `json:"hub_id"`
	HasStock     bool              `json:"has_stock"`
	MissingItems []models.CartDish `json:"missing_items"`
}

type HubWithStock struct {
	models.Hub
	HubStockInfo
	Distance *struct {
		Value float64 `json:"value"`
		Unit  string  `json:"unit"`
	} `json:"distance,omitempty"`
}

func getHubsHasStocks(db *gorm.DB, params getHubsParams) ([]HubWithStock, error) {
	if len(params.HubIDs) == 0 {
		return nil, nil
	}

	var hubStocks []models.HubStock
	if err := db.Where("hub_id IN ?", params.HubIDs).Find(&hubStocks).Error; err != nil {
		return nil, err
	}

	var hubs []models.Hub
	if err := db.Where("id IN ? AND status = ?", params.HubIDs, "active").Find(&hubs).Error; err != nil {
		return nil, err
	}

	hubStockInfos := make(map[string]HubStockInfo)
	for _, hubID := range params.HubIDs {
		hubStocksMap := make(map[string]float64)
		for _, stock := range hubStocks {
			if stock.HubID == hubID {
				hubStocksMap[stock.Code] = stock.Quantity
			}
		}

		var missingItems []models.CartDish
		for _, item := range params.Items {
			brandItem := findItemInMenuByName(params.Categories, item)
			if brandItem == nil || brandItem.QuantityUnlimited {
				continue
			}

			minQuantity := getItemStock(item, hubStocksMap)
			if minQuantity <= brandItem.QuantityMinimum {
				missingItems = append(missingItems, item)
			}
		}

		hubStockInfos[hubID] = HubStockInfo{
			HubID:        hubID,
			HasStock:     len(missingItems) == 0,
			MissingItems: missingItems,
		}
	}

	if params.Address != "" {
		userProvince, err := getAddressProvince(params.Address)
		if err != nil {
			return nil, err
		}
		if userProvince == "" {
			return nil, nil
		}

		var hubsWithDistance []HubWithStock
		for _, hub := range hubs {
			hubInfo := hubStockInfos[hub.ID]
			distance, err := getDistance(hub.Address, params.Address)
			if err != nil {
				continue
			}
			hubsWithDistance = append(hubsWithDistance, HubWithStock{
				Hub:          hub,
				HubStockInfo: hubInfo,
				Distance:     distance,
			})
		}

		sort.Slice(hubsWithDistance, func(i, j int) bool {
			return hubsWithDistance[i].Distance.Value < hubsWithDistance[j].Distance.Value
		})

		return hubsWithDistance, nil
	}

	var result []HubWithStock
	for _, hub := range hubs {
		result = append(result, HubWithStock{
			Hub:          hub,
			HubStockInfo: hubStockInfos[hub.ID],
		})
	}

	return result, nil
}

func getItemStock(item models.CartDish, stocks map[string]float64) float64 {
	if len(item.Combo) == 0 {
		return 0
	}

	var minQuantity *float64
	for _, combo := range item.Combo {
		if stockQuantity, ok := stocks[combo.Code]; ok {
			if minQuantity == nil || stockQuantity < *minQuantity {
				minQuantity = &stockQuantity
			}
		}
	}

	if minQuantity == nil {
		return 0
	}
	return *minQuantity
}

func getLocation(address string) (*gjson.Result, error) {
	url := fmt.Sprintf("https://maps.googleapis.com/maps/api/geocode/json?address=%s&key=%s",
		url.QueryEscape(address), os.Getenv("GOOGLE_MAP_API_KEY"))

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	result := gjson.ParseBytes(body)
	if !result.Get("results.0").Exists() {
		return nil, nil
	}

	firstResult := result.Get("results.0")
	return &firstResult, nil
}

func getAddressProvince(address string) (string, error) {
	location, err := getLocation(address)
	if err != nil || location == nil {
		return "", err
	}

	components := location.Get("address_components").Array()
	for _, comp := range components {
		types := comp.Get("types").Array()
		for _, t := range types {
			if t.String() == "administrative_area_level_1" {
				return comp.Get("long_name").String(), nil
			}
		}
	}
	return "", nil
}

func getDistance(fromAddress, toAddress string) (*struct {
	Value float64 `json:"value"`
	Unit  string  `json:"unit"`
}, error) {
	url := fmt.Sprintf("https://maps.googleapis.com/maps/api/distancematrix/json?origins=%s&destinations=%s&key=%s",
		url.QueryEscape(fromAddress), url.QueryEscape(toAddress), os.Getenv("GOOGLE_MAP_API_KEY"))

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	result := gjson.ParseBytes(body)
	if result.Get("status").String() != "OK" {
		return nil, fmt.Errorf("google api error: %s", result.Get("status").String())
	}

	distance := result.Get("rows.0.elements.0.distance")
	if !distance.Exists() {
		return nil, nil
	}

	return &struct {
		Value float64 `json:"value"`
		Unit  string  `json:"unit"`
	}{
		Value: distance.Get("value").Float(),
		Unit:  "meters",
	}, nil
}
