package router

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/bill"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DeliveryType represents the delivery method for an order
type DeliveryType string

// Constants for allowed delivery types
const (
	DeliveryTypePickUp   DeliveryType = "pick_up"
	DeliveryTypeDelivery DeliveryType = "delivery"
	DeliveryTypeTakeAway DeliveryType = "take_away"
)

// PaymentMethod represents the payment method for an order
type PaymentMethod string

// PosCreateOrderRequest defines the request structure for creating a POS order
type WebCreateOrderRequest struct {
	SiteID        string        `json:"site_id" binding:"required"`
	CustomerPhone string        `json:"customer_phone,omitempty"`
	CustomerName  string        `json:"customer_name,omitempty"`
	DeliveryType  DeliveryType  `json:"delivery_type" binding:"required,oneof=pick_up delivery take_away"`
	PaymentMethod PaymentMethod `json:"payment_method" binding:"required,oneof=CASH COD VNPAY MOMO BANK_TRANSFER"`
	VoucherCodes  []string      `json:"voucher_codes,omitempty"`
	// AppliedDiscount float64      `json:"applied_discount,omitempty"`
	OrderItems []struct {
		Code     string  `json:"code" binding:"required"`
		Quantity int     `json:"quantity" binding:"required,min=1"`
		Price    float64 `json:"price" binding:"required,min=0"`
		Note     string  `json:"note,omitempty"` // Note specific to this order item
	} `json:"order_items" binding:"required,min=1,dive"`
	CustomerAddress *struct {
		Lat  float64 `json:"lat" binding:"required_with=Long Text"`
		Long float64 `json:"long" binding:"required_with=Lat Text"`
		Text string  `json:"text" binding:"required_with=Lat Long"`
	} `json:"customer_address,omitempty" binding:"required_if=DeliveryType delivery"`
	PickupTime *string `json:"pickup_time,omitempty" binding:"required_if=DeliveryType pick_up"`
	Note       string  `json:"note,omitempty"`
}

// CreatePosOrder handles the request to create a new order through POS
func CreateWebOrder(c *gin.Context) {
	var req WebCreateOrderRequest

	// Validate request body against struct validation rules
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)

	// Declare orderID variable at function scope to use it after the transaction
	var orderID string

	// Start a database transaction
	err := db.Transaction(func(tx *gorm.DB) error {
		// Fetch site data
		var site models.Site
		if err := tx.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
			return fmt.Errorf("site not found: %w", err)
		}

		// Calculate subtotal and total
		var subtotal float64
		for _, item := range req.OrderItems {
			subtotal += item.Price * float64(item.Quantity)
		}

		total := subtotal // For POS orders, we assume no additional fees or taxes unless specified

		// Generate order ID
		// Format: {site_code}-{YYMMDD}-{index}
		now := time.Now()
		orderGroup := fmt.Sprintf("%s-%s", strings.ToUpper(site.Code), now.Format("060102"))

		// Get or create order index
		var siteOrderIndex models.SiteOrderIndex
		if err := tx.Where("site_id = ? AND group_name = ?", req.SiteID, orderGroup).
			FirstOrCreate(&siteOrderIndex, models.SiteOrderIndex{
				SiteID:    req.SiteID,
				GroupName: orderGroup,
				Index:     0,
			}).Error; err != nil {
			return fmt.Errorf("failed to generate order ID: %w", err)
		}

		// Increment index
		siteOrderIndex.Index++
		if err := tx.Save(&siteOrderIndex).Error; err != nil {
			return fmt.Errorf("failed to update order index: %w", err)
		}

		// Set the orderID - this will be accessible outside the transaction
		orderID = fmt.Sprintf("%s-%03d", orderGroup, siteOrderIndex.Index)

		// Create order dishes
		dishes := make([]models.OrderDish, len(req.OrderItems))
		for i, item := range req.OrderItems {
			dishes[i] = models.OrderDish{
				Name:          item.Code, // Using code as name temporarily
				Quantity:      int64(item.Quantity),
				Price:         item.Price * float64(item.Quantity),
				DiscountPrice: item.Price * float64(item.Quantity), // No discount by default
				Note:          item.Note,                           // Use the note from each order item
			}
		}

		// Determine status based on payment method
		var status string
		switch PaymentMethod(req.PaymentMethod) {
		case PaymentMethod(VendorCash):
			status = "FINISH"
		case PaymentMethod(VendorCOD):
			status = "PENDING"
		case PaymentMethod(VendorVNPay), PaymentMethod(VendorMomo):
			status = "WAITING_PAYMENT"
		default:
			status = "PENDING" // Fallback status
		}

		// Determine payment status based on payment method
		var paymentStatus string
		switch PaymentMethod(req.PaymentMethod) {
		case PaymentMethod(VendorCash):
			paymentStatus = "COMPLETED"
		case PaymentMethod(VendorCOD):
			paymentStatus = "PENDING"
		case PaymentMethod(VendorVNPay), PaymentMethod(VendorMomo):
			paymentStatus = "PENDING"
		default:
			paymentStatus = "PENDING"
		}

		// Create the order object with raw data
		rawOrderData := map[string]interface{}{
			"id":                 orderID,
			"order_id":           orderID,
			"source":             "local",
			"customer_name":      req.CustomerName,
			"customer_phone":     req.CustomerPhone,
			"customer_address":   req.CustomerAddress,
			"dishes":             dishes,
			"note":               req.Note,
			"total":              subtotal,
			"total_discount":     0,
			"total_for_biz":      total,
			"order_time":         now.Format(time.RFC3339),
			"order_time_sort":    now.Unix(),
			"delivery_time":      now.Format(time.RFC3339),
			"delivery_time_unix": now.Unix(),
			"pick_time":          now.Format(time.RFC3339),
			"raw":                req,
		}

		// Add payment information for all payment methods
		// rawOrderData["payments"] = []models.OrderPayment{
		// 	{
		// 		Vendor:      string(req.PaymentMethod),
		// 		Amount:      total,
		// 		OrderID:     orderID,
		// 		Status:      paymentStatus,
		// 		CreatedAt:   now,
		// 		UpdatedAt:   now,
		// 		Currency:    "VND",
		// 		Description: fmt.Sprintf("Payment for order %s", orderID),
		// 	},
		// }

		rawOrderData["payments"] = []models.Payment{
			{
				Method: string(req.PaymentMethod),
				Total:  total,
				Status: paymentStatus,
				Note:   fmt.Sprintf("Payment for order %s", orderID),
			},
		}

		// Use the standard mapping approach with the sdk/mapping package
		merchantOrder := &models.MerchantOrder{
			LongOrderID:  orderID,
			ShortOrderID: orderID,
			Source:       "local",
			DataInDetail: rawOrderData,
		}

		// Get the data mapping using the mapLocalOrder function
		dataMapping := mapping.MapOrder(merchantOrder).DataMapping.Data

		// Create the order object
		order := models.Order{
			OrderID:      orderID,
			ShortOrderID: orderID,
			Source:       "local",
			SiteID:       req.SiteID,
			HubID:        fmt.Sprintf("%v", site.HubID),
			Status:       status,
			DataMapping: models.JSONField[models.DataMapping]{
				Data: dataMapping,
			},
			Data: models.JSONField[map[string]any]{
				Data: rawOrderData,
			},
			CreatedAt: &now,
			UpdatedAt: &now,
		}

		// Save the order to database within the transaction
		if err := tx.Create(&order).Error; err != nil {
			return fmt.Errorf("failed to create order: %w", err)
		}

		// Create and persist order payment record to database
		// orderPayment := models.OrderPayment{
		// 	Vendor:      string(req.PaymentMethod),
		// 	OrderID:     orderID,
		// 	Amount:      total,
		// 	Status:      paymentStatus,
		// 	CreatedAt:   now,
		// 	UpdatedAt:   now,
		// 	Currency:    "VND",
		// 	Description: fmt.Sprintf("Payment for order %s", orderID),
		// }

		// // Save the payment record to database within the transaction
		// if err := tx.Create(&orderPayment).Error; err != nil {
		// 	return fmt.Errorf("failed to save payment record: %w", err)
		// }

		return nil // Transaction will be committed if no error is returned
	})

	// Handle transaction error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Re-fetch the order data using the orderID variable that was set in the transaction
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Order created but failed to retrieve order details",
		})
		return
	}

	// if order is already paid, generate bills
	if req.PaymentMethod == PaymentMethod(VendorCash) {
		totalPaidCompleted := order.Data.Data["total_for_biz"].(float64) // Use the saved order total

		// Send bills to printer and Zalo if applicable
		if totalPaidCompleted == order.Data.Data["total_for_biz"].(float64) {
			// Generate kitchen bill and send to channel
			_, err := bill.GenerateOrderBills(db, order.OrderID, bill.BillOptions{
				BillType:     "bill_for_kitchen",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, order.OrderID, bill.ChannelOptions{
					BillTemplate: "bill_for_kitchen",
					Bill:         true,
					Zalo:         true, // Send kitchen bills to Zalo
				})
			}

			// Generate complete bill and send to channel
			_, err = bill.GenerateOrderBills(db, order.OrderID, bill.BillOptions{
				BillType:     "bill_for_complete",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, order.OrderID, bill.ChannelOptions{
					BillTemplate: "bill_for_complete",
					Bill:         true,
				})
			}
		}
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"order_id":       order.OrderID,
		"message":        "Order created successfully",
		"payment_method": req.PaymentMethod,
		"status":         order.Status,
	})
}
