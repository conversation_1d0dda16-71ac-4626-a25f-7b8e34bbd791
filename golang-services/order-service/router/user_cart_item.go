package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"slices"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/thoas/go-funk"
)

func UpdateCartItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	// Request body binding
	var request struct {
		ID       string                `json:"_id"`     // cart dish id
		ItemID   string                `json:"item_id"` // menu item id
		Quantity int64                 `json:"quantity"`
		Note     string                `json:"note"`
		Options  [][]models.DishOption `json:"options"`
		Gifts    []models.CartDish     `json:"gifts"`
	}
	if err := c.<PERSON>(&request); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Get menu
	var siteMenu models.SiteMenuGroup
	if err := db.Where("site_id = ?", siteID).First(&siteMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	menuItems := buildMenuItems(&siteMenu)

	// Find or create cart
	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	isUpdateItem := request.ID != ""
	if isUpdateItem {
		// Find dish in cart
		var dishIndex = -1
		for i, dish := range cart.Dishes.Data {
			if dish.ID == request.ID && !dish.IsGift {
				dishIndex = i
				break
			}
		}
		if dishIndex < 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "dish_not_found",
			})
			return
		}

		// Calculate new quantity
		newQuantity := cart.Dishes.Data[dishIndex].Quantity + request.Quantity
		if newQuantity <= 0 {
			cart.Dishes.Data = slices.Delete(cart.Dishes.Data, dishIndex, dishIndex+1)
		} else {
			// Update item
			options := request.Options
			if len(options) == 0 {
				options = cart.Dishes.Data[dishIndex].Options
			}
			totalOptionPrice := calculateTotalOptionPrice(options)

			// Find menu item
			var menuItem models.MenuItem
			for _, item := range menuItems {
				if item.ID == cart.Dishes.Data[dishIndex].ItemID {
					menuItem = item
					break
				}
			}
			if menuItem.ID == "" {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "menu_item_is_missing",
				})
				return
			}

			cart.Dishes.Data[dishIndex].Quantity = newQuantity
			cart.Dishes.Data[dishIndex].UnitPrice = menuItem.Price
			cart.Dishes.Data[dishIndex].Price = float64(newQuantity) * (menuItem.Price + totalOptionPrice)

			if request.Note != "" {
				cart.Dishes.Data[dishIndex].Note = request.Note
			}
			if len(request.Options) > 0 {
				cart.Dishes.Data[dishIndex].Options = request.Options
			}
		}
	} else if request.ItemID != "" {
		// Add new item
		var menuItem models.MenuItem
		for _, item := range menuItems {
			if item.ID == request.ItemID {
				menuItem = item
				break
			}
		}
		if menuItem.ID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "menu_item_is_missing",
			})
			return
		}

		totalOptionPrice := calculateTotalOptionPrice(request.Options)
		price := float64(request.Quantity) * (menuItem.Price + totalOptionPrice)

		newDish := models.CartDish{
			ID:           uuid.New().String(), // Generate new ID
			ItemID:       menuItem.ID,
			Name:         menuItem.Name,
			CategoryName: menuItem.CategoryName,
			Quantity:     request.Quantity,
			UnitPrice:    menuItem.Price,
			Price:        price,
			Note:         request.Note,
			Options:      request.Options,
		}
		cart.Dishes.Data = append(cart.Dishes.Data, newDish)
	}

	validateCartItems(&cart, menuItems)
	if err := populateCartGifts(db, &site, user, &cart, request.Gifts); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cart,
	})
}

func CloneCartFromOrder(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	siteID := c.Param("site_id")

	var request struct {
		OrderID     string `json:"order_id" binding:"required"`
		ReplaceCart bool   `json:"replace_cart"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Find site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Find order
	var order models.Order
	if err := db.Where("order_id = ? AND site_id = ?", request.OrderID, siteID).First(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": "order_not_found"})
		return
	}

	// Get menu
	var siteMenu models.SiteMenuGroup
	if err := db.Where("site_id = ?", siteID).First(&siteMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}
	menuItems := buildMenuItems(&siteMenu)

	// Find or create cart
	var cart models.UserCart
	if err := db.Where(models.UserCart{
		UserID: user.ID,
		SiteID: site.ID,
		Status: "created",
	}).FirstOrCreate(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Get dishes from order
	var dishesFromOrder []models.CartDish
	for _, dish := range order.DataMapping.Data.Dishes {
		if dish.IsGift {
			continue
		}

		menuItem := funk.Find(menuItems, func(item models.MenuItem) bool {
			return item.Name == dish.Name && item.CategoryName == dish.CategoryName
		}).(models.MenuItem)

		if menuItem.ID == "" {
			continue
		}

		dishesFromOrder = append(dishesFromOrder, models.CartDish{
			ID:           uuid.New().String(),
			ItemID:       menuItem.ID,
			Name:         menuItem.Name,
			CategoryName: menuItem.CategoryName,
			Quantity:     dish.Quantity,
			UnitPrice:    menuItem.Price,
			Price:        float64(dish.Quantity) * menuItem.Price,
			Note:         dish.Note,
			Options:      dish.Options,
		})
	}

	// Combine dishes
	var cartDishes []models.CartDish
	if !request.ReplaceCart {
		cartDishes = funk.Filter(cart.Dishes.Data, func(d models.CartDish) bool {
			return !d.IsGift
		}).([]models.CartDish)
	}

	for _, dish := range dishesFromOrder {
		found := false
		for i, cartDish := range cartDishes {
			if cartDish.Name == dish.Name && cartDish.CategoryName == dish.CategoryName {
				cartDishes[i].Quantity += dish.Quantity
				cartDishes[i].Price += dish.Price
				found = true
				break
			}
		}
		if !found {
			cartDishes = append(cartDishes, dish)
		}
	}

	cart.Dishes = models.JSONField[[]models.CartDish]{Data: cartDishes}
	if err := db.Save(&cart).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cart,
	})
}
