package router

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"sync"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/disintegration/imaging"
	"github.com/gin-gonic/gin"
)

const (
	maxFileSize       = 10 << 20 // 10MB
	maxConcurrentJobs = 4
)

type Response struct {
	Success bool   `json:"success"`
	Data    any    `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

func UploadImage(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Invalid file upload",
		})
		return
	}

	if file.Size > maxFileSize {
		c.<PERSON>(http.StatusBadRequest, Response{
			Success: false,
			Message: "File size exceeds maximum limit",
		})
		return
	}

	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to process file",
		})
		return
	}
	defer fileReader.Close()

	buffer := make([]byte, file.Size)
	if _, err := io.ReadFull(fileReader, buffer); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to read file",
		})
		return
	}

	// Generate MD5 hash
	hash := md5.Sum(buffer)
	hashString := hex.EncodeToString(hash[:])
	fileName := fmt.Sprintf("%s-%s.jpg", filepath.Base(file.Filename), hashString)

	optimizedImage, err := resizeImage(buffer)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to process image",
		})
		return
	}

	bucket := middlewares.GetStorageClient(c).Bucket("nexpos-images")
	object := bucket.Object(fmt.Sprintf("images/%s", fileName))
	writer := object.NewWriter(c)

	if _, err = io.Copy(writer, bytes.NewBuffer(optimizedImage)); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to upload to storage",
		})
		return
	}

	if err := writer.Close(); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to finalize upload",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    fmt.Sprintf("https://storage.googleapis.com/nexpos-images/images/%s", fileName),
	})
}

func UploadMultipleImages(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "no_files_uploaded_or_invalid",
		})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "no_files_uploaded_or_invalid",
		})
		return
	}

	urls := make([]string, len(files))
	errors := make([]error, len(files))
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrentJobs)

	for i, file := range files {
		wg.Add(1)
		go func(index int, fileHeader *multipart.FileHeader) {
			defer wg.Done()
			semaphore <- struct{}{}        // Acquire semaphore
			defer func() { <-semaphore }() // Release semaphore

			url, err := processAndUploadImage(c, fileHeader)
			urls[index] = url
			errors[index] = err
		}(i, file)
	}

	wg.Wait()

	// Check for errors
	for _, err := range errors {
		if err != nil {
			c.JSON(http.StatusBadRequest, Response{
				Success: false,
				Message: "Failed to upload one or more files",
			})
			return
		}
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    urls,
	})
}

func UploadFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Invalid file",
		})
		return
	}

	if file.Size > maxFileSize {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "File size exceeds maximum limit",
		})
		return
	}

	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to process file",
		})
		return
	}
	defer fileReader.Close()

	buffer := make([]byte, file.Size)
	if _, err := io.ReadFull(fileReader, buffer); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to read file",
		})
		return
	}

	hash := md5.Sum(buffer)
	hashString := hex.EncodeToString(hash[:])
	fileName := fmt.Sprintf("uploads/%s_%s", hashString, file.Filename)

	bucket := middlewares.GetStorageClient(c).Bucket("nexpos-files")
	object := bucket.Object(fileName)
	writer := object.NewWriter(c)

	if _, err = io.Copy(writer, bytes.NewBuffer(buffer)); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to upload to storage",
		})
		return
	}

	if err := writer.Close(); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Failed to finalize upload",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    fmt.Sprintf("https://storage.googleapis.com/nexpos-files/%s", fileName),
	})
}

func GetFileHeaders(c *gin.Context) {
	var request struct {
		File string `json:"file" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "Invalid file URL",
		})
		return
	}

	client := &http.Client{}
	req, err := http.NewRequestWithContext(c, "HEAD", request.File, nil)
	if err != nil {
		c.JSON(http.StatusOK, Response{
			Success: true,
			Data:    map[string]string{},
		})
		return
	}

	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusOK, Response{
			Success: true,
			Data:    map[string]string{},
		})
		return
	}
	defer resp.Body.Close()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    resp.Header,
	})
}

func resizeImage(image []byte) ([]byte, error) {
	img, err := imaging.Decode(bytes.NewReader(image))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %v", err)
	}

	resized := imaging.Resize(img, 1080, 0, imaging.Lanczos)

	buf := new(bytes.Buffer)
	options := imaging.JPEGQuality(85)
	err = imaging.Encode(buf, resized, imaging.JPEG, options)
	if err != nil {
		return nil, fmt.Errorf("failed to encode image: %v", err)
	}

	return buf.Bytes(), nil
}

func processAndUploadImage(c *gin.Context, fileHeader *multipart.FileHeader) (string, error) {
	fileReader, err := fileHeader.Open()
	if err != nil {
		return "", err
	}
	defer fileReader.Close()

	buffer := make([]byte, fileHeader.Size)
	if _, err := io.ReadFull(fileReader, buffer); err != nil {
		return "", err
	}

	hash := md5.Sum(buffer)
	hashString := hex.EncodeToString(hash[:])
	fileName := fmt.Sprintf("%s-%s.jpg", filepath.Base(fileHeader.Filename), hashString)

	optimizedImage, err := resizeImage(buffer)
	if err != nil {
		return "", err
	}

	bucket := middlewares.GetStorageClient(c).Bucket("nexpos-images")
	object := bucket.Object(fmt.Sprintf("images/%s", fileName))
	writer := object.NewWriter(c)

	if _, err = io.Copy(writer, bytes.NewBuffer(optimizedImage)); err != nil {
		return "", err
	}

	if err := writer.Close(); err != nil {
		return "", err
	}

	return fmt.Sprintf("https://storage.googleapis.com/nexpos-images/images/%s", fileName), nil
}
