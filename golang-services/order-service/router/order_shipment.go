package router

import (
	"encoding/json"
	"fmt"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"

	"reflect"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery/ahamove"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery/grab_express"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery/viettel_post"
)

// DeliveryProviderFactory defines an interface for creating delivery provider clients
type DeliveryProviderFactory interface {
	// CreateAhamoveClient creates and returns an Ahamove client
	CreateAhamoveClient() delivery.Provider

	// CreateGrabExpressClient creates and returns a Grab Express client
	CreateGrabExpressClient() delivery.Provider

	// CreateViettelPostClient creates and returns a Viettel Post client
	CreateViettelPostClient() delivery.Provider

	// GetProvider returns the appropriate provider based on vendor name
	GetProvider(vendor string) delivery.Provider
}

// DefaultDeliveryProviderFactory is the default implementation of DeliveryProviderFactory
type DefaultDeliveryProviderFactory struct{}

// NewDeliveryProviderFactory creates a new DeliveryProviderFactory
func NewDeliveryProviderFactory() DeliveryProviderFactory {
	return &DefaultDeliveryProviderFactory{}
}

// CreateAhamoveClient creates and returns an Ahamove client
func (f *DefaultDeliveryProviderFactory) CreateAhamoveClient() delivery.Provider {
	return ahamove.NewAhamoveClient()
}

// CreateGrabExpressClient creates and returns a Grab Express client
func (f *DefaultDeliveryProviderFactory) CreateGrabExpressClient() delivery.Provider {
	return grab_express.NewClient()
}

// CreateViettelPostClient creates and returns a Viettel Post client
func (f *DefaultDeliveryProviderFactory) CreateViettelPostClient() delivery.Provider {
	return viettel_post.NewClient()
}

// GetProvider returns the appropriate provider based on vendor name
func (f *DefaultDeliveryProviderFactory) GetProvider(vendor string) delivery.Provider {
	switch vendor {
	case "ahamove":
		return f.CreateAhamoveClient()
	case "grab_express":
		return f.CreateGrabExpressClient()
	case "viettel_post":
		return f.CreateViettelPostClient()
	default:
		return nil
	}
}

// ScheduleInfo represents a delivery schedule
type ScheduleInfo struct {
	FromTime     string `json:"from_time"`
	ToTime       string `json:"to_time"`
	FromDateTime string `json:"from_date_time"`
	ToDateTime   string `json:"to_date_time"`
}

// ShipmentData represents the structure of the shipment data in our Order object
type ShipmentData struct {
	Vendor      string             `json:"vendor"`
	ShipmentID  string             `json:"shipment_id"`
	TrackingURL string             `json:"tracking_url"`
	Price       float64            `json:"price"`
	Schedule    *ScheduleInfo      `json:"schedule,omitempty"`
	Status      string             `json:"status"`
	Note        string             `json:"note,omitempty"`
	PromoCode   string             `json:"promo_code,omitempty"`
	Dimensions  models.Dimensions  `json:"dimensions,omitempty"`
	From        models.Location    `json:"from,omitempty"`
	To          models.Location    `json:"to,omitempty"`
	Service     models.ShipService `json:"service,omitempty"`
}

// GetOrderShipments handles fetching shipment options for an order
func GetOrderShipments(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")
	dimensionsStr := c.Query("dimensions")

	// Parse dimensions if provided
	dimensions, err := parseDimensions(dimensionsStr)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "error": "invalid_dimensions_format"})
		return
	}

	// Fetch order from database
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "order_not_found"})
		return
	}

	// Fetch site information
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Get shipment data from order
	shipmentData, err := getShipmentDataFromOrder(order)
	if err != nil {
		c.JSON(500, gin.H{"success": false, "error": "failed_to_parse_shipment_data"})
		return
	}

	// Check if it's a delivery or pickup order
	isDelivery := isDeliveryOrder(shipmentData)
	if !isDelivery {
		// For pickup orders, return pickup slots
		slots := utils.GetPickupSlots()
		c.JSON(200, gin.H{
			"success": true,
			"data":    slots,
		})
		return
	}

	// Add dimensions to shipment if provided
	if dimensionsStr != "" {
		shipmentData.Dimensions = dimensions
	}

	// Get shipping options from providers
	shipmentOptions, err := getShippingOptionsFromProviders(db, site, shipmentData, dimensions)
	if err != nil {
		c.JSON(500, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Format response
	c.JSON(200, gin.H{
		"success": true,
		"data":    formatShipmentOptions(shipmentOptions),
	})
}

// CreateOrderShipments handles creating a shipment for an order
func CreateOrderShipments(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")

	// Parse request body
	var reqBody struct {
		Vendor        string             `json:"vendor"`
		Dimensions    map[string]float64 `json:"dimensions"`
		IsSubShipment bool               `json:"is_sub_shipment"`
		Raw           map[string]any     `json:"raw"`
		ShipmentIDs   []string           `json:"shipment_ids"`
	}

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.JSON(400, gin.H{"success": false, "error": "invalid_request_body"})
		return
	}

	// Fetch order, site, and hub
	order, site, hub, err := fetchOrderData(db, orderID)
	if err != nil {
		c.JSON(404, gin.H{"success": false, "error": err.Error()})
		return
	}

	// Get shipment data from order
	shipmentData, err := getShipmentDataFromOrder(order)
	if err != nil {
		c.JSON(500, gin.H{"success": false, "error": "failed_to_parse_shipment_data"})
		return
	}

	// Update shipment information
	shipmentData.Vendor = reqBody.Vendor
	updateShipmentFromRaw(&shipmentData, reqBody.Raw)

	// Calculate remaining payment (COD)
	totalRemain := calculateRemainingPayment(order, reqBody.IsSubShipment)

	// Check if we support this vendor
	if isSupportedVendor(reqBody.Vendor) {
		// Create shipment using provider
		err := createShipmentWithProvider(c, db, order, site, hub, shipmentData, reqBody, totalRemain)
		if err != nil {
			return // Error already sent to client
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    order,
	})
}

// CreateOrderManualShipments handles creating a manual shipment record for an order
func CreateOrderManualShipments(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")

	// Parse request body
	var reqBody struct {
		Vendor     string  `json:"vendor"`
		COD        float64 `json:"cod"`
		Price      float64 `json:"price"`
		ShipmentID string  `json:"shipment_id"`
		ToAddress  string  `json:"to_address"`
		ToName     string  `json:"to_name"`
		ToPhone    string  `json:"to_phone"`
	}

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.JSON(400, gin.H{"success": false, "error": "invalid_request_body"})
		return
	}

	// Fetch order and hub
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "order_not_found"})
		return
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "hub_not_found"})
		return
	}

	// Create manual order shipment record
	orderShipment := createManualOrderShipment(reqBody, order.OrderID, hub)
	if err := db.Create(&orderShipment).Error; err != nil {
		c.JSON(500, gin.H{"success": false, "error": "failed_to_create_shipment"})
		return
	}

	// Get existing shipment data and update it
	shipmentData, _ := getShipmentDataFromOrder(order)
	updateShipmentDataForManual(&shipmentData, reqBody, hub)

	// Create OrderShipment object for the order
	orderShipmentObj := createOrderShipmentFromData(shipmentData, reqBody, hub)

	// Assign to order.Shipment
	order.Shipment = models.JSONField[models.OrderShipment]{
		Data: orderShipmentObj,
	}

	// Save the updated order
	if err := db.Save(&order).Error; err != nil {
		c.JSON(500, gin.H{"success": false, "error": "failed_to_update_order"})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    order,
	})
}

// CancelOrderShipments handles canceling a shipment for an order
func CancelOrderShipments(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")

	// Parse request body
	var reqBody struct {
		ShipmentID string `json:"shipment_id"`
		Vendor     string `json:"vendor"`
	}

	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.JSON(400, gin.H{"success": false, "error": "invalid_request_body"})
		return
	}

	// Fetch order and site
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "order_not_found"})
		return
	}

	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "site_not_found"})
		return
	}

	// Check if shipment ID is provided
	if reqBody.ShipmentID == "" {
		c.JSON(200, gin.H{"success": true, "data": order})
		return
	}

	// Get delivery provider
	providerFactory := NewDeliveryProviderFactory()
	provider := providerFactory.GetProvider(reqBody.Vendor)
	if provider == nil {
		c.JSON(400, gin.H{"success": false, "error": "provider_not_found"})
		return
	}

	// Get the order shipment record
	var orderShipment models.OrderShipment
	if err := db.Where("shipment_id = ?", reqBody.ShipmentID).First(&orderShipment).Error; err != nil {
		c.JSON(404, gin.H{"success": false, "error": "shipment_not_found"})
		return
	}

	// Get token for the vendor
	siteToken, _ := token.GetTokenBySite(db, site, reqBody.Vendor)
	if siteToken == nil {
		c.JSON(400, gin.H{"success": false, "error": "token_not_found"})
		return
	}

	// Cancel the shipment
	cancelResp, err := provider.CancelOrder(&models.ShipToken{AccessToken: siteToken.AccessToken}, reqBody.ShipmentID)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "error": err.Error()})
		return
	}

	if !cancelResp.Success {
		c.JSON(400, gin.H{"success": false, "error": cancelResp.Message})
		return
	}

	// Update shipment statuses
	if err := updateShipmentStatusesToCancelled(db, order, orderShipment); err != nil {
		c.JSON(500, gin.H{"success": false, "error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    order,
	})
}

// Helper functions

// parseDimensions parses dimensions from a string
func parseDimensions(dimensionsStr string) (models.Dimensions, error) {
	var dimensions models.Dimensions
	if dimensionsStr == "" {
		return dimensions, nil
	}

	// First try to unmarshal directly to Dimensions struct
	if err := json.Unmarshal([]byte(dimensionsStr), &dimensions); err != nil {
		// If that fails, try parsing as map and then convert
		var dimMap map[string]float64
		if err := json.Unmarshal([]byte(dimensionsStr), &dimMap); err != nil {
			return dimensions, err
		}

		// Convert map to Dimensions struct
		if width, ok := dimMap["width"]; ok {
			dimensions.Width = width
		}
		if length, ok := dimMap["length"]; ok {
			dimensions.Length = length
		}
		if height, ok := dimMap["height"]; ok {
			dimensions.Height = height
		}
		if weight, ok := dimMap["weight"]; ok {
			dimensions.Weight = weight
		}
	}

	return dimensions, nil
}

// getShipmentDataFromOrder extracts ShipmentData from an Order
func getShipmentDataFromOrder(order models.Order) (ShipmentData, error) {
	shipmentData := ShipmentData{}

	// Check if order.Shipment exists and has data
	if reflect.ValueOf(order.Shipment).IsZero() || reflect.ValueOf(order.Shipment.Data).IsZero() {
		return shipmentData, nil
	}

	// Marshal and unmarshal to convert between types
	shipmentBytes, err := json.Marshal(order.Shipment.Data)
	if err != nil {
		return shipmentData, err
	}

	if err := json.Unmarshal(shipmentBytes, &shipmentData); err != nil {
		return shipmentData, err
	}

	return shipmentData, nil
}

// isDeliveryOrder checks if an order is for delivery (vs pickup)
func isDeliveryOrder(shipmentData ShipmentData) bool {
	if shipmentData.Service.Code == "pick_up" {
		return false
	}
	return true
}

// getShippingOptionsFromProviders fetches shipping options from all providers
func getShippingOptionsFromProviders(db *gorm.DB, site models.Site, shipmentData ShipmentData, dimensions models.Dimensions) ([]models.ShipmentOption, error) {
	// Get tokens for different providers
	ahamoveToken, _ := token.GetTokenBySite(db, site, "ahamove")
	grabExpressToken, _ := token.GetTokenBySite(db, site, "grab_express")
	viettelPostToken, _ := token.GetTokenBySite(db, site, "viettel_post")

	// Create shipment request
	shipmentReq := &models.GetShipmentsRequest{
		From:       models.Location(shipmentData.From),
		To:         models.Location(shipmentData.To),
		Dimensions: dimensions,
		PromoCode:  shipmentData.PromoCode,
	}

	// Create delivery provider factory
	providerFactory := NewDeliveryProviderFactory()

	// Get shipments from each provider
	var ahamoveShipments, grabShipments, grab2hShipments, viettelPostShipments []models.ShipmentOption

	// Use Ahamove provider if token exists
	if ahamoveToken != nil {
		ahamoveProvider := providerFactory.CreateAhamoveClient()
		ahamoveShipments, _ = ahamoveProvider.GetShipments(&models.ShipToken{AccessToken: ahamoveToken.AccessToken}, shipmentReq)
	}

	// Use Grab Express provider if token exists
	if grabExpressToken != nil {
		grabExpressProvider := providerFactory.CreateGrabExpressClient()
		grabShipments, _ = grabExpressProvider.GetShipments(&models.ShipToken{AccessToken: grabExpressToken.AccessToken}, shipmentReq)
	}

	// Use Viettel Post provider if token exists
	if viettelPostToken != nil {
		viettelPostProvider := providerFactory.CreateViettelPostClient()
		viettelPostShipments, _ = viettelPostProvider.GetShipments(&models.ShipToken{AccessToken: viettelPostToken.AccessToken}, shipmentReq)
	}

	// Combine all shipment options
	allShipments := []models.ShipmentOption{}
	allShipments = append(allShipments, ahamoveShipments...)
	allShipments = append(allShipments, grabShipments...)
	allShipments = append(allShipments, grab2hShipments...)
	allShipments = append(allShipments, viettelPostShipments...)

	// Filter only options with price > 0
	var filteredResult []models.ShipmentOption
	for _, shipment := range allShipments {
		if shipment.Price > 0 {
			filteredResult = append(filteredResult, shipment)
		}
	}

	return filteredResult, nil
}

// formatShipmentOptions converts ShipmentOption to map for response
func formatShipmentOptions(options []models.ShipmentOption) []map[string]any {
	result := make([]map[string]any, len(options))
	for i, option := range options {
		result[i] = map[string]any{
			"vendor":      option.Vendor,
			"code":        option.Code,
			"name":        option.Name,
			"description": option.Description,
			"price":       option.Price,
			"options":     option.ExtraServices,
			"raw":         option.Raw,
		}
	}
	return result
}

// fetchOrderData retrieves order, site and hub information
func fetchOrderData(db *gorm.DB, orderID string) (models.Order, models.Site, models.Hub, error) {
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return order, models.Site{}, models.Hub{}, fmt.Errorf("order_not_found")
	}

	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return order, site, models.Hub{}, fmt.Errorf("site_not_found")
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return order, site, hub, fmt.Errorf("hub_not_found")
	}

	return order, site, hub, nil
}

// updateShipmentFromRaw updates a ShipmentData object from raw request data
func updateShipmentFromRaw(shipmentData *ShipmentData, raw map[string]any) {
	// Copy service data
	shipmentData.Service = models.ShipService{}
	if code, ok := raw["code"].(string); ok {
		shipmentData.Service.Code = code
	}

	// Set status
	shipmentData.Status = "ORDER_CREATED"

	// Handle schedule if provided
	if schedule, exists := raw["schedule"].(map[string]any); exists {
		scheduleInfo := &ScheduleInfo{}
		if fromTime, ok := schedule["from_time"].(string); ok {
			scheduleInfo.FromTime = fromTime
		}
		if toTime, ok := schedule["to_time"].(string); ok {
			scheduleInfo.ToTime = toTime
		}
		if fromDateTime, ok := schedule["from_date_time"].(string); ok {
			scheduleInfo.FromDateTime = fromDateTime
		}
		if toDateTime, ok := schedule["to_date_time"].(string); ok {
			scheduleInfo.ToDateTime = toDateTime
		}
		shipmentData.Schedule = scheduleInfo
	}
}

// calculateRemainingPayment determines how much is left to pay as COD
func calculateRemainingPayment(order models.Order, isSubShipment bool) float64 {
	totalPaid := 0.0
	for _, payment := range order.DataMapping.Data.Payments {
		if payment.Status == "COMPLETED" && payment.Method != "CASH" && payment.Method != "COD" {
			totalPaid += payment.Total
		}
	}

	totalRemain := order.DataMapping.Data.TotalForBiz - totalPaid
	if totalRemain < 0 {
		totalRemain = 0
	}
	if isSubShipment {
		totalRemain = 0
	}

	return totalRemain
}

// isSupportedVendor checks if a shipping vendor is supported
func isSupportedVendor(vendor string) bool {
	supportedVendors := map[string]bool{
		"ahamove":         true,
		"grab_express":    true,
		"grab_express_2h": true,
		"viettel_post":    true,
	}
	return supportedVendors[vendor]
}

// createShipmentWithProvider uses a shipping provider to create a shipment
func createShipmentWithProvider(c *gin.Context, db *gorm.DB, order models.Order, site models.Site, hub models.Hub, shipmentData ShipmentData, reqBody struct {
	Vendor        string             `json:"vendor"`
	Dimensions    map[string]float64 `json:"dimensions"`
	IsSubShipment bool               `json:"is_sub_shipment"`
	Raw           map[string]any     `json:"raw"`
	ShipmentIDs   []string           `json:"shipment_ids"`
}, totalRemain float64) error {
	// Get token for the vendor
	siteToken, _ := token.GetTokenBySite(db, site, reqBody.Vendor)
	//siteToken := site.GetToken(reqBody.Vendor)
	if siteToken == nil {
		c.JSON(400, gin.H{"success": false, "error": "token_not_found"})
		return fmt.Errorf("token_not_found")
	}

	// Get the appropriate delivery provider
	providerFactory := NewDeliveryProviderFactory()
	provider := providerFactory.GetProvider(reqBody.Vendor)
	if provider == nil {
		c.JSON(400, gin.H{"success": false, "error": "provider_not_found"})
		return fmt.Errorf("provider_not_found")
	}

	// Extract dishes and prepare service information
	dishItems, service, dimensions := prepareShipmentData(order, shipmentData, reqBody.Dimensions)

	// Create shipment request
	shipmentReq := &models.CreateShipmentRequest{
		PromoCode:      shipmentData.PromoCode,
		Dishes:         dishItems,
		From:           models.Location{Address: hub.Address, Phone: hub.Phone, Name: hub.Name},
		To:             models.Location(shipmentData.To),
		Service:        service,
		COD:            totalRemain,
		Note:           fmt.Sprintf("Đến lấy hàng, đọc mã đơn hàng: %s cho thu ngân", order.OrderID),
		TrackingNumber: order.OrderID,
		Dimensions:     dimensions,
	}

	// Create the shipment
	shipResp, err := provider.CreateOrder(&models.ShipToken{AccessToken: siteToken.AccessToken}, shipmentReq)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "error": err.Error()})
		return err
	}

	// Update shipment data
	shipmentData.ShipmentID = shipResp.ShipmentID
	shipmentData.TrackingURL = shipResp.TrackingURL
	shipmentData.Price = shipResp.Price

	// Create and save order shipment record
	if err := saveOrderShipment(c, db, order, hub, shipmentData, reqBody, shipResp, totalRemain); err != nil {
		return err
	}

	return nil
}

// prepareShipmentData extracts dishes, service and dimensions for a shipment
func prepareShipmentData(order models.Order, shipmentData ShipmentData, dimensionsMap map[string]float64) ([]models.Dish, models.ShipService, models.Dimensions) {
	// Extract dish data
	var dishItems []models.Dish
	dishesRaw, _ := json.Marshal(order.DataMapping.Data.Dishes)
	_ = json.Unmarshal(dishesRaw, &dishItems)

	// Create service struct with extra services
	service := shipmentData.Service

	// Create dimensions struct
	dimensions := models.Dimensions{}
	if dimensionsMap != nil {
		if width, ok := dimensionsMap["width"]; ok {
			dimensions.Width = width
		}
		if length, ok := dimensionsMap["length"]; ok {
			dimensions.Length = length
		}
		if height, ok := dimensionsMap["height"]; ok {
			dimensions.Height = height
		}
		if weight, ok := dimensionsMap["weight"]; ok {
			dimensions.Weight = weight
		}
	}

	return dishItems, service, dimensions
}

// saveOrderShipment creates/updates shipment record and updates the order
func saveOrderShipment(c *gin.Context, db *gorm.DB, order models.Order, hub models.Hub, shipmentData ShipmentData, reqBody struct {
	Vendor        string             `json:"vendor"`
	Dimensions    map[string]float64 `json:"dimensions"`
	IsSubShipment bool               `json:"is_sub_shipment"`
	Raw           map[string]any     `json:"raw"`
	ShipmentIDs   []string           `json:"shipment_ids"`
}, shipResp *models.CreateShipmentResponse, totalRemain float64) error {
	// Create OrderShipment object for the database
	orderShipment := models.OrderShipment{
		OrderID:       order.OrderID,
		IsSubShipment: reqBody.IsSubShipment,
		COD:           totalRemain,
		Vendor:        reqBody.Vendor,
		ShipmentID:    shipResp.ShipmentID,
		FromName:      hub.Name,
		FromPhone:     hub.Phone,
		FromAddress:   hub.Address,
		ToName:        shipmentData.To.Name,
		ToPhone:       shipmentData.To.Phone,
		ToAddress:     shipmentData.To.Address,
		TrackingURL:   shipResp.TrackingURL,
		PriceForUser:  shipmentData.Price,
		Price:         shipResp.Price,
		Status:        "ORDER_CREATED",
	}

	// Marshal webhook data
	webhookJSON, _ := json.Marshal([]any{shipResp.RawResponse})
	orderShipment.Webhooks = json.RawMessage(webhookJSON)

	// Upsert order shipment record
	var existingShipment models.OrderShipment
	result := db.Where("shipment_id = ?", shipResp.ShipmentID).First(&existingShipment)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			if err := db.Create(&orderShipment).Error; err != nil {
				c.JSON(500, gin.H{"success": false, "error": "failed_to_create_shipment_record"})
				return err
			}
		} else {
			c.JSON(500, gin.H{"success": false, "error": "database_error"})
			return result.Error
		}
	} else {
		if err := db.Model(&existingShipment).Updates(orderShipment).Error; err != nil {
			c.JSON(500, gin.H{"success": false, "error": "failed_to_update_shipment_record"})
			return err
		}
	}

	// Create OrderShipment object for the order
	orderShipmentObj := models.OrderShipment{
		Vendor:       shipmentData.Vendor,
		ShipmentID:   shipmentData.ShipmentID,
		TrackingURL:  shipmentData.TrackingURL,
		Status:       shipmentData.Status,
		PriceForUser: shipmentData.Price,
		Price:        shipmentData.Price,
		ToName:       shipmentData.To.Name,
		ToPhone:      shipmentData.To.Phone,
		ToAddress:    shipmentData.To.Address,
		FromName:     hub.Name,
		FromPhone:    hub.Phone,
		FromAddress:  hub.Address,
	}

	// Assign to order.Shipment
	order.Shipment = models.JSONField[models.OrderShipment]{
		Data: orderShipmentObj,
	}

	// Save the updated order
	if err := db.Save(&order).Error; err != nil {
		c.JSON(500, gin.H{"success": false, "error": "failed_to_update_order"})
		return err
	}

	return nil
}

// createManualOrderShipment creates a manual shipment record
func createManualOrderShipment(reqBody struct {
	Vendor     string  `json:"vendor"`
	COD        float64 `json:"cod"`
	Price      float64 `json:"price"`
	ShipmentID string  `json:"shipment_id"`
	ToAddress  string  `json:"to_address"`
	ToName     string  `json:"to_name"`
	ToPhone    string  `json:"to_phone"`
}, orderID string, hub models.Hub) models.OrderShipment {
	return models.OrderShipment{
		IsManual:     true,
		COD:          reqBody.COD,
		OrderID:      orderID,
		Vendor:       reqBody.Vendor,
		ShipmentID:   reqBody.ShipmentID,
		FromName:     hub.Name,
		FromPhone:    hub.Phone,
		FromAddress:  hub.Address,
		ToName:       reqBody.ToName,
		ToPhone:      reqBody.ToPhone,
		ToAddress:    reqBody.ToAddress,
		PriceForUser: reqBody.Price,
		Price:        reqBody.Price,
		Status:       "COMPLETED",
	}
}

// updateShipmentDataForManual updates shipment data for manual delivery
func updateShipmentDataForManual(shipmentData *ShipmentData, reqBody struct {
	Vendor     string  `json:"vendor"`
	COD        float64 `json:"cod"`
	Price      float64 `json:"price"`
	ShipmentID string  `json:"shipment_id"`
	ToAddress  string  `json:"to_address"`
	ToName     string  `json:"to_name"`
	ToPhone    string  `json:"to_phone"`
}, hub models.Hub) {
	shipmentData.ShipmentID = reqBody.ShipmentID
	shipmentData.Note = fmt.Sprintf("Vận đơn ngoài hệ thống: %s %s", reqBody.Vendor, reqBody.ShipmentID)
	shipmentData.Vendor = reqBody.Vendor
	shipmentData.Status = "COMPLETED"
	shipmentData.To = models.Location{
		Name:    reqBody.ToName,
		Phone:   reqBody.ToPhone,
		Address: reqBody.ToAddress,
	}
	shipmentData.From = models.Location{
		Name:    hub.Name,
		Phone:   hub.Phone,
		Address: hub.Address,
	}
}

// createOrderShipmentFromData creates an OrderShipment object from ShipmentData
func createOrderShipmentFromData(shipmentData ShipmentData, reqBody struct {
	Vendor     string  `json:"vendor"`
	COD        float64 `json:"cod"`
	Price      float64 `json:"price"`
	ShipmentID string  `json:"shipment_id"`
	ToAddress  string  `json:"to_address"`
	ToName     string  `json:"to_name"`
	ToPhone    string  `json:"to_phone"`
}, hub models.Hub) models.OrderShipment {
	return models.OrderShipment{
		Vendor:       shipmentData.Vendor,
		ShipmentID:   shipmentData.ShipmentID,
		Status:       shipmentData.Status,
		IsManual:     true,
		ToName:       reqBody.ToName,
		ToPhone:      reqBody.ToPhone,
		ToAddress:    reqBody.ToAddress,
		FromName:     hub.Name,
		FromPhone:    hub.Phone,
		FromAddress:  hub.Address,
		PriceForUser: reqBody.Price,
		Price:        reqBody.Price,
	}
}

// updateShipmentStatusesToCancelled updates shipment statuses to "CANCELLED"
func updateShipmentStatusesToCancelled(db *gorm.DB, order models.Order, orderShipment models.OrderShipment) error {
	// Update shipment status
	orderShipment.Status = "CANCELLED"
	if err := db.Save(&orderShipment).Error; err != nil {
		return fmt.Errorf("failed_to_update_shipment")
	}

	// Update order shipment status
	shipmentData := ShipmentData{}
	if !reflect.ValueOf(order.Shipment).IsZero() {
		shipmentBytes, err := json.Marshal(order.Shipment.Data)
		if err != nil {
			return fmt.Errorf("failed_to_marshal_shipment_data")
		}

		if err := json.Unmarshal(shipmentBytes, &shipmentData); err != nil {
			return fmt.Errorf("failed_to_parse_shipment_data")
		}
	}

	shipmentData.Status = "CANCELLED"

	// Fix order.Shipment assignment
	shipmentJSON, err := json.Marshal(shipmentData)
	if err != nil {
		return fmt.Errorf("failed_to_marshal_updated_shipment_data")
	}

	var shipmentMap map[string]any
	if err := json.Unmarshal(shipmentJSON, &shipmentMap); err != nil {
		return fmt.Errorf("failed_to_unmarshal_to_map")
	}
	orderShipmentObj := models.OrderShipment{
		Vendor:     shipmentData.Vendor,
		ShipmentID: shipmentData.ShipmentID,
		Status:     "CANCELLED",
	}

	// Assign using same fix as above
	order.Shipment = models.JSONField[models.OrderShipment]{
		Data: orderShipmentObj,
	}

	if err := db.Save(&order).Error; err != nil {
		return fmt.Errorf("failed_to_update_order")
	}

	return nil
}
