package router

import (
	"fmt"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/bill"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PrintOrder prints an order bill
func PrintOrder(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.J<PERSON>N(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Get template from query parameter
	template := c.Query("template")
	if template == "" {
		template = "bill_for_kitchen"
	}

	// Check if this is a preview
	preview := c.Query("preview") == "true"

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Generate bill URL by calling the utility function
	// This will update the order with the bill URL
	updatedOrder, err := bill.GenerateOrderBills(db, orderID, bill.BillOptions{
		BillType:     template,
		GenerateBill: true,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "bill_generation_failed: " + err.Error(),
		})
		return
	}

	// If not preview, send bill to channel
	if !preview {
		err := bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
			BillTemplate: template,
			Bill:         true,
			Zalo:         template == "bill_for_kitchen", // Only send kitchen bills to Zalo
		})
		if err != nil {
			c.JSON(http.StatusBadRequest, OrderDetailResponse{
				Success: false,
				Error:   "send_to_channel_failed: " + err.Error(),
			})
			return
		}
	}

	// Get the bill URL based on the template
	var billURL string
	switch template {
	case "bill_for_kitchen":
		billURL = updatedOrder.BillURL
	case "bill_for_payment":
		billURL = updatedOrder.BillForPaymentURL
	case "bill_for_complete":
		billURL = updatedOrder.BillForCompleteURL
	case "bill_for_complete_app":
		billURL = updatedOrder.BillForCompleteAppURL
	case "bill_for_cancel":
		billURL = updatedOrder.BillForCancelURL
	default:
		billURL = updatedOrder.BillURL
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data: map[string]any{
			"bill":   billURL,
			"labels": updatedOrder.LabelURLs,
		},
	})
}

// PrintOrderPaymentBill prints a payment bill for an order
func PrintOrderPaymentBill(c *gin.Context) {
	// Get order ID from path parameter
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "order_id_required",
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)
	if db == nil {
		return
	}

	// Get the current user
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, OrderDetailResponse{
			Success: false,
			Error:   "user_not_authenticated",
		})
		return
	}

	// Find order by order_id
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, OrderDetailResponse{
				Success: false,
				Error:   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "database_error",
		})
		return
	}

	// Calculate total paid and remaining amount
	var totalPaid float64
	for _, payment := range order.DataMapping.Data.Payments {
		if payment.Status == "COMPLETED" {
			totalPaid += payment.Total
		}
	}
	totalRemaining := order.DataMapping.Data.TotalForBiz - totalPaid

	// Check if remaining amount is too low
	if totalRemaining < 2000 {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "total_too_low_min_2000",
		})
		return
	}

	// Generate transaction ID and payment URL
	transactionID := fmt.Sprintf("tx_%d", time.Now().Unix())
	payURL := "https://example.com/pay/" + transactionID

	// Add pending payment to order
	var payments []models.Payment
	if order.DataMapping.Data.Payments != nil {
		payments = order.DataMapping.Data.Payments
	}
	payments = append(payments, models.Payment{
		Method: "NEXDORPAY",
		Total:  totalRemaining,
		Status: "PENDING",
		Note:   payURL,
	})

	// Update order with payment
	updates := map[string]any{
		"data_mapping.payments": payments,
		"data.payments":         payments,
	}
	if err := db.Model(&order).Updates(updates).Error; err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "update_failed",
		})
		return
	}

	// Generate bill with payment QR code
	updatedOrder, err := bill.GenerateOrderBills(db, orderID, bill.BillOptions{
		BillType:     "bill_for_payment",
		GenerateBill: true,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "bill_generation_failed: " + err.Error(),
		})
		return
	}

	// Send bill to channel
	err = bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
		BillTemplate: "bill_for_payment",
		Bill:         true,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, OrderDetailResponse{
			Success: false,
			Error:   "send_to_channel_failed: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, OrderDetailResponse{
		Success: true,
		Data:    updatedOrder.BillForPaymentURL,
	})
}
