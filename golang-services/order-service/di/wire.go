//go:build wireinject
// +build wireinject

package di

import (
	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	"github.com/nexdorvn/nexpos-backend/golang-services/order-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/di"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/localize"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"gorm.io/gorm"
)

// ServiceSet provides the service-specific providers
var ServiceSet = wire.NewSet(
	ProvideRouter,
)

// ProvideRouter creates a new Gin router with all middleware and routes
func ProvideRouter(middlewareProvider *di.MiddlewareProvider) *gin.Engine {
	r := gin.New()

	// Add middleware
	r.Use(middlewareProvider.CorsMiddleware())
	r.Use(middlewareProvider.GormMiddleware())
	r.Use(middlewareProvider.GoogleStorageMiddleware())
	r.Use(middlewareProvider.ErrorLocalizeMiddleware())
	r.Use(middlewareProvider.RedisMiddleware())
	r.Use(middlewareProvider.RabbitMQMiddleware())

	// Load routes
	router.LoadHandlers(r)

	// Add standard Gin middleware
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	return r
}

// ProvideMiddlewareProvider creates a middleware provider with all required dependencies
func ProvideMiddlewareProvider(
	db *gorm.DB,
	storageClient *storage.Client,
	localizer *localize.Localizer,
	redisClient *redis.Client,
	rabbitClient *rabbitmq.RabbitClient,
) *di.MiddlewareProvider {
	return di.NewMiddlewareProvider(db, storageClient, localizer, redisClient, rabbitClient)
}

// InitializeApp sets up the application with all dependencies
func InitializeApp() (*gin.Engine, error) {
	wire.Build(
		di.BaseSet,
		di.RedisProviderSet,
		di.RabbitMQProviderSet,
		ServiceSet,
	)
	return nil, nil
}
