package router

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

func HandleTestBillEvents(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		log.Printf("Error upgrading to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	clients[conn] = &Client{conn, nil, ""}
	defer delete(clients, conn)

	go sendTestBillEvents(conn, c)

	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Error reading WebSocket message: %v", err)
			break
		}
	}
}

func sendTestBillEvents(conn *websocket.Conn, c *gin.Context) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			mockEvent := generateMockBillEvent()
			err := conn.WriteMessage(websocket.TextMessage, []byte(mockEvent))
			if err != nil {
				log.Printf("Error sending test event: %v", err)
				return
			}
		}
	}
}

func generateMockBillEvent() string {
	event := map[string]any{
		"id":        fmt.Sprintf("mock-%d", time.Now().UnixNano()),
		"type":      "bill",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   "Bạn có 1 đơn hàng mới từ grab, mã đơn: GF-123",
		"data": map[string]any{
			"site_code": "S123",
			"hub_code":  "H123",
			"file_url":  "https://storage.googleapis.com/nexpos-images/printer%2F376986078-C6XHC36TRZKAVE_1728717212291.png",
		},
	}

	eventJSON, _ := json.Marshal(event)
	encodedJSON := strings.ToValidUTF8(string(eventJSON), "")
	return encodedJSON
}
