package t2s

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

type CacheItem struct {
	Data       []byte
	Expiration time.Time
}

var (
	cache      sync.Map
	cacheMutex sync.Mutex
)

func TextToSpeech(text string) ([]byte, error) {
	cacheKey := fmt.Sprintf("tts:%s", text)

	// Check if the result is in the local cache
	if cachedData, ok := getFromCache(cacheKey); ok {
		return cachedData, nil
	}

	client := &http.Client{}

	encodedText := url.QueryEscape(text)

	url := fmt.Sprintf("https://translate-pa.googleapis.com/v1/textToSpeech?client=at&text=%s&language=vi&voice_speed=0.5", encodedText)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("User-Agent", "GoogleTranslate/8.18.48.675349608.1-release (Linux; U; Android 13; 23021RAAEG)")
	req.Header.Set("X-Goog-Spatula", "CkEKIWNvbS5nb29nbGUuYW5kcm9pZC5hcHBzLnRyYW5zbGF0ZRocSkxza3dGNUg0Szc2YUtXS2RtRjUyYllUcGdBPRIgwuVA8V6lyLv36LiUaUvR4eMSfl3JwO4AeaQQa/oFWxYYj9TB68C0v6s7INuckpzy0sq/7QEqWQBeaoCcqUTErzF3jjeRg6j8o6Gdq5zb2TTg9ANWInH/fx2PwanYNIjo/LDFuf9ByB/boMUpBiHXI1ASnhnWooUeL0UXLGoaPpOtZu4hcImoLXK5/7j6SMO9")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	var result struct {
		AudioContent string `json:"audioContent"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("error parsing JSON response: %v", err)
	}

	audioContent := cleanBase64(result.AudioContent)
	audioBuffer, err := base64.StdEncoding.DecodeString(audioContent)
	if err != nil {
		return nil, fmt.Errorf("error decoding audio content: %v", err)
	}

	setToCache(cacheKey, audioBuffer, 15*time.Minute)

	return audioBuffer, nil
}

func getFromCache(key string) ([]byte, bool) {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	if item, ok := cache.Load(key); ok {
		cacheItem := item.(CacheItem)
		if time.Now().Before(cacheItem.Expiration) {
			return cacheItem.Data, true
		}
		cache.Delete(key)
	}
	return nil, false
}

func setToCache(key string, data []byte, duration time.Duration) {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	cache.Store(key, CacheItem{
		Data:       data,
		Expiration: time.Now().Add(duration),
	})
}

func cleanBase64(input string) string {
	validChars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
	cleaned := strings.Map(func(r rune) rune {
		if strings.ContainsRune(validChars, r) {
			return r
		}
		return -1
	}, input)

	for len(cleaned)%4 != 0 {
		cleaned += "="
	}

	return cleaned
}
