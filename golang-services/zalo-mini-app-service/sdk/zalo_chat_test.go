package sdk

import (
	"fmt"
	"testing"
)

func Test_decodeAES(t *testing.T) {
	text := `aNvoWEHh1e83HeSWlGBYrGXzt+IVnAMZfnHy3f4V6c/HORtaED7roaSer/HvZbA6PahFvqcMVB/sEzrky/qdqQsbtZTmBv5iFBkOWs7P+ojsha7Ua6RzF/AsT3uIr6unW67NU/dZVRbKV/Y7MXHPB8a+zsCpU4YHQWD32yVel7IqaWEJ7XINBQJCLESjig==`
	fmt.Println(DecodeAES(text, "6KNoE/6/gafVvJHjKJgWOQ=="))
	fmt.Println("done")
}

func TestDecodeAES(t *testing.T) {
	type args struct {
		ciphertext string
		secret     string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DecodeAES(tt.args.ciphertext, tt.args.secret); got != tt.want {
				t.Errorf("DecodeAES() = %v, want %v", got, tt.want)
			}
		})
	}
}
