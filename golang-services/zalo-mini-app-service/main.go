package main

import (
	"context"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/zalo-mini-app-service/route"

	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	app := cli.NewApp()
	app.Name = "Zalo Mini App Service"
	app.Usage = "A service for Zalo Mini App"
	app.Commands = []cli.Command{
		{
			Name:  "start",
			Usage: "Start the Zalo Mini App Service",
			Action: func(c *cli.Context) error {
				startService()
				return nil
			},
		},
	}

	err := app.Run(os.Args)
	if err != nil {
		panic(err)
	}
}

func startService() {
	router := gin.Default()
	router.Use(cors.Default())

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(os.Getenv("MONGODB_URI"))
	mongoClient, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		panic(err)
	}
	defer mongoClient.Disconnect(context.Background())

	// Pass the mongoClient to the gin context
	router.Use(func(c *gin.Context) {
		c.Set("mongoClient", mongoClient.Database("food-app"))
		c.Next()
	})

	router.GET("/api/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
	})

	router.POST("/api/convert_code_to_user_info", route.ConvertCodeToUserInfo)
	router.POST("/api/user/surveys", route.CreateUserSurvey)
	router.POST("/api/webhooks", route.ZaloWebHook)
	router.POST("/api/zalo_chat/group/send_message", route.SendMessageToGroup)
	router.POST("/api/zalo_chat/group/send_image", route.SendImageToGroup)
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}
	router.Run(":" + port)
}
