package mapping

import (
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/google/uuid"
	"github.com/tidwall/gjson"
)

// MapMenu maps external menu data to BrandMenu format
func MapMenu(source string, rawMenu map[string]any) *models.BrandMenu {
	var categories []models.Category
	var optionCategories []models.OptionCategory

	switch source {
	case "shopee_fresh", "shopee_ecom":
		categories, optionCategories = mapShopeeMenu(source, rawMenu)
	case "grab":
		categories, optionCategories = mapGrabMenu(source, rawMenu)
	case "grab_mart":
		categories, optionCategories = mapGrabMartMenu(source, rawMenu)
	case "be":
		categories, optionCategories = mapBeMenu(source, rawMenu)
	case "haravan":
		categories, optionCategories = mapHaravanMenu(source, rawMenu)
	}

	return &models.BrandMenu{
		Categories:       categories,
		OptionCategories: filterOptionCategories(optionCategories),
	}
}

func mapShopeeMenu(source string, data map[string]any) ([]models.Category, []models.OptionCategory) {
	var categories []models.Category
	var optionCategories []models.OptionCategory

	// Map categories
	utils.StructToJSON(data["categories"]).ForEach(func(_, cat gjson.Result) bool {
		category := models.Category{
			ID:      cat.Get("catalog_id").String(),
			Name:    startCase(cat.Get("catalog_name").String()),
			Code:    utils.SlugifyText(cat.Get("catalog_name").String()),
			Sources: []string{source},
			Active:  true,
		}

		// Map items
		cat.Get("dishes").ForEach(func(_, dish gjson.Result) bool {
			item := models.MenuItem{
				ID:          dish.Get("id").String(),
				Name:        startCase(dish.Get("name").String()),
				Code:        utils.SlugifyText(dish.Get("name").String()),
				Sources:     []string{source},
				Description: dish.Get("description").String(),
				Image:       dish.Get("picture_url").String(),
				Price:       dish.Get("price").Float(),
				Active:      dish.Get("stock_info.stock").Int() == 1,
			}
			category.Items = append(category.Items, item)
			return true
		})

		categories = append(categories, category)
		return true
	})

	// Map option categories
	utils.StructToJSON(data["option_categories"]).ForEach(func(_, opt gjson.Result) bool {
		optCategory := models.OptionCategory{
			ID:      opt.Get("id").String(),
			Name:    startCase(opt.Get("name").String()),
			Sources: []string{source},
			Rule: models.OptionRule{
				Type:        "SELECT_ONE",
				Required:    true,
				MinQuantity: 1,
				MaxQuantity: 1,
			},
		}

		opt.Get("options").ForEach(func(_, o gjson.Result) bool {
			option := models.Option{
				ID:      o.Get("id").String(),
				Name:    startCase(o.Get("name").String()),
				Sources: []string{source},
				Price:   o.Get("price").Float(),
				Active:  o.Get("is_active").Bool(),
			}
			optCategory.Options = append(optCategory.Options, option)
			return true
		})

		optionCategories = append(optionCategories, optCategory)
		return true
	})

	return categories, optionCategories
}

func mapGrabMenu(source string, data map[string]any) ([]models.Category, []models.OptionCategory) {
	var categories []models.Category
	var optionCategories []models.OptionCategory

	// Map categories
	utils.StructToJSON(data["categories"]).ForEach(func(_, cat gjson.Result) bool {
		category := models.Category{
			ID:      cat.Get("categoryID").String(),
			Name:    startCase(cat.Get("categoryName").String()),
			Code:    utils.SlugifyText(cat.Get("categoryName").String()),
			Sources: []string{source},
			Active:  cat.Get("availableStatus").Int() == 1,
		}

		cat.Get("items").ForEach(func(_, item gjson.Result) bool {
			category.Items = append(category.Items, models.MenuItem{
				ID:          nameToID(item.Get("itemName").String()),
				Name:        startCase(item.Get("itemName").String()),
				Code:        utils.SlugifyText(item.Get("itemName").String()),
				Sources:     []string{source},
				Description: item.Get("description").String(),
				Image:       item.Get("imageURL").String(),
				Price:       item.Get("priceInMin").Float(),
				Active:      item.Get("availableStatus").Int() == 1,
			})
			return true
		})

		categories = append(categories, category)
		return true
	})

	// Map option categories
	utils.StructToJSON(data["option_categories"]).ForEach(func(_, opt gjson.Result) bool {
		selectionMax := opt.Get("selectionRangeMax").Int()
		optCategory := models.OptionCategory{
			ID:      opt.Get("modifierGroupID").String(),
			Name:    startCase(opt.Get("modifierGroupName").String()),
			Sources: []string{source},
			Rule: models.OptionRule{
				Type:        getSelectionType(selectionMax),
				Required:    opt.Get("selectionRangeMin").Int() > 0,
				MinQuantity: float64(opt.Get("selectionRangeMin").Int()),
				MaxQuantity: float64(selectionMax),
			},
		}

		opt.Get("modifiers").ForEach(func(_, mod gjson.Result) bool {
			option := models.Option{
				ID:      mod.Get("modifierID").String(),
				Name:    startCase(mod.Get("modifierName").String()),
				Sources: []string{source},
				Price:   mod.Get("priceInMin").Float(),
				Active:  mod.Get("availableStatus").Int() == 1,
			}
			optCategory.Options = append(optCategory.Options, option)
			return true
		})

		optCategory.CategoryIDs = getRelatedItemIDs(categories, opt.Get("relatedItemIDs").Array())
		optionCategories = append(optionCategories, optCategory)
		return true
	})

	return categories, optionCategories
}

func mapGrabMartMenu(source string, data map[string]any) ([]models.Category, []models.OptionCategory) {
	var categories []models.Category
	var optionCategories []models.OptionCategory

	utils.StructToJSON(data["categories"]).ForEach(func(_, cat gjson.Result) bool {
		category := models.Category{
			ID:      cat.Get("itemClassID").String(),
			Name:    startCase(cat.Get("itemClassName").String()),
			Code:    utils.SlugifyText(cat.Get("itemClassName").String()),
			Sources: []string{source},
			Active:  true,
		}

		cat.Get("subDepartments").ForEach(func(_, sub gjson.Result) bool {
			subCategory := models.SubCategory{
				ID:      sub.Get("itemClassID").String(),
				Name:    startCase(sub.Get("itemClassName").String()),
				Code:    utils.SlugifyText(sub.Get("itemClassName").String()),
				Sources: []string{source},
			}

			sub.Get("items").ForEach(func(_, item gjson.Result) bool {
				subCategory.Items = append(subCategory.Items, models.MenuItem{
					ID:          nameToID(item.Get("itemName").String()),
					Name:        startCase(item.Get("itemName").String()),
					Code:        utils.SlugifyText(item.Get("itemName").String()),
					Sources:     []string{source},
					Description: item.Get("description").String(),
					Image:       item.Get("imageURL").String(),
					Price:       item.Get("priceInMin").Float(),
					Active:      item.Get("availableStatus").Int() == 1,
				})
				return true
			})

			category.SubCategories = append(category.SubCategories, subCategory)
			return true
		})

		categories = append(categories, category)
		return true
	})

	// Map option categories
	utils.StructToJSON(data["option_categories"]).ForEach(func(_, opt gjson.Result) bool {
		selectionMax := opt.Get("selectionRangeMax").Int()
		optCategory := models.OptionCategory{
			ID:      opt.Get("modifierGroupID").String(),
			Name:    startCase(opt.Get("modifierGroupName").String()),
			Sources: []string{source},
			Rule: models.OptionRule{
				Type:        getSelectionType(selectionMax),
				Required:    opt.Get("selectionRangeMin").Int() > 0,
				MinQuantity: float64(opt.Get("selectionRangeMin").Int()),
				MaxQuantity: float64(selectionMax),
			},
		}

		opt.Get("modifiers").ForEach(func(_, mod gjson.Result) bool {
			option := models.Option{
				ID:      mod.Get("modifierID").String(),
				Name:    startCase(mod.Get("modifierName").String()),
				Sources: []string{source},
				Price:   mod.Get("priceInMin").Float(),
				Active:  mod.Get("availableStatus").Int() == 1,
			}
			optCategory.Options = append(optCategory.Options, option)
			return true
		})

		optCategory.CategoryIDs = getRelatedItemIDs(categories, opt.Get("relatedItemIDs").Array())
		optionCategories = append(optionCategories, optCategory)
		return true
	})

	return categories, optionCategories
}

func mapHaravanMenu(source string, data map[string]any) ([]models.Category, []models.OptionCategory) {
	category := models.Category{
		ID:      uuid.New().String(),
		Name:    "Haravan Menu",
		Code:    "haravan_menu",
		Sources: []string{source},
		Active:  true,
	}

	utils.StructToJSON(data["categories"]).ForEach(func(_, item gjson.Result) bool {
		category.Items = append(category.Items, models.MenuItem{
			ID:          item.Get("id").String(),
			Name:        startCase(item.Get("title").String()),
			Code:        utils.SlugifyText(item.Get("title").String()),
			Sources:     []string{source},
			Description: item.Get("body_html").String(),
			Image:       item.Get("images.0.src").String(),
			Price:       item.Get("variants.0.price").Float(),
			Active:      true,
		})
		return true
	})

	return []models.Category{category}, []models.OptionCategory{}
}

func mapBeMenu(source string, data map[string]any) ([]models.Category, []models.OptionCategory) {
	var categories []models.Category

	utils.StructToJSON(data["categories"]).ForEach(func(_, cat gjson.Result) bool {
		category := models.Category{
			ID:      cat.Get("category.category_id").String(),
			Name:    startCase(cat.Get("category.name").String()),
			Code:    utils.SlugifyText(cat.Get("category.name").String()),
			Sources: []string{source},
			Active:  true,
		}

		cat.Get("items").ForEach(func(_, item gjson.Result) bool {
			category.Items = append(category.Items, models.MenuItem{
				ID:          nameToID(item.Get("item_name").String()),
				Name:        startCase(item.Get("item_name").String()),
				Code:        utils.SlugifyText(item.Get("item_name").String()),
				Sources:     []string{source},
				Description: item.Get("item_details").String(),
				Image:       item.Get("item_image").String(),
				Price:       item.Get("price").Float(),
				Active:      item.Get("is_active").Int() == 1,
			})
			return true
		})

		categories = append(categories, category)
		return true
	})

	return categories, nil
}

// Helper functions
func unique(slice []string) []string {
	seen := make(map[string]struct{})
	var result []string
	for _, item := range slice {
		if _, ok := seen[item]; !ok {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

func startCase(s string) string {
	words := strings.Fields(strings.ToLower(s))
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + word[1:]
		}
	}
	return strings.Join(words, " ")
}

func nameToID(name string) string {
	return strings.ReplaceAll(strings.ToLower(name), " ", "-")
}
