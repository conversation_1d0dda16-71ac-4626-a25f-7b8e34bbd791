package mapping

import (
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/tidwall/gjson"
)

// filterOptionCategories filters option categories that have associated category IDs
func filterOptionCategories(options []models.OptionCategory) []models.OptionCategory {
	var filtered []models.OptionCategory
	for _, oc := range options {
		if len(oc.CategoryIDs) > 0 {
			filtered = append(filtered, oc)
		}
	}
	return filtered
}

// filterDishes gets dishes for a specific section ID from option categories
func filterDishes(optionCategories gjson.Result, sectionID string) []gjson.Result {
	var dishes []gjson.Result

	optionCategories.ForEach(func(_, dish gjson.Result) bool {
		// Match by section ID or category reference
		if dish.Get("sectionId").String() == sectionID ||
			dish.Get("categoryId").String() == sectionID {
			dishes = append(dishes, dish)
		}
		return true
	})

	return dishes
}

// getRelatedItemIDs finds matching item IDs from categories based on provided IDs
func getRelatedItemIDs(categories []models.Category, relatedIDs []gjson.Result) []string {
	var ids []string
	relatedIDMap := make(map[string]bool)

	// Create map of related IDs for faster lookup
	for _, rid := range relatedIDs {
		relatedIDMap[rid.String()] = true
	}

	// Search through categories and items
	for _, category := range categories {
		// Check items in main category
		for _, item := range category.Items {
			if relatedIDMap[item.ID] {
				ids = append(ids, item.ID)
			}
		}

		// Check items in subcategories
		for _, subCategory := range category.SubCategories {
			for _, item := range subCategory.Items {
				if relatedIDMap[item.ID] {
					ids = append(ids, item.ID)
				}
			}
		}
	}

	return unique(ids) // Remove any duplicates
}

// GetOptionCategoriesForItem returns option categories associated with an item
func GetOptionCategoriesForItem(item *models.MenuItem, optionCategories []models.OptionCategory) []models.OptionCategory {
	var matched []models.OptionCategory

	for _, oc := range optionCategories {
		for _, categoryID := range oc.CategoryIDs {
			if categoryID == item.ID {
				matched = append(matched, oc)
				break
			}
		}
	}

	return matched
}

// GetActiveOptions returns only active options from an option category
func GetActiveOptions(optionCategory *models.OptionCategory) []models.Option {
	var activeOptions []models.Option

	for _, option := range optionCategory.Options {
		if option.Active {
			activeOptions = append(activeOptions, option)
		}
	}

	return activeOptions
}

// ValidateOptionSelections checks if selected options are valid for an item
func ValidateOptionSelections(item *models.MenuItem, optionCategories []models.OptionCategory, selectedOptions map[string][]string) (bool, string) {
	itemOptionCategories := GetOptionCategoriesForItem(item, optionCategories)

	for _, oc := range itemOptionCategories {
		selectedOpts := selectedOptions[oc.ID]

		// Check required options
		if oc.Rule.Required && len(selectedOpts) == 0 {
			return false, "Missing required options"
		}

		// Check quantity limits
		if float64(len(selectedOpts)) < oc.Rule.MinQuantity {
			return false, "Too few options selected"
		}
		if float64(len(selectedOpts)) > oc.Rule.MaxQuantity {
			return false, "Too many options selected"
		}

		// Validate option IDs
		validOptionIDs := make(map[string]bool)
		for _, opt := range oc.Options {
			if opt.Active {
				validOptionIDs[opt.ID] = true
			}
		}

		for _, selectedOptID := range selectedOpts {
			if !validOptionIDs[selectedOptID] {
				return false, "Invalid option selected"
			}
		}
	}

	return true, ""
}

// CalculateItemPrice calculates total price including selected options
func CalculateItemPrice(item *models.MenuItem, optionCategories []models.OptionCategory, selectedOptions map[string][]string) float64 {
	total := item.Price

	for ocID, optIDs := range selectedOptions {
		// Find matching option category
		var oc *models.OptionCategory
		for i, category := range optionCategories {
			if category.ID == ocID {
				oc = &optionCategories[i]
				break
			}
		}
		if oc == nil {
			continue
		}

		// Add option prices
		for _, optID := range optIDs {
			for _, opt := range oc.Options {
				if opt.ID == optID && opt.Active {
					total += opt.Price
					break
				}
			}
		}
	}

	return total
}

func getSelectionType(max int64) string {
	// Handle special cases first
	if max == 0 {
		return "SELECT_ONE" // Default to single selection if no max specified
	}

	// Multiple selections allowed
	if max > 1 {
		return "SELECT_MANY"
	}

	// Single selection
	return "SELECT_ONE"
}
