package di

import (
	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/localize"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"gorm.io/gorm"
)

// MiddlewareProvider provides middleware functions for Gin
type MiddlewareProvider struct {
	DB            *gorm.DB
	StorageClient *storage.Client
	Localizer     *localize.Localizer
	RedisClient   *redis.Client
	RabbitClient  *rabbitmq.RabbitClient
}

// NewMiddlewareProvider creates a new MiddlewareProvider
func NewMiddlewareProvider(
	db *gorm.DB,
	storageClient *storage.Client,
	localizer *localize.Localizer,
	redisClient *redis.Client,
	rabbitClient *rabbitmq.RabbitClient,
) *MiddlewareProvider {
	return &MiddlewareProvider{
		DB:            db,
		StorageClient: storageClient,
		Localizer:     localizer,
		RedisClient:   redisClient,
		RabbitClient:  rabbitClient,
	}
}

// GormMiddleware provides the database connection to the request context
func (p *MiddlewareProvider) GormMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("db", p.DB)
		c.Next()
	}
}

// GoogleStorageMiddleware provides the storage client to the request context
func (p *MiddlewareProvider) GoogleStorageMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("storage", p.StorageClient)
		c.Next()
	}
}

// ErrorLocalizeMiddleware provides error localization
func (p *MiddlewareProvider) ErrorLocalizeMiddleware() gin.HandlerFunc {
	return middlewares.ErrorLocalizeMiddleware()
}

// RedisMiddleware provides the Redis client to the request context
func (p *MiddlewareProvider) RedisMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("redis", p.RedisClient)
		c.Next()
	}
}

// RabbitMQMiddleware provides the RabbitMQ client to the request context
func (p *MiddlewareProvider) RabbitMQMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("rabbitmq", p.RabbitClient)
		c.Next()
	}
}

// CorsMiddleware provides CORS support
func (p *MiddlewareProvider) CorsMiddleware() gin.HandlerFunc {
	return middlewares.MyCors()
}
