package di

import (
	"cloud.google.com/go/storage"
	"github.com/go-redis/redis/v8"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/localize"
	"gorm.io/gorm"
)

// App represents the common application dependencies
type App struct {
	DB            *gorm.DB
	StorageClient *storage.Client
	Localizer     *localize.Localizer
	RedisClient   *redis.Client // Optional
}

// NewApp creates a new App with the required dependencies
func NewApp(db *gorm.DB, storageClient *storage.Client, localizer *localize.Localizer) *App {
	return &App{
		DB:            db,
		StorageClient: storageClient,
		Localizer:     localizer,
	}
}

// NewAppWithRedis creates a new App with Redis client
func NewAppWithRedis(db *gorm.DB, storageClient *storage.Client, localizer *localize.Localizer, redisClient *redis.Client) *App {
	return &App{
		DB:            db,
		StorageClient: storageClient,
		Localizer:     localizer,
		RedisClient:   redisClient,
	}
}
