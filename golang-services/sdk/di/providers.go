package di

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/localize"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"google.golang.org/api/option"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ProvideDB creates a new database connection
func ProvideDB() (*gorm.DB, error) {
	dsn := os.Getenv("POSTGRESQL_URI")
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	db = db.Debug()
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxOpenConns(25)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(5 * time.Minute)

	return db, nil
}

// ProvideStorageClient creates a new Google Storage client
func ProvideStorageClient() (*storage.Client, error) {
	privateKeyJSON := os.Getenv("GOOGLE_PRIVATE_KEY")
	if privateKeyJSON == "" {
		return nil, ErrMissingGooglePrivateKey
	}

	var credentials map[string]any
	if err := json.Unmarshal([]byte(privateKeyJSON), &credentials); err != nil {
		return nil, fmt.Errorf("failed to parse GOOGLE_PRIVATE_KEY JSON: %v", err)
	}

	if privateKey, ok := credentials["private_key"].(string); ok {
		credentials["private_key"] = strings.ReplaceAll(privateKey, "\\n", "\n")
	}

	fixedJSON, err := json.Marshal(credentials)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal fixed credentials: %v", err)
	}

	client, err := storage.NewClient(context.Background(), option.WithCredentialsJSON(fixedJSON))
	if err != nil {
		return nil, err
	}

	return client, nil
}

// ProvideRedisClient creates a new Redis client
func ProvideRedisClient() (*redis.Client, error) {
	uri := os.Getenv("REDIS_URI")
	pass := os.Getenv("REDIS_PASSWORD")

	// If URI doesn't have redis:// prefix and contains a colon, assume it's a host:port format
	if !strings.HasPrefix(uri, "redis://") && strings.Contains(uri, ":") {
		// Create options directly instead of using ParseURL
		client := redis.NewClient(&redis.Options{
			Addr:     uri, // Use the full host:port string
			Password: pass,
			DB:       0,
		})

		// Test connection
		_, err := client.Ping(context.Background()).Result()
		if err != nil {
			return nil, err
		}

		return client, nil
	}

	// For properly formatted URIs, use ParseURL
	opt, err := redis.ParseURL(uri)
	if err != nil {
		return nil, err
	}

	opt.Password = pass
	client := redis.NewClient(opt)

	// Test connection
	_, err = client.Ping(context.Background()).Result()
	if err != nil {
		return nil, err
	}

	return client, nil
}

// ProvideRabbitMQClient creates a new RabbitMQ client
func ProvideRabbitMQClient() (*rabbitmq.RabbitClient, error) {
	amqpURI := os.Getenv("RABBITMQ_URI")
	if amqpURI == "" {
		amqpURI = "amqp://nexdor:Nexdor2025@**************:5672/"
	}

	client, err := rabbitmq.NewRabbitClient(amqpURI)
	if err != nil {
		return nil, err
	}

	return client, nil
}

// ProvideLocalizer creates a new Localizer for error messages
func ProvideLocalizer() (*localize.Localizer, error) {
	return localize.GetLocalizer()
}

// Common provider set for all services
var CommonProviderSet = wire.NewSet(
	ProvideDB,
	ProvideStorageClient,
	ProvideLocalizer,
)

// Optional provider set for services that need Redis
var RedisProviderSet = wire.NewSet(
	ProvideRedisClient,
)

// Optional provider set for services that need RabbitMQ
var RabbitMQProviderSet = wire.NewSet(
	ProvideRabbitMQClient,
)
