package rabbitmq

import (
	"context"
	"os"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-amqp/v2/pkg/amqp"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/samber/lo"
)

var NODE_ENV = lo.If(os.Getenv("NODE_ENV") == "prod", "prod").Else("dev")

// RabbitClient represents a RabbitMQ client with pub-sub capabilities
type RabbitClient struct {
	URI        string
	Publisher  message.Publisher
	Subscriber *amqp.Subscriber
	Logger     watermill.LoggerAdapter
}

// NewRabbitClient creates a new RabbitMQ client with the provided URI
func NewRabbitClient(uri string) (*RabbitClient, error) {
	logger := watermill.NewStdLogger(false, false)

	publisher, err := amqp.NewPublisher(
		amqp.NewDurablePubSubConfig(uri, nil),
		logger,
	)
	if err != nil {
		return nil, err
	}

	return &RabbitClient{
		URI:       uri,
		Publisher: publisher,
		Logger:    logger,
	}, nil
}

// Subscribe creates a subscriber and subscribes to a topic in one operation
// Returns a channel of messages from the specified topic
func (c *RabbitClient) Subscribe(topic string, consumerGroup string) (<-chan *message.Message, error) {
	ctx := context.Background()
	subscriber, err := amqp.NewSubscriber(
		amqp.NewDurablePubSubConfig(
			c.URI,
			amqp.GenerateQueueNameTopicNameWithSuffix(consumerGroup),
		),
		c.Logger,
	)
	if err != nil {
		return nil, err
	}

	c.Subscriber = subscriber
	return subscriber.Subscribe(ctx, NODE_ENV+"_"+topic)
}

// Publish sends a message to the specified topic
func (c *RabbitClient) Publish(topic string, payload []byte) error {
	msg := message.NewMessage(watermill.NewUUID(), payload)
	return c.Publisher.Publish(NODE_ENV+"_"+topic, msg)
}

// ProcessMessages processes messages from a channel with a handler function
func ProcessMessages(messages <-chan *message.Message, handler func(*message.Message)) {
	for msg := range messages {
		handler(msg)
		msg.Ack()
	}
}

// Close closes the client connections
func (c *RabbitClient) Close() error {
	if c.Publisher != nil {
		if err := c.Publisher.Close(); err != nil {
			return err
		}
	}

	if c.Subscriber != nil {
		if err := c.Subscriber.Close(); err != nil {
			return err
		}
	}

	return nil
}
