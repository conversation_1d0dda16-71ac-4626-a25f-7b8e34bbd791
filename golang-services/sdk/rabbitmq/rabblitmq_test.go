package rabbitmq

import (
	"log"
	"testing"

	"github.com/ThreeDotsLabs/watermill/message"
)

func TestNewPubSub(t *testing.T) {
	// Create a client with your RabbitMQ URI
	amqpURI := "********************************************/"

	// Create the first client instance
	client, err := NewRabbitClient(amqpURI)
	if err != nil {
		panic(err)
	}
	defer client.Close()

	// Subscribe to a topic
	messages1, err := client.Subscribe("example.topic", "test_consumer_group_1")
	if err != nil {
		panic(err)
	}

	// Process messages in a goroutine
	go ProcessMessages(messages1, func(m *message.Message) {
		log.Printf("Subscriber 1 received message: %s", string(m.Payload))
		m.Ack()
	})

	payload := []byte("Hello, world!")
	if err := client.Publish("example.topic", payload); err != nil {
		log.Printf("Error publishing message: %v", err)
	}
}
