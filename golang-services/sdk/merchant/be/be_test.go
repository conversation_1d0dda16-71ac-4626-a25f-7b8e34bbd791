package be

import (
	"fmt"
	"testing"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func TestBEMerchantClients(t *testing.T) {
	client := NewBEMerchantClient()
	t.Run("GetToken", func(t *testing.T) {
		token := models.Token{
			SiteID:      `{"merchant_id":59085,"restaurant_id":90096,"user_id":70705,"vendor_id":91025}`,
			AccessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************.lGn_HKG9Dvu53KJqgOO-xYOQBZsXh7rSW_m4gUkh7w8",
		}
		// if err := GetToken(&token); err != nil {
		// 	t.Errorf("BEMerchantClient.GetToken() error = %v", err)
		// }
		if orderMap, err := client.GetOrderListByDuration(&token, time.Now().Add(-3*24*time.Hour), time.Now()); err != nil {
			fmt.Println(orderMap)
			t.<PERSON>rrorf("BEMerchantClient.GetOrderListV2() error = %v", err)
		}
	})
}
