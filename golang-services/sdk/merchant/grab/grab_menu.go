package grab

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"os"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/tidwall/gjson"
)

// SyncMenu synchronizes a menu item with Grab
func (c *GrabMerchantClient) SyncMenu(auth *models.Token, siteMenu *models.BrandMenu, itemID string) error {
	if auth.AccessToken == "" {
		return nil
	}

	// Get current menu from Grab
	headers := baseHeaders(auth, false)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v2/menu", headers, nil)
	if err != nil {
		return err
	}

	grabCategories := gjson.ParseBytes(data).Get("categories").Array()

	// Find the item to sync in the site menu
	var itemToSync *models.MenuItem
	var categoryOfItem *models.Category

	for _, category := range siteMenu.Categories {
		for _, item := range category.Items {
			if item.ID == itemID {
				itemToSync = &item
				categoryOfItem = &category
				break
			}
		}
		if itemToSync != nil {
			break
		}
	}

	if itemToSync == nil {
		return fmt.Errorf("item not found in menu")
	}

	// Upload image if available
	var newImage string
	if itemToSync.Image != "" {
		uploadedImage, err := c.uploadImage(auth, itemToSync.Image)
		if err != nil {
			return err
		}
		newImage = uploadedImage
	}

	// Find matching category in Grab menu
	var grabCategory gjson.Result
	for _, cat := range grabCategories {
		if strings.EqualFold(cat.Get("name").String(), categoryOfItem.Name) {
			grabCategory = cat
			break
		}
	}

	if !grabCategory.Exists() {
		// Create new category if not found
		categoryData, err := utils.DoRequest("POST", "https://api.grab.com/food/merchant/v2/categories", headers, map[string]any{
			"name": categoryOfItem.Name,
		})
		if err != nil {
			return err
		}
		grabCategory = gjson.ParseBytes(categoryData)
	}

	// Find matching item in Grab category
	var grabItem gjson.Result
	grabCategoryItems := grabCategory.Get("items").Array()
	for _, item := range grabCategoryItems {
		if strings.EqualFold(item.Get("name").String(), itemToSync.Name) {
			grabItem = item
			break
		}
	}

	if grabItem.Exists() {
		// Update existing item
		_, err = utils.DoRequest("PUT", fmt.Sprintf("https://api.grab.com/food/merchant/v2/items/%s", grabItem.Get("itemID").String()), headers, map[string]any{
			"name":        itemToSync.Name,
			"description": itemToSync.Description,
			"price":       itemToSync.Price,
			"imageURL":    newImage,
			"categoryID":  grabCategory.Get("categoryID").String(),
		})
		if err != nil {
			return err
		}
	} else {
		// Create new item
		itemData, err := utils.DoRequest("POST", "https://api.grab.com/food/merchant/v2/items", headers, map[string]any{
			"name":        itemToSync.Name,
			"description": itemToSync.Description,
			"price":       itemToSync.Price,
			"imageURL":    newImage,
			"categoryID":  grabCategory.Get("categoryID").String(),
		})
		if err != nil {
			return err
		}

		// Add proof item (required by Grab)
		newItemID := gjson.ParseBytes(itemData).Get("itemID").String()
		_, err = utils.DoRequest("POST", fmt.Sprintf("https://api.grab.com/food/merchant/v2/items/%s/proof-item", newItemID), headers, map[string]any{
			"proofImageURL": "",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// ActiveMenuItems activates or deactivates menu items
func (c *GrabMerchantClient) ActiveMenuItems(auth *models.Token, updateItems []map[string]any, updateAllItems bool) ([]map[string]any, error) {
	if auth.AccessToken == "" {
		return []map[string]any{}, nil
	}

	if len(updateItems) == 0 {
		return []map[string]any{}, nil
	}

	updatedItems := []map[string]any{}

	// Get current menu from Grab
	headers := baseHeaders(auth, false)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v2/menu", headers, nil)
	if err != nil {
		return nil, err
	}

	grabCategories := gjson.ParseBytes(data).Get("categories").Array()

	for _, category := range grabCategories {
		categoryName := category.Get("name").String()
		categoryItems := category.Get("items").Array()

		for _, item := range categoryItems {
			itemName := item.Get("name").String()

			// Find if this item should be updated
			var updateItem map[string]any
			for _, ui := range updateItems {
				if strings.EqualFold(ui["category_name"].(string), categoryName) &&
					strings.EqualFold(ui["item_name"].(string), itemName) {
					updateItem = ui
					break
				}
			}

			if updateItem == nil && !updateAllItems {
				continue
			}

			// Determine new status
			var newStatus bool
			if updateItem != nil {
				newStatus = updateItem["active"].(bool)
			} else {
				newStatus = false // Default for updateAllItems
			}

			// Get current status
			oldStatus := item.Get("available").Bool()

			// Skip if status is already correct
			if oldStatus == newStatus {
				continue
			}

			// Update item status
			_, err := utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/items/available-status", headers, map[string]any{
				"availableStatus": utils.ShortIf(newStatus, 1, 3),
				"itemIDs": []string{
					item.Get("itemID").String(),
				},
			})
			if err != nil {
				return updatedItems, err
			}

			if updateItem != nil {
				updatedItems = append(updatedItems, updateItem)
			}
		}
	}

	return updatedItems, nil
}

// DeleteMenuCategoryItem deletes an item from a category
func (c *GrabMerchantClient) DeleteMenuCategoryItem(auth *models.Token, categoryName, itemName string) error {
	if auth.AccessToken == "" {
		return nil
	}

	// Get current menu from Grab
	headers := baseHeaders(auth, false)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v2/menu", headers, nil)
	if err != nil {
		return err
	}

	grabCategories := gjson.ParseBytes(data).Get("categories").Array()

	// Find matching category
	var grabCategory gjson.Result
	for _, cat := range grabCategories {
		if strings.EqualFold(cat.Get("name").String(), categoryName) {
			grabCategory = cat
			break
		}
	}

	if !grabCategory.Exists() {
		return fmt.Errorf("category not found: %s", categoryName)
	}

	// Find matching item
	var grabItem gjson.Result
	grabCategoryItems := grabCategory.Get("items").Array()
	for _, item := range grabCategoryItems {
		if strings.EqualFold(item.Get("name").String(), itemName) {
			grabItem = item
			break
		}
	}

	if !grabItem.Exists() {
		return fmt.Errorf("item not found: %s", itemName)
	}

	// Delete item
	_, err = utils.DoRequest("DELETE", fmt.Sprintf("https://api.grab.com/food/merchant/v2/items/%s", grabItem.Get("itemID").String()), headers, nil)
	if err != nil {
		return err
	}

	return nil
}

// ActiveMenuOptionItem activates or deactivates menu option items
func (c *GrabMerchantClient) ActiveMenuOptionItems(auth *models.Token, updateItems []map[string]any, updateAllItems bool) ([]map[string]any, error) {
	if auth.AccessToken == "" {
		return []map[string]any{}, nil
	}

	if len(updateItems) == 0 {
		return []map[string]any{}, nil
	}

	updatedItems := []map[string]any{}

	// Get current menu from Grab
	headers := baseHeaders(auth, false)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v2/mart-menu", headers, nil)
	if err != nil {
		return nil, err
	}

	optionCategories := gjson.ParseBytes(data).Get("modifierGroups").Array()

	for _, category := range optionCategories {
		categoryName := category.Get("name").String()
		options := category.Get("options").Array()

		for _, option := range options {
			optionName := option.Get("name").String()

			// Find if this option should be updated
			var updateItem map[string]any
			for _, ui := range updateItems {
				if strings.EqualFold(ui["category_name"].(string), categoryName) &&
					strings.EqualFold(ui["name"].(string), optionName) {
					updateItem = ui
					break
				}
			}

			if updateItem == nil && !updateAllItems {
				continue
			}

			// Determine new status
			var newStatus bool
			if updateItem != nil {
				newStatus = updateItem["active"].(bool)
			} else {
				newStatus = false // Default for updateAllItems
			}

			// Get current status
			oldStatus := option.Get("available").Bool()

			// Skip if status is already correct
			if oldStatus == newStatus {
				continue
			}

			// Update option status
			_, err := utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v2/options/available-status", headers, map[string]any{
				"availableStatus": utils.ShortIf(newStatus, 1, 3),
				"optionIDs": []string{
					option.Get("optionID").String(),
				},
			})
			if err != nil {
				return updatedItems, err
			}

			if updateItem != nil {
				updatedItems = append(updatedItems, updateItem)
			}
		}
	}

	return updatedItems, nil
}

// Helper function to upload an image to Grab
func (c *GrabMerchantClient) uploadImage(auth *models.Token, imageURL string) (string, error) {
	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "grab-image-*.jpg")
	if err != nil {
		return "", err
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Copy the image to the temporary file
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		return "", err
	}

	// Prepare multipart form
	var requestBody bytes.Buffer
	multipartWriter := multipart.NewWriter(&requestBody)

	// Add the file
	fileWriter, err := multipartWriter.CreateFormFile("file", tempFile.Name())
	if err != nil {
		return "", err
	}

	// Reset file pointer to beginning
	tempFile.Seek(0, 0)

	// Copy file content to form
	_, err = io.Copy(fileWriter, tempFile)
	if err != nil {
		return "", err
	}

	// Close multipart writer
	multipartWriter.Close()

	// Create request
	req, err := http.NewRequest("POST", "https://api.grab.com/food/merchant/v2/images", &requestBody)
	if err != nil {
		return "", err
	}

	// Set headers
	headers := baseHeaders(auth, false)
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	req.Header.Set("Content-Type", multipartWriter.FormDataContentType())

	// Send request
	client := &http.Client{}
	resp2, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp2.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp2.Body)
	if err != nil {
		return "", err
	}

	// Parse response
	imageURL = gjson.ParseBytes(respBody).Get("imageURL").String()
	if imageURL == "" {
		return "", fmt.Errorf("failed to upload image: %s", string(respBody))
	}

	return imageURL, nil
}
