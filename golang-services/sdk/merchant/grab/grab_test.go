package grab

import (
	"fmt"
	"testing"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func TestGrabMerchantClients(t *testing.T) {
	client := NewGrabMerchantClient()
	t.Run("GetToken", func(t *testing.T) {
		token := models.Token{
			Username:    "gp.nexdor.30thang4-SA226",
			Password:    "Nexdor@123",
			SiteType:    "MART",
			AccessToken: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		}
		// if err := GetToken(&token); err != nil {
		// 	t.Errorf("GrabMerchantClient.GetToken() error = %v", err)
		// }
		if orderMap, err := client.GetOrderListV2(&token); err != nil {
			fmt.Println(orderMap)
			t.Errorf("GrabMerchantClient.GetOrderListV2() error = %v", err)
		}
	})
}
