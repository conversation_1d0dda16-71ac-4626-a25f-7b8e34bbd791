package grab

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/golang-module/carbon"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

// GrabMerchantClient implements the Client interface
type GrabMerchantClient struct {
	httpClient *http.Client
}

// NewGrabMerchantClient creates a new GrabMerchantClient
func NewGrabMerchantClient() *GrabMerchantClient {
	return &GrabMerchantClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Base method to check if API should be skipped based on Vietnam time
func skipCallAPI() bool {
	vietnamTime := carbon.Now().SetTimezone("Asia/Ho_Chi_Minh")
	hour := vietnamTime.Hour()
	return hour >= 1 && hour <= 5
}

// baseHeaders generates the base headers for API requests
func baseHeaders(auth *models.Token, isMart bool) map[string]string {
	return map[string]string{
		"user-profile-data":       "",
		"device-os":               "Android",
		"user-agent":              "Grab Merchant/4.120.0 (android 9; Build 210)",
		"business-type":           utils.ShortIf(isMart, "SHOP", "FOOD"),
		"x-agent":                 "mexapp",
		"authorization":           auth.AccessToken,
		"x-mts-ssid":              auth.AccessToken,
		"mex-country":             "VN",
		"mex-type":                "MEXUSERS",
		"x-platform-type":         "android",
		"accept-language":         "vi",
		"networkkit-disable-gzip": "true",
		"device-model":            "SM-N960N",
		"x-grabkit-clientid":      "GrabMerchant-App",
	}
}

// webBaseHeaders generates the base headers for web API requests
func webBaseHeaders(auth *models.Token) map[string]string {
	return map[string]string{
		"authority":          "merchant.grab.com",
		"accept":             "application/json, text/plain, */*",
		"accept-language":    "en-US,en;q=0.9",
		"authorization":      auth.AccessToken,
		"requestsource":      "troyPortal",
		"x-agent":            "mexapp",
		"x-app-platform":     "web",
		"x-app-version":      "1.2(v67)",
		"x-client-id":        "GrabMerchant-Portal",
		"x-currency":         "VND",
		"x-device-id":        "ios",
		"x-grabkit-clientid": "GrabMerchant-Portal",
		"x-language":         "gb",
		"x-mts-jb":           "false",
		"x-mts-ssid":         auth.AccessToken,
		"x-user-type":        "user-profile",
	}
}

// Implementation of all interface methods

// GetToken implements the authentication flow
func (c *GrabMerchantClient) GetToken(token *models.Token) error {
	if token.Password == "" {
		return fmt.Errorf("password is required")
	}

	headers := map[string]string{
		"x-platform-type": "android",
		"user-agent":      "Grab Merchant/4.72.0 (android 10; Build 146)",
		"x-agent":         "mexapp",
		"content-type":    "application/json; charset=utf-8",
	}

	resp, err := utils.DoRequest("POST", "https://api.grabpay.com/mex-app/troy/user-profile/v1/login", headers, map[string]any{
		"username": token.Username,
		"password": token.Password,
	})
	if err != nil {
		return err
	}
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, resp, "", "  "); err != nil {
		return err
	}
	fmt.Println(prettyJSON.String())
	token.AccessToken = gjson.ParseBytes(resp).Get("data.data.jwt").String()
	if strings.Contains(gjson.ParseBytes(resp).Get("data.data.business_line_ids").String(), "GM") {
		token.SiteType = "MART"
	} else {
		token.SiteType = "FOOD"
	}

	// Get store info
	store, err := c.GetStore(token)
	if err != nil {
		return err
	}
	if store != nil {
		token.SiteName = store.Name
	}

	return nil
}

// GetStoreListByAuth authenticates and retrieves a list of stores
func (c *GrabMerchantClient) GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error) {
	if token.Password == "" {
		return nil, fmt.Errorf("password is required")
	}

	headers := map[string]string{
		"x-platform-type": "android",
		"user-agent":      "Grab Merchant/4.72.0 (android 10; Build 146)",
		"x-agent":         "mexapp",
		"content-type":    "application/json; charset=utf-8",
	}

	resp, err := utils.DoRequest("POST", "https://api.grabpay.com/mex-app/troy/user-profile/v1/login", headers, map[string]any{
		"username": token.Username,
		"password": token.Password,
	})
	if err != nil {
		return nil, err
	}

	// Extract the JWT token
	accessToken := gjson.ParseBytes(resp).Get("data.data.jwt").String()
	if accessToken == "" {
		return nil, fmt.Errorf("failed to get access token")
	}

	// Create a temporary token with the access token
	tempToken := &models.Token{
		AccessToken: accessToken,
	}

	// Determine if it's a MART or FOOD account
	if strings.Contains(gjson.ParseBytes(resp).Get("data.data.business_line_ids").String(), "GM") {
		tempToken.SiteType = "MART"
	} else {
		tempToken.SiteType = "FOOD"
	}

	// Get store info
	store, err := c.GetStore(tempToken)
	if err != nil {
		return nil, err
	}

	result := []models.StoreItem{}

	if store != nil {
		result = append(result, models.StoreItem{
			AccessToken: accessToken,
			StoreType:   tempToken.SiteType,
			StoreID:     store.ID,
			StoreName:   store.Name,
		})
	}

	return result, nil
}

// GetStore retrieves store information
func (c *GrabMerchantClient) GetStore(token *models.Token) (*models.StoreDetail, error) {
	if token.AccessToken == "" {
		return nil, nil
	}

	headers := baseHeaders(token, token.SiteType == "MART")
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v2/merchants", headers, nil)
	if err != nil {
		return nil, err
	}

	// Parse the merchant data
	merchantData := gjson.ParseBytes(data).Get("merchant")
	if !merchantData.Exists() {
		return nil, fmt.Errorf("merchant data not found")
	}

	// Extract store details
	return &models.StoreDetail{
		ID:      merchantData.Get("ID").String(),
		Name:    merchantData.Get("name").String(),
		Phone:   strings.ReplaceAll(merchantData.Get("cePhone").String(), "+84", "0"),
		Address: merchantData.Get("address").String(),
		Raw:     merchantData.Value(),
	}, nil
}

// GetStoreList retrieves the list of stores for a merchant
func (c *GrabMerchantClient) GetStoreList(token *models.Token) ([]any, error) {
	// For Grab, we'll just return the store from GetStore as a list
	if token.AccessToken == "" {
		return nil, nil
	}

	store, err := c.GetStore(token)
	if err != nil {
		return nil, err
	}

	if store == nil || store.Raw == nil {
		return []any{}, nil
	}

	// Return as a single-item array
	return []any{store.Raw}, nil
}

// GetOrderListV2 retrieves the list of orders
func (c *GrabMerchantClient) GetOrderListV2(auth *models.Token) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{}
	if auth.AccessToken == "" || skipCallAPI() {
		return result, nil
	}

	// Get DOING orders
	headers := baseHeaders(auth, true)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v3/orders-pagination?pageType=Preparing&autoAcceptGroup=1&timestamp=", headers, nil)
	if err != nil {
		return result, err
	}
	fmt.Println(string(data))

	gjson.ParseBytes(data).Get("orders").ForEach(func(key, value gjson.Result) bool {
		result["DOING"] = append(result["DOING"], models.MerchantOrder{
			LongOrderID:  value.Get("orderID").String(),
			ShortOrderID: value.Get("displayID").String(),
			DataInList:   value.Raw,
		})
		return true
	})
	location, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	now := time.Now().In(location)
	// Get FINISH and CANCEL orders
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location).UTC().Format("2006-01-02T15:04:05.000Z")
	endTime := time.Now().Format("2006-01-02T15:04:05.000Z")
	url := fmt.Sprintf("https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=0&pageSize=100&startTime=%s&endTime=%s", startTime, endTime)

	data, err = utils.DoRequest("GET", url, headers, nil)
	if err != nil {
		return result, err
	}

	gjson.ParseBytes(data).Get("statements").ForEach(func(key, value gjson.Result) bool {
		if funk.ContainsString([]string{"ORDER_EXECUTING", "COMPLETED"}, value.Get("deliveryStatus").String()) {
			result["FINISH"] = append(result["FINISH"], models.MerchantOrder{
				LongOrderID:  value.Get("ID").String(),
				ShortOrderID: value.Get("displayID").String(),
				DataInList:   value.Value(),
			})
		}
		if funk.ContainsString([]string{"FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"}, value.Get("deliveryStatus").String()) {
			result["CANCEL"] = append(result["CANCEL"], models.MerchantOrder{
				LongOrderID:  value.Get("ID").String(),
				ShortOrderID: value.Get("displayID").String(),
				DataInList:   value.Value(),
			})
		}
		return true
	})

	return result, nil
}

// GetOrderListByDuration retrieves orders within a specific time range
func (c *GrabMerchantClient) GetOrderListByDuration(auth *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{
		"FINISH": {},
		"CANCEL": {},
	}

	if auth.AccessToken == "" || skipCallAPI() {
		return result, nil
	}

	// If the date range is more than 30 days, use finance list to get orders
	if time.Now().Sub(startTime) >= 30*24*time.Hour {
		// This would need implementation of GetFinanceList method
		// Placeholder for future implementation
		return result, fmt.Errorf("finance list for long date ranges not implemented yet")
	}

	// Otherwise use the reports API
	pageIndex := 0
	isMart := auth.SiteType == "MART"

	for {
		url := fmt.Sprintf("https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=%d&pageSize=100&startTime=%s&endTime=%s",
			pageIndex, startTime.Format("2006-01-02T15:04:05.000Z"), endTime.Format("2006-01-02T15:04:05.000Z"))

		headers := baseHeaders(auth, isMart)
		data, err := utils.DoRequest("GET", url, headers, nil)
		if err != nil {
			return result, err
		}

		// Use gjson to parse the response like other methods
		hasMore := gjson.ParseBytes(data).Get("hasMore").Bool()

		gjson.ParseBytes(data).Get("statements").ForEach(func(key, value gjson.Result) bool {
			order := models.MerchantOrder{
				LongOrderID:  value.Get("ID").String(),
				ShortOrderID: value.Get("displayID").String(),
				DataInList:   value.Value(),
			}

			deliveryStatus := value.Get("deliveryStatus").String()

			if deliveryStatus == "COMPLETED" || deliveryStatus == "ORDER_EXECUTING" {
				result["FINISH"] = append(result["FINISH"], order)
			} else if funk.ContainsString([]string{"FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"}, deliveryStatus) {
				result["CANCEL"] = append(result["CANCEL"], order)
			}
			return true
		})

		if !hasMore {
			break
		}
		pageIndex++
	}

	return result, nil
}

// ConfirmOrder confirms an order
func (c *GrabMerchantClient) ConfirmOrder(auth *models.Token, orderID string) error {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil
	}

	requestBody := map[string]any{
		"markStatus": 1,
		"orderIDs":   []string{orderID},
	}

	isMart := auth.SiteType == "MART"
	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("POST", "https://api.grab.com/food/merchant/orders/mark", headers, requestBody)
	if err != nil {
		return err
	}

	success := gjson.ParseBytes(data).Get("success").Bool()
	if !success {
		return fmt.Errorf("failed to confirm order")
	}
	return nil
}

// CancelOrder cancels an order
func (c *GrabMerchantClient) CancelOrder(auth *models.Token, orderID string, cancelType string) error {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil
	}

	cancelReason := "Quán hết món"
	if cancelType != "out_stock" {
		cancelReason = "Chúng tôi quá bận"
	}

	requestBody := map[string]any{
		"orderID":      orderID,
		"cancelReason": cancelReason,
		"cancelCode":   1002, // TODO: Determine correct codes
	}

	isMart := auth.SiteType == "MART"
	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/cancel-order/"+orderID, headers, requestBody)
	if err != nil {
		return err
	}

	success := gjson.ParseBytes(data).Get("success").Bool()
	if !success {
		return fmt.Errorf("failed to cancel order")
	}
	return nil
}

// GetOrderDetail retrieves details of a specific order
func (c *GrabMerchantClient) GetOrderDetail(auth *models.Token, orderID string) (any, error) {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil, nil
	}

	isMart := auth.SiteType == "MART"
	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v3/orders/"+orderID, headers, nil)
	if err != nil {
		return nil, err
	}

	orderData := gjson.ParseBytes(data).Get("order")
	if !orderData.Exists() {
		return nil, fmt.Errorf("order data not found")
	}

	return orderData.Value(), nil
}

// UpdateStoreStatus updates the store's open/close status
func (c *GrabMerchantClient) UpdateStoreStatus(auth *models.Token, status string, duration int) error {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil
	}

	var requestBody map[string]any
	isMart := auth.SiteType == "MART"

	if status == "close" {
		// Close store
		endTime := time.Now()
		if duration > 0 {
			endTime = endTime.Add(time.Duration(duration) * time.Minute)
		} else {
			// End of day if no duration specified
			endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 0, endTime.Location())
		}

		requestBody = map[string]any{
			"busyModeRequest": map[string]any{},
			"fromState":       "NORMAL",
			"tempPauseRequest": map[string]any{
				"isUnpause":    false,
				"tempPauseEnd": endTime.Format(time.RFC3339),
			},
			"toState": "TEMPPAUSED",
		}
	} else {
		// Open store
		requestBody = map[string]any{
			"busyModeRequest": map[string]any{
				"busyModeFoodPrepareTime": 0,
				"option":                  0,
			},
			"fromState":        "BUSY",
			"tempPauseRequest": map[string]any{},
			"toState":          "NORMAL",
		}
	}

	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/merchant/status", headers, requestBody)
	if err != nil {
		return err
	}

	success := gjson.ParseBytes(data).Get("success").Bool()
	if !success {
		return fmt.Errorf("failed to update store status")
	}

	// If opening store, need to make additional API calls
	if status == "open" {
		// Change request for TEMPPAUSED state
		requestBody["fromState"] = "TEMPPAUSED"
		_, _ = utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/merchant/status", headers, requestBody)

		// Additional unpause call
		unpauseBody := map[string]any{}
		_, _ = utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/un-pause", headers, unpauseBody)
	}

	return nil
}

// GetOpenStatus checks if the store is currently open
func (c *GrabMerchantClient) GetOpenStatus(auth *models.Token) (bool, error) {
	if auth.AccessToken == "" || skipCallAPI() {
		return false, nil
	}

	isMart := auth.SiteType == "MART"
	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("GET", "https://api.grab.com/food/merchant/v3/open-status", headers, nil)
	if err != nil {
		return false, err
	}

	isOpen := gjson.ParseBytes(data).Get("isOpen").Bool()
	isMexInBusyMode := gjson.ParseBytes(data).Get("isMexInBusyMode").Bool()

	return isOpen && !isMexInBusyMode, nil
}

// GetOpeningHour retrieves the store's opening hours
func (c *GrabMerchantClient) GetOpeningHour(auth *models.Token) (any, error) {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil, nil
	}

	// For Grab, we can get opening hours from the merchant data
	store, err := c.GetStore(auth)
	if err != nil {
		return nil, err
	}

	if store == nil || store.Raw == nil {
		return nil, nil
	}

	// Extract opening hours from the merchant data
	merchantData, ok := store.Raw.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("invalid merchant data format")
	}

	openingHours, exists := merchantData["openingHours"]
	if !exists {
		return nil, fmt.Errorf("opening hours not found in merchant data")
	}

	return openingHours, nil
}

// UpdateOpeningHour updates the store's opening hours
func (c *GrabMerchantClient) UpdateOpeningHour(auth *models.Token, workingHours any) error {
	if auth.AccessToken == "" || skipCallAPI() {
		return nil
	}

	// Convert workingHours to the expected format
	requestBody := map[string]any{
		"openingHours": workingHours,
		"force":        false,
	}

	isMart := auth.SiteType == "MART"
	headers := baseHeaders(auth, isMart)
	data, err := utils.DoRequest("PUT", "https://api.grab.com/food/merchant/v1/opening-hours", headers, requestBody)
	if err != nil {
		return err
	}

	success := gjson.ParseBytes(data).Get("success").Bool()
	if !success {
		return fmt.Errorf("failed to update opening hours")
	}

	return nil
}
