package nutifood

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"gorm.io/gorm"
)

type Client struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
	db         *gorm.DB
}

func NewClient(baseURL, apiKey string, db *gorm.DB) *Client {
	return &Client{
		baseURL:    baseURL,
		apiKey:     apiKey,
		httpClient: &http.Client{Timeout: 10 * time.Second},
		db:         db,
	}
}

func (c *Client) GetMenuItems(ctx context.Context, shop string, items []string) ([]models.NutifoodMenuItem, error) {
	allItems := make([]models.NutifoodMenuItem, 0)
	maxRetry := 3
	limit := 100
	offset := 0

	for {
		params := map[string]string{
			"shops":  utils.StructToJSON([]string{shop}).Raw,
			"items":  utils.StructToJSON(items).Raw,
			"offset": fmt.Sprintf("%d", offset),
			"limit":  fmt.Sprintf("%d", limit),
		}

		req, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("%s/v1/api/partner/stock", c.baseURL), nil)
		if err != nil {
			return nil, fmt.Errorf("create request failed: %w", err)
		}

		q := req.URL.Query()
		for key, value := range params {
			q.Add(key, value)
		}
		req.URL.RawQuery = q.Encode()
		req.Header.Set("api_key", c.apiKey)

		resp, err := c.httpClient.Do(req)
		if err != nil {
			maxRetry--
			if maxRetry == 0 {
				return nil, fmt.Errorf("request failed after retries: %w", err)
			}
			continue
		}
		defer resp.Body.Close()

		var result struct {
			Data struct {
				Stock []models.NutifoodMenuItem `json:"stock"`
			} `json:"data"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			return nil, fmt.Errorf("decode response failed: %w", err)
		}

		if len(result.Data.Stock) == 0 {
			break
		}

		allItems = append(allItems, result.Data.Stock...)
		offset += limit
	}

	return c.processMenuItems(allItems, shop, items), nil
}

// UpdateOpeningHour updates the store's opening hours (stub implementation)
func (c *Client) UpdateOpeningHour(token *models.Token, workingHours any) error {
	// NutiFood doesn't have an API for updating opening hours
	// This is a stub implementation to satisfy the interface
	return nil
}

func (c *Client) processMenuItems(items []models.NutifoodMenuItem, shop string, requestedItems []string) []models.NutifoodMenuItem {
	groupedItems := make(map[string]models.NutifoodMenuItem)

	// Group and sum quantities
	for _, item := range items {
		if existing, ok := groupedItems[item.ItemCode]; ok {
			existing.Quantity += item.Quantity
			groupedItems[item.ItemCode] = existing
		} else {
			groupedItems[item.ItemCode] = item
		}
	}

	// Add missing items
	for _, itemCode := range requestedItems {
		if _, exists := groupedItems[itemCode]; !exists {
			groupedItems[itemCode] = models.NutifoodMenuItem{
				ItemCode: itemCode,
				ItemName: "Không có trong kho",
				Unit:     "",
				Quantity: 0,
				ShopCode: shop,
				ShopName: "",
			}
		}
	}

	result := make([]models.NutifoodMenuItem, 0, len(groupedItems))
	for _, item := range groupedItems {
		result = append(result, item)
	}

	return result
}
