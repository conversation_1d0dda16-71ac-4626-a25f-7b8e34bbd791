package nutifood

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/thoas/go-funk"
)

// GetMenuItemsV1 retrieves menu items from NutiFood API and returns a structured response
func (c *Client) GetMenuItemsV1(ctx context.Context, shop string, items []string) (*models.NutifoodMenuItemsResponse, error) {
	allItems := make([]models.NutifoodMenuItem, 0)
	maxRetry := 3
	limit := 100
	offset := 0

	for {
		params := map[string]string{
			"shops":  utils.StructToJSON([]string{shop}).Raw,
			"items":  utils.StructToJSON(items).Raw,
			"offset": fmt.Sprintf("%d", offset),
			"limit":  fmt.Sprintf("%d", limit),
		}

		req, err := c.newRequest(ctx, "GET", "/v1/api/partner/stock", params, nil)
		if err != nil {
			return nil, fmt.Errorf("create request failed: %w", err)
		}

		var result struct {
			Data struct {
				Stock []models.NutifoodMenuItem `json:"stock"`
			} `json:"data"`
			Message string `json:"message"`
		}

		if err := c.doRequest(req, &result); err != nil {
			maxRetry--
			if maxRetry == 0 {
				return nil, fmt.Errorf("request failed after retries: %w", err)
			}
			continue
		}

		if result.Data.Stock == nil {
			maxRetry--
			if maxRetry == 0 {
				return nil, fmt.Errorf("failed to get stock data: %s", result.Message)
			}
			continue
		}

		allItems = append(allItems, result.Data.Stock...)
		offset += limit

		if len(result.Data.Stock) == 0 {
			break
		}
	}

	// Group items by item_code and sum their quantities
	groupedItems := make(map[string]models.NutifoodMenuItem)
	for _, item := range allItems {
		if existing, ok := groupedItems[item.ItemCode]; ok {
			existing.Quantity += item.Quantity
			groupedItems[item.ItemCode] = existing
		} else {
			groupedItems[item.ItemCode] = item
		}
	}

	// Add missing items
	for _, itemCode := range items {
		if _, exists := groupedItems[itemCode]; !exists {
			groupedItems[itemCode] = models.NutifoodMenuItem{
				ItemCode: itemCode,
				ItemName: "Không có trong kho",
				Unit:     "",
				Quantity: 0,
				ShopCode: shop,
				ShopName: "",
			}
		}
	}

	// Convert map to slice
	uniqueItems := make([]models.NutifoodMenuItem, 0, len(groupedItems))
	for _, item := range groupedItems {
		uniqueItems = append(uniqueItems, item)
	}

	return &models.NutifoodMenuItemsResponse{
		Success:   true,
		MenuItems: uniqueItems,
	}, nil
}

// GetMenuItemsV2 retrieves menu items from multiple shops
func (c *Client) GetMenuItemsV2(ctx context.Context, shops []string, items []string) (*models.NutifoodMenuItemsResponse, error) {
	allItems := make([]models.NutifoodMenuItem, 0)

	result := &models.NutifoodMenuItemsResponse{
		Success:   true,
		MenuItems: []models.NutifoodMenuItem{},
		RawItems:  []models.NutifoodMenuItem{},
	}

	for _, shop := range shops {
		shopItems, err := c.GetMenuItems(ctx, shop, items)
		if err != nil {
			return nil, err
		}

		allItems = append(allItems, shopItems...)
		result.RawItems = append(result.RawItems, shopItems...)
	}

	// Group items by item_code and sum their quantities
	groupedItems := make(map[string]models.NutifoodMenuItem)
	for _, item := range allItems {
		if existing, ok := groupedItems[item.ItemCode]; ok {
			existing.Quantity += item.Quantity
			groupedItems[item.ItemCode] = existing
		} else {
			groupedItems[item.ItemCode] = item
		}
	}

	// Convert map to slice
	uniqueItems := make([]models.NutifoodMenuItem, 0, len(groupedItems))
	for _, item := range groupedItems {
		uniqueItems = append(uniqueItems, item)
	}

	result.MenuItems = uniqueItems
	result.RawItems = allItems

	return result, nil
}

// SyncAnOrder synchronizes an order with NutiFood
func (c *Client) SyncAnOrder(ctx context.Context, order *models.Order, site *models.Site, hub *models.Hub, brandMenu *models.BrandMenu) (*models.NutifoodSyncResponse, error) {
	// Extract data mapping from order
	dataMapping := order.DataMapping.Data

	// Add stock to dishes
	dishesResp, err := utils.AddStockToDishes(dataMapping.Dishes, brandMenu)
	if err != nil {
		message := fmt.Sprintf("[Thất bại]mã đơn: %s, bán hàng trên %s - %s, Lỗi: %s",
			dataMapping.ID, order.Source, site.Name, strings.Join(dishesResp.ErrorMessages, ", "))
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: message,
			Data: models.NutifoodSyncData{
				Request:  dataMapping,
				Response: map[string]any{},
			},
		}, nil
	}

	// Extract dishes with stocks
	var dishes []map[string]any
	for _, dish := range dishesResp.Dishes {
		for _, stock := range dish.Stocks {
			if stock.Quantity > 0 {
				dishes = append(dishes, map[string]any{
					"item_code": stock.Code,
					"price":     stock.UnitPrice,
					"qty":       stock.Quantity,
				})
			}
		}
	}

	if len(dishes) == 0 {
		message := fmt.Sprintf("[Thất bại] Mã đơn: %s, bán hàng trên %s - %s sai thông tin món",
			dataMapping.ID, order.Source, site.Name)
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: message,
			Data: models.NutifoodSyncData{
				Request:  map[string]any{},
				Response: map[string]any{},
			},
		}, nil
	}

	// Check if all items exist in hub stock
	for _, dish := range dishes {
		var stock models.HubStock
		itemCode := dish["item_code"].(string)
		if err := c.db.Where("hub_id = ? AND code = ?", site.HubID, itemCode).First(&stock).Error; err != nil {
			message := fmt.Sprintf("[Thất bại] Item code: %s không tồn tại trong kho", itemCode)
			return &models.NutifoodSyncResponse{
				Success: false,
				Message: message,
				Data: models.NutifoodSyncData{
					Request:  map[string]any{},
					Response: map[string]any{},
				},
			}, nil
		}
	}

	// Prepare order data
	var brand models.Brand
	if err := c.db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		return nil, fmt.Errorf("failed to get brand: %w", err)
	}

	// Calculate order values
	subTotal := 0.0
	for _, dish := range dishes {
		subTotal += dish["price"].(float64) * float64(dish["qty"].(int))
	}

	// Prepare notes
	var notes []string
	if dataMapping.Note != "" {
		notes = append(notes, dataMapping.Note)
	}

	// Handle Grab hiding customer info
	customerName := dataMapping.CustomerName
	customerPhone := dataMapping.CustomerPhone
	if customerName == "***" {
		customerName = "NEXPOS"
		customerPhone = "0900000000"
	}

	// Calculate total paid
	// Commented out as it's not used
	// totalPaid := subTotal - dataMapping.OrderDiscount

	// Determine channel code
	channelCode := "Q-COMMERCE"
	if order.Source == "he" {
		channelCode = "NEXDOR_DLCN"
	}
	if brand.Name == "NutiFood Bliss" {
		channelCode = "BLISS"
	}

	// Prepare NutiFood dishes
	nutifoodDishes := []map[string]any{}
	for _, dish := range dataMapping.Dishes {
		item := utils.FindItemInMenuByName(brandMenu.Categories, dish.Name)
		if item != nil && len(item.Combo) > 0 {
			comboPriceWeight := 0.0
			for _, combo := range item.Combo {
				comboPriceWeight += combo.Price * float64(combo.Quantity)
			}

			for _, combo := range item.Combo {
				priceWeight := combo.Price * float64(combo.Quantity) / comboPriceWeight
				if int(dish.Quantity)*int(combo.Quantity) > 0 {
					nutifoodDishes = append(nutifoodDishes, map[string]any{
						"item_code": combo.Code,
						"price":     math.Ceil(item.StockPrice * priceWeight / float64(combo.Quantity)),
						"qty":       int(dish.Quantity) * int(combo.Quantity),
					})
				}
			}
		}
	}

	// Calculate NutiFood subtotal
	nutifoodSubTotal := 0.0
	for _, dish := range nutifoodDishes {
		nutifoodSubTotal += dish["price"].(float64) * float64(dish["qty"].(int))
	}

	// Prepare request payload
	requestData := map[string]any{
		"order_id":       dataMapping.ID,
		"order_code":     order.OrderID,
		"order_date":     time.Now().Format("2006-01-02 15:04:05"),
		"channel_code":   channelCode,
		"shop_code":      hub.Code,
		"customer_name":  customerName,
		"customer_phone": customerPhone,
		"customer_email": "",
		"address":        dataMapping.CustomerAddress,
		"note":           strings.Join(notes, ", "),
		"sub_total":      nutifoodSubTotal,
		"discount":       0,
		"shipping_fee":   0,
		"total":          nutifoodSubTotal,
		"items":          nutifoodDishes,
	}

	// Make API request to NutiFood
	req, err := c.newRequest(ctx, "POST", "/v1/api/partner/order", nil, requestData)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	var responseData struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Data    struct {
			OrderID string `json:"order_id"`
		} `json:"data"`
	}

	if err := c.doRequest(req, &responseData); err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	if !responseData.Success {
		message := fmt.Sprintf("[Thất bại] mã đơn: %s, bán hàng trên %s - %s, Lỗi: %s",
			dataMapping.ID, order.Source, site.Name, responseData.Message)
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: message,
			Data: models.NutifoodSyncData{
				Request:  requestData,
				Response: responseData,
			},
		}, nil
	}

	// Success response
	message := fmt.Sprintf("[Thành công] mã đơn: %s, bán hàng trên %s - %s, đồng bô thành công.",
		dataMapping.ID, order.Source, site.Name)

	return &models.NutifoodSyncResponse{
		Success: true,
		Message: message,
		Data: models.NutifoodSyncData{
			Request: map[string]any{
				"hub":    utils.PickFields(hub, []string{"ID", "Name"}),
				"dishes": dishesResp.Dishes,
			},
			Response: map[string]any{
				"success": true,
			},
			StockRequest: map[string]any{
				"shops": []string{hub.Code},
				"items": funk.Map(dishes, func(dish map[string]any) string {
					return dish["item_code"].(string)
				}).([]string),
			},
			StockResponse: []any{},
		},
	}, nil
}

// SyncAnOrderV2 synchronizes an order with NutiFood using the V2 API
func (c *Client) SyncAnOrderV2(ctx context.Context, order *models.Order, site *models.Site, hub *models.Hub, brandMenu *models.BrandMenu) (*models.NutifoodSyncResponse, error) {
	// Extract data mapping from order
	dataMapping := order.DataMapping.Data

	// Add stock to dishes
	dishesResp, err := utils.AddStockToDishes(dataMapping.Dishes, brandMenu)
	if err != nil {
		message := fmt.Sprintf("[Thất bại]mã đơn: %s, bán hàng trên %s - %s, Lỗi: %s",
			dataMapping.ID, order.Source, site.Name, strings.Join(dishesResp.ErrorMessages, ", "))
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: message,
			Data: models.NutifoodSyncData{
				Request:  dataMapping,
				Response: map[string]any{},
			},
		}, nil
	}

	// Prepare NutiFood dishes
	nutifoodDishes := []map[string]any{}
	for _, dish := range dataMapping.Dishes {
		item := utils.FindItemInMenuByName(brandMenu.Categories, dish.Name)
		if item != nil && len(item.Combo) > 0 {
			comboPriceWeight := 0.0
			for _, combo := range item.Combo {
				comboPriceWeight += combo.Price * float64(combo.Quantity)
			}

			for _, combo := range item.Combo {
				priceWeight := combo.Price * float64(combo.Quantity) / comboPriceWeight
				if int(dish.Quantity)*int(combo.Quantity) > 0 {
					nutifoodDishes = append(nutifoodDishes, map[string]any{
						"item_code": combo.Code,
						"price":     math.Ceil(item.StockPrice * priceWeight / float64(combo.Quantity)),
						"qty":       int(dish.Quantity) * int(combo.Quantity),
					})
				}
			}
		}
	}

	if len(nutifoodDishes) == 0 {
		message := fmt.Sprintf("[Thất bại] Mã đơn: %s, bán hàng trên %s - %s sai thông tin món",
			dataMapping.ID, order.Source, site.Name)
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: message,
			Data: models.NutifoodSyncData{
				Request:  map[string]any{},
				Response: map[string]any{},
			},
		}, nil
	}

	// Calculate NutiFood subtotal
	nutifoodSubTotal := 0.0
	for _, dish := range nutifoodDishes {
		nutifoodSubTotal += dish["price"].(float64) * float64(dish["qty"].(int))
	}

	// Calculate real subtotal and discount
	realSubTotal := 0.0
	for _, dish := range dataMapping.Dishes {
		realSubTotal += dish.Price
	}
	realOrderDiscount := dataMapping.TotalDiscount

	// Calculate total paid and nutifood order discount
	totalPaid := realSubTotal - realOrderDiscount
	nutifoodOrderDiscount := nutifoodSubTotal - totalPaid

	// Handle case where nutifoodOrderDiscount is negative
	if nutifoodOrderDiscount < 0 {
		// In a real implementation, you would send a notification here
		nutifoodOrderDiscount = 0
		totalPaid = nutifoodSubTotal
	}

	// Prepare notes
	var notes []string
	if !utils.Contains([]string{"he", "local"}, order.Source) {
		notes = append(notes, fmt.Sprintf("Mã đơn: %s ", dataMapping.OrderID))
	}

	if dataMapping.Note != "" {
		notes = append(notes, dataMapping.Note)
	}

	// Get brand information
	var brand models.Brand
	if err := c.db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		return nil, fmt.Errorf("failed to get brand: %w", err)
	}

	// Determine channel code
	channelCode := "Q-COMMERCE"
	if order.Source == "he" {
		channelCode = "NEXDOR_DLCN"
	}
	if brand.Name == "NutiFood Bliss" {
		channelCode = "BLISS"
	}

	// Prepare request payload for order-channel endpoint
	orderSyncReqData := map[string]any{
		"delivery": map[string]any{
			"delivery_code": dataMapping.ID,
			"delivery_fee":  0,
			"partner_code":  "Q-COMMERCE",
		},
		"lines":        nutifoodDishes,
		"channel_code": channelCode,
		"customers": map[string]any{
			"ward_name": "",
			"address":   "214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam",
			"name":      "NEXPOS",
			"phone":     "0900000000",
		},
		"date_order": time.Unix(dataMapping.OrderTimeSort, 0).Format("2006-01-02"),
		"payment_ids": []map[string]any{
			{
				"amount":       totalPaid,
				"payment_type": "Q-COMMERCE",
			},
		},
		"shop_code":  hub.Code,
		"user_id":    1,
		"to_invoice": false,
		"note":       strings.Join(notes, ", "),
		"discount":   nutifoodOrderDiscount,
		"attr_1":     "0900000000",
		"attr_2":     "NEXPOS",
		"attr_3":     "214 Nguyễn Trọng Tuyển, phường 8, Phú Nhuận, Ho Chi Minh City, Vietnam",
	}

	// Set source code based on order source
	sourceCodes := map[string]string{
		"shopee":       "SHOPEE",
		"shopee_fresh": "SHOPEE",
		"gojek":        "GOJEK",
		"grab":         "GRAB",
		"grab_mart":    "GRAB",
		"be":           "BE",
		"baemin":       "BAEMIN",
	}

	if sourceCode, ok := sourceCodes[order.Source]; ok {
		orderSyncReqData["source_code"] = sourceCode
	} else {
		orderSyncReqData["source_code"] = "NEXDOR_DLCN"
	}

	// Make API request to NutiFood
	req, err := c.newRequest(ctx, "POST", "/v1/api/order-delivery/order-channel", nil, orderSyncReqData)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	var responseData map[string]any
	if err := c.doRequest(req, &responseData); err != nil {
		return &models.NutifoodSyncResponse{
			Success: false,
			Message: fmt.Sprintf("[Thất bại] mã đơn: %s, bán hàng trên %s - %s lỗi: %s",
				dataMapping.ID, order.Source, site.Name, err.Error()),
			Data: models.NutifoodSyncData{
				Request:  orderSyncReqData,
				Response: map[string]any{},
			},
		}, nil
	}

	// Get menu items after sync
	menuItems, err := c.GetMenuItems(ctx, hub.Code, funk.Map(nutifoodDishes, func(dish map[string]any) string {
		return dish["item_code"].(string)
	}).([]string))
	if err != nil {
		// Log error but continue
		fmt.Printf("Failed to get menu items: %v\n", err)
	}

	// Success response
	message := fmt.Sprintf("[Thành công] mã đơn: %s, bán hàng trên %s - %s, đồng bô thành công.",
		dataMapping.ID, order.Source, site.Name)

	return &models.NutifoodSyncResponse{
		Success: true,
		Message: message,
		Data: models.NutifoodSyncData{
			Request:       orderSyncReqData,
			Response:      responseData,
			StockRequest:  map[string]any{"shops": []string{hub.Code}, "items": funk.Map(nutifoodDishes, func(dish map[string]any) string { return dish["item_code"].(string) })},
			StockResponse: menuItems,
		},
	}, nil
}

// Helper methods for HTTP requests
func (c *Client) newRequest(_ context.Context, method, path string, params map[string]string, body any) (*utils.Request, error) {
	url := fmt.Sprintf("%s%s", c.baseURL, path)

	req := &utils.Request{
		Method: method,
		URL:    url,
		Headers: map[string]string{
			"api_key": c.apiKey,
		},
		Params: params,
	}

	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		req.Body = jsonBody
	}

	return req, nil
}

func (c *Client) doRequest(req *utils.Request, result any) error {
	resp, err := utils.DoRequestWithClient(c.httpClient, req)
	if err != nil {
		return err
	}

	if result != nil {
		if err := json.Unmarshal(resp, result); err != nil {
			return err
		}
	}

	return nil
}
