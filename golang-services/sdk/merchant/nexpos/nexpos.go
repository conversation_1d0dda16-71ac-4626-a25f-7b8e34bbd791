package nexpos

import (
	"context"
	"fmt"

	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// Client represents a NexPOS client
type Client struct {
	db *gorm.DB
}

// NewClient creates a new NexPOS client
func NewClient(db *gorm.DB) *Client {
	return &Client{
		db: db,
	}
}

// GetMenuItems retrieves menu items from NexPOS
func (c *Client) GetMenuItems(ctx context.Context, shop string, items []string) ([]models.NexposMenuItem, error) {
	// Group items by item_code and sum their quantities
	uniqueItems := []models.NexposMenuItem{}

	for _, item := range items {
		// Check if item already exists in uniqueItems
		exists := false
		for _, ui := range uniqueItems {
			if ui.ItemCode == item {
				exists = true
				break
			}
		}

		if !exists {
			uniqueItems = append(uniqueItems, models.NexposMenuItem{
				ItemCode: item,
				ItemName: "Không có trong kho",
				Unit:     "",
				Quantity: 0,
				ShopCode: shop,
				ShopName: "",
			})
		}
	}

	return uniqueItems, nil
}

// UpdateOpeningHour updates the store's opening hours (stub implementation)
func (c *Client) UpdateOpeningHour(token *models.Token, workingHours any) error {
	// NexPOS doesn't have an API for updating opening hours
	// This is a stub implementation to satisfy the interface
	return nil
}

// SyncAnOrder synchronizes an order with NexPOS
func (c *Client) SyncAnOrder(ctx context.Context, order *models.Order) (*models.NexposSyncResponse, error) {
	// Get site information
	var site models.Site
	if err := c.db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return nil, fmt.Errorf("failed to get site: %w", err)
	}

	// Get hub information
	var hub models.Hub
	if err := c.db.Where("id = ?", site.HubID).First(&hub).Error; err != nil {
		return nil, fmt.Errorf("failed to get hub: %w", err)
	}

	// Get brand menu
	var brandMenu models.BrandMenu
	if err := c.db.Where("brand_id = ?", site.BrandID).First(&brandMenu).Error; err != nil {
		// Create brand menu if it doesn't exist
		brandMenu = models.BrandMenu{
			BrandID:          site.BrandID,
			Categories:       []models.Category{},
			OptionCategories: []models.OptionCategory{},
		}
		if err := c.db.Create(&brandMenu).Error; err != nil {
			return nil, fmt.Errorf("failed to create brand menu: %w", err)
		}
	}

	// Extract data mapping from order
	dataMapping := order.DataMapping.Data

	// Add stock to dishes
	dishesResp, err := utils.AddStockToDishes(dataMapping.Dishes, &brandMenu)
	if err != nil {
		message := fmt.Sprintf("[Thất bại]mã đơn: %s, bán hàng trên %s - %s, Lỗi: %s",
			dataMapping.ID, order.Source, site.Name, strings.Join(dishesResp.ErrorMessages, ", "))
		return &models.NexposSyncResponse{
			Success: false,
			Message: message,
			Data: models.NexposSyncData{
				Request:  dataMapping,
				Response: map[string]any{},
			},
		}, nil
	}

	// Extract dishes with stocks
	var dishes []map[string]any
	for _, dish := range dishesResp.Dishes {
		for _, stock := range dish.Stocks {
			if stock.Quantity > 0 {
				dishes = append(dishes, map[string]any{
					"item_code": stock.Code,
					"price":     stock.UnitPrice,
					"qty":       stock.Quantity,
				})
			}
		}
	}

	if len(dishes) == 0 {
		message := fmt.Sprintf("[Thất bại] Mã đơn: %s, bán hàng trên %s - %s sai thông tin món",
			dataMapping.ID, order.Source, site.Name)
		return &models.NexposSyncResponse{
			Success: false,
			Message: message,
			Data: models.NexposSyncData{
				Request:  map[string]any{},
				Response: map[string]any{},
			},
		}, nil
	}

	// Check if all items exist in hub stock
	for _, dish := range dishes {
		var stock models.HubStock
		itemCode := dish["item_code"].(string)
		if err := c.db.Where("hub_id = ? AND code = ?", site.HubID, itemCode).First(&stock).Error; err != nil {
			message := fmt.Sprintf("[Thất bại] Item code: %s không tồn tại trong kho", itemCode)
			return &models.NexposSyncResponse{
				Success: false,
				Message: message,
				Data: models.NexposSyncData{
					Request:  map[string]any{},
					Response: map[string]any{},
				},
			}, nil
		}
	}

	// Success response
	message := fmt.Sprintf("[Thành công] mã đơn: %s, bán hàng trên %s - %s, đồng bô thành công.",
		dataMapping.ID, order.Source, site.Name)

	return &models.NexposSyncResponse{
		Success: true,
		Message: message,
		Data: models.NexposSyncData{
			Request: map[string]any{
				"hub":    utils.PickFields(hub, []string{"ID", "Name"}),
				"dishes": dishesResp.Dishes,
			},
			Response: map[string]any{
				"success": true,
			},
			StockRequest: map[string]any{
				"shops": []string{hub.Code},
				"items": funk.Map(dishes, func(dish map[string]any) string {
					return dish["item_code"].(string)
				}).([]string),
			},
			StockResponse: []any{},
		},
	}, nil
}
