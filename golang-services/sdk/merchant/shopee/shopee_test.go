package shopee

import (
	"fmt"
	"testing"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func TestShopeeMerchantClients(t *testing.T) {

	t.Run("Run Test", func(t *testing.T) {
		token := models.Token{
			SiteID:      "10277065",
			AccessToken: "B:tQ6Ugo9konFGmSBFYhxwkjwI6QXaLaLht3ruhyLJLGLvUiAJyW+56SE5VsfrxZRyYF2JVrc5yupBTahST+B0KMwS15kDvqUz5aoDnj7Lgo0=",
		}
		client := NewShopeeMerchantClient()
		if orderMap, err := client.GetOrderListByDuration(&token, time.Now().AddDate(0, 0, -7), time.Now()); err != nil {
			fmt.Println(orderMap)
			t.Errorf("ShopeeMerchantClient.GetOrderListByDuration() error = %v", err)
		}

		if orderMap, err := client.GetOrderDetail(&token, "08035-330242821"); err != nil {
			fmt.Println(orderMap)
			t.Errorf("ShopeeMerchantClient.GetOrderDetail() error = %v", err)
		}

		if orderMap, err := client.GetOrderListV2(&token); err != nil {
			fmt.Println(orderMap)
			t.Errorf("ShopeeMerchantClient.GetOrderListV2() error = %v", err)
		}

		// Test GetStoreList
		if storeList, err := client.GetStoreList(&token); err != nil {
			fmt.Println(storeList)
			t.Errorf("ShopeeMerchantClient.GetStoreList() error = %v", err)
		} else {
			fmt.Printf("GetStoreList returned %d stores\n", len(storeList))
		}
	})
}
