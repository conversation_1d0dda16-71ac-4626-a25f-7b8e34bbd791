package shopee

import (
	"encoding/json"
	"fmt"
	"net/http"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"maps"

	"github.com/go-resty/resty/v2"
	"github.com/golang-module/carbon"
	"github.com/tidwall/gjson"
)

// ShopeeMerchantClient implements the Client interface
type ShopeeMerchantClient struct {
	httpClient *http.Client
}

// NewShopeeMerchantClient creates a new ShopeeMerchantClient
func NewShopeeMerchantClient() *ShopeeMerchantClient {
	return &ShopeeMerchantClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

var httpClient = &http.Client{
	Timeout: 30 * time.Second,
}

// skipCallAPI checks if API should be skipped based on Vietnam time
func skipCallAPI() bool {
	vietnamTime := carbon.Now().SetTimezone("Asia/Bangkok")
	hour := vietnamTime.Hour()
	return hour < 8 || hour > 21
}

// baseHeaders generates the base headers for API requests
func baseHeaders(token *models.Token) map[string]string {
	return map[string]string{
		"host":                    "gmerchant.deliverynow.vn",
		"operate-source":          "partnerapp",
		"user-agent":              "appver=32300 secid=4002 (Linux; Android 10; samsung Build/QP1A.190711.020) food_rn_ver=3272 spp_rn_ver=2915 app_type=2 shopee_rn_bundle_version=285000000",
		"x-foody-access-token":    token.AccessToken,
		"x-foody-api-version":     "1",
		"x-foody-app-type":        "1024",
		"x-foody-client-id":       "GG+mO/PW1/A8ErH3hX9hxGEF4yYeIDMmalqFvTtvowA=",
		"x-foody-client-language": "vi",
		"x-foody-client-type":     "1",
		"x-foody-client-version":  "3.0.0",
		"x-foody-entity-id":       token.SiteID,
	}
}

// webBaseHeaders generates the base headers for web API requests
func webBaseHeaders(token *models.Token) map[string]string {
	return map[string]string{
		"authority":               "gmerchant.deliverynow.vn",
		"accept":                  "application/json, text/plain, */*",
		"accept-language":         "en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7",
		"origin":                  "https://partner.shopee.vn",
		"referer":                 "https://partner.shopee.vn/",
		"user-agent":              "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
		"x-foody-access-token":    token.AccessToken,
		"x-foody-entity-id":       token.SiteID,
		"x-foody-api-version":     "1",
		"x-foody-app-type":        "1025",
		"x-foody-client-id":       "ffffffff-c9a4-034c-ffff-ffffc2e834d9",
		"x-foody-client-language": "vi",
		"x-foody-client-type":     "1",
		"x-foody-client-version":  "3.0.0",
	}
}

// baseHeaders2 generates the base headers for Shopee Partner API requests
func baseHeaders2(token *models.Token) map[string]string {
	return map[string]string{
		"a-appversion":     "32101",
		"a-brand":          "unknown",
		"a-lang":           "vi",
		"a-os":             "android",
		"content-type":     "application/json",
		"host":             "app.partner.shopee.vn",
		"user-agent":       "okhttp/3.12.4 app_type=2 shopee_rn_bundle_version=285000000",
		"x-merchant-token": token.AccessToken,
	}
}

// signRequest signs the request by adding necessary security headers
func signRequest(url string, body string) (map[string]string, error) {
	fmt.Println(url)
	fmt.Println(body)
	signReq := map[string]any{
		"url":  url,
		"body": body,
	}

	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(signReq).
		Post("https://shopee.nexpos.io/sign")
	if err != nil {
		return nil, err
	}

	signedValue := gjson.ParseBytes(resp.Body()).Get("signedValue")
	result := map[string]string{
		"x-sap-ri":  signedValue.Get("x-sap-ri").String(),
		"x-sap-sec": signedValue.Get("x-sap-sec").String(),
	}

	return result, nil
}

// GetToken implements the authentication flow
func (c ShopeeMerchantClient) GetToken(token *models.Token) error {
	resp, err := utils.DoRequest("POST", "https://shopee.nexpos.io/login", nil, map[string]string{
		"email":    token.Username,
		"password": token.Password,
	})
	if err != nil {
		return err
	}

	loginResp := gjson.ParseBytes(resp)

	token.AccessToken = loginResp.Get("access_token").String()
	return nil
}

// GetToken implements the authentication flow
func (c ShopeeMerchantClient) GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error) {
	resp, err := utils.DoRequest("POST", "https://shopee.nexpos.io/login", nil, map[string]string{
		"email":    token.Username,
		"password": token.Password,
	})
	if err != nil {
		return nil, err
	}

	result := []models.StoreItem{}

	loginResp := gjson.ParseBytes(resp)
	for _, merchant := range loginResp.Get("data").Array() {
		for _, store := range merchant.Get("store_list").Array() {
			result = append(result, models.StoreItem{
				AccessToken: merchant.Get("User.token").String(),
				StoreID:     store.Get("store_id").String(),
				StoreName:   store.Get("store_name").String(),
			})
		}
	}
	return result, nil
}

// GetStore retrieves store information
func (c ShopeeMerchantClient) GetStore(token *models.Token) (*models.StoreDetail, error) {
	if token.AccessToken == "" {
		return nil, nil
	}

	headers := baseHeaders(token)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/get_basic_info?request_action_type=3", "")
	if err != nil {
		return nil, err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/get_basic_info?request_action_type=3", headers, nil)
	if err != nil {
		return nil, err
	}

	// Parse the store data
	storeData := gjson.ParseBytes(data).Get("data")
	if !storeData.Exists() {
		return nil, fmt.Errorf("store data not found")
	}

	return &models.StoreDetail{
		ID:      storeData.Get("restaurant_id").String(),
		Name:    storeData.Get("name").String(),
		Phone:   storeData.Get("primary_contact_number").String(),
		Address: fmt.Sprintf("%s, %s", storeData.Get("address").String(), storeData.Get("city_name").String()),
		Raw:     storeData.Value(),
	}, nil
}

// GetOrderListV2 retrieves the list of orders
func (c ShopeeMerchantClient) GetOrderListV2(token *models.Token) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{
		"DOING":   {},
		"PENDING": {},
		"FINISH":  {},
		"CANCEL":  {},
	}

	if token.AccessToken == "" || skipCallAPI() {
		return result, nil
	}

	// Filter types for getting orders
	filterTypes := map[string]map[string]any{
		"DOING": {
			"order_filter_type": 31,
			"next_item_id":      "",
			"request_count":     100,
			"sort_type":         5,
		},
		"PENDING": {
			"order_filter_type": 30,
			"next_item_id":      "",
			"request_count":     100,
			"sort_type":         6,
		},
		"FINISH_OR_CANCEL": {
			"order_filter_type": 40,
			"next_item_id":      "",
			"request_count":     100,
			"from_time":         carbon.Now().AddDays(-3).EndOfDay().Timestamp(),
			"to_time":           carbon.Now().EndOfDay().Timestamp(),
			"sort_type":         12,
		},
	}

	for status, filter := range filterTypes {
		headers := baseHeaders(token)

		requestBody, err := json.Marshal(filter)
		if err != nil {
			return result, err
		}

		// Add sign request headers
		signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/order/get_list", string(requestBody))
		if err != nil {
			return result, err
		}

		maps.Copy(headers, signHeaders)

		data, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/order/get_list", headers, filter)
		if err != nil {
			return result, err
		}

		orders := gjson.ParseBytes(data).Get("data.orders").Array()

		if status == "FINISH_OR_CANCEL" {
			for _, order := range orders {
				orderStatus := order.Get("order_status").Int()
				orderObj := models.MerchantOrder{
					LongOrderID:  order.Get("code").String(),
					ShortOrderID: order.Get("code").String(),
					DataInList:   order.Value(),
				}

				if orderStatus == 8 {
					result["CANCEL"] = append(result["CANCEL"], orderObj)
				} else {
					result["FINISH"] = append(result["FINISH"], orderObj)
				}
			}
		} else {
			for _, order := range orders {
				result[status] = append(result[status], models.MerchantOrder{
					LongOrderID:  order.Get("code").String(),
					ShortOrderID: order.Get("code").String(),
					DataInList:   order.Value(),
				})
			}
		}
	}

	return result, nil
}

// GetOrderListByDuration retrieves orders within a specific time range
func (c ShopeeMerchantClient) GetOrderListByDuration(token *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{
		"FINISH": {},
		"CANCEL": {},
	}

	if token.AccessToken == "" || skipCallAPI() {
		return result, nil
	}

	nextItemID := ""
	hasMore := true

	for hasMore {
		filter := map[string]any{
			"order_filter_type": 40,
			"next_item_id":      nextItemID,
			"request_count":     100,
			"from_time":         startTime.Unix(),
			"to_time":           endTime.Unix(),
			"sort_type":         12,
		}

		headers := baseHeaders(token)

		requestBody, err := json.Marshal(filter)
		if err != nil {
			return result, err
		}

		// Add sign request headers
		signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/order/get_list", string(requestBody))
		if err != nil {
			return result, err
		}

		maps.Copy(headers, signHeaders)

		data, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/order/get_list", headers, filter)
		if err != nil {
			return result, err
		}

		orders := gjson.ParseBytes(data).Get("data.orders").Array()
		nextItemID = gjson.ParseBytes(data).Get("data.next_item_id").String()
		hasMore = gjson.ParseBytes(data).Get("data.has_more").Bool()

		for _, order := range orders {
			orderStatus := order.Get("order_status").Int()
			orderObj := models.MerchantOrder{
				LongOrderID:  order.Get("code").String(),
				ShortOrderID: order.Get("code").String(),
				DataInList:   order.Value(),
			}

			if orderStatus == 8 {
				result["CANCEL"] = append(result["CANCEL"], orderObj)
			} else {
				result["FINISH"] = append(result["FINISH"], orderObj)
			}
		}
	}

	return result, nil
}

// ConfirmOrder confirms an order
func (c ShopeeMerchantClient) ConfirmOrder(token *models.Token, orderID string) error {
	if token.AccessToken == "" || skipCallAPI() {
		return nil
	}

	// First get order details
	orderDetail, err := c.GetOrderDetail(token, orderID)
	if err != nil {
		return err
	}

	orderJ := utils.StructToJSON(orderDetail)
	id := orderJ.Get("id").String()
	code := orderJ.Get("code").String()
	serial := orderJ.Get("serial").String()

	confirmRequest := map[string]any{
		"order_id":   id,
		"order_code": code,
		"serial":     serial,
	}

	headers := baseHeaders(token)

	requestBody, err := json.Marshal(confirmRequest)
	if err != nil {
		return err
	}

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/order/confirm", string(requestBody))
	if err != nil {
		return err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/order/confirm", headers, confirmRequest)
	if err != nil {
		return err
	}

	msg := gjson.ParseBytes(data).Get("msg").String()
	if msg != "success" {
		return fmt.Errorf("failed to confirm order: %s", msg)
	}

	return nil
}

// CancelOrder cancels an order
func (c ShopeeMerchantClient) CancelOrder(token *models.Token, orderID string, cancelType string) error {
	if token.AccessToken == "" || skipCallAPI() {
		return nil
	}

	// First get order details
	orderDetail, err := c.GetOrderDetail(token, orderID)
	if err != nil {
		return err
	}
	orderJ := utils.StructToJSON(orderDetail)
	id := orderJ.Get("id").String()
	code := orderJ.Get("code").String()
	serial := orderJ.Get("serial").String()

	// Determine reason id based on cancel type
	reasonID := 79 // Default: Out of stock
	if cancelType != "out_stock" {
		reasonID = 81 // Merchant busy
	}

	cancelRequest := map[string]any{
		"order_id":    id,
		"order_code":  code,
		"serial":      serial,
		"reason_id":   reasonID,
		"cancel_note": utils.ShortIf(cancelType == "out_stock", "Hết món", "Quán bận"),
	}

	headers := baseHeaders(token)

	requestBody, err := json.Marshal(cancelRequest)
	if err != nil {
		return err
	}

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/order/cancel", string(requestBody))
	if err != nil {
		return err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/order/cancel", headers, cancelRequest)
	if err != nil {
		return err
	}

	msg := gjson.ParseBytes(data).Get("msg").String()
	if msg != "success" {
		return fmt.Errorf("failed to cancel order: %s", msg)
	}

	return nil
}

// GetOrderDetail retrieves details of a specific order
func (c ShopeeMerchantClient) GetOrderDetail(token *models.Token, orderID string) (any, error) {
	if token.AccessToken == "" || skipCallAPI() {
		return nil, nil
	}

	headers := baseHeaders(token)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/order/get_detail?order_code="+orderID, "")
	if err != nil {
		return nil, err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/order/get_detail?order_code="+orderID, headers, nil)
	if err != nil {
		return nil, err
	}

	orderData := gjson.ParseBytes(data).Get("data.order")
	if !orderData.Exists() {
		return nil, fmt.Errorf("order data not found")
	}

	return orderData.Value(), nil
}

// UpdateStoreStatus updates the store's open/close status
func (c ShopeeMerchantClient) UpdateStoreStatus(token *models.Token, status string, duration int) error {
	if token.AccessToken == "" || skipCallAPI() {
		return nil
	}

	var requestBody map[string]any
	var url string

	if status == "close" {
		url = "https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/set-busy"

		// Calculate end time
		endTime := time.Now()
		if duration > 0 {
			endTime = endTime.Add(time.Duration(duration) * time.Minute)
		} else {
			// End of day if no duration specified
			now := time.Now()
			endTime = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
		}

		requestBody = map[string]any{
			"from_time": time.Now().Format("2006-01-02 15:04:05"),
			"to_time":   endTime.Format("2006-01-02 15:04:05"),
		}
	} else {
		url = "https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/set-open"
		requestBody = map[string]any{}
	}

	headers := baseHeaders(token)

	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}

	// Add sign request headers
	signHeaders, err := signRequest(url, string(requestBodyBytes))
	if err != nil {
		return err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("POST", url, headers, requestBody)
	if err != nil {
		return err
	}

	msg := gjson.ParseBytes(data).Get("msg").String()
	if msg != "success" {
		return fmt.Errorf("failed to update store status: %s", msg)
	}

	return nil
}

// GetOpenStatus checks if the store is currently open
func (c ShopeeMerchantClient) GetOpenStatus(token *models.Token) (bool, error) {
	if token.AccessToken == "" || skipCallAPI() {
		return false, nil
	}

	headers := baseHeaders(token)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/refact/get", "")
	if err != nil {
		return false, err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/opening-status/refact/get", headers, nil)
	if err != nil {
		return false, err
	}

	// Check if store is closed (no close_status and no pause_time)
	closeStatus := gjson.ParseBytes(data).Get("data.opening_status.close_status.close_type").Exists()
	pauseEndTime := gjson.ParseBytes(data).Get("data.opening_status.pause_time.pause_end_time").Exists()

	return !closeStatus && !pauseEndTime, nil
}

// GetOpeningHour retrieves the store's opening hours
func (c ShopeeMerchantClient) GetOpeningHour(token *models.Token) (any, error) {
	if token.AccessToken == "" || skipCallAPI() {
		return nil, nil
	}

	headers := baseHeaders(token)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/get", "")
	if err != nil {
		return nil, err
	}

	maps.Copy(headers, signHeaders)

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/get", headers, nil)
	if err != nil {
		return nil, err
	}

	regularHours := gjson.ParseBytes(data).Get("data.regular_hours")
	if !regularHours.Exists() {
		return nil, fmt.Errorf("regular hours not found")
	}

	return regularHours.Value(), nil
}

// GetStoreList retrieves the list of stores for a merchant
func (c ShopeeMerchantClient) GetStoreList(token *models.Token) ([]any, error) {
	if token.AccessToken == "" {
		return nil, nil
	}

	headers := baseHeaders2(token)

	// Create request body
	requestBody := map[string]any{
		"page_size": 500,
	}

	data, err := utils.DoRequest("POST", "https://app.partner.shopee.vn/mss/app-api/PartnerRNServer/GetStoreList", headers, requestBody)
	if err != nil {
		return nil, err
	}

	// Parse response
	storeList := gjson.ParseBytes(data).Get("data.store_list").Array()
	if len(storeList) == 0 {
		return []any{}, nil
	}

	// Convert to array of any
	result := make([]any, len(storeList))
	for i, store := range storeList {
		result[i] = store.Value()
	}

	return result, nil
}

// UpdateOpeningHour updates the store's opening hours
func (c ShopeeMerchantClient) UpdateOpeningHour(token *models.Token, workingHours any) error {
	if token.AccessToken == "" || skipCallAPI() {
		return nil
	}

	headers := baseHeaders(token)

	// Prepare request body
	requestBody := map[string]any{
		"regular_hours": workingHours,
	}

	// Convert request body to JSON for signing
	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/refact/set", string(requestBodyBytes))
	if err != nil {
		return err
	}

	maps.Copy(headers, signHeaders)

	// Make the request
	data, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/regular-hours/refact/set", headers, requestBody)
	if err != nil {
		return err
	}

	// Check response
	msg := gjson.ParseBytes(data).Get("msg").String()
	if msg != "success" {
		return fmt.Errorf("failed to update opening hours: %s", msg)
	}

	return nil
}
