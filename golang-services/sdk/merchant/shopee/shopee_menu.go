package shopee

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"

	"os"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/tidwall/gjson"
)

// TextToSlug converts text to a slug format
func textToSlug(text string) string {
	// Simple implementation - in a real app, this would be more robust
	return strings.ToLower(strings.ReplaceAll(strings.TrimSpace(text), " ", "-"))
}

// SyncMenu synchronizes a menu item with Shopee
func (c ShopeeMerchantClient) SyncMenu(auth *models.Token, siteMenu *models.BrandMenu, itemID string) error {
	if auth.AccessToken == "" {
		return nil
	}

	// Get current menu from Shopee
	headers := baseHeaders(auth)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", "")
	if err != nil {
		return err
	}
	for k, v := range signHeaders {
		headers[k] = v
	}

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", headers, nil)
	if err != nil {
		return err
	}

	shopeeCategories := gjson.ParseBytes(data).Get("data.catalogs").Array()

	// Find the item to sync in the site menu
	var itemToSync *models.MenuItem
	var categoryOfItem *models.Category

	for _, category := range siteMenu.Categories {
		for _, item := range category.Items {
			if item.ID == itemID {
				itemToSync = &item
				categoryOfItem = &category
				break
			}
		}
		if itemToSync != nil {
			break
		}
	}

	if itemToSync == nil {
		return fmt.Errorf("item not found in menu")
	}

	// Upload image if available
	var newImage string
	if itemToSync.Image != "" {
		uploadedImage, err := c.uploadImage(auth, itemToSync.Image)
		if err != nil {
			return err
		}
		newImage = uploadedImage
	}

	// Find matching category in Shopee menu
	var shopeeCategory gjson.Result
	for _, cat := range shopeeCategories {
		if strings.EqualFold(cat.Get("name").String(), categoryOfItem.Name) {
			shopeeCategory = cat
			break
		}
	}

	if !shopeeCategory.Exists() {
		// Create new category if not found
		categoryData := map[string]any{
			"name": categoryOfItem.Name,
		}

		categoryDataJSON, err := json.Marshal(categoryData)
		if err != nil {
			return err
		}

		// Add sign request headers for category creation
		signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/catalogs", string(categoryDataJSON))
		if err != nil {
			return err
		}

		categoryHeaders := baseHeaders(auth)
		for k, v := range signHeaders {
			categoryHeaders[k] = v
		}

		categoryResp, err := utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/catalogs", categoryHeaders, categoryData)
		if err != nil {
			return err
		}

		// Get the newly created category
		catalogID := gjson.ParseBytes(categoryResp).Get("data.catalog_id").Int()
		if catalogID == 0 {
			return fmt.Errorf("failed to create category: %s", string(categoryResp))
		}

		// Refresh the menu to get the new category
		data, err = utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", headers, nil)
		if err != nil {
			return err
		}

		shopeeCategories = gjson.ParseBytes(data).Get("data.catalogs").Array()
		for _, cat := range shopeeCategories {
			if cat.Get("id").Int() == catalogID {
				shopeeCategory = cat
				break
			}
		}
	}

	// Find matching item in Shopee category
	var shopeeItem gjson.Result
	shopeeCategoryItems := shopeeCategory.Get("dishes").Array()
	for _, item := range shopeeCategoryItems {
		if strings.EqualFold(item.Get("name").String(), itemToSync.Name) {
			shopeeItem = item
			break
		}
	}

	// Prepare item data
	itemData := map[string]any{
		"name":        itemToSync.Name,
		"description": itemToSync.Description,
		"price":       itemToSync.Price,
		"catalog_id":  shopeeCategory.Get("id").Int(),
	}

	if newImage != "" {
		itemData["photos"] = []map[string]any{
			{
				"value": newImage,
			},
		}
	}

	itemDataJSON, err := json.Marshal(itemData)
	if err != nil {
		return err
	}

	if shopeeItem.Exists() {
		// Update existing item
		// Add sign request headers for item update
		signHeaders, err := signRequest(fmt.Sprintf("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes/%d", shopeeItem.Get("id").Int()), string(itemDataJSON))
		if err != nil {
			return err
		}

		itemHeaders := baseHeaders(auth)
		for k, v := range signHeaders {
			itemHeaders[k] = v
		}

		_, err = utils.DoRequest("PUT", fmt.Sprintf("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes/%d", shopeeItem.Get("id").Int()), itemHeaders, itemData)
		if err != nil {
			return err
		}
	} else {
		// Create new item
		// Add sign request headers for item creation
		signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", string(itemDataJSON))
		if err != nil {
			return err
		}

		itemHeaders := baseHeaders(auth)
		for k, v := range signHeaders {
			itemHeaders[k] = v
		}

		_, err = utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", itemHeaders, itemData)
		if err != nil {
			return err
		}
	}

	return nil
}

// ActiveMenuItems activates or deactivates menu items
func (c ShopeeMerchantClient) ActiveMenuItems(auth *models.Token, updateItems []map[string]any, updateAllItems bool) ([]map[string]any, error) {
	if auth.AccessToken == "" {
		return []map[string]any{}, nil
	}

	if len(updateItems) == 0 {
		return []map[string]any{}, nil
	}

	updatedItems := []map[string]any{}

	// Get current menu from Shopee
	headers := baseHeaders(auth)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", "")
	if err != nil {
		return nil, err
	}
	for k, v := range signHeaders {
		headers[k] = v
	}

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", headers, nil)
	if err != nil {
		return nil, err
	}

	shopeeCategories := gjson.ParseBytes(data).Get("data.catalogs").Array()

	for _, category := range shopeeCategories {
		categoryName := category.Get("name").String()
		categoryItems := category.Get("dishes").Array()

		for _, item := range categoryItems {
			itemName := item.Get("name").String()

			// Find if this item should be updated
			var updateItem map[string]any
			for _, ui := range updateItems {
				if strings.EqualFold(textToSlug(ui["category_name"].(string)), textToSlug(categoryName)) &&
					strings.EqualFold(textToSlug(ui["name"].(string)), textToSlug(itemName)) {
					updateItem = ui
					break
				}
			}

			if updateItem == nil && !updateAllItems {
				continue
			}

			// Determine new status
			var newStatus bool
			if updateItem != nil {
				newStatus = updateItem["active"].(bool)
			} else {
				newStatus = false // Default for updateAllItems
			}

			// Get current status
			oldStatus := item.Get("stock_info.stock").Int() == 1

			// Skip if status is already correct
			if oldStatus == newStatus {
				continue
			}

			// Prepare update data
			updateData := map[string]any{
				"dishes": []map[string]any{
					{
						"dish_id": item.Get("id").Int(),
						"stock":   utils.ShortIf(newStatus, 1, 2),
					},
				},
			}

			updateDataJSON, err := json.Marshal(updateData)
			if err != nil {
				return updatedItems, err
			}

			// Add sign request headers for stock update
			stockSignHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes/stock/bulk_set", string(updateDataJSON))
			if err != nil {
				return updatedItems, err
			}

			stockHeaders := baseHeaders(auth)
			for k, v := range stockSignHeaders {
				stockHeaders[k] = v
			}

			// Update item status
			_, err = utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes/stock/bulk_set", stockHeaders, updateData)
			if err != nil {
				return updatedItems, err
			}

			if updateItem != nil {
				updatedItems = append(updatedItems, updateItem)
			}
		}
	}

	return updatedItems, nil
}

// ActiveMenuOptionItems activates or deactivates menu option items
func (c ShopeeMerchantClient) ActiveMenuOptionItems(auth *models.Token, updateItems []map[string]any) ([]map[string]any, error) {
	if auth.AccessToken == "" {
		return []map[string]any{}, nil
	}

	if len(updateItems) == 0 {
		return []map[string]any{}, nil
	}

	updatedItems := []map[string]any{}

	// Get menu from Shopee
	menu, err := c.GetMenu(auth)
	if err != nil {
		return nil, err
	}

	for _, category := range menu.OptionCategories {
		for _, option := range category.Options {
			// Find if this option should be updated
			var updateItem map[string]any
			for _, ui := range updateItems {
				if strings.EqualFold(textToSlug(ui["category_name"].(string)), textToSlug(category.Name)) &&
					strings.EqualFold(textToSlug(ui["name"].(string)), textToSlug(option.Name)) {
					updateItem = ui
					break
				}
			}

			if updateItem == nil {
				continue
			}

			// Determine new status
			newStatus := updateItem["active"].(bool)

			// Get current status
			oldStatus := option.StockInfo.Stock == 1

			// Skip if status is already correct
			if oldStatus == newStatus {
				continue
			}

			// Prepare update data
			updateData := map[string]any{
				"options": []map[string]any{
					{
						"option_id": option.ID,
						"stock":     utils.ShortIf(newStatus, 1, 2),
					},
				},
			}

			updateDataJSON, err := json.Marshal(updateData)
			if err != nil {
				return updatedItems, err
			}

			// Add sign request headers for option stock update
			stockSignHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/options/stock/bulk_set", string(updateDataJSON))
			if err != nil {
				return updatedItems, err
			}

			stockHeaders := baseHeaders(auth)
			for k, v := range stockSignHeaders {
				stockHeaders[k] = v
			}

			// Update option status
			_, err = utils.DoRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/options/stock/bulk_set", stockHeaders, updateData)
			if err != nil {
				return updatedItems, err
			}

			updatedItems = append(updatedItems, updateItem)
		}
	}

	return updatedItems, nil
}

// GetMenu retrieves the menu from Shopee
func (c ShopeeMerchantClient) GetMenu(auth *models.Token) (*models.ShopeeMenu, error) {
	if auth.AccessToken == "" {
		return &models.ShopeeMenu{
			Categories:       []models.ShopeeCategory{},
			OptionCategories: []models.ShopeeOptionCategory{},
		}, nil
	}

	headers := baseHeaders(auth)

	// Add sign request headers
	signHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", "")
	if err != nil {
		return nil, err
	}
	for k, v := range signHeaders {
		headers[k] = v
	}

	data, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/dishes", headers, nil)
	if err != nil {
		return nil, err
	}

	// Parse categories
	categories := []models.ShopeeCategory{}
	catalogs := gjson.ParseBytes(data).Get("data.catalogs").Array()
	for _, catalog := range catalogs {
		category := models.ShopeeCategory{
			ID:    catalog.Get("id").Int(),
			Name:  catalog.Get("name").String(),
			Items: []models.ShopeeItem{},
		}

		dishes := catalog.Get("dishes").Array()
		for _, dish := range dishes {
			item := models.ShopeeItem{
				ID:   dish.Get("id").Int(),
				Name: dish.Get("name").String(),
				StockInfo: models.ShopeeStockInfo{
					Stock: int(dish.Get("stock_info.stock").Int()),
				},
			}
			category.Items = append(category.Items, item)
		}

		categories = append(categories, category)
	}

	// Get option categories
	optionSignHeaders, err := signRequest("https://gmerchant.deliverynow.vn/api/v5/seller/store/options", "")
	if err != nil {
		return nil, err
	}

	optionHeaders := baseHeaders(auth)
	for k, v := range optionSignHeaders {
		optionHeaders[k] = v
	}

	optionData, err := utils.DoRequest("GET", "https://gmerchant.deliverynow.vn/api/v5/seller/store/options", optionHeaders, nil)
	if err != nil {
		return nil, err
	}

	// Parse option categories
	optionCategories := []models.ShopeeOptionCategory{}
	optionGroups := gjson.ParseBytes(optionData).Get("data.option_groups").Array()
	for _, group := range optionGroups {
		optionCategory := models.ShopeeOptionCategory{
			ID:      group.Get("id").Int(),
			Name:    group.Get("name").String(),
			Options: []models.ShopeeOption{},
		}

		options := group.Get("options").Array()
		for _, option := range options {
			opt := models.ShopeeOption{
				ID:   option.Get("id").Int(),
				Name: option.Get("name").String(),
				StockInfo: models.ShopeeStockInfo{
					Stock: int(option.Get("stock_info.stock").Int()),
				},
			}
			optionCategory.Options = append(optionCategory.Options, opt)
		}

		optionCategories = append(optionCategories, optionCategory)
	}

	return &models.ShopeeMenu{
		Categories:       categories,
		OptionCategories: optionCategories,
	}, nil
}

// Helper function to upload an image to Shopee
func (c ShopeeMerchantClient) uploadImage(auth *models.Token, imageURL string) (string, error) {
	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Create a temporary file
	tempFile, err := os.CreateTemp("", "shopee-image-*.jpg")
	if err != nil {
		return "", err
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Copy the image to the temporary file
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		return "", err
	}

	// Prepare multipart form
	var requestBody bytes.Buffer
	multipartWriter := multipart.NewWriter(&requestBody)

	// Add the file
	fileWriter, err := multipartWriter.CreateFormFile("file", tempFile.Name())
	if err != nil {
		return "", err
	}

	// Reset file pointer to beginning
	tempFile.Seek(0, 0)

	// Copy file content to form
	_, err = io.Copy(fileWriter, tempFile)
	if err != nil {
		return "", err
	}

	// Close multipart writer
	multipartWriter.Close()

	// Create request
	req, err := http.NewRequest("POST", "https://gmerchant.deliverynow.vn/api/v5/seller/store/upload_image", &requestBody)
	if err != nil {
		return "", err
	}

	// Set headers
	headers := baseHeaders(auth)
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	req.Header.Set("Content-Type", multipartWriter.FormDataContentType())

	// Add sign request headers
	// Note: We can't sign this request because it's multipart form data
	// In a real implementation, you would need to handle this case specially

	// Send request
	client := &http.Client{}
	resp2, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp2.Body.Close()

	// Read response
	respBody, err := io.ReadAll(resp2.Body)
	if err != nil {
		return "", err
	}

	// Parse response
	imageURL = gjson.ParseBytes(respBody).Get("data.url").String()
	if imageURL == "" {
		return "", fmt.Errorf("failed to upload image: %s", string(respBody))
	}

	return imageURL, nil
}
