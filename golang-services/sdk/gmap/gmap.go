package gmap

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

type PlacesAutocompleteResponse struct {
	Predictions []Prediction `json:"predictions"`
	Status      string       `json:"status"`
}

type Prediction struct {
	Description          string               `json:"description"`
	MatchedSubstrings    []MatchedSubstring   `json:"matched_substrings"`
	PlaceID              string               `json:"place_id"`
	Reference            string               `json:"reference"`
	StructuredFormatting StructuredFormatting `json:"structured_formatting"`
	Terms                []Term               `json:"terms"`
	Types                []string             `json:"types"`
}

type MatchedSubstring struct {
	Length int `json:"length"`
	Offset int `json:"offset"`
}

type StructuredFormatting struct {
	MainText                  string             `json:"main_text"`
	MainTextMatchedSubstrings []MatchedSubstring `json:"main_text_matched_substrings"`
	SecondaryText             string             `json:"secondary_text"`
}

type Term struct {
	Offset int    `json:"offset"`
	Value  string `json:"value"`
}

type Location struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type Geometry struct {
	Location Location `json:"location"`
}

type GeocodingResult struct {
	Geometry Geometry `json:"geometry"`
}

type GeocodingResponse struct {
	Results []GeocodingResult `json:"results"`
	Status  string            `json:"status"`
}

func GetLocation(address string) (*GeocodingResult, error) {
	apiKey := os.Getenv("GOOGLE_MAP_API_KEY")
	baseURL := "https://maps.googleapis.com/maps/api/geocode/json"

	params := url.Values{}
	params.Add("address", address)
	params.Add("key", apiKey)

	resp, err := http.Get(fmt.Sprintf("%s?%s", baseURL, params.Encode()))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var response GeocodingResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	if response.Status != "OK" || len(response.Results) == 0 {
		return nil, fmt.Errorf("geocoding failed: %s", response.Status)
	}

	return &response.Results[0], nil
}

func GetSuggestionAddresses(address string) ([]models.AddressObj, error) {
	apiKey := os.Getenv("GOOGLE_MAP_API_KEY")
	baseURL := "https://maps.googleapis.com/maps/api/place/autocomplete/json"

	params := url.Values{}
	params.Add("input", address)
	params.Add("key", apiKey)
	params.Add("components", "country:VN")
	params.Add("language", "vi")

	resp, err := http.Get(fmt.Sprintf("%s?%s", baseURL, params.Encode()))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var response PlacesAutocompleteResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, err
	}

	var suggestions []models.AddressObj
	for _, prediction := range response.Predictions {
		terms := prediction.Terms
		if len(terms) < 5 {
			continue
		}

		suggestions = append(suggestions, models.AddressObj{
			FormattedAddress: prediction.Description,
			Location:         models.AddressLocation{Lat: 0, Lng: 0},
			ProvinceName:     terms[len(terms)-2].Value,
			DistrictName:     terms[len(terms)-3].Value,
			WardName:         terms[len(terms)-4].Value,
			Route:            terms[len(terms)-5].Value,
		})
	}

	return suggestions, nil
}
