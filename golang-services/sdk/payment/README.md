# Generic Payment SDK

This package provides a generic payment interface for handling multiple payment providers including MOMO, NEXDORPay, PayOS, and VNPay. It's designed to be consistent with the existing Node.js implementation while providing a clean, type-safe Go interface.

## Features

- **Generic Interface**: Unified interface for all payment providers
- **Multiple Providers**: Support for MOMO, NEXDORPay, PayOS, and VNPay
- **Consistent API**: Same request/response structures across all providers
- **Signature Generation**: Automatic signature generation and validation
- **Callback Handling**: Built-in callback processing for webhooks
- **Error Handling**: Comprehensive error handling with localized messages
- **Type Safety**: Full type safety with Go structs

## Supported Payment Providers

### MOMO
- **Variants**: MOMO, MOMO_VTS, MOMO_ATM, MOMO_MOD
- **Features**: QR Code, Deeplink, Mobile app integration
- **Signature**: HMAC-SHA256 based signature generation

### NEXDORPay
- **Features**: QR Code generation, Transaction tracking
- **Signature**: HMAC-SHA256 based signature generation
- **Special**: Supports both signed and unsigned requests

### PayOS
- **Features**: QR Code, Bank transfer integration
- **Signature**: HMAC-SHA256 based signature generation
- **Special**: Numeric order code generation

### VNPay
- **Variants**: VNPAY_QR, VNBANK, INTCARD, VISA, MASTERCARD, JCB
- **Features**: QR Code, Bank transfer, Credit/Debit cards, Internet banking
- **Signature**: HMAC-SHA512 based signature generation
- **Special**: Most popular payment gateway in Vietnam, supports multiple payment methods

## Usage

### Basic Usage

```go
package main

import (
    "fmt"
    "log"
    
    "github.com/nexdorvn/nexpos-backend/golang-services/sdk/payment"
    "github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func main() {
    // Configure token (credentials)
    token := &models.Token{
        SiteID:   "PARTNER123",
        Username: "test_username", 
        Password: "test_password",
    }

    // Create payment request
    request := &payment.PaymentRequest{
        OrderID:        "ORDER_12345",
        Amount:         100000, // 100,000 VND
        Description:    "Payment for order ORDER_12345",
        ClientCallback: "https://example.com/payment/success",
        ServerCallback: "https://example.com/api/payment/callback",
        PaymentMethod:  "VNPAY_QR", // VNPay QR code
        Language:       "vi",
    }

    // Create payment provider
    provider := payment.NewProvider("vnpay")
    if provider == nil {
        log.Fatal("Unsupported payment provider")
    }

    // Create payment link
    response, err := provider.CreatePaymentLink(token, request)
    if err != nil {
        log.Fatalf("Payment creation failed: %v", err)
    }

    fmt.Printf("Payment URL: %s\n", response.PayURL)
    fmt.Printf("QR Code: %s\n", response.QRCode)
    fmt.Printf("Transaction ID: %s\n", response.TransactionID)
}
```

### VNPay Specific Usage

```go
// VNPay with different payment methods
vnpayRequest := &payment.PaymentRequest{
    OrderID:        "ORDER_12345",
    Amount:         100000,
    Description:    "Payment for order ORDER_12345",
    ClientCallback: "https://example.com/payment/success",
    ServerCallback: "https://example.com/api/payment/callback",
    PaymentMethod:  "VNPAY_QR", // Options: VNPAY_QR, VNBANK, INTCARD, VISA, MASTERCARD, JCB
    Language:       "vi", // "vi" for Vietnamese, "en" for English
}

provider := payment.NewProvider("vnpay")
response, err := provider.CreatePaymentLink(token, vnpayRequest)
```

### Processing VNPay Callbacks

```go
// VNPay callback processing
vnpayCallbackData := map[string]interface{}{
    "vnp_ResponseCode":  "00", // Success code
    "vnp_TxnRef":        "ORDER_12345_20240101120000",
    "vnp_Amount":        "********", // 100,000 VND * 100
    "vnp_TransactionNo": "********",
    "vnp_SecureHash":    "generated_hash",
}

callbackResp, err := provider.ProcessCallback(vnpayCallbackData)
if err != nil {
    log.Printf("Failed to process VNPay callback: %v", err)
} else {
    fmt.Printf("Payment Success: %t\n", callbackResp.Success)
    fmt.Printf("Status: %s\n", callbackResp.Status)
    fmt.Printf("Message: %s\n", callbackResp.Message)
}
```

## Provider-Specific Configuration

### MOMO Configuration
- **SiteID**: Partner Code from MOMO
- **Username**: Access Key from MOMO
- **Password**: Secret Key from MOMO

### NEXDORPay Configuration
- **SiteID**: Not used
- **Username**: Cake Account from NEXDORPay
- **Password**: Secret Key from NEXDORPay

### PayOS Configuration
- **SiteID**: Checksum Key from PayOS
- **Username**: Client ID from PayOS
- **Password**: API Key from PayOS

### VNPay Configuration
- **SiteID**: Terminal ID (vnp_TmnCode) from VNPay
- **Username**: Not used
- **Password**: Secret Key (Hash Secret) from VNPay

## VNPay Payment Methods

VNPay supports various payment methods through the `PaymentMethod` field:

- **VNPAY_QR**: QR Code payment (most popular for mobile)
- **VNBANK**: Vietnamese bank accounts
- **INTCARD**: International cards
- **VISA**: Visa cards
- **MASTERCARD**: Mastercard
- **JCB**: JCB cards
- **Empty string**: Let user choose payment method

## VNPay Response Codes

Common VNPay response codes and their meanings:

- **00**: Transaction successful
- **07**: Transaction successful but suspicious
- **09**: Customer's card/account not registered for Internet Banking
- **10**: Customer authentication failed more than 3 times
- **11**: Payment timeout
- **12**: Customer's card/account is locked
- **13**: Wrong OTP
- **24**: Customer cancelled transaction
- **51**: Insufficient balance
- **65**: Daily transaction limit exceeded
- **75**: Bank under maintenance
- **79**: Wrong payment password too many times
- **99**: Other errors

## Testing

Run the example to test all payment systems including VNPay:

```go
payment.Example()
```

This will demonstrate all payment providers and callback handling.

## Migration from Node.js

This Go implementation maintains compatibility with the existing Node.js implementation:

- Same signature generation algorithms
- Same request/response formats
- Same callback handling logic
- Same transaction ID generation
- Added VNPay support with industry-standard implementation

## Dependencies

- `github.com/go-resty/resty/v2` - HTTP client
- `github.com/google/uuid` - UUID generation
- Standard Go crypto libraries for signature generation

## Contributing

When adding new payment providers:

1. Implement the `PaymentProvider` interface
2. Add the provider to the `NewProvider` factory function
3. Add comprehensive tests
4. Update this documentation
