package payment

import (
	"fmt"
	"log"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// VNPayQRCodeExample demonstrates how to use VNPay QRCode payment
func VNPayQRCodeExample() {
	// Create VNPay client
	vnpayClient := NewVNPayClient()

	// Create a test token (in real implementation, this would come from your authentication system)
	token := &models.Token{
		SiteID:   "0318864354EV",   // Test merchant code
		Password: "vnpay@MERCHANT", // Test secret key for QR creation
	}

	// Example 1: Create QRCode Payment
	fmt.Println("=== VNPay QRCode Payment Example ===")

	paymentRequest := &PaymentRequest{
		OrderID:        "ORDER_001",
		Amount:         100000, // 100,000 VND
		Currency:       "VND",
		Description:    "Payment for order ORDER_001",
		ClientCallback: "https://your-app.com/payment/callback",
		ServerCallback: "https://your-app.com/payment/webhook",
		PaymentMethod:  "VNPAY_QR",
		Language:       "vn",
	}

	paymentResponse, err := vnpayClient.CreatePaymentLink(token, paymentRequest)
	if err != nil {
		log.Printf("Error creating payment: %v", err)
		return
	}

	fmt.Printf("Payment created successfully:\n")
	fmt.Printf("- Transaction ID: %s\n", paymentResponse.TransactionID)
	fmt.Printf("- QR Code: %s\n", paymentResponse.QRCode)
	fmt.Printf("- Payment URL: %s\n", paymentResponse.PayURL)
	fmt.Printf("- Success: %t\n", paymentResponse.Success)
	fmt.Printf("- Message: %s\n", paymentResponse.Message)

	// Example 2: Check Transaction Status
	fmt.Println("\n=== Check Transaction Status ===")

	// Use different token for check transaction (different secret key)
	checkToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpay@123@langhaHangLa", // Test secret key for check transaction
	}

	status, err := vnpayClient.GetTransactionStatus(checkToken, paymentResponse.TransactionID)
	if err != nil {
		log.Printf("Error checking transaction status: %v", err)
		return
	}

	fmt.Printf("Transaction status:\n")
	fmt.Printf("- Transaction ID: %s\n", status.TransactionID)
	fmt.Printf("- Order ID: %s\n", status.OrderID)
	fmt.Printf("- Amount: %.2f\n", status.Amount)
	fmt.Printf("- Status: %s\n", status.Status)
	fmt.Printf("- Message: %s\n", status.Message)
	fmt.Printf("- Created At: %s\n", status.CreatedAt.Format("2006-01-02 15:04:05"))

	// Example 3: Process Refund
	fmt.Println("\n=== Process Refund ===")

	refundRequest := &RefundRequest{
		TransactionID: paymentResponse.TransactionID,
		Amount:        50000, // Partial refund of 50,000 VND
		Reason:        "Customer requested refund",
	}

	// Use different token for refund (different secret key)
	refundToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpayRefund", // Test secret key for refund
	}

	refundResponse, err := vnpayClient.Refund(refundToken, refundRequest)
	if err != nil {
		log.Printf("Error processing refund: %v", err)
		return
	}

	fmt.Printf("Refund processed:\n")
	fmt.Printf("- Success: %t\n", refundResponse.Success)
	fmt.Printf("- Transaction ID: %s\n", refundResponse.TransactionID)
	fmt.Printf("- Refund ID: %s\n", refundResponse.RefundID)
	fmt.Printf("- Amount: %.2f\n", refundResponse.Amount)
	fmt.Printf("- Status: %s\n", refundResponse.Status)
	fmt.Printf("- Message: %s\n", refundResponse.Message)

	// Example 4: Process Callback (webhook simulation)
	fmt.Println("\n=== Process Callback ===")

	// Simulate callback data from VNPay
	callbackData := map[string]interface{}{
		"vnp_Amount":        "********", // Amount in smallest unit (100,000 VND * 100)
		"vnp_BankCode":      "VNPAYQR",
		"vnp_OrderInfo":     "Payment for order ORDER_001",
		"vnp_ResponseCode":  "00", // Success code
		"vnp_TmnCode":       "0318864354EV",
		"vnp_TxnRef":        paymentResponse.TransactionID,
		"vnp_TransactionNo": "********",
		"vnp_PayDate":       "**************",
	}

	callbackResponse, err := vnpayClient.ProcessCallback(callbackData)
	if err != nil {
		log.Printf("Error processing callback: %v", err)
		return
	}

	fmt.Printf("Callback processed:\n")
	fmt.Printf("- Success: %t\n", callbackResponse.Success)
	fmt.Printf("- Transaction ID: %s\n", callbackResponse.TransactionID)
	fmt.Printf("- Order ID: %s\n", callbackResponse.OrderID)
	fmt.Printf("- Status: %s\n", callbackResponse.Status)
	fmt.Printf("- Amount: %.2f\n", callbackResponse.Amount)
	fmt.Printf("- Message: %s\n", callbackResponse.Message)
}

// VNPayTestCredentials shows the test credentials provided by VNPay
func VNPayTestCredentials() {
	fmt.Println("=== VNPay Test Credentials ===")
	fmt.Println("QR Information:")
	fmt.Println("- merchantCode: 0318864354EV")
	fmt.Println("- merchantName: EVERY TECH")
	fmt.Println("- merchantType: 4513")
	fmt.Println("- terminalId: EVETESTT")
	fmt.Println()
	fmt.Println("API Information:")
	fmt.Println("- Gen QR URL: https://doitac-tran.vnpaytest.vn/QRCreateAPIRestV2/rest/CreateQrcodeApi/createQrcode")
	fmt.Println("- Gen QR secretKey: vnpay@MERCHANT")
	fmt.Println("- Gen QR appID: MERCHANT")
	fmt.Println()
	fmt.Println("- Check Transaction URL: https://doitac-tran.vnpaytest.vn/CheckTransaction/rest/api/CheckTrans")
	fmt.Println("- Check Transaction secretKey: vnpay@123@langhaHangLa")
	fmt.Println()
	fmt.Println("- Refund URL: https://doitac-tran.vnpaytest.vn/mms/refund")
	fmt.Println("- Refund secretKey: vnpayRefund")
	fmt.Println()
	fmt.Println("Test Tool Information:")
	fmt.Println("- Account: **********")
	fmt.Println("- Password: aaaa1111")
	fmt.Println("- OTP: 123456")
	fmt.Println()
	fmt.Println("iOS TestFlight: https://testflight.apple.com/join/jNWVzY6E")
}

// Usage example function
func ExampleUsage() {
	fmt.Println("Running VNPay QRCode Examples...")

	// Show test credentials
	VNPayTestCredentials()

	fmt.Println("\n" + strings.Repeat("=", 50))

	// Run the main example
	VNPayQRCodeExample()
}
