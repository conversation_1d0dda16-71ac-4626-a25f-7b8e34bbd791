package delivery

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// Provider represents the interface for shipping service providers
type Provider interface {
	// GetToken gets authentication token
	GetToken(username, password string) (*models.ShipToken, error)

	// CheckPromoCode validates a promo code for a shipment
	CheckPromoCode(token *models.ShipToken, request *models.PromoCheckRequest) (*models.PromoCheckResponse, error)

	// CreateOrder creates a new shipment order
	CreateOrder(token *models.ShipToken, request *models.CreateShipmentRequest) (*models.CreateShipmentResponse, error)

	// CancelOrder cancels an existing shipment order
	CancelOrder(token *models.ShipToken, shipmentID string) (*models.CancelShipmentResponse, error)

	// GetOrderDetail retrieves details of a specific shipment
	GetOrderDetail(token *models.ShipToken, shipmentID string) (*models.ShipmentDetail, error)

	// GetShipments retrieves available shipment options
	GetShipments(token *models.ShipToken, request *models.GetShipmentsRequest) ([]models.ShipmentOption, error)

	// GetStore retrieves store information
	GetStore(token *models.ShipToken) (any, error)

	// ProcessWebhook processes webhook data from the shipping provider
	ProcessWebhook(data map[string]any) (*models.WebhookResponse, error)
}

// Common HTTP client for delivery service requests
var HttpClient = &http.Client{
	Timeout: 30 * time.Second,
}
