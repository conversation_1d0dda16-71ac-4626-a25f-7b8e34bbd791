package grab_express

import (
	"encoding/json"
	"fmt"

	"os"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/gmap"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/tidwall/gjson"
)

// Configuration for Grab Express API
type Config struct {
	BaseURL string
}

// Client implements the delivery.Provider interface for Grab Express
type Client struct {
	Config Config
}

// NewClient creates a new Grab Express client
func NewClient() delivery.Provider {
	var config Config
	if os.Getenv("NODE_ENV") == "prod" {
		config = Config{
			BaseURL: "https://partner-api.grab.com/grab-express",
		}
	} else {
		config = Config{
			BaseURL: "https://partner-api.grab.com/grab-express-sandbox",
		}
	}

	return &Client{
		Config: config,
	}
}

// GetToken gets authentication token from Grab Express
func (c *Client) GetToken(username, password string) (*models.ShipToken, error) {
	clientID := username
	clientSecret := password

	if os.Getenv("NODE_ENV") == "dev" {
		return &models.ShipToken{
			Username:     username,
			AccessToken:  "**********",
			RefreshToken: "**********",
		}, nil
	}

	if clientSecret == "" {
		return nil, fmt.Errorf("client secret is required")
	}

	headers := map[string]string{
		"content-type": "application/json; charset=utf-8",
	}

	body := map[string]any{
		"client_id":     clientID,
		"client_secret": clientSecret,
		"grant_type":    "client_credentials",
		"scope":         "grab_express.partner_deliveries",
	}

	data, err := utils.DoRequest("POST", "https://partner-api.grab.com/grabid/v1/oauth2/token", headers, body)
	if err != nil {
		return nil, err
	}

	accessToken := gjson.GetBytes(data, "access_token").String()
	if accessToken == "" {
		return nil, fmt.Errorf("failed to get access token")
	}

	return &models.ShipToken{
		Username:    username,
		AccessToken: accessToken,
		ExpiredAt:   time.Now().Add(1 * time.Hour), // Token typically expires in 1 hour
	}, nil
}

// CheckPromoCode validates a promo code for Grab Express shipment
func (c *Client) CheckPromoCode(token *models.ShipToken, request *models.PromoCheckRequest) (*models.PromoCheckResponse, error) {
	return &models.PromoCheckResponse{
		Valid: false,
		Data:  nil,
	}, nil
}

// CreateOrder creates a new shipment order with Grab Express
func (c *Client) CreateOrder(token *models.ShipToken, request *models.CreateShipmentRequest) (*models.CreateShipmentResponse, error) {
	if token.AccessToken == "" {
		return nil, fmt.Errorf("access token is required")
	}

	// Get location coordinates for from and to addresses
	fromLocation, err := gmap.GetLocation(request.From.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get location for from address: %v", err)
	}

	toLocation, err := gmap.GetLocation(request.To.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get location for to address: %v", err)
	}

	// Format phone numbers (assuming they are Vietnamese numbers)
	fromPhone := formatPhoneNumber(request.From.Phone)
	toPhone := formatPhoneNumber(request.To.Phone)

	// Create packages from dishes
	packages := make([]map[string]any, 0, len(request.Dishes))
	for _, dish := range request.Dishes {
		packages = append(packages, map[string]any{
			"name":        dish.Name,
			"description": "",
			"quantity":    dish.Quantity,
			"price":       dish.DiscountPrice,
			"dimensions": map[string]any{
				"height": 0,
				"width":  0,
				"depth":  0,
				"weight": 0,
			},
		})
	}

	// Create request body
	body := map[string]any{
		"merchantOrderID": request.TrackingNumber + "_" + fmt.Sprintf("%d", time.Now().Unix()),
		"serviceType":     request.Service.Code,
		"vehicleType":     "BIKE",
		"codType":         "REGULAR",
		"paymentMethod":   "CASHLESS", // Delivery fee (paid to driver) payment method
		"highValue":       false,
		"packages":        packages,
		"origin": map[string]any{
			"address": request.From.Address,
			"coordinates": map[string]any{
				"latitude":  fromLocation.Geometry.Location.Lat,
				"longitude": fromLocation.Geometry.Location.Lng,
			},
		},
		"destination": map[string]any{
			"address": request.To.Address,
			"coordinates": map[string]any{
				"latitude":  toLocation.Geometry.Location.Lat,
				"longitude": toLocation.Geometry.Location.Lng,
			},
			"cashOnDelivery": map[string]any{
				"amount": request.COD,
			},
		},
		"recipient": map[string]any{
			"firstName":  request.To.Name,
			"phone":      toPhone,
			"smsEnabled": true,
		},
		"sender": map[string]any{
			"firstName":   request.From.Name,
			"phone":       fromPhone,
			"smsEnabled":  true,
			"instruction": request.Note,
		},
		"payer": "SENDER",
		"cashOnDelivery": map[string]any{
			"amount": request.COD,
		},
	}

	// Handle scheduled orders
	if request.ScheduleOrderTime > 0 && request.ScheduleOrderTime > time.Now().Add(1*time.Minute).Unix() {
		pickupTime := time.Unix(request.ScheduleOrderTime, 0).Format("2006-01-02T15:04:05Z")
		body["schedule"] = map[string]any{
			"pickupTimeFrom": pickupTime,
			"pickupTimeTo":   pickupTime,
		}
	} else if request.Service.Code == "INSTANT" {
		pickupTime := time.Now().Add(1 * time.Minute).Format("2006-01-02T15:04:05Z")
		body["schedule"] = map[string]any{
			"pickupTimeFrom": pickupTime,
			"pickupTimeTo":   pickupTime,
		}
	} else if request.Service.Code == "SAME_DAY" {
		pickupTime := time.Now().Add(5 * time.Hour).Format("2006-01-02T15:04:05Z")
		body["schedule"] = map[string]any{
			"pickupTimeFrom": pickupTime,
			"pickupTimeTo":   pickupTime,
		}
	}

	// Make API request
	headers := map[string]string{
		"Authorization": "Bearer " + token.AccessToken,
		"Content-Type":  "application/json",
	}

	data, err := utils.DoRequest("POST", c.Config.BaseURL+"/v1/deliveries", headers, body)
	if err != nil {
		return nil, err
	}

	// Parse response
	deliveryID := gjson.GetBytes(data, "deliveryID").String()
	trackingURL := gjson.GetBytes(data, "trackingURL").String()
	amount := gjson.GetBytes(data, "quote.amount").Float()

	return &models.CreateShipmentResponse{
		ShipmentID:  deliveryID,
		TrackingURL: trackingURL,
		Price:       amount,
		RawRequest:  body,
		RawResponse: json.RawMessage(data),
	}, nil
}

// CancelOrder cancels an existing Grab Express shipment
func (c *Client) CancelOrder(token *models.ShipToken, shipmentID string) (*models.CancelShipmentResponse, error) {
	if token.AccessToken == "" {
		return nil, fmt.Errorf("access token is required")
	}

	// Make API request
	headers := map[string]string{
		"Authorization": "Bearer " + token.AccessToken,
		"Content-Type":  "application/json",
	}

	_, err := utils.DoRequest("DELETE", c.Config.BaseURL+"/v1/deliveries/"+shipmentID, headers, nil)
	if err != nil {
		return &models.CancelShipmentResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &models.CancelShipmentResponse{
		Success: true,
		Message: "",
	}, nil
}

// GetOrderDetail retrieves details of a specific Grab Express shipment
func (c *Client) GetOrderDetail(token *models.ShipToken, shipmentID string) (*models.ShipmentDetail, error) {
	if token.AccessToken == "" {
		return nil, fmt.Errorf("access token is required")
	}

	// Make API request
	headers := map[string]string{
		"Authorization": "Bearer " + token.AccessToken,
		"Content-Type":  "application/json",
	}

	data, err := utils.DoRequest("GET", c.Config.BaseURL+"/v1/deliveries/"+shipmentID, headers, nil)
	if err != nil {
		return nil, err
	}

	// Parse response
	status := gjson.GetBytes(data, "status").String()
	driverName := gjson.GetBytes(data, "driver.name").String()
	driverPhone := gjson.GetBytes(data, "driver.phone").String()

	return &models.ShipmentDetail{
		Status:      status,
		DriverName:  driverName,
		DriverPhone: driverPhone,
		RawData:     json.RawMessage(data),
	}, nil
}

// GetShipments retrieves available Grab Express shipment options
func (c *Client) GetShipments(token *models.ShipToken, request *models.GetShipmentsRequest) ([]models.ShipmentOption, error) {
	if token.AccessToken == "" {
		return nil, fmt.Errorf("access token is required")
	}

	// Get location coordinates for from and to addresses
	fromLocation, err := gmap.GetLocation(request.From.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get location for from address: %v", err)
	}

	toLocation, err := gmap.GetLocation(request.To.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get location for to address: %v", err)
	}

	// Determine service types to query
	serviceTypes := []string{"INSTANT", "SAME_DAY"}
	if siteData, ok := token.SiteData["service_types"].(string); ok && siteData != "" {
		serviceTypes = strings.Split(siteData, "|")
		for i, st := range serviceTypes {
			serviceTypes[i] = strings.TrimSpace(st)
		}
	}

	// Query each service type
	var shipmentOptions []models.ShipmentOption

	for _, serviceType := range serviceTypes {
		// Create request body
		body := map[string]any{
			"serviceType": serviceType,
			"codType":     "REGULAR",
			"packages": []map[string]any{
				{
					"name":        "Hàng tiêu dùng",
					"description": "Hàng tiêu dùng, bánh, sữa",
					"quantity":    1,
					"price":       1,
					"dimensions": map[string]any{
						"height": 0,
						"width":  0,
						"depth":  0,
						"weight": 0,
					},
				},
			},
			"origin": map[string]any{
				"address": request.From.Address,
				"coordinates": map[string]any{
					"latitude":  fromLocation.Geometry.Location.Lat,
					"longitude": fromLocation.Geometry.Location.Lng,
				},
			},
			"destination": map[string]any{
				"address": request.To.Address,
				"coordinates": map[string]any{
					"latitude":  toLocation.Geometry.Location.Lat,
					"longitude": toLocation.Geometry.Location.Lng,
				},
			},
		}

		// Make API request
		headers := map[string]string{
			"Authorization": "Bearer " + token.AccessToken,
			"Content-Type":  "application/json",
		}

		data, err := utils.DoRequest("POST", c.Config.BaseURL+"/v1/deliveries/quotes", headers, body)
		if err != nil {
			continue // Skip this service type if there's an error
		}

		// Parse quotes from response
		quotes := gjson.GetBytes(data, "quotes").Array()
		for _, quote := range quotes {
			// Map the quote to a shipment option
			option := mapShipmentOption("grab_express", quote, data)
			shipmentOptions = append(shipmentOptions, option)
		}
	}

	return shipmentOptions, nil
}

// GetStore retrieves Grab Express store information
func (c *Client) GetStore(token *models.ShipToken) (any, error) {
	// Grab Express doesn't have a store information endpoint
	return nil, nil
}

// ProcessWebhook processes webhook data from Grab Express
func (c *Client) ProcessWebhook(data map[string]any) (*models.WebhookResponse, error) {
	// Extract data from webhook payload
	merchantOrderID, _ := data["merchantOrderID"].(string)
	deliveryID, _ := data["deliveryID"].(string)
	status, _ := data["status"].(string)
	trackURL, _ := data["trackURL"].(string)
	failedReason, _ := data["failedReason"].(string)

	// Map Grab Express status to internal status
	statusMap := map[string]struct {
		OrderStatus    string
		ShipmentStatus string
	}{
		"QUEUEING":         {OrderStatus: "DOING", ShipmentStatus: "ORDER_CREATED"},
		"ALLOCATING":       {OrderStatus: "DOING", ShipmentStatus: "DRIVER_ASSIGNING"},
		"PENDING_PICKUP":   {OrderStatus: "DOING", ShipmentStatus: "DRIVER_PICKING_UP"},
		"PICKING_UP":       {OrderStatus: "DOING", ShipmentStatus: "DRIVER_PICKING_UP"},
		"IN_DELIVERY":      {OrderStatus: "PICK", ShipmentStatus: "IN_DELIVERY"},
		"PENDING_DROP_OFF": {OrderStatus: "PICK", ShipmentStatus: "DRIVER_PICKING_UP"},
		"COMPLETED":        {OrderStatus: "FINISH", ShipmentStatus: "COMPLETED"},
		"IN_RETURN":        {OrderStatus: "RETURNING", ShipmentStatus: "RETURNED"},
		"RETURNED":         {OrderStatus: "RETURNED", ShipmentStatus: "RETURNED"},
		"CANCELED":         {OrderStatus: "", ShipmentStatus: "CANCELLED"},
		"FAILED":           {OrderStatus: "", ShipmentStatus: "CANCELLED"},
	}

	// Get mapped status
	mappedStatus, exists := statusMap[status]
	if !exists {
		mappedStatus = struct {
			OrderStatus    string
			ShipmentStatus string
		}{OrderStatus: "", ShipmentStatus: ""}
	}

	// Extract driver information
	var driverName, driverPhone string
	if driver, ok := data["driver"].(map[string]any); ok {
		driverName, _ = driver["name"].(string)
		driverPhone, _ = driver["phone"].(string)
	}

	// Create response
	response := &models.WebhookResponse{
		ShipmentID:     deliveryID,
		OrderID:        merchantOrderID,
		ShipmentStatus: mappedStatus.ShipmentStatus,
		OrderStatus:    mappedStatus.OrderStatus,
		DriverName:     driverName,
		DriverPhone:    driverPhone,
		TrackingURL:    trackURL,
	}

	// Handle cancellation
	if mappedStatus.ShipmentStatus == "CANCELLED" {
		// Map cancellation reasons
		cancelMap := map[string]struct {
			CancelBy     string
			CancelType   string
			CancelReason string
		}{
			"1|Canceled by Sender":                    {CancelBy: "merchant", CancelType: "", CancelReason: "Quán hủy đơn"},
			"2|Canceled by Driver: fake sender/order": {CancelBy: "driver", CancelType: "", CancelReason: "Tài xế hủy đơn"},
			"5|Canceled by Grab Operator":             {CancelBy: "system", CancelType: "", CancelReason: "Hủy bởi Grab (hệ thống)"},
			"6|Could not find driver":                 {CancelBy: "driver", CancelType: "", CancelReason: "Không tìm được tài xế"},
		}

		if cancelInfo, exists := cancelMap[failedReason]; exists {
			response.Cancel = &models.CancelInfo{
				CancelBy:     cancelInfo.CancelBy,
				CancelType:   cancelInfo.CancelType,
				CancelReason: fmt.Sprintf("%s (%s)", failedReason, cancelInfo.CancelReason),
			}
		} else {
			response.Cancel = &models.CancelInfo{
				CancelBy:     "system",
				CancelType:   "",
				CancelReason: failedReason,
			}
		}
	}

	return response, nil
}

// Helper function to format phone numbers
func formatPhoneNumber(phone string) string {
	// Simple implementation - in production, use a proper phone number library
	phone = strings.TrimSpace(phone)
	phone = strings.TrimPrefix(phone, "+84")
	phone = strings.TrimPrefix(phone, "0")
	return phone
}

// Helper function to map a Grab Express quote to a shipment option
func mapShipmentOption(vendor string, quote gjson.Result, rawData []byte) models.ShipmentOption {
	// Extract data from quote
	serviceType := quote.Get("service.type").String()
	amount := quote.Get("amount").Float()

	// Extract timeline information
	pickupTime := quote.Get("estimatedTimeline.pickup").String()
	dropoffTime := quote.Get("estimatedTimeline.dropoff").String()

	// Parse times
	pickupTimeParsed, _ := time.Parse(time.RFC3339, pickupTime)
	dropoffTimeParsed, _ := time.Parse(time.RFC3339, dropoffTime)

	// Create name mapping
	nameMap := map[string]string{
		"SAME_DAY": "Trong ngày",
		"INSTANT":  "Siêu tốc",
	}

	// Create description
	description := fmt.Sprintf("Dự kiến thời gian lấy hàng: %s, giao hàng: %s",
		pickupTimeParsed.Format("15:04"),
		dropoffTimeParsed.Format("15:04"))

	// Create raw data with schedule information
	raw := map[string]any{
		"service": map[string]any{
			"type": serviceType,
		},
		"amount": amount,
		"schedule": map[string]any{
			"from_time":      pickupTimeParsed.Format("15:04"),
			"to_time":        dropoffTimeParsed.Format("15:04"),
			"from_date_time": pickupTimeParsed.Format(time.RFC3339),
			"to_date_time":   dropoffTimeParsed.Format(time.RFC3339),
		},
	}

	// Add the original quote data
	rawJSON := make(map[string]any)
	json.Unmarshal(rawData, &rawJSON)
	for k, v := range rawJSON {
		raw[k] = v
	}

	return models.ShipmentOption{
		Vendor:      vendor,
		Code:        serviceType,
		GroupCode:   serviceType,
		Price:       amount,
		Name:        nameMap[serviceType],
		Description: description,
		Raw:         raw,
	}
}
