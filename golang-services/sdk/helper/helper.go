package helper

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
)

// GenerateExternalID generates a random external ID for orders
func GenerateExternalID() string {
	b := make([]byte, 8)
	_, err := rand.Read(b)
	if err != nil {
		return fmt.Sprintf("%d", 12345678)
	}
	return hex.EncodeToString(b)
}

// FormatCurrency formats a number as currency string
func FormatCurrency(amount float64, locale, currency string) string {
	// This is a simplified version - a real implementation would use
	// proper locale-aware formatting with currency symbols
	return fmt.Sprintf("%.0f", amount)
}
