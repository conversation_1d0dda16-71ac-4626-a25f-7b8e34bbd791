package redis

import (
	"context"
	"encoding/json"
	"fmt"

	"sync"
	"time"

	"maps"

	"github.com/go-redis/redis/v8"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/thoas/go-funk"
)

var RedisClient *redis.Client

func InitRedis(addr, pass string) {
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: pass,
		DB:       3,
	})
}

func GetByKey(key string) string {
	ctx := context.Background()
	val, err := RedisClient.Get(ctx, key).Result()
	if err != nil {
		return ""
	}
	return val
}

func GetAllByPrefix(prefix string) (map[string]string, error) {
	ctx := context.Background()
	keys, err := RedisClient.Keys(ctx, prefix+"*").Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	var mutex sync.Mutex

	tasks := []*utils.Task{}
	for _, key := range keys {
		taskKey := key
		tasks = append(tasks, utils.NewTask(func() error {
			val, err := RedisClient.Get(ctx, taskKey).Result()
			if err == nil {
				mutex.Lock()
				result[taskKey] = val
				mutex.Unlock()
			}
			return nil
		}))
	}
	pool := utils.NewPool(tasks, 50)
	pool.Run()

	return result, nil
}

func SetKeyValue(key, value string) error {
	ctx := context.Background()
	if err := RedisClient.Set(ctx, key, value, 0).Err(); err != nil {
		return fmt.Errorf("Error setting key value: %v", err)
	}
	return nil
}

func GetObj(key string) (map[string]any, error) {
	ctx := context.Background()
	val, err := RedisClient.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var result map[string]any
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, err
	}

	return result, nil
}

func SetObj(key string, value any, expiration time.Duration) error {
	ctx := context.Background()
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return RedisClient.Set(ctx, key, jsonData, expiration).Err()
}

func GetObjsByPrefix(prefix string) ([]map[string]any, error) {
	ctx := context.Background()

	var results []map[string]any
	var cursor uint64 = 0

	for {
		var scanKeys []string
		var err error

		// Get a batch of keys matching the pattern
		scanKeys, cursor, err = RedisClient.Scan(ctx, cursor, prefix+"*", 100).Result()
		if err != nil {
			return nil, err
		}

		// Get values for all keys in this batch using a pipeline for efficiency
		if len(scanKeys) > 0 {
			pipe := RedisClient.Pipeline()
			cmds := make(map[string]*redis.StringCmd)

			// Queue up all the get operations
			for _, key := range scanKeys {
				cmds[key] = pipe.Get(ctx, key)
			}

			// Execute pipeline
			_, err := pipe.Exec(ctx)
			if err != nil && err != redis.Nil {
				return nil, err
			}

			// Process results
			for key, cmd := range cmds {
				val, err := cmd.Result()
				if err == nil {
					var obj map[string]any
					if err := json.Unmarshal([]byte(val), &obj); err == nil {
						// Add a field for the key if helpful
						obj["_redis_key"] = key
						results = append(results, obj)
					}
				}
			}
		}

		// If cursor is 0, we've completed the scan
		if cursor == 0 {
			break
		}
	}

	return results, nil
}

// Queue operations client
type QueueClient struct{}

var Queue = &QueueClient{}

// GetQueue retrieves items from a queue
func (q *QueueClient) GetQueue(name string) ([]map[string]any, error) {
	ctx := context.Background()
	val, err := RedisClient.Get(ctx, name).Result()
	if err != nil {
		if err == redis.Nil {
			return []map[string]any{}, nil
		}
		return nil, err
	}

	var result []map[string]any
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, err
	}

	return result, nil
}

// SetQueue stores items in a queue with updated timestamp
func (q *QueueClient) SetQueue(name string, queueList []map[string]any) error {
	ctx := context.Background()

	// Add timestamp to each item
	now := time.Now().Unix()
	updatedList := funk.Map(queueList, func(item map[string]any) map[string]any {
		newItem := make(map[string]any)
		maps.Copy(newItem, item)
		newItem["updated_at_unix"] = now
		return newItem
	}).([]map[string]any)

	jsonData, err := json.Marshal(updatedList)
	if err != nil {
		return err
	}

	return RedisClient.Set(ctx, name, jsonData, 24*time.Hour).Err()
}

// LSwapQueueByID finds an item by ID and moves it to the front of the queue
func (q *QueueClient) LSwapQueueByID(name, id string) ([]map[string]any, error) {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return nil, err
	}

	// Find item by ID
	itemIndex := -1
	for i, item := range queueList {
		if itemID, ok := item["_id"].(string); ok && itemID == id {
			itemIndex = i
			break
		}
	}

	if itemIndex == -1 {
		return queueList, nil
	}

	// Remove item and add to front
	item := queueList[itemIndex]
	queueList = append(queueList[:itemIndex], queueList[itemIndex+1:]...)
	queueList = append([]map[string]any{item}, queueList...)

	// Save updated queue
	if err := q.SetQueue(name, queueList); err != nil {
		return nil, err
	}

	return queueList, nil
}

// FindAndSetQueueCount increments count for an existing item or adds a new one
func (q *QueueClient) FindAndSetQueueCount(name string, query func(map[string]any) bool, item map[string]any) error {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return err
	}

	// Find matching item
	var foundItem map[string]any
	for _, qItem := range queueList {
		if query(qItem) {
			foundItem = qItem
			break
		}
	}

	now := time.Now().Unix()
	item["updated_at_unix"] = now

	if foundItem != nil {
		// Update existing item
		count, _ := foundItem["count"].(float64)
		foundItem["count"] = count + 1

		// Copy all properties from item to foundItem
		for k, v := range item {
			foundItem[k] = v
		}

		return q.SetQueue(name, queueList)
	} else {
		// Add new item
		item["count"] = 1
		queueList = append(queueList, item)
		return q.SetQueue(name, queueList)
	}
}

// PickQueue selects items from the queue for processing
func (q *QueueClient) PickQueue(name string, options map[string]any) ([]map[string]any, error) {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return nil, err
	}

	if len(queueList) == 0 {
		return []map[string]any{}, nil
	}

	// Extract options with defaults
	size := 5
	if val, ok := options["size"].(int); ok {
		size = val
	}

	minDuration := 60 // default 60 seconds
	if val, ok := options["min_duration"].(int); ok {
		minDuration = val
	}

	deleteQueue := false
	if val, ok := options["delete_queue"].(bool); ok {
		deleteQueue = val
	}

	// Calculate number of items to pick
	pickCount := size
	if pickCount > len(queueList) {
		pickCount = len(queueList)
	}

	// Select items from the front of the queue
	selectedItems := queueList[:pickCount]
	remainingItems := queueList[pickCount:]

	// Check if enough time has passed since the last update
	if firstItem := selectedItems[0]; firstItem != nil {
		if updatedAt, ok := firstItem["updated_at_unix"].(float64); ok {
			now := float64(time.Now().Unix())
			if now-updatedAt < float64(minDuration) {
				return []map[string]any{}, nil
			}
		}
	}

	if !deleteQueue {
		// Update timestamps and move to end of queue
		now := time.Now().Unix()
		for _, item := range selectedItems {
			item["updated_at_unix"] = now
			remainingItems = append(remainingItems, item)
		}
	}

	// Save the updated queue
	if err := RedisClient.Set(context.Background(), name, remainingItems, 24*time.Hour).Err(); err != nil {
		return nil, err
	}

	return selectedItems, nil
}

// DeleteQueue removes specified items from the queue
func (q *QueueClient) DeleteQueue(name string, items []map[string]any) error {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return err
	}

	// Create a map of IDs to remove for quick lookup
	removeIDs := make(map[string]bool)
	for _, item := range items {
		if id, ok := item["_id"].(string); ok {
			removeIDs[id] = true
		}
	}

	// Filter out items to be removed
	var filteredList []map[string]any
	for _, item := range queueList {
		if id, ok := item["_id"].(string); ok && !removeIDs[id] {
			filteredList = append(filteredList, item)
		}
	}

	// Save the filtered queue
	return q.SetQueue(name, filteredList)
}
