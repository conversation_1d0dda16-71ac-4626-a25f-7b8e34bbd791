package models

import (
	"fmt"
	"time"
)

// SubscriptionStatus represents the current state of a user's subscription
type SubscriptionStatus string

const (
	Active    SubscriptionStatus = "active"
	Expired   SubscriptionStatus = "expired"
	Suspended SubscriptionStatus = "suspended" // e.g., due to payment failure
	Postponed SubscriptionStatus = "postponed" // added for subscription upgrade scenarios
)

// // SubscriptionSource tracks how a subscription was acquired
// type SubscriptionSource string

// const (
// 	DirectPurchase  SubscriptionSource = "direct_purchase"
// 	TrialConversion SubscriptionSource = "trial_conversion"
// 	AdminAssigned   SubscriptionSource = "admin_assigned"
// 	Promotion       SubscriptionSource = "promotion"
// )

// UserSubscription represents a user's subscription to a plan
type UserSubscription struct {
	ID                 ObjectID           `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	UserID             string             `json:"user_id" gorm:"not null;index"`
	SubscriptionPlanID PlanID             `json:"subscription_plan_id" gorm:"not null;index;type:text"`
	Status             SubscriptionStatus `json:"status" gorm:"not null;default:'active'"`
	StartDate          time.Time          `json:"start_date" gorm:"not null"`
	EndDate            *time.Time         `json:"end_date,omitempty"`
	Period             SubscriptionPeriod `json:"period,omitempty"` // Monthly, Yearly, Trial, or empty for perpetual subscriptions

	// Payment and billing information
	PaymentMethod     string     `json:"payment_method,omitempty"`
	LastBillingDate   *time.Time `json:"last_billing_date,omitempty"`
	NextBillingDate   *time.Time `json:"next_billing_date,omitempty"`
	BillingCycleCount int        `json:"billing_cycle_count" gorm:"default:0"`

	// For tracking conversions from trial plans
	ConvertedFromPlanID *string `json:"converted_from_plan_id,omitempty"`

	// Customizable limits that can be expanded beyond base plan
	Limits *JSONField[FeatureLimits] `json:"limits,omitempty" gorm:"type:jsonb"`

	// Postponed subscription relationship tracking
	PostponedBySubscriptionID *ObjectID `json:"postponed_by_subscription_id,omitempty" gorm:"index"` // ID of subscription that caused this one to be postponed
	PostponedSubscriptionID   *ObjectID `json:"postponed_subscription_id,omitempty" gorm:"index"`    // ID of subscription that this one postponed
	RemainingDays             int       `json:"remaining_days,omitempty"`                            // Days remaining when subscription was postponed

	// Change tracking
	ChangeRequestID *ObjectID `json:"change_request_id,omitempty" gorm:"index"`

	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// IsTrial returns whether this subscription is for a trial plan
func (us *UserSubscription) IsTrial() bool {
	return us.SubscriptionPlanID == TrialPlanID
}

// IsWithinLimit checks if the current value for a specific feature is within the subscription limits
// Returns true if within limits, false if exceeds limits, and any error encountered
// A limit of -1 means unlimited (no restriction)
func (us *UserSubscription) IsWithinLimit(featureType ResourceCode, currentValue int) (bool, error) {
	// Check if subscription exists
	if us == nil {
		return false, fmt.Errorf("subscription is nil")
	}

	// Check if subscription is not active
	if us.Status != Active {
		return false, fmt.Errorf("subscription is not active: %s", us.Status)
	}

	// First check for custom limits in the subscription
	if us.Limits != nil && us.Limits.Data != nil {
		// Check if the feature exists in custom limits
		if limit, exists := us.Limits.Data[featureType]; exists {
			// A limit of -1 means unlimited
			if limit == -1 {
				return true, nil
			}
			// Check if current value is within the limit
			return currentValue <= limit, nil
		}
	}

	// Fall back to plan limits if no custom limits
	plan, found := us.GetPlan()
	if !found {
		// If plan not found, be conservative and return an error
		return false, fmt.Errorf("subscription plan %s not found", us.SubscriptionPlanID)
	}

	// Check the plan's limits for this feature
	if limit, exists := plan.Limits[featureType]; exists {
		// A limit of -1 means unlimited
		if limit == -1 {
			return true, nil
		}
		// Check if current value is within the limit
		return currentValue <= limit, nil
	}

	// If the feature doesn't have a defined limit, be conservative and return an error
	return false, fmt.Errorf("feature %s has no defined limit in plan %s", featureType, us.SubscriptionPlanID)
}

// GetPlan returns the subscription plan for this subscription
func (us *UserSubscription) GetPlan() (*SubscriptionPlan, bool) {
	return GetPlanByID(us.SubscriptionPlanID)
}
