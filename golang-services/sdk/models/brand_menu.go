package models

import (
	"time"
)

type BrandMenu struct {
	ID               ObjectID         `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	BrandID          string           `json:"brand_id" gorm:"unique"`
	Categories       []Category       `json:"categories" gorm:"type:jsonb"`
	OptionCategories []OptionCategory `json:"option_categories" gorm:"type:jsonb"`
	CreatedAt        time.Time        `json:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at"`
}

type Category struct {
	ID            string        `json:"id"`
	Name          string        `json:"name"`
	Code          string        `json:"code"`
	Sources       []string      `json:"sources"`
	SubCategories []SubCategory `json:"sub_categories"`
	Items         []MenuItem    `json:"items"`
	Active        bool          `json:"active"`
}

type SubCategory struct {
	ID      string     `json:"id"`
	Name    string     `json:"name"`
	Code    string     `json:"code"`
	Sources []string   `json:"sources"`
	Items   []MenuItem `json:"items"`
}

type MenuItem struct {
	ID                string      `json:"id"`
	Name              string      `json:"name"`
	CategoryName      string      `json:"category_name,omitempty"`
	CategoryCode      string      `json:"category_code,omitempty"`
	Code              string      `json:"code"`
	Unit              string      `json:"unit"`
	Sources           []string    `json:"sources"`
	Description       string      `json:"description"`
	Image             string      `json:"image"`
	ImagePreview      string      `json:"image_preview"`
	Price             float64     `json:"price"`
	StockPrice        float64     `json:"stock_price"`
	FloorPrice        float64     `json:"floor_price"`
	CeilingPrice      float64     `json:"ceiling_price"`
	QuantityUnlimited bool        `json:"quantity_unlimited"`
	QuantityMinimum   float64     `json:"quantity_minimum"`
	Quantity          float64     `json:"quantity"`
	Active            bool        `json:"active"`
	ForceUpdate       bool        `json:"force_update"`
	ForceUpdateBy     string      `json:"force_update_by"`
	Combo             []ComboItem `json:"combo"`
	FromBrandID       string      `json:"from_brand_id"`
	BrandName         string      `json:"brand_name"`
}

type ComboItem struct {
	Name      string   `json:"name"`
	Code      string   `json:"code"`
	Price     float64  `json:"price"`
	Quantity  float64  `json:"quantity"`
	Unit      string   `json:"unit"`
	Category  string   `json:"category"`
	Weight    float64  `json:"weight"`
	Images    []string `json:"images"`
	CostPrice float64  `json:"cost_price"`
}

type OptionCategory struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Sources     []string   `json:"sources"`
	Options     []Option   `json:"options"`
	Rule        OptionRule `json:"rule"`
	CategoryIDs []string   `json:"category_ids"`
}

type Option struct {
	ID                string        `json:"id"`
	Name              string        `json:"name"`
	Code              string        `json:"code"`
	Sources           []string      `json:"sources"`
	Price             float64       `json:"price"`
	Active            bool          `json:"active"`
	QuantityUnlimited bool          `json:"quantity_unlimited"`
	QuantityMinimum   float64       `json:"quantity_minimum"`
	Combo             []ComboOption `json:"combo"`
}

type ComboOption struct {
	Name     string  `json:"name"`
	Code     string  `json:"code"`
	Price    float64 `json:"price"`
	Quantity float64 `json:"quantity"`
	Unit     string  `json:"unit"`
}

type OptionRule struct {
	Type        string  `json:"type"`
	Required    bool    `json:"required"`
	MaxQuantity float64 `json:"max_quantity"`
	MinQuantity float64 `json:"min_quantity"`
}

func (BrandMenu) TableName() string {
	return "brand_menus"
}
