package models

import (
	"encoding/json"
	"time"
)

// OrderFeedback represents customer feedback for orders
type OrderFeedback struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Feedback information
	RefID         string          `json:"ref_id"`
	SiteID        string          `json:"site_id" gorm:"index"`
	OrderID       string          `json:"order_id" gorm:"index"`
	Source        string          `json:"source"`
	CustomerName  string          `json:"customer_name"`
	Rating        int             `json:"rating" gorm:"check:rating >= 1 AND rating <= 5"`
	Comment       string          `json:"comment"`
	CreatedAtUnix int64           `json:"created_at_unix"`
	Data          json.RawMessage `json:"data" gorm:"type:jsonb"`
}

// TableName specifies the table name for the OrderFeedback model
func (OrderFeedback) TableName() string {
	return "order_feedbacks"
}
