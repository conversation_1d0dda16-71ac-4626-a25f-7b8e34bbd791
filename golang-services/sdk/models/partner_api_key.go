package models

import (
	"time"

	"github.com/lib/pq"
)

type PartnerAPI<PERSON>ey struct {
	ID          ObjectID       `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	Name        string         `json:"name" gorm:"uniqueIndex;not null"`
	APIKey      string         `json:"api_key" gorm:"uniqueIndex;not null"`
	Permissions pq.StringArray `json:"permissions" gorm:"type:text[]"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

func (PartnerAPIKey) TableName() string {
	return "partner_api_keys"
}
