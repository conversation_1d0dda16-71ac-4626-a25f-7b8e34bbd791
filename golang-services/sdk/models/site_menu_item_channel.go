package models

// SiteMenuItemChannel represents a sales channel for a menu item
type SiteMenuItemChannel struct {
	Channel    string         `json:"channel" gorm:"type:varchar(255)"`
	Active     bool           `json:"active" gorm:"default:true"`
	Price      float64        `json:"price" gorm:"default:0"`
	Name       string         `json:"name,omitempty" gorm:"type:varchar(255)"`
	Categories []string       `json:"categories,omitempty" gorm:"type:jsonb"`
	Additional map[string]any `json:"additional,omitempty" gorm:"type:jsonb"`
}
