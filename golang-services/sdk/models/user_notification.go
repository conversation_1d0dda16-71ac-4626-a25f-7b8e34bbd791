package models

import (
	"time"
)

type UserNotification struct {
	ID               ObjectID  `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	UserID           uint      `json:"user_id"`
	NotificationType string    `json:"notification_type"`
	Title            string    `json:"title"`
	Message          string    `json:"message"`
	Status           string    `json:"status" gorm:"default:unread"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`

	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

func (UserNotification) TableName() string {
	return "user_notifications"
}
