package models

import (
	"time"
)

// SiteMenuChannel represents a channel for a site menu
type SiteMenuChannel struct {
	Channel    string     `json:"channel"`
	ModifiedOn *time.Time `json:"modified_on"`
	Synced     bool       `json:"synced" gorm:"default:false"`
}

// SiteMenu represents the status of a site menu
type SiteMenu struct {
	ID         string                     `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID     string                     `json:"site_id" gorm:"unique;index"`
	ModifiedOn *time.Time                 `json:"modified_on"`
	TemplateID string                     `json:"template_id"`
	Channels   JSONArray[SiteMenuChannel] `json:"channels" gorm:"type:jsonb"`
	Histories  JSONArray[any]             `json:"histories" gorm:"type:jsonb"`
	CreatedAt  time.Time                  `json:"created_at"`
	UpdatedAt  time.Time                  `json:"updated_at"`
}

// TableName specifies the table name for the SiteMenu model
func (SiteMenu) TableName() string {
	return "site_menus"
}

// MarkModified marks the site menu as modified
func (sm *SiteMenu) MarkModified() {
	now := time.Now()
	sm.ModifiedOn = &now
}

// MarkChannelModified marks a specific channel as modified
func (sm *SiteMenu) MarkChannelModified(channel string) {
	now := time.Now()
	for i := range sm.Channels {
		if sm.Channels[i].Channel == channel {
			sm.Channels[i].ModifiedOn = &now
			sm.Channels[i].Synced = false
			return
		}
	}

	// If channel doesn't exist, add it
	sm.Channels = append(sm.Channels, SiteMenuChannel{
		Channel:    channel,
		ModifiedOn: &now,
		Synced:     false,
	})
}

// MarkChannelSynced marks a specific channel as synced
func (sm *SiteMenu) MarkChannelSynced(channel string) {
	for i := range sm.Channels {
		if sm.Channels[i].Channel == channel {
			sm.Channels[i].Synced = true
			return
		}
	}
}
