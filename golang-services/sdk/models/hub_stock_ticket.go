package models

import (
	"time"
)

type HubStockTicketItem struct {
	Code              string   `json:"code"`
	Name              string   `json:"name"`
	Unit              string   `json:"unit"`
	StockQuantity     float64  `json:"stock_quantity"`
	ExchangeQuantity  float64  `json:"exchange_quantity"`
	ExchangedQuantity float64  `json:"exchanged_quantity"`
	StockReportTypes  []string `json:"stock_report_types"`
	StockReportNote   string   `json:"stock_report_note"`
}

type HubStockTicket struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	TicketNumber  string                          `json:"ticket_number" gorm:"uniqueIndex"`
	TicketType    string                          `json:"ticket_type" gorm:"type:varchar(20);check:ticket_type IN ('import', 'export', 'trash')"`
	HubID         string                          `json:"hub_id" gorm:"index"`
	TargetHubID   *string                         `json:"target_hub_id" gorm:"index"`
	TargetHubDate *time.Time                      `json:"target_hub_date"`
	Status        string                          `json:"status" gorm:"type:varchar(20);check:status IN ('created', 'submitted', 'approved', 'rejected');default:'created'"`
	Items         JSONField[[]HubStockTicketItem] `json:"items" gorm:"type:jsonb"`
	CreatedBy     string                          `json:"created_by" gorm:"index"`
	ApprovedBy    *string                         `json:"approved_by" gorm:"index"`
	RejectedBy    *string                         `json:"rejected_by" gorm:"index"`
	CreatedAt     time.Time                       `json:"created_at"`
	UpdatedAt     time.Time                       `json:"updated_at"`

	// Relations
	Hub       *Hub  `json:"hub,omitempty" gorm:"foreignKey:HubID"`
	TargetHub *Hub  `json:"target_hub,omitempty" gorm:"foreignKey:TargetHubID"`
	Creator   *User `json:"created_by_user,omitempty" gorm:"foreignKey:CreatedBy"`
	Approver  *User `json:"approved_by_user,omitempty" gorm:"foreignKey:ApprovedBy"`
	Rejecter  *User `json:"rejected_by_user,omitempty" gorm:"foreignKey:RejectedBy"`
}

func (HubStockTicket) TableName() string {
	return "hub_stock_tickets"
}
