package models

import (
	"encoding/json"
	"time"
)

// JobType represents the type of scheduled job
type JobType string

const (
	JobTypeAPIRequest       JobType = "api_request"
	JobTypeGetOrder         JobType = "get_order"
	JobTypeGetOrderFeedback JobType = "get_order_feedback"
)

// JobStatus represents the status of a job execution
type JobStatus string

const (
	JobStatusSuccess JobStatus = "success"
	JobStatusFailed  JobStatus = "failed"
	JobStatusPending JobStatus = "pending"
)

// ScheduleJob represents a scheduled job configuration
type ScheduleJob struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Job configuration
	Name      string          `json:"name" gorm:"type:varchar(50);not null"`
	LastRunAt *time.Time      `json:"last_run_at"`
	JobData   json.RawMessage `json:"job_data" gorm:"type:jsonb"`
}

// TableName specifies the table name for the ScheduleJob model
func (ScheduleJob) TableName() string {
	return "schedule_jobs"
}

// ScheduleJobHistory represents the execution history of scheduled jobs
type ScheduleJobHistory struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationship
	JobName string `json:"job_name" gorm:"type:varchar(50);not null;index"`

	// Execution details
	Status       JobStatus       `json:"status" gorm:"type:varchar(20);not null"`
	StartAt      time.Time       `json:"start_at"`
	EndAt        time.Time       `json:"end_at"`
	RequestData  json.RawMessage `json:"request_data" gorm:"type:jsonb"`
	ResponseData json.RawMessage `json:"response_data" gorm:"type:jsonb"`
}

// TableName specifies the table name for the ScheduleJobHistory model
func (ScheduleJobHistory) TableName() string {
	return "schedule_job_histories"
}
