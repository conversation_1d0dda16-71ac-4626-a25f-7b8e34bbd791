package models

import (
	"time"
)

// JSONMap is a generic map for storing dynamic JSON data
type JSONMap map[string]any

// VoucherConfig represents the configuration for a voucher
type VoucherConfig struct {
	VoucherCode    string    `json:"voucher_code"`
	Prefix         string    `json:"prefix"`
	UsageCount     int       `json:"usage_count"`
	Quantity       int       `json:"quantity"`
	EndDate        time.Time `json:"end_date"`
	ApplyWithOther bool      `json:"apply_with_other"`
	Type           string    `json:"type"`
}

type GiftItem struct {
	// CategoryName string  `json:"category_name"`
	// Name         string  `json:"name"`
	Code     string  `json:"code"`
	Quantity float64 `json:"quantity"`
	Price    float64 `json:"gift_price"`
}

type SaleItem struct {
	ID string `json:"_id"`
	// ItemName       string  `json:"item_name"`
	// Name           string  `json:"name"`
	CategoryCode   string  `json:"category_code"`
	Code           string  `json:"code"`
	DiscountType   string  `json:"discount_type"`
	DiscountValue  float64 `json:"discount_value"`
	MaxDiscount    float64 `json:"max_discount"`
	Discount       float64 `json:"discount"`
	MinValue       float64 `json:"min_value"`
	Value          float64 `json:"value"`
	MinOrderAmount float64 `json:"min_order_amount"`
	Quantity       float64 `json:"quantity"`
	Type           string  `json:"type"`

	GiftQuantity float64 `json:"gift_quantity"`
	GiftPrice    float64 `json:"gift_price"`
	GiftCode     string  `json:"gift_code"`
	ForceGetY    bool    `json:"force_get_y"`
}

type SaleConfig struct {
	Items           []SaleItem `json:"items"`
	GiftItems       []GiftItem `json:"gift_items"`
	PercentDiscount float64    `json:"percent_discount"`
	Provinces       []string   `json:"provinces"`
	MinOrderAmount  float64    `json:"min_order_amount"`
	GiftCode        string     `json:"gift_code"`
	GiftQuantity    int64      `json:"gift_quantity"`
	GiftPrice       float64    `json:"gift_price"`
	ForceGetY       bool       `json:"force_get_y"`
	DiscountValue   float64    `json:"discount_value"`
	DiscountType    string     `json:"discount_type"`
	ItemType        string     `json:"item_type"`
	ItemCode        string     `json:"item_code"`
	Quantity        int64      `json:"quantity"`
	FirstOrderOnly  bool       `json:"first_order_only"`
}

/*
	{

  // For order_bonus and some other types
  "items": [
    {
      // For order_bonus
      "min_order_amount": 1000000,  // Minimum order amount to qualify
      "gift_item_name": "Cơm gà",   // Name of gift item
      "gift_category_name": "Món chính", // Category of gift item
      "gift_quantity": 1,           // Quantity of gift to provide
      "gift_price": 0,              // Price of gift item (can be 0 or more)
      "force_get_y": false,         // If true, always add gift item to cart

      // For percent_discount
      "item_name": "Cơm gà",        // Item to discount
      "category_name": "Món chính",  // Category of item
      "percent_discount": 10,        // Percentage discount to apply

      // For fixed_discount
      "discount": 10000,            // Fixed discount amount

      // For order_discount and ship_discount
      "min_value": 100000,          // Minimum order value to qualify
      "type": "fixed" | "percent" | "discount" | "free_ship" | "flat_rate", // Discount type
      "value": 10000                // Discount amount or percentage value
    }
  ],

  // For buy_x_get_y type
  "item_name": "Cơm gà",           // Item that must be purchased
  "category_name": "Món chính",     // Category of item
  "quantity": 2,                    // Quantity required to purchase
  "gift_item_name": "Cơm gà",       // Name of gift item
  "gift_category_name": "Món chính", // Category of gift item
  "gift_quantity": 1,               // Quantity of gift to provide
  "gift_price": 0,                  // Price of gift item (can be 0 or more)
  "force_get_y": false,             // If true, always add gift item to cart

  // For buy_x_discount_y type
  "item_type": "category" | "dish" | "option", // Type of item to qualify
  "gift_items": [                   // Items to receive discount
    {
      "name": "Cơm gà",
      "category_name": "Món chính"
    }
  ],
  "gift_quantity": 1,               // Quantity of discounted items
  "discount_type": "fixed" | "percent" | "flat", // Type of discount
  "discount_value": 10000,          // Fixed value or percent

  // For discount type
  "discount": 10000,                // Discount amount

  // Ship discount specific
  "provinces": [1, 2, 3],           // Province IDs where discount applies

  // Order discount specific
  "first_order_only": false         // Whether to apply only to first orders
}
*/
// RetailerSaleConfig represents sale promotion configurations for retailers
type RetailerSaleConfig struct {
	ID            string                   `json:"id" gorm:"primaryKey;type:varchar(36)"`
	CreatedAt     time.Time                `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time                `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	BrandID       string                   `json:"brand_id" gorm:"not null;type:varchar(36)"`
	Type          string                   `json:"type" gorm:"type:varchar(100)"`
	Name          string                   `json:"name" gorm:"type:varchar(255)"`
	Description   string                   `json:"description" gorm:"type:text"`
	Active        bool                     `json:"active" gorm:"default:false"`
	StartDate     *time.Time               `json:"start_date"`
	EndDate       *time.Time               `json:"end_date"`
	Order         float64                  `json:"order" gorm:"default:0"`
	Config        JSONField[SaleConfig]    `json:"config" gorm:"type:jsonb"`
	VoucherConfig JSONField[VoucherConfig] `json:"voucher_config" gorm:"type:jsonb"`
	Brand         *Brand                   `json:"brand" gorm:"-"`
	SiteIDs       JSONField[[]string]      `json:"site_ids" gorm:"type:jsonb"`
}

// TableName specifies the database table name
func (r *RetailerSaleConfig) TableName() string {
	return "retailer_sale_configs"
}

// OrderSummary represents a simplified order structure for reporting
type OrderSummary struct {
	OrderID      string  `json:"order_id"`
	OrderTime    string  `json:"order_time"`
	CustomerName string  `json:"customer_name"`
	Total        float64 `json:"total"`
	Status       string  `json:"status"`
}
