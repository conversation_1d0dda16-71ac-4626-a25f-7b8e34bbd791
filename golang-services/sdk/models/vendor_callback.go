package models

import (
	"encoding/json"
	"time"
)

// VendorCallback represents external vendor webhook callback records
type VendorCallback struct {
	ID           ObjectID        `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	Vendor       string          `json:"vendor" gorm:"type:varchar(20);check:vendor IN ('zalo', 'momo', 'grab', 'grab_food', 'be', 'payos', 'nexdorpay', 'ghn', 'ahamove', 'grab_express', 'viettel_post', 'shopee', 'dpoint', 'momo_mini', 'lazada', 'tiktok')"`
	Type         string          `json:"type" gorm:"type:varchar(10);check:type IN ('order', 'menu')"`
	URL          string          `json:"url"`
	Method       string          `json:"method"`
	Headers      json.RawMessage `json:"headers" gorm:"type:jsonb"`
	Success      bool            `json:"success" gorm:"default:false"`
	RequestData  json.RawMessage `json:"request_data" gorm:"type:jsonb"`
	ResponseData json.RawMessage `json:"response_data" gorm:"type:jsonb"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
}

func (VendorCallback) TableName() string {
	return "vendor_callbacks"
}
