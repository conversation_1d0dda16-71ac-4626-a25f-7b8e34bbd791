package models

import (
	"time"
)

// VoucherPromotion represents voucher data for sale promotions
type VoucherPromotion struct {
	ID         string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Code       string     `json:"code" gorm:"unique;not null;type:varchar(100)"`
	Vendor     string     `json:"vendor" gorm:"type:varchar(50);default:'nexdor'"`
	BrandID    string     `json:"brand_id" gorm:"not null;type:varchar(36)"`
	IsUsed     bool       `json:"is_used" gorm:"default:false"`
	UsedAt     *time.Time `json:"used_at"`
	ExpiredAt  *time.Time `json:"expired_at"`
	TradeAt    *time.Time `json:"trade_at"`
	OwnerPhone string     `json:"owner_phone" gorm:"type:varchar(20)"`
	CreatedAt  time.Time  `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time  `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
}

// TableName specifies the database table name
func (v *VoucherPromotion) TableName() string {
	return "vouchers"
}
