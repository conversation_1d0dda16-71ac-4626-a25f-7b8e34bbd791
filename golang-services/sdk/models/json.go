package models

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// JSONField is a generic type for a struct stored as JSON
type J<PERSON>NField[T any] struct {
	Data T
}

// GormDataType specifies the database data type
func (o JSONField[T]) GormDataType() string {
	return "json"
}

func (o JSONField[T]) MarshalJSON() ([]byte, error) {
	return json.Marshal(o.Data)
}

func (o *JSONField[T]) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &o.Data)
}

// GormValue converts the object to JSON for storage
func (o JSONField[T]) GormValue(ctx context.Context, db *gorm.DB) clause.Expr {
	data, _ := json.Marshal(o.Data)
	return clause.Expr{
		SQL:  "?",
		Vars: []any{string(data)},
	}
}

// <PERSON>an converts the database JSON back to the struct
func (o *<PERSON><PERSON><PERSON>ield[T]) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to convert database value to []byte")
	}
	return json.Unmarshal(bytes, &o.Data)
}

// Value implements the driver.Valuer interface
func (o JSONField[T]) Value() (driver.Value, error) {
	return json.Marshal(o.Data)
}

type JSONArray[T any] []T

func (a JSONArray[T]) GormDataType() string {
	return "json"
}

func (a JSONArray[T]) GormValue(ctx context.Context, db *gorm.DB) clause.Expr {
	data, _ := json.Marshal(a)
	return clause.Expr{
		SQL:  "?",
		Vars: []any{string(data)},
	}
}

func (a *JSONArray[T]) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("failed to convert database value to []byte")
	}
	return json.Unmarshal(bytes, a)
}
