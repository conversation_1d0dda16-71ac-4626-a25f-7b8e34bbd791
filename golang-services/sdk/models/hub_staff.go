package models

import (
	"time"
)

type HubStaff struct {
	// Base model fields
	ID     string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	HubID  string `json:"hub_id" gorm:"not null;index"`
	Hub    *Hub   `json:"hub,omitempty" gorm:"foreignKey:HubID"`
	UserID string `json:"user_id" gorm:"not null;index"`
	User   *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`

	// Role StaffRole `json:"role" gorm:"not null"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName specifies the table name for the Brand model
func (HubStaff) TableName() string {
	return "hub_staffs"
}
