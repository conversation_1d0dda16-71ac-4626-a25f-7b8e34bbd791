package models

import (
	"time"
)

// WorkingShiftStatus represents the status of a working shift
type WorkingShiftStatus string

const (
	WorkingShiftStatusOpen   WorkingShiftStatus = "open"
	WorkingShiftStatusClosed WorkingShiftStatus = "closed"
)

// WorkingShiftIncome represents an income entry in a working shift
type WorkingShiftIncome struct {
	OrderID       string  `json:"order_id,omitempty"`
	PaymentMethod string  `json:"payment_method" gorm:"not null;default:'CASH'"`
	Amount        float64 `json:"amount"`
	Note          string  `json:"note"`
}

// WorkingShiftOutcome represents an outcome entry in a working shift
type WorkingShiftOutcome struct {
	PaymentMethod string  `json:"payment_method" gorm:"not null;default:'CASH'"`
	Amount        float64 `json:"amount"`
	Note          string  `json:"note"`
}

// WorkingShiftReport represents the report data for a working shift
type WorkingShiftReport struct {
	HubName           string                       `json:"hub_name"`
	CreatedAt         string                       `json:"created_at"`
	CloseBy           string                       `json:"close_by"`
	TotalOrder        int                          `json:"total_order"`
	TotalGrossReceived float64                     `json:"total_gross_received"`
	TotalNetReceived  float64                      `json:"total_net_received"`
	Merchants         map[string]MerchantReport    `json:"merchants"`
	PaymentMethods    map[string]PaymentMethodInfo `json:"payment_methods"`
}

// MerchantReport represents the report data for a merchant in a working shift
type MerchantReport struct {
	Orders            []map[string]string `json:"orders"`
	GrossReceived     []float64           `json:"gross_received"`
	NetReceived       []float64           `json:"net_received"`
	TotalOrders       int                 `json:"total_orders"`
	TotalGrossReceived float64            `json:"total_gross_received"`
	TotalNetReceived  float64             `json:"total_net_received"`
}

// PaymentMethodInfo represents information about a payment method in a working shift
type PaymentMethodInfo struct {
	Total float64 `json:"total"`
}

// WorkingShift represents a working shift in the system
type WorkingShift struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	HubID          string           `json:"hub_id" gorm:"not null;index"`
	OpenByUserID   string           `json:"open_by_user_id" gorm:"not null"`
	ClosedByUserID string           `json:"closed_by_user_id"`
	StartTime      time.Time        `json:"start_time"`
	EndTime        time.Time        `json:"end_time"`
	InitialAmount  float64          `json:"initial_amount"`
	Income         JSONArray[WorkingShiftIncome]  `json:"income" gorm:"type:jsonb"`
	Outcome        JSONArray[WorkingShiftOutcome] `json:"outcome" gorm:"type:jsonb"`
	TotalAmount    float64          `json:"total_amount"`
	ActualAmount   float64          `json:"actual_amount"`
	Status         WorkingShiftStatus `json:"status" gorm:"type:varchar(10);check:status IN ('open', 'closed');default:'open'"`
	Note           string           `json:"note"`
	Report         JSONField[WorkingShiftReport] `json:"report" gorm:"type:jsonb"`
	ReportURL      string           `json:"report_url"`

	// Relations (not stored in database)
	OpenByUser   *User `json:"open_by_user,omitempty" gorm:"-"`
	ClosedByUser *User `json:"closed_by_user,omitempty" gorm:"-"`
	Hub          *Hub  `json:"hub,omitempty" gorm:"-"`
}

// TableName specifies the table name for the WorkingShift model
func (WorkingShift) TableName() string {
	return "working_shifts"
}

// WorkingShiftWithUserInfo represents a working shift with user information
type WorkingShiftWithUserInfo struct {
	WorkingShift
	OpenByUser   UserInfo `json:"open_by_user"`
	ClosedByUser UserInfo `json:"closed_by_user"`
}

// UserInfo represents basic user information
type UserInfo struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}
