package models

import (
	"encoding/json"
	"time"
)

// OrderDish updated to match existing structure
type OrderDish struct {
	Name          string         `json:"name"`
	CategoryName  string         `json:"category_name"`
	Description   string         `json:"description"`
	Quantity      int64          `json:"quantity"`
	Price         float64        `json:"price"`
	Discount      float64        `json:"discount"`
	DiscountPrice float64        `json:"discount_price"`
	Note          string         `json:"note"`
	IsGift        bool           `json:"is_gift"`
	Options       [][]DishOption `json:"options"`
}

type OrderCoupon struct {
	Name  string  `json:"name"`
	Code  string  `json:"code"`
	Total float64 `json:"total"`
}

type Payment struct {
	Method string  `json:"method"`
	Total  float64 `json:"total"`
	Status string  `json:"status"`
	Note   string  `json:"note"`
}

// StockDish represents a dish with stock management
type StockDish struct {
	ID       string `json:"id"`
	Quantity int64  `json:"quantity"`
}

type CustomerData struct {
	OriginalPrice    float64 `json:"original_price"`
	SellPrice        float64 `json:"sell_price"`
	ShipmentFee      float64 `json:"shipment_fee"`
	OrderDiscount    float64 `json:"order_discount"`
	ShipmentDiscount float64 `json:"shipment_discount"`
	AdditionalIncome float64 `json:"additional_income"`
	TotalPaid        float64 `json:"total_paid"`
}

type FinanceData struct {
	OriginalPrice        float64 `json:"original_price"`
	SellPrice            float64 `json:"sell_price"`
	CoFundPromotionPrice float64 `json:"co_fund_promotion_price"`
	OtherPromotionPrice  float64 `json:"other_promotion_price"`
	TotalPromotionPrice  float64 `json:"total_promotion_price"`
	GrossReceived        float64 `json:"gross_received"`
	Commission           float64 `json:"commission"`
	TransactionFee       float64 `json:"transaction_fee"`
	AdjustmentFee        float64 `json:"adjustment_fee"`
	OtherFee             float64 `json:"other_fee"`
	TotalShipment        float64 `json:"total_shipment"`
	ShippingFee          float64 `json:"shipping_fee"`
	ShippingDiscount     float64 `json:"shipping_discount"`
	NetReceived          float64 `json:"net_received"`
	AdditionalIncome     float64 `json:"additional_income"`
	RealReceived         float64 `json:"real_received"`
}

type StockSync struct {
	Status string          `json:"status"`
	Detail json.RawMessage `json:"detail"`
}

type DataMapping struct {
	ID               string        `json:"id"`
	OrderID          string        `json:"order_id"`
	Source           string        `json:"source"`
	OrderTime        string        `json:"order_time"`
	PickTime         string        `json:"pick_time"`
	DeliveryTime     string        `json:"delivery_time"`
	DeliveryTimeUnix int64         `json:"delivery_time_unix"`
	OrderTimeSort    int64         `json:"order_time_sort"`
	DriverName       string        `json:"driver_name"`
	DriverPhone      string        `json:"driver_phone"`
	CustomerName     string        `json:"customer_name"`
	CustomerAddress  string        `json:"customer_address"`
	CustomerPhone    string        `json:"customer_phone"`
	Dishes           []OrderDish   `json:"dishes"`
	DishChanged      bool          `json:"dish_changed"`
	Total            float64       `json:"total"`
	Commission       float64       `json:"commission"`
	TotalDiscount    float64       `json:"total_discount"`
	TotalSurcharge   float64       `json:"total_surcharge"`
	TotalForBiz      float64       `json:"total_for_biz"`
	TotalShipment    float64       `json:"total_shipment"`    // Giá ship sau cùng (sau khuyến mãi)
	ShipmentDiscount float64       `json:"shipment_discount"` // Giảm giá ship
	ShipmentFee      float64       `json:"shipment_fee"`      // Giá ship trước khuyến mãi
	TransactionFee   float64       `json:"transaction_fee"`   // Phí giao dịch
	Note             string        `json:"note"`
	CancelType       string        `json:"cancel_type"` // enum: ['out_stock', 'merchant_busy', 'incorrect_order']
	CancelReason     string        `json:"cancel_reason"`
	CancelBy         string        `json:"cancel_by"` // enum: ['merchant', 'system', 'user', 'driver']
	Coupons          []OrderCoupon `json:"coupons"`
	Payments         []Payment     `json:"payments"`
	StockDishes      []StockDish   `json:"stock_dishes"` // Món theo tồn kho
	CustomerData     CustomerData  `json:"customer_data"`
	FinanceData      FinanceData   `json:"finance_data"`
	Raw              any           `json:"raw"`
}

// Order struct based on the SQL query fields with additional fields for compatibility
type Order struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	ShortOrderID string                    `json:"short_order_id" gorm:"type:text"`
	OrderID      string                    `json:"order_id" gorm:"type:text"`
	Source       string                    `json:"source" gorm:"type:text"`
	SiteID       string                    `json:"site_id" gorm:"type:object_id"`
	HubID        string                    `json:"hub_id" gorm:"type:object_id"`
	Status       string                    `json:"status" gorm:"type:text"`
	ExternalID   string                    `json:"external_id" gorm:"type:text"` // Renamed from HEID to match query
	Shipment     JSONField[OrderShipment]  `json:"shipment" gorm:"type:jsonb"`
	Data         JSONField[map[string]any] `json:"data" gorm:"type:jsonb"`
	DataMapping  JSONField[DataMapping]    `json:"data_mapping" gorm:"type:jsonb"`

	AutoPrinted                     bool                      `json:"auto_printed" gorm:"type:bool"`
	AutoLabelPrinted                bool                      `json:"auto_label_printed" gorm:"type:bool"`
	AutoCancelPrinted               bool                      `json:"auto_cancel_printed" gorm:"type:bool"`
	AutoConfirmed                   bool                      `json:"auto_confirmed" gorm:"type:bool"`
	IsNew                           bool                      `json:"is_new" gorm:"type:bool"`
	OdooSyncRetry                   int64                     `json:"odoo_sync_retry" gorm:"type:numeric"`
	VendorSync                      JSONField[map[string]any] `json:"vendor_sync" gorm:"type:jsonb"`
	VendorSyncRetry                 int64                     `json:"vendor_sync_retry" gorm:"type:numeric"`
	PushedNotification              bool                      `json:"pushed_notification" gorm:"type:bool"`
	NotificationCountOrderConfirm   int64                     `json:"notification_count_order_confirm" gorm:"type:numeric"`
	NotificationCountOrderCancel    int64                     `json:"notification_count_order_cancel" gorm:"type:numeric"`
	FinanceDataSyncRetry            int64                     `json:"finance_data_sync_retry" gorm:"type:numeric"`
	LabelURLs                       JSONField[[]string]       `json:"label_urls" gorm:"type:jsonb"`
	SentBillNotification            bool                      `json:"sent_bill_notification" gorm:"type:bool;default:false"`
	CreatedAt                       *time.Time                `json:"created_at" gorm:"type:timestamp"`
	UpdatedAt                       *time.Time                `json:"updated_at" gorm:"type:timestamp"`
	DataHistory                     JSONArray[map[string]any] `json:"data_history" gorm:"type:jsonb"`
	BillURL                         string                    `json:"bill_url" gorm:"type:text"`
	BillForCompleteAppURL           string                    `json:"bill_for_complete_app_url" gorm:"type:text"`
	BillForCompleteURL              string                    `json:"bill_for_complete_url" gorm:"type:text"`
	NotificationCountOrderConfirmAt *time.Time                `json:"notification_count_order_confirm_at" gorm:"type:timestamp"`
	LastCronTransaction             *time.Time                `json:"last_cron_transaction" gorm:"type:timestamp"`
	DpointSync                      JSONField[map[string]any] `json:"dpoint_sync" gorm:"type:jsonb"`
	BillForCancelURL                string                    `json:"bill_for_cancel_url"`
	BillForPaymentURL               string                    `json:"bill_for_payment_url"`

	UserID       string `json:"user_id"`
	IsFirstOrder bool   `json:"is_first_order"`
	// RejectedHubs                    JSONField[map[string]any] `json:"rejected_hubs" gorm:"type:jsonb"`
}
