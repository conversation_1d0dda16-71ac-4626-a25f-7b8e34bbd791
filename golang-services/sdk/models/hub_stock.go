package models

import (
	"time"
)

type HubStock struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	HubID        string    `json:"hub_id" gorm:"index;not null"`
	Code         string    `json:"code" gorm:"type:varchar(100);not null;index"`
	Name         string    `json:"name" gorm:"type:varchar(255);not null;index"`
	Unit         string    `json:"unit" gorm:"type:varchar(50);default:'unit'"`
	Quantity     float64   `json:"quantity" gorm:"default:0"`
	LockedStatus string    `json:"locked_status" gorm:"type:varchar(20);check:locked_status IN ('use_stock', 'alway_active', 'alway_inactive');default:'use_stock'"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Relations
	Hub *Hub `json:"hub,omitempty" gorm:"foreignKey:HubID"`
}

func (HubStock) TableName() string {
	return "hub_stocks"
}

type HubStockHistory struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	HubID               ObjectID  `json:"hub_id" gorm:"index"`
	Code                string    `json:"code" gorm:"type:varchar(100);not null;index"`
	FromQuantity        float64   `json:"from_quantity" gorm:"default:0"`
	ToQuantity          float64   `json:"to_quantity" gorm:"default:0"`
	UpdatedType         string    `json:"updated_type" gorm:"type:varchar(20);check:updated_type IN ('edit', 'order', 'order_cancel', 'import', 'server', 'ticket');not null"`
	UpdatedBy           *string   `json:"updated_by" gorm:"type:varchar(100)"`
	UpdatedOrderID      *string   `json:"updated_order_id" gorm:"type:varchar(100)"`
	UpdatedImportFile   *string   `json:"updated_import_file" gorm:"type:varchar(255)"`
	UpdatedServer       *string   `json:"updated_server" gorm:"type:varchar(100)"`
	UpdatedTicketNumber *string   `json:"updated_ticket_number" gorm:"type:varchar(100)"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`

	// Relations
	Hub *Hub `json:"hub,omitempty" gorm:"foreignKey:HubID"`
}

func (HubStockHistory) TableName() string {
	return "hub_stock_histories"
}
