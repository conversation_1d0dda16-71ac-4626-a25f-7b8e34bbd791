package models

type Token struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	SiteType     string `json:"site_type"` // MART or FOOD
	SiteID       string `json:"site_id"`
	SiteName     string `json:"site_name"`
	<PERSON>rna<PERSON>     string `json:"username"`
	Password     string `json:"password"`
}

type StoreDetail struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Phone   string `json:"phone"`
	Address string `json:"address"`
	Raw     any    `json:"raw,omitempty"` // Raw store data for additional fields
}

type MerchantOrder struct {
	Source       string `json:"source"`
	LongOrderID  string `json:"long_order_id"`
	ShortOrderID string `json:"short_order_id"`
	Status       string `json:"status"`
	MD5          string `json:"md5"`
	DataInList   any    `json:"data_in_list"`
	DataInDetail any    `json:"data_in_detail"`
}

// StoreItem represents a store item in the store list
type StoreItem struct {
	AccessToken string `json:"access_token"`
	StoreType   string `json:"store_type"` // MART or FOOD
	StoreID     string `json:"store_id"`
	StoreName   string `json:"store_name"`
}
