package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

type TokenAccount struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()" `

	TokenCode     string          `json:"token_code" gorm:"not null;unique"`
	Source        string          `json:"source" gorm:"type:varchar(50);check:source IN ('shopee', 'shopee_ecom','shopee_food', 'grab', 'grab_express', 'gojek', 'be', 'zalo', 'momo', 'payos', 'vnpay', 'ghn', 'ahamove', 'viettel_post', 'lazada', 'tiktok', 'shopee_official', 'grab_mart_official', 'grab_food_official', 'facebook')"`
	SiteID        string          `json:"site_id"`
	SiteName      string          `json:"site_name"`
	SiteData      json.RawMessage `json:"site_data" gorm:"type:jsonb"`
	Username      string          `json:"username"`
	Password      string          `json:"password"`
	AccessToken   string          `json:"access_token" gorm:"type:text"`
	RefreshToken  string          `json:"refresh_token" gorm:"type:text"`
	LastUpdated   time.Time       `json:"last_updated"`
	Working       bool            `json:"working"`
	LastWorkingAt time.Time       `json:"last_working_at"`
	ExpiredAt     time.Time       `json:"expired_at"`
	FailCount     int             `json:"fail_count"`
	Description   string          `json:"description" gorm:"type:text"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

func (TokenAccount) TableName() string {
	return "token_accounts"
}

func GetTokenBySite(db *gorm.DB, site *Site, source string) (*TokenAccount, error) {
	// Get token from site
	siteToken := site.GetToken(source)
	if siteToken == nil || siteToken.TokenCode == "" {
		// If site is not head site, try to get token from head site
		if !site.IsHeadSite {
			var headSite Site
			if err := db.Where("brand_id = ? AND is_head_site = ?", site.BrandID, true).First(&headSite).Error; err != nil {
				return nil, err
			}
			// Recursive call to get token from head site
			return GetTokenBySite(db, &headSite, source)
		}
		return nil, nil
	}

	// Try to get token account
	var tokenAccount TokenAccount
	if err := db.Where("token_code = ?", siteToken.TokenCode).First(&tokenAccount).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &TokenAccount{
				Source:    siteToken.Source,
				SiteName:  siteToken.SiteName,
				TokenCode: siteToken.TokenCode,
				SiteID:    siteToken.SiteID,
			}, nil
		}
		return nil, err
	}

	return &tokenAccount, nil
}
