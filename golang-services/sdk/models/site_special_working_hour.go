// sdk/models/site.go
package models

import "time"

const (
	OpenTypeCloseAllTime = "close_all_time"
	OpenTypeOpenAllTime  = "open_all_time"
	OpenTypeOpenAtTime   = "open_at_time"
)

type SpecialHour struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type SiteSpecialWorkingHour struct {
	ID        string                 `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID    string                 `json:"site_id"`
	Source    string                 `json:"source"`
	Name      string                 `json:"name"`
	FromDate  string                 `json:"from_date"`
	ToDate    string                 `json:"to_date"`
	OpenType  string                 `json:"open_type"`
	OpenHours JSONArray[SpecialHour] `json:"open_hours"`
	SyncAt    *time.Time             `json:"sync_at"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}
