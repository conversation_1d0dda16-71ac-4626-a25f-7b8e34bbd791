package models

import "time"

type NutifoodMenuItem struct {
	ItemCode string  `json:"item_code"`
	ItemName string  `json:"item_name"`
	Unit     string  `json:"unit"`
	Quantity float64 `json:"quantity"`
	ShopCode string  `json:"shop_code"`
	ShopName string  `json:"shop_name"`
}

type NutifoodOrderRequest struct {
	BaseModel
	Delivery struct {
		DeliveryCode string  `json:"delivery_code"`
		DeliveryFee  float64 `json:"delivery_fee"`
		PartnerCode  string  `json:"partner_code"`
	} `json:"delivery"`
	Lines       []NutifoodOrderLine `json:"lines"`
	ChannelCode string              `json:"channel_code"`
	Customers   NutifoodCustomer    `json:"customers"`
	DateOrder   time.Time           `json:"date_order"`
	PaymentIDs  []NutifoodPayment   `json:"payment_ids"`
	ShopCode    string              `json:"shop_code"`
	UserID      uint                `json:"user_id"`
	ToInvoice   bool                `json:"to_invoice"`
	Note        string              `json:"note"`
	Discount    float64             `json:"discount"`
	SourceCode  string              `json:"source_code"`
	Attr1       string              `json:"attr_1"`
	Attr2       string              `json:"attr_2"`
	Attr3       string              `json:"attr_3"`
}

type NutifoodOrderLine struct {
	ItemCode string  `json:"item_code"`
	Price    float64 `json:"price"`
	Qty      float64 `json:"qty"`
}

type NutifoodCustomer struct {
	WardName string `json:"ward_name"`
	Address  string `json:"address"`
	Name     string `json:"name"`
	Phone    string `json:"phone"`
}

type NutifoodPayment struct {
	Amount      float64 `json:"amount"`
	PaymentType string  `json:"payment_type"`
}

// NutifoodMenuItemsResponse represents the response from GetMenuItems
type NutifoodMenuItemsResponse struct {
	Success   bool               `json:"success"`
	MenuItems []NutifoodMenuItem `json:"menu_items"`
	RawItems  []NutifoodMenuItem `json:"raw_items,omitempty"`
}

// NutifoodSyncData represents the data returned from SyncAnOrder
type NutifoodSyncData struct {
	Request       any `json:"request"`
	Response      any `json:"response"`
	StockRequest  any `json:"stock_request,omitempty"`
	StockResponse any `json:"stock_response,omitempty"`
}

// NutifoodSyncResponse represents the response from SyncAnOrder
type NutifoodSyncResponse struct {
	Success bool             `json:"success"`
	Message string           `json:"message"`
	Data    NutifoodSyncData `json:"data"`
}
