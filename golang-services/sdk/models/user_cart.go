package models

import (
	"encoding/json"
	"time"
)

type UserCart struct {
	ID            ObjectID                     `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	UserID        string                       `json:"user_id" gorm:"not null"`
	SiteID        string                       `json:"site_id" gorm:"not null"`
	Dishes        JSONField[[]CartDish]        `json:"dishes" gorm:"type:jsonb"`
	Note          string                       `json:"note"`
	TotalItems    int64                        `json:"total_items" gorm:"default:0"`
	SubTotal      float64                      `json:"sub_total" gorm:"default:0"`
	ShippingFee   float64                      `json:"shipping_fee" gorm:"default:0"`
	Total         float64                      `json:"total" gorm:"default:0"`
	Gifts         JSONField[[]CartDish]        `json:"gifts" gorm:"type:jsonb"`
	Discount      JSONField[[]CartDiscount]    `json:"discount" gorm:"type:jsonb"`
	ShipDiscount  JSONField[[]CartDiscount]    `json:"ship_discount" gorm:"type:jsonb"`
	ShippingPromo *ShippingPromo               `json:"shipping_promo" gorm:"type:jsonb"`
	Vouchers      JSONField[[]Voucher]         `json:"vouchers" gorm:"type:jsonb"`
	Shipment      CartShipment                 `json:"shipment" gorm:"embedded;type:jsonb"`
	Shipments     JSONField[[]ShipmentService] `json:"shipments" gorm:"type:jsonb"`
	PaymentMethod string                       `json:"payment_method" gorm:"type:varchar(10);default:'CASH'"`
	Status        string                       `json:"status" gorm:"type:varchar(20);check:status IN ('created','completed')"`
	CreatedAt     time.Time                    `json:"created_at"`
	UpdatedAt     time.Time                    `json:"updated_at"`
}

// CartDish represents a dish in a cart
type CartDish struct {
	ID              string         `json:"_id"`
	ItemID          string         `json:"item_id"`
	Name            string         `json:"name"`
	CategoryName    string         `json:"category_name"`
	SubCategoryName string         `json:"sub_category_name"`
	CategoryCode    string         `json:"category_code"`
	Unit            string         `json:"unit"`
	Code            string         `json:"code"`
	Image           string         `json:"image"`
	Description     string         `json:"description"`
	Price           float64        `json:"price"`
	UnitPrice       float64        `json:"unit_price"`
	Quantity        int64          `json:"quantity"`
	Note            string         `json:"note"`
	Error           string         `json:"error,omitempty"`
	IsGift          bool           `json:"is_gift"`
	IsApplicable    bool           `json:"is_applicable"`
	ConfigID        string         `json:"config_id,omitempty"`
	Options         [][]DishOption `json:"options,omitempty"`
	Combo           []Combo        `json:"combo,omitempty"`
}

type Combo struct {
	Name     string   `json:"name"`
	Code     string   `json:"code"`
	Price    float64  `json:"price"`
	Quantity int      `json:"quantity"`
	Unit     string   `json:"unit"`
	Category string   `json:"category"`
	Weight   float64  `json:"weight"`
	Images   []string `json:"images"`
}

// DishOption represents options for a dish
type DishOption struct {
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	Price      float64 `json:"price"`
	Quantity   int64   `json:"quantity"`
	OptionItem string  `json:"option_item"`
	OptionName string  `json:"option_name"`
}

// CartDiscount represents a discount applied to cart
type CartDiscount struct {
	CategoryName string  `json:"category_name"`
	ItemName     string  `json:"item_name"`
	Type         string  `json:"type"`
	Code         string  `json:"code"`
	Amount       float64 `json:"amount"`
	Note         string  `json:"note"`
	DishID       string  `json:"dish_id,omitempty"`
	IsApplicable bool    `json:"is_applicable"`
	ConfigID     string  `json:"config_id,omitempty"`
}

// Voucher represents a voucher applied to cart
type Voucher struct {
	Code     string  `json:"code"`
	Discount float64 `json:"discount"`
	Vendor   string  `json:"vendor"` // dpoint, nexpos
}

// ShippingPromo represents shipping promotion details
type ShippingPromo struct {
	Error    string  `json:"error"`
	Discount float64 `json:"discount"`
	Code     string  `json:"code"`
}

// ShipmentSchedule represents delivery schedule
type ShipmentSchedule struct {
	FromTime     string    `json:"from_time"`
	ToTime       string    `json:"to_time"`
	FromDateTime time.Time `json:"from_date_time"`
	ToDateTime   time.Time `json:"to_date_time"`
}

// ShipmentParty represents from/to address info
type ShipmentParty struct {
	UserID     string     `json:"user_id,omitempty"`
	Address    string     `json:"address"`
	AddressObj AddressObj `json:"address_obj"`
	Phone      string     `json:"phone"`
	Name       string     `json:"name"`
}

// ShipmentService represents shipping service details
type ShipmentService struct {
	Vendor      string             `json:"vendor"`
	Code        string             `json:"code"`
	Price       float64            `json:"price"`
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Options     []map[string]any   `json:"options,omitempty"`
	Option      CartShipmentOption `json:"option"`
	Raw         json.RawMessage    `json:"raw,omitempty"`
}

// CartShipment represents shipment details for cart
type CartShipment struct {
	Service  ShipmentService   `json:"service"`
	From     ShipmentParty     `json:"from"`
	To       ShipmentParty     `json:"to"`
	Schedule *ShipmentSchedule `json:"schedule,omitempty"`
	Price    float64           `json:"price"`
}

// CartShipmentOption represents a simplified shipment option used in cart
type CartShipmentOption struct {
	Price float64 `json:"price"`
}

// TableName specifies the table name for GORM
func (UserCart) TableName() string {
	return "user_carts"
}
