package models

import (
	"encoding/json"
	"time"
)

// VendorRequest represents outgoing requests to external vendors
type VendorRequest struct {
	ID           ObjectID        `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	Vendor       string          `json:"vendor" gorm:"type:varchar(20);check:vendor IN ('nutifood', 'payos', 'zalo')"`
	Type         string          `json:"type" gorm:"type:varchar(20);check:type IN ('get_hub_stock', 'get_order_stock', 'sync_order', 'send_zns')"`
	URL          string          `json:"url"`
	Method       string          `json:"method"`
	Headers      json.RawMessage `json:"headers" gorm:"type:jsonb"`
	Success      bool            `json:"success" gorm:"default:false"`
	RequestData  json.RawMessage `json:"request_data" gorm:"type:jsonb"`
	ResponseData json.RawMessage `json:"response_data" gorm:"type:jsonb"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
}

func (VendorRequest) TableName() string {
	return "vendor_requests"
}
