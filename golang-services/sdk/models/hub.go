package models

import (
	"time"
)

// Hub represents a physical location or distribution center
type Hub struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	Name        string                `json:"name" gorm:"not null;index"`
	Code        string                `json:"code" gorm:"unique;index"`
	Phone       string                `json:"phone"`
	Address     string                `json:"address" gorm:"not null"`
	AddressObj  JSONField[AddressObj] `json:"address_obj" gorm:"type:jsonb"`
	Description string                `json:"description"`

	// Inventory configuration
	InventorySource string    `json:"inventory_source" gorm:"type:varchar(20);check:inventory_source IN ('manual', 'nexpos', 'odoo', 'nutifood');default:'manual'"`
	LastCronStock   time.Time `json:"last_cron_stock"`

	// Operational settings
	PrinterIP          string            `json:"printer_ip" gorm:"default:'*************:9100'"`
	ZaloGroup          string            `json:"zalo_group"`
	Status             string            `json:"status" gorm:"type:varchar(10);check:status IN ('active', 'inactive');default:'active'"`
	EnableWorkingShift bool              `json:"enable_working_shift"`
	BrandIDs           JSONArray[string] `json:"brand_ids" gorm:"type:text[]"`

	ManagerName  string `json:"manager_name" gorm:"type:varchar(255)"`
	ManagerPhone string `json:"manager_phone" gorm:"type:varchar(20)"`
}

// TableName specifies the table name for the Hub model
func (Hub) TableName() string {
	return "hubs"
}

type HubDetail struct {
	Hub
	SiteCount int64 `json:"site_count"`
}
