package models

import (
	"time"
)

type SiteMenuGroup struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	SiteID           string                    `json:"site_id" gorm:"unique;index"`
	Categories       JSONArray[Category]       `json:"categories" gorm:"type:jsonb"`
	OptionCategories JSONArray[OptionCategory] `json:"option_categories" gorm:"type:jsonb"`
	CreatedAt        time.Time                 `json:"created_at"`
	UpdatedAt        time.Time                 `json:"updated_at"`
}

// SiteMenuCategory represents a menu category for a site
type SiteMenuCategory struct {
	ID          string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID      string    `json:"site_id" gorm:"index"`
	Name        string    `json:"name"`
	ParentID    string    `json:"parent_id"`
	Order       int64     `json:"order"`
	Active      bool      `json:"active" gorm:"default:true"`
	TemplateID  string    `json:"template_id" gorm:"index"`
	TemplateCID string    `json:"template_cid" gorm:"index"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SiteMenuItem represents a menu item for a site
type SiteMenuItem struct {
	ID           string                         `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID       string                         `json:"site_id" gorm:"index"`
	CategoryID   string                         `json:"category_id" gorm:"index"`
	Name         string                         `json:"name"`
	Code         string                         `json:"code"`
	Unit         string                         `json:"unit"`
	Sources      JSONArray[string]              `json:"sources" gorm:"type:jsonb"`
	Description  string                         `json:"description"`
	Images       JSONArray[string]              `json:"images" gorm:"type:jsonb"`
	ImagePreview string                         `json:"image_preview"`
	Price        float64                        `json:"price"`
	Order        int64                          `json:"order"`
	Active       bool                           `json:"active" gorm:"default:true"`
	Channels     JSONArray[SiteMenuItemChannel] `json:"channels,omitempty" gorm:"type:jsonb"`
	TemplateID   string                         `json:"template_id" gorm:"index"`
	TemplateIID  string                         `json:"template_i_id" gorm:"index"`
	CreatedAt    time.Time                      `json:"created_at"`
	UpdatedAt    time.Time                      `json:"updated_at"`
}

// SiteMenuOption represents an option category for a site menu
type SiteMenuOption struct {
	ID          string                            `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID      string                            `json:"site_id" gorm:"index"`
	Name        string                            `json:"name"`
	Order       int64                             `json:"order"`
	Active      bool                              `json:"active" gorm:"default:true"`
	Rule        JSONField[MenuTemplateOptionRule] `json:"rule" gorm:"type:jsonb"`
	ItemIDs     JSONArray[string]                 `json:"item_ids" gorm:"type:jsonb"`
	TemplateID  string                            `json:"template_id" gorm:"index"`
	TemplateOID string                            `json:"template_oid" gorm:"index"`
	CreatedAt   time.Time                         `json:"created_at"`
	UpdatedAt   time.Time                         `json:"updated_at"`
}

// SiteMenuOptionItem represents an option item for a site menu
type SiteMenuOptionItem struct {
	ID          string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	SiteID      string    `json:"site_id" gorm:"index"`
	OptionID    string    `json:"option_id" gorm:"index"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Price       float64   `json:"price"`
	Order       int64     `json:"order"`
	Active      bool      `json:"active" gorm:"default:true"`
	TemplateID  string    `json:"template_id" gorm:"index"`
	TemplateOID string    `json:"template_oid" gorm:"index"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Table names
func (SiteMenuCategory) TableName() string {
	return "site_menu_categories"
}

func (SiteMenuItem) TableName() string {
	return "site_menu_items"
}

func (SiteMenuOption) TableName() string {
	return "site_menu_options"
}

func (SiteMenuOptionItem) TableName() string {
	return "site_menu_option_items"
}
