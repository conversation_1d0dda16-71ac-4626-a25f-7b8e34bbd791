package models

import (
	"encoding/json"
	"time"
)

type AddressLocation struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// AddressObj represents a structured address with geocoding information
type AddressObj struct {
	FormattedAddress string          `json:"formatted_address"`
	Location         AddressLocation `json:"location"`
	WardName         string          `json:"ward_name"`
	DistrictName     string          `json:"district_name"`
	ProvinceName     string          `json:"province_name"`
	Route            string          `json:"route"`
}

// BrandToken represents integration tokens for various platforms
type BrandToken struct {
	TokenCode         string          `json:"token_code"`
	TokenCodeOfficial string          `json:"token_code_official"`
	Source            string          `json:"source"`
	SiteID            string          `json:"site_id"`
	SiteData          json.RawMessage `json:"site_data"`
	Username          string          `json:"username"`
	Password          string          `json:"password"`
	AccessToken       string          `json:"access_token"`
	RefreshToken      string          `json:"refresh_token"`
	LastUpdated       time.Time       `json:"last_updated"`
	Working           bool            `json:"working"`
}

// BrandBanner represents promotional banners for the brand
type BrandBanner struct {
	URL         string `json:"url"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

type BrandType string

const (
	FoodAndBeverage BrandType = "F&B"
	Retailer        BrandType = "Retailer"
)

// Brand represents a business entity in the system
type Brand struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	Name        string                `json:"name" gorm:"not null;index"`
	Address     string                `json:"address" gorm:"not null"`
	AddressObj  JSONField[AddressObj] `json:"address_obj" gorm:"type:jsonb"`
	Description string                `json:"description"`
	Logo        string                `json:"logo"`
	BrandType   BrandType             `json:"brand_type" gorm:"type:enum('F&B', 'Retailer')"`
	Status      string                `json:"status"`

	// Integration and configuration
	MenuSheetFileID string                 `json:"menu_sheet_file_id"`
	Hotline         string                 `json:"hotline"`
	Banners         JSONArray[BrandBanner] `json:"banners" gorm:"type:jsonb"`
	Tokens          JSONArray[BrandToken]  `json:"tokens" gorm:"type:jsonb"`

	// Operational settings
	LastCronToken         time.Time `json:"last_cron_token"`
	LastCronOrderChecking time.Time `json:"last_cron_order_checking"`
	UseCDP                bool      `json:"use_cdp" gorm:"default:false"`
}

// TableName specifies the table name for the Brand model
func (Brand) TableName() string {
	return "brands"
}

// GetToken retrieves a token for a specific source
func (b *Brand) GetToken(source string) *BrandToken {
	for _, token := range b.Tokens {
		if token.Source == source {
			return &token
		}
	}
	return nil
}

// GetTokensForUser retrieves tokens with sensitive information removed
func (b *Brand) GetTokensForUser() []BrandToken {
	tokens := make([]BrandToken, len(b.Tokens))
	copy(tokens, b.Tokens)

	for i := range tokens {
		if tokens[i].Working {
			tokens[i].Username = ""
			tokens[i].Password = ""
			tokens[i].AccessToken = ""
			tokens[i].RefreshToken = ""
			tokens[i].SiteID = ""
		}
	}

	return tokens
}
