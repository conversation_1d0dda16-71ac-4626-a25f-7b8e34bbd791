package models

import (
	"time"
)

// MenuTemplateOptionRule represents the rules for a menu template option category
type MenuTemplateOptionRule struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

// MenuTemplate represents a menu template for a brand
type MenuTemplate struct {
	ID          string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	BrandID     string    `json:"brand_id" gorm:"index"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MenuTemplateCategory represents a category in a menu template
type MenuTemplateCategory struct {
	ID         string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	TemplateID string    `json:"template_id" gorm:"index"`
	Name       string    `json:"name"`
	ParentID   string    `json:"parent_id"`
	Order      int64     `json:"order"`
	Active     bool      `json:"active" gorm:"default:true"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// MenuTemplateItem represents an item in a menu template category
type MenuTemplateItem struct {
	ID           string            `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	TemplateID   string            `json:"template_id" gorm:"index"`
	CategoryID   string            `json:"category_id" gorm:"index"`
	Name         string            `json:"name"`
	Code         string            `json:"code"`
	Unit         string            `json:"unit"`
	Sources      JSONArray[string] `json:"sources" gorm:"type:jsonb"`
	Description  string            `json:"description"`
	Images       JSONArray[string] `json:"images" gorm:"type:jsonb"`
	ImagePreview string            `json:"image_preview"`
	Price        float64           `json:"price"`
	Order        int64             `json:"order"`
	Active       bool              `json:"active" gorm:"default:true"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// MenuTemplateOption represents an option category in a menu template
type MenuTemplateOption struct {
	ID         string                            `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	TemplateID string                            `json:"template_id" gorm:"index"`
	Name       string                            `json:"name"`
	Order      int64                             `json:"order"`
	Active     bool                              `json:"active" gorm:"default:true"`
	Rule       JSONField[MenuTemplateOptionRule] `json:"rule" gorm:"type:jsonb"`
	ItemIDs    JSONArray[string]                 `json:"item_ids" gorm:"type:jsonb"`
	CreatedAt  time.Time                         `json:"created_at"`
	UpdatedAt  time.Time                         `json:"updated_at"`
}

// MenuTemplateOptionItem represents an option item in a menu template
type MenuTemplateOptionItem struct {
	ID         string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	TemplateID string    `json:"template_id" gorm:"index"`
	OptionID   string    `json:"option_id" gorm:"index"`
	Name       string    `json:"name"`
	Code       string    `json:"code"`
	Price      float64   `json:"price"`
	Order      int64     `json:"order"`
	Active     bool      `json:"active" gorm:"default:true"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// Table names
func (MenuTemplate) TableName() string {
	return "menu_templates"
}

func (MenuTemplateCategory) TableName() string {
	return "menu_template_categories"
}

func (MenuTemplateItem) TableName() string {
	return "menu_template_items"
}

func (MenuTemplateOption) TableName() string {
	return "menu_template_options"
}

func (MenuTemplateOptionItem) TableName() string {
	return "menu_template_option_items"
}
