package models

import (
	"encoding/json"
	"time"
)

// OrderShipment represents shipping information for orders
type OrderShipment struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Shipment information
	Vendor        string  `json:"vendor"`
	ShipmentID    string  `json:"shipment_id" gorm:"uniqueIndex"`
	IsSubShipment bool    `json:"is_sub_shipment" gorm:"default:false"`
	IsManual      bool    `json:"is_manual" gorm:"default:false"`
	COD           float64 `json:"cod"`
	PriceForUser  float64 `json:"price_for_user"`
	OrderID       string  `json:"order_id" gorm:"index"`

	// Address information
	FromAddress string `json:"from_address"`
	FromName    string `json:"from_name"`
	FromPhone   string `json:"from_phone"`
	ToAddress   string `json:"to_address"`
	ToName      string `json:"to_name"`
	ToPhone     string `json:"to_phone"`

	// Driver information
	DriverName  string `json:"driver_name"`
	DriverPhone string `json:"driver_phone"`

	// Tracking information
	TrackingURL  string          `json:"tracking_url"`
	Price        float64         `json:"price"`
	Status       string          `json:"status" gorm:"type:varchar(20);check:status IN ('ORDER_CREATING', 'ORDER_CREATED', 'DRIVER_ASSIGNING', 'DRIVER_PICKING_UP', 'IN_DELIVERY', 'COMPLETED', 'CANCELLED', 'RETURNED');default:'ORDER_CREATING'"`
	CancelBy     string          `json:"cancel_by"`
	CancelReason string          `json:"cancel_reason"`
	Webhooks     json.RawMessage `json:"webhooks" gorm:"type:jsonb"`
}

// TableName specifies the table name for the OrderShipment model
func (OrderShipment) TableName() string {
	return "order_shipments"
}
