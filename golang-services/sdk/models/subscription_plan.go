package models

import (
	"time"
)

// ResourceCode represents identifiers for countable resources with limits
type ResourceCode string

const (
	// Resource codes for subscription plan limits
	UsersResourceCode  ResourceCode = "users"
	StoresResourceCode ResourceCode = "stores"

	// ReportHistoryDaysResourceCode defines how many days of report history a plan can access
	// ReportHistoryDaysResourceCode ResourceCode = "report_history_days"
)

// GetAllResourceCodes returns all defined resource codes
func GetAllResourceCodes() []ResourceCode {
	return []ResourceCode{
		UsersResourceCode,
		StoresResourceCode,
		// ReportHistoryDaysResourceCode,
	}
}

// PlanID represents the identifier for subscription plans
type PlanID string

const (
	TrialPlanID     PlanID = "trial"
	FreePlanID      PlanID = "free"
	ECommercePlanID PlanID = "ecommerce"
	BasicPlanID     PlanID = "basic"
	StandardPlanID  PlanID = "standard"
)

// FeatureLimits defines resource limits for a plan using ResourceCode as keys
type FeatureLimits map[ResourceCode]int

// NewFeatureLimits creates a new FeatureLimits map with default values
func NewFeatureLimits(users, stores, reportHistoryDays int) FeatureLimits {
	return FeatureLimits{
		UsersResourceCode:  users,
		StoresResourceCode: stores,
		// ReportHistoryDaysResourceCode: reportHistoryDays,
	}
}

// SalesFeatures represents sales-related features
type SalesFeatures struct {
	OfflineMode      bool `json:"offline_mode"`
	MultiplePayments bool `json:"multiple_payments"`
	DigitalReceipts  bool `json:"digital_receipts"`
	Discounts        bool `json:"discounts"`
	GiftCards        bool `json:"gift_cards"`
	CustomerLoyalty  bool `json:"customer_loyalty"`
}

// InventoryFeatures represents inventory management features
type InventoryFeatures struct {
	BarcodeScanning    bool `json:"barcode_scanning"`
	Variants           bool `json:"variants"`
	BundleProducts     bool `json:"bundle_products"`
	LowStockAlerts     bool `json:"low_stock_alerts"`
	InventoryTracking  bool `json:"inventory_tracking"`
	SupplierManagement bool `json:"supplier_management"`
}

// ReportingFeatures represents analytics and reporting features
type ReportingFeatures struct {
	SalesReports      bool `json:"sales_reports"`
	InventoryReports  bool `json:"inventory_reports"`
	CustomerReports   bool `json:"customer_reports"`
	ExportReports     bool `json:"export_reports"`
	AdvancedAnalytics bool `json:"advanced_analytics"`
	CustomReports     bool `json:"custom_reports"`
}

// UserManagementFeatures represents user and employee management features
type UserManagementFeatures struct {
	StaffAccounts      bool `json:"staff_accounts"`
	RolePermissions    bool `json:"role_permissions"`
	TimeTracking       bool `json:"time_tracking"`
	CommissionTracking bool `json:"commission_tracking"`
}

// IntegrationFeatures represents third-party integration capabilities
type IntegrationFeatures struct {
	Accounting        bool `json:"accounting"`
	ECommerce         bool `json:"e_commerce"`
	EmailMarketing    bool `json:"email_marketing"`
	PaymentProcessors bool `json:"payment_processors"`
	ThirdPartyApps    bool `json:"third_party_apps"`
}

// FeatureLists contains all feature categories organized by functional area
type FeatureLists struct {
	Sales          SalesFeatures          `json:"sales"`
	Inventory      InventoryFeatures      `json:"inventory"`
	Reporting      ReportingFeatures      `json:"reporting"`
	UserManagement UserManagementFeatures `json:"user_management"`
	Integrations   IntegrationFeatures    `json:"integrations"`
}

// SubscriptionPlan represents a product subscription offering
type SubscriptionPlan struct {
	ID           PlanID             `json:"id"`
	Name         string             `json:"name"`
	Description  string             `json:"description"`
	MonthlyPrice float64            `json:"monthly_price"` // Price for monthly subscription
	YearlyPrice  float64            `json:"yearly_price"`  // Price for yearly subscription (usually discounted)
	Period       SubscriptionPeriod `json:"period"`        // Default period for display purposes
	Limits       FeatureLimits      `json:"limits"`        // Resource limits with ResourceCode as keys
	FeatureLists FeatureLists       `json:"features"`      // Structured features by category
}

// GetPriceForPeriod returns the price for the specified period
func (p *SubscriptionPlan) GetPriceForPeriod(period SubscriptionPeriod) float64 {
	switch period {
	case Monthly:
		return p.MonthlyPrice
	case Yearly:
		return p.YearlyPrice
	case Trial:
		return 0
	default:
		return p.MonthlyPrice
	}
}

// CalculateEndDate returns the end date for a given subscription period starting from now
func CalculateEndDate(period SubscriptionPeriod) time.Time {
	switch period {
	case Monthly:
		return time.Now().AddDate(0, 0, 30) // 1 month
	case Yearly:
		return time.Now().AddDate(0, 0, 365) // 1 year
	default:
		return time.Now().AddDate(0, 0, 14) // 14 days for trial
	}
}

// CalculateEndDateFromNow returns the end date for a given subscription period starting from now
func CalculateEndDateFromNow(period SubscriptionPeriod) time.Time {
	switch period {
	case Monthly:
		return time.Now().AddDate(0, 1, 0) // 1 month
	case Yearly:
		return time.Now().AddDate(1, 0, 0) // 1 year
	default:
		return time.Now().AddDate(0, 0, 14) // 14 days for trial
	}
}

// CalculateEndDateFromRemainingDays calculates a new end date based on remaining days
func CalculateEndDateFromRemainingDays(days int) time.Time {
	return time.Now().AddDate(0, 0, days)
}

// Predefined subscription plans as package constants
var (
	// Trial plan - 14-day free trial with premium features
	TrialPlan = SubscriptionPlan{
		ID:           TrialPlanID,
		Name:         "Trial Plan",
		Description:  "14-day free trial with premium features",
		MonthlyPrice: 0,
		YearlyPrice:  0,
		Period:       Trial,
		// Resource limits based on the CSV data
		Limits: NewFeatureLimits(
			8,  // users - maximum 8 users during trial per CSV
			8,  // stores - maximum 8 stores to test multi-store features per CSV
			-1, // report history days - unlimited during trial
		),
		// Feature list configurations for trial plan
		FeatureLists: FeatureLists{
			Sales: SalesFeatures{
				OfflineMode:      true,
				MultiplePayments: true,
				DigitalReceipts:  true,
				Discounts:        true,
				GiftCards:        true,
				CustomerLoyalty:  true,
			},
			Inventory: InventoryFeatures{
				BarcodeScanning:    true,
				Variants:           true,
				BundleProducts:     true,
				LowStockAlerts:     true,
				InventoryTracking:  true,
				SupplierManagement: true,
			},
			Reporting: ReportingFeatures{
				SalesReports:      true, // Full revenue, promotion, platform fee reports
				InventoryReports:  true, // Full inventory reports
				CustomerReports:   true, // Customer data reports
				ExportReports:     true, // Export capabilities
				AdvancedAnalytics: true, // Advanced analytics during trial
				CustomReports:     true, // Custom reporting during trial
			},
			UserManagement: UserManagementFeatures{
				StaffAccounts:      true,
				RolePermissions:    true,
				TimeTracking:       true,
				CommissionTracking: true,
			},
			Integrations: IntegrationFeatures{
				Accounting:        true,
				ECommerce:         true,
				EmailMarketing:    true,
				PaymentProcessors: true,
				ThirdPartyApps:    true,
			},
		},
	}

	// Free plan - limited but permanent free tier
	FreePlan = SubscriptionPlan{
		ID:           FreePlanID,
		Name:         "Free Plan",
		Description:  "Basic features for small businesses",
		MonthlyPrice: 0,
		YearlyPrice:  0,
		Period:       Monthly,
		// Resource limits based on the CSV data
		Limits: NewFeatureLimits(
			3, // users - maximum 3 staff per store per CSV "3 nhân viên/ cửa hàng"
			2, // stores - limited to 2 sales channels per CSV "Tối đa 2 kênh bán"
			3, // report history days - limited to last 3 days per CSV "Chỉ xem được 3 ngày gần nhất"
		),
		// Feature list configurations for free plan
		FeatureLists: FeatureLists{
			Sales: SalesFeatures{
				OfflineMode:      true,
				MultiplePayments: false, // Cash only per CSV
				DigitalReceipts:  false,
				Discounts:        false,
				GiftCards:        false,
				CustomerLoyalty:  false,
			},
			Inventory: InventoryFeatures{
				BarcodeScanning:    true,
				Variants:           false,
				BundleProducts:     false,
				LowStockAlerts:     false,
				InventoryTracking:  false,
				SupplierManagement: false,
			},
			Reporting: ReportingFeatures{
				SalesReports:      true,  // Limited to last 3 days per CSV "Chỉ xem được 3 ngày gần nhất"
				InventoryReports:  false, // No inventory reports
				CustomerReports:   false, // No customer data reports
				ExportReports:     false, // No export capabilities
				AdvancedAnalytics: false, // No advanced analytics
				CustomReports:     false, // No custom reporting
			},
			UserManagement: UserManagementFeatures{
				StaffAccounts:      false,
				RolePermissions:    false,
				TimeTracking:       false,
				CommissionTracking: false,
			},
			Integrations: IntegrationFeatures{
				Accounting:        false,
				ECommerce:         true, // Basic e-commerce integration
				EmailMarketing:    false,
				PaymentProcessors: false,
				ThirdPartyApps:    false,
			},
		},
	}

	// E-Commerce plan - focused on online selling
	ECommercePlan = SubscriptionPlan{
		ID:           ECommercePlanID,
		Name:         "E-Commerce Plan",
		Description:  "Optimized for online stores and multichannel selling",
		MonthlyPrice: 100000,
		YearlyPrice:  1200000, // Annual pricing
		Period:       Monthly,
		// Resource limits based on the CSV data
		Limits: NewFeatureLimits(
			3,  // users - 3 staff per store per CSV "3 nhân viên/ cửa hàng"
			3,  // stores - up to 3 sales channels per CSV "Tối đa 3 kênh bán + 30.000đ/gian hàng/tháng"
			-1, // report history days - unlimited (not specified in CSV)
		),
		// Feature list configurations for e-commerce plan
		FeatureLists: FeatureLists{
			Sales: SalesFeatures{
				OfflineMode:      true,
				MultiplePayments: true,
				DigitalReceipts:  true,
				Discounts:        false, // No promotions per CSV
				GiftCards:        false,
				CustomerLoyalty:  false,
			},
			Inventory: InventoryFeatures{
				BarcodeScanning:    true,
				Variants:           true,
				BundleProducts:     true,
				LowStockAlerts:     false, // No inventory management features
				InventoryTracking:  false,
				SupplierManagement: false,
			},
			Reporting: ReportingFeatures{
				SalesReports:      true,  // Full revenue, promotion reports per CSV
				InventoryReports:  false, // No inventory reports per CSV
				CustomerReports:   true,  // Customer data reports per CSV
				ExportReports:     true,  // Export capabilities
				AdvancedAnalytics: false, // No advanced analytics
				CustomReports:     false, // No custom reporting
			},
			UserManagement: UserManagementFeatures{
				StaffAccounts:      true,
				RolePermissions:    true,
				TimeTracking:       false,
				CommissionTracking: false,
			},
			Integrations: IntegrationFeatures{
				Accounting:        false,
				ECommerce:         true,
				EmailMarketing:    false,
				PaymentProcessors: false, // No payment integration
				ThirdPartyApps:    false,
			},
		},
	}

	// Basic plan - entry-level paid plan
	BasicPlan = SubscriptionPlan{
		ID:           BasicPlanID,
		Name:         "Basic Plan",
		Description:  "Essential features for growing businesses",
		MonthlyPrice: 200000,
		YearlyPrice:  2400000, // Annual pricing
		Period:       Monthly,
		// Resource limits based on the CSV data
		Limits: NewFeatureLimits(
			-1, // users - unlimited staff accounts per CSV "Không giới hạn"
			4,  // stores - up to 4 sales channels per CSV "Tối đa 4 kênh bán + 30.000đ/gian hàng/tháng"
			-1, // report history days - unlimited (not specified in CSV)
		),
		// Feature list configurations for basic plan
		FeatureLists: FeatureLists{
			Sales: SalesFeatures{
				OfflineMode:      true,
				MultiplePayments: true,
				DigitalReceipts:  true,
				Discounts:        true, // Promotions enabled
				GiftCards:        false,
				CustomerLoyalty:  false,
			},
			Inventory: InventoryFeatures{
				BarcodeScanning:    true,
				Variants:           true,
				BundleProducts:     true,
				LowStockAlerts:     false, // No inventory management features
				InventoryTracking:  false,
				SupplierManagement: false,
			},
			Reporting: ReportingFeatures{
				SalesReports:      true,  // Full revenue reports per CSV
				InventoryReports:  false, // No inventory reports per CSV
				CustomerReports:   true,  // Customer data reports per CSV
				ExportReports:     true,  // Export capabilities per CSV
				AdvancedAnalytics: false, // No advanced analytics
				CustomReports:     false, // No custom reporting
			},
			UserManagement: UserManagementFeatures{
				StaffAccounts:      true,
				RolePermissions:    true,
				TimeTracking:       false,
				CommissionTracking: false,
			},
			Integrations: IntegrationFeatures{
				Accounting:        true, // Accounting integration
				ECommerce:         true,
				EmailMarketing:    false,
				PaymentProcessors: true, // Payment integration
				ThirdPartyApps:    false,
			},
		},
	}

	// Standard plan - mid-tier offering
	StandardPlan = SubscriptionPlan{
		ID:           StandardPlanID,
		Name:         "Standard Plan",
		Description:  "Complete solution for established businesses",
		MonthlyPrice: 300000,
		YearlyPrice:  3600000, // Annual pricing
		Period:       Monthly,
		// Resource limits based on the CSV data
		Limits: NewFeatureLimits(
			-1, // users - unlimited staff accounts per CSV "Không giới hạn"
			8,  // stores - up to 8 sales channels per CSV "Tối đa 8 kênh bán + 30.000đ/gian hàng/tháng"
			-1, // report history days - unlimited (not specified in CSV)
		),
		// Feature list configurations for standard plan
		FeatureLists: FeatureLists{
			Sales: SalesFeatures{
				OfflineMode:      true,
				MultiplePayments: true,
				DigitalReceipts:  true,
				Discounts:        true,
				GiftCards:        true,
				CustomerLoyalty:  true,
			},
			Inventory: InventoryFeatures{
				BarcodeScanning:    true,
				Variants:           true,
				BundleProducts:     true,
				LowStockAlerts:     true, // Full inventory management
				InventoryTracking:  true,
				SupplierManagement: true,
			},
			Reporting: ReportingFeatures{
				SalesReports:      true,  // Full revenue reports per CSV
				InventoryReports:  true,  // Full inventory reports per CSV
				CustomerReports:   true,  // Customer data reports per CSV
				ExportReports:     true,  // Export capabilities per CSV
				AdvancedAnalytics: true,  // Advanced analytics per CSV
				CustomReports:     false, // No custom reporting
			},
			UserManagement: UserManagementFeatures{
				StaffAccounts:      true,
				RolePermissions:    true,
				TimeTracking:       true,
				CommissionTracking: false,
			},
			Integrations: IntegrationFeatures{
				Accounting:        true,
				ECommerce:         true,
				EmailMarketing:    true,
				PaymentProcessors: true,
				ThirdPartyApps:    true,
			},
		},
	}
)

// GetPlanByID returns a subscription plan by its ID
func GetPlanByID(id PlanID) (*SubscriptionPlan, bool) {
	switch id {
	case TrialPlanID:
		return &TrialPlan, true
	case FreePlanID:
		return &FreePlan, true
	case ECommercePlanID:
		return &ECommercePlan, true
	case BasicPlanID:
		return &BasicPlan, true
	case StandardPlanID:
		return &StandardPlan, true
	default:
		return nil, false
	}
}

// GetAllPlans returns all defined subscription plans
func GetAllPlans() []SubscriptionPlan {
	return []SubscriptionPlan{
		TrialPlan,
		FreePlan,
		ECommercePlan,
		BasicPlan,
		StandardPlan,
	}
}
