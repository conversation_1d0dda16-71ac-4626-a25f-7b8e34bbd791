package models

import (
	"encoding/json"
	"time"
)

// OrderPayment represents payment transactions for orders
type OrderPayment struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Payment information
	Vendor               string          `json:"vendor" gorm:"type:varchar(20);check:vendor IN ('momo', 'payos', 'nexdorpay','vnpay')"`
	OrderID              string          `json:"order_id" gorm:"index"`
	TransactionID        string          `json:"transaction_id"`
	PartnerTransactionID string          `json:"partner_transaction_id"`
	Amount               float64         `json:"amount"`
	Description          string          `json:"description"`
	Currency             string          `json:"currency" gorm:"type:varchar(3);default:'VND'"`
	Status               string          `json:"status" gorm:"type:varchar(20);check:status IN ('PENDING', 'COMPLETED', 'CANCELLED');default:'PENDING'"`
	PaymentData          json.RawMessage `json:"payment_data" gorm:"type:jsonb"`
	CallbackData         json.RawMessage `json:"callback_data" gorm:"type:jsonb"`
}

// TableName specifies the table name for the OrderPayment model
func (OrderPayment) TableName() string {
	return "order_payments"
}
