package models

import (
	"time"
)

type BrandBillConfig struct {
	ID              ObjectID  `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	Name            string    `json:"name" gorm:"not null"`
	BrandID         string    `json:"brand_id" gorm:"not null"`
	BillType        string    `json:"bill_type" gorm:"not null"`
	BillSize        int       `json:"bill_size" gorm:"default:80"`
	ShowBankPayment bool      `json:"show_bank_payment" gorm:"default:false"`
	ContentHTML     string    `json:"content_html"`
	Active          bool      `json:"active" gorm:"default:true"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	Brand *Brand `json:"brand,omitempty" gorm:"foreignKey:BrandID"`
}

func (BrandBillConfig) TableName() string {
	return "brand_bill_configs"
}
