package models

import (
	"time"
)

type BrandStaff struct {
	// Base model fields
	ID      string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	BrandID string `json:"brand_id" gorm:"not null;index"`
	Brand   *Brand `json:"brand,omitempty" gorm:"foreignKey:BrandID"`
	UserID  string `json:"user_id" gorm:"not null;index"`
	User    *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// Role    StaffRole `json:"role" gorm:"not null"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName specifies the table name for the Brand model
func (BrandStaff) TableName() string {
	return "brand_staffs"
}
