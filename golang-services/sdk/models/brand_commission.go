package models

import (
	"time"
)

// PartnerHubCommission represents commission settings for partner hubs
type PartnerHubCommission struct {
	OnlineOrder struct {
		Packaging  float64 `json:"packaging"`
		Commission float64 `json:"commission"`
	} `json:"online_order"`
	OfflineOrder struct {
		Packaging  float64 `json:"packaging"`
		Commission float64 `json:"commission"`
	} `json:"offline_order"`
}

// PersonalCommission represents a personal commission tier
type PersonalCommission struct {
	MinSale int `json:"min_sale"` // Minimum sale in a month, VND
	Bonus   int `json:"bonus"`    // Percentage over overachievement sale
}

// TeamCommission represents team commission settings
type TeamCommission struct {
	MinSalePersonal int `json:"min_sale_personal"`
	MinTeamSize     int `json:"min_team_size"`
	Scales          []struct {
		MinSaleTeam int `json:"min_sale_team"`
		Bonus       int `json:"bonus"` // Percentage of total team sale
	} `json:"scales"`
}

// ReferralCommission represents referral commission settings
type ReferralCommission struct {
	FirstOrder *struct {
		Bonus         int `json:"bonus"`
		MinOrderValue int `json:"min_order_value"`
	} `json:"first_order,omitempty"`

	FirstMonth *struct {
		Bonus         int `json:"bonus"`
		MinNoOfOrders int `json:"min_no_of_orders"`
		MinOrderValue int `json:"min_order_value"`
	} `json:"first_month,omitempty"`

	NextNMonths *struct {
		Bonus         int `json:"bonus"`
		N             int `json:"n"` // Number of months
		MinNoOfOrders int `json:"min_no_of_orders"`
		MinOrderValue int `json:"min_order_value"`
	} `json:"next_n_months,omitempty"`
}

// PartnerHubSettings represents partner hub tier settings
type PartnerHubSettings struct {
	Basic  PartnerHubCommission `json:"basic"`
	Growth PartnerHubCommission `json:"growth"`
}

// NewCustomerCommission represents commission for new customers
type NewCustomerCommission struct {
	Bonus int `json:"bonus"` // VND
}

// BrandCommission represents the commission configuration for a brand
type BrandCommission struct {
	// Base model fields
	ID        ObjectID  `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Commission data
	BrandID     string                           `json:"brand_id" gorm:"type:varchar(64);not null;index:idx_brand_type,unique"`
	Name        string                           `json:"name" gorm:"type:varchar(255);not null"`
	Status      string                           `json:"status" gorm:"type:varchar(10);not null;check:status IN ('active', 'inactive')"`
	Type        string                           `json:"type" gorm:"type:varchar(20);check:type IN ('personal', 'team', 'new_customer', 'referral', 'partner_hub');index:idx_brand_type,unique"`
	NewCustomer JSONField[NewCustomerCommission] `json:"new_customer,omitempty" gorm:"type:jsonb"`
	Personal    JSONField[[]PersonalCommission]  `json:"personal,omitempty" gorm:"type:jsonb"`
	Team        JSONField[TeamCommission]        `json:"team,omitempty" gorm:"type:jsonb"`
	Referral    JSONField[ReferralCommission]    `json:"referral,omitempty" gorm:"type:jsonb"`
	PartnerHub  JSONField[PartnerHubSettings]    `json:"partner_hub,omitempty" gorm:"type:jsonb"`
}

// TableName specifies the table name for the BrandCommission model
func (BrandCommission) TableName() string {
	return "brand_commissions"
}
