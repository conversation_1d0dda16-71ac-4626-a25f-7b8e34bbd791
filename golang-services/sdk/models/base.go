package models

import (
	"database/sql/driver"
	"time"
)

type BaseModel struct {
	ID        ObjectID   `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type ObjectID string

// <PERSON>an implements the sql.Scanner interface
func (id *ObjectID) Scan(value any) error {
	if value == nil {
		*id = ""
		return nil
	}

	switch v := value.(type) {
	case []byte:
		*id = ObjectID(v)
	case string:
		*id = ObjectID(v)
	}
	return nil
}

// Value implements the driver.Valuer interface
func (id ObjectID) Value() (driver.Value, error) {
	if id == "" {
		return nil, nil
	}
	return string(id), nil
}
