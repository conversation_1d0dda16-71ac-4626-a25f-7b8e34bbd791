package models

// ShopeeStockInfo represents stock information for Shopee items
type ShopeeStockInfo struct {
	Stock int `json:"stock"`
}

// ShopeeItem represents a menu item in Shopee
type ShopeeItem struct {
	ID        int64          `json:"id"`
	Name      string         `json:"name"`
	StockInfo ShopeeStockInfo `json:"stock_info"`
}

// ShopeeCategory represents a menu category in Shopee
type ShopeeCategory struct {
	ID    int64        `json:"id"`
	Name  string       `json:"name"`
	Items []ShopeeItem `json:"items"`
}

// ShopeeOption represents a menu option in Shopee
type ShopeeOption struct {
	ID        int64          `json:"id"`
	Name      string         `json:"name"`
	StockInfo ShopeeStockInfo `json:"stock_info"`
}

// ShopeeOptionCategory represents an option category in Shopee
type ShopeeOptionCategory struct {
	ID      int64          `json:"id"`
	Name    string         `json:"name"`
	Options []ShopeeOption `json:"options"`
}

// ShopeeMenu represents the complete menu structure in Shopee
type ShopeeMenu struct {
	Categories       []ShopeeCategory       `json:"categories"`
	OptionCategories []ShopeeOptionCategory `json:"option_categories"`
}
