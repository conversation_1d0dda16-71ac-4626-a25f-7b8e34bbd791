package models

// PaginationResponse represents the standard response format for paginated data
// @Description Pagination response wrapper
type PaginationResponse struct {
	Success    bool  `json:"success" example:"true"`
	Data       any   `json:"data"`
	Total      int64 `json:"totalDocs" example:"100"`
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"limit" example:"10"`
	TotalPages int   `json:"totalPages" example:"10"`
	HasNext    bool  `json:"hasNextPage" example:"true"`
	HasPrev    bool  `json:"hasPrevPage" example:"false"`
}
