package models

import (
	"encoding/json"
	"time"
)

// PrintQueue represents the original print queue model
type PrintQueue struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	// Changed to just index
	SiteID          string          `json:"site_id" gorm:"index"`
	HubID           string          `json:"hub_id" gorm:"index"`
	OrderID         string          `json:"order_id" gorm:"type:varchar(100);index"`
	OrderPrintCount int             `json:"order_print_count" gorm:"default:1"`
	Source          string          `json:"source" gorm:"type:varchar(50)"`
	PrintType       string          `json:"print_type" gorm:"type:varchar(50)"`
	Status          string          `json:"status" gorm:"type:varchar(20);check:status IN ('created', 'printing', 'printed', 'error')"`
	ErrorMessage    string          `json:"error_message" gorm:"type:text"`
	Data            json.RawMessage `json:"data" gorm:"type:jsonb"`
	FileURL         string          `json:"file_url" gorm:"type:text"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`

	// Relations
	Site *Site `json:"site,omitempty" gorm:"foreignKey:SiteID"`
	Hub  *Hub  `json:"hub,omitempty" gorm:"foreignKey:HubID"`
}

func (PrintQueue) TableName() string {
	return "print_queues"
}

// PrintQueueV2 represents the newer version of print queue
type PrintQueueV2 struct {
	ID ObjectID `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	// Changed to just index
	SiteID       int64     `json:"site_id" gorm:"index"`
	HubID        int64     `json:"hub_id" gorm:"index"`
	OrderID      string    `json:"order_id" gorm:"type:varchar(100);index"`
	Status       string    `json:"status" gorm:"type:varchar(20);check:status IN ('created', 'printing', 'printed', 'error');default:'created'"`
	FileURL      string    `json:"file_url" gorm:"type:text"`
	ErrorMessage string    `json:"error_message" gorm:"type:text"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Relations
	Site *Site `json:"site,omitempty" gorm:"foreignKey:SiteID"`
	Hub  *Hub  `json:"hub,omitempty" gorm:"foreignKey:HubID"`
}

func (PrintQueueV2) TableName() string {
	return "print_queues_v2"
}
