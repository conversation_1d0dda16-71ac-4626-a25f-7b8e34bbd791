package models

import (
	"time"
)

// Province represents a geographical province/state in the system
type Province struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Province information
	Code        string            `json:"code" gorm:"unique;not null"`
	Name        string            `json:"name" gorm:"not null"`
	GoogleNames JSONArray[string] `json:"google_names" gorm:"type:text[]"`
}

// TableName specifies the table name for the Province model
func (Province) TableName() string {
	return "provinces"
}

// ProvinceResponse represents the response format for province data
type ProvinceResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
