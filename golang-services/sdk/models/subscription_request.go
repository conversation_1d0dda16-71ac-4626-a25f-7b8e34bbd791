package models

import (
	"time"
)

// RequestType represents the type of subscription request
type RequestType string

const (
	InitialSubscription RequestType = "initial"
	PlanChange          RequestType = "change"
)

// ChangeRequestStatus represents the status of a subscription request
type ChangeRequestStatus string

const (
	Pending  ChangeRequestStatus = "pending"
	Paid     ChangeRequestStatus = "paid"
	Canceled ChangeRequestStatus = "canceled"
)

type PaymentStatus string

const (
	PaymentPending   PaymentStatus = "pending"
	PaymentSuccess   PaymentStatus = "success"
	PaymentFailed    PaymentStatus = "failed"
	PaymentCancelled PaymentStatus = "cancelled"
)

type SubscriptionPeriod string

const (
	Monthly SubscriptionPeriod = "monthly"
	Yearly  SubscriptionPeriod = "yearly"
	Trial   SubscriptionPeriod = "trial"
)

// SubscriptionRequest represents a request to create or change a subscription plan
type SubscriptionRequest struct {
	ID                 ObjectID            `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	RequestType        RequestType         `json:"request_type" gorm:"not null"`
	UserID             string              `json:"user_id" gorm:"not null;index"`               // User ID for all requests
	UserSubscriptionID *ObjectID           `json:"user_subscription_id,omitempty" gorm:"index"` // Required for change, nil for initial
	UserSubscription   *UserSubscription   `json:"user_subscription,omitempty" gorm:"foreignKey:UserSubscriptionID"`
	PlanID             PlanID              `json:"plan_id" gorm:"not null;type:text"` // For both initial and change requests
	Period             SubscriptionPeriod  `json:"period" gorm:"not null"`            // Monthly, Yearly, or Trial
	Status             ChangeRequestStatus `json:"status" gorm:"not null;default:'pending'"`
	StartTime          time.Time           `json:"start_time" gorm:"autoCreateTime"`
	ExpiredTime        *time.Time          `json:"expired_time,omitempty"` // When the request expires if not processed
	CreatedAt          time.Time           `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt          time.Time           `json:"updated_at" gorm:"autoUpdateTime"`

	// Embedded Payment Details
	PaymentStatus      PaymentStatus `json:"payment_status" gorm:"not null;default:'pending'"`
	Amount             float64       `json:"amount" gorm:"not null"`         // Charge amount
	PaymentURL         string        `json:"payment_url,omitempty"`          // URL for payment processing
	TransactionID      *string       `json:"transaction_id,omitempty"`       // Payment gateway transaction ID
	PaymentExpiredTime *time.Time    `json:"payment_expired_time,omitempty"` // When payment window expires

	// Reference to created subscription
	CreatedSubscriptionID *ObjectID `json:"created_subscription_id,omitempty" gorm:"index"`

	// a field to store callback data from the payment gateway, should be array, default to []
	CallbackData []string `json:"callback_data,omitempty" gorm:"type:text[]"`
}

// IsPending returns true if the request is still in a pending state
func (r *SubscriptionRequest) IsPending() bool {
	return r.Status == Pending
}

// IsInitial returns true if this is an initial subscription request
func (r *SubscriptionRequest) IsInitial() bool {
	return r.RequestType == InitialSubscription
}

// IsChange returns true if this is a plan change request
func (r *SubscriptionRequest) IsChange() bool {
	return r.RequestType == PlanChange
}

// IsPaymentPending returns true if payment is still pending
func (r *SubscriptionRequest) IsPaymentPending() bool {
	return r.PaymentStatus == PaymentPending
}

// IsComplete returns true if the request is fully processed
func (r *SubscriptionRequest) IsComplete() bool {
	if r.Status != Paid {
		return false
	}

	// For initial subscriptions, verify a subscription was created
	if r.IsInitial() {
		return r.CreatedSubscriptionID != nil
	}

	// For plan changes, just being paid is sufficient
	return true
}

// GetPlan returns the subscription plan for this request
func (r *SubscriptionRequest) GetPlan() (*SubscriptionPlan, bool) {
	return GetPlanByID(r.PlanID)
}
