// Package models contains data models for the application
package models

import "time"

// SiteOrderIndex represents an incrementing order index for a site and group
// It's used to generate unique sequential order IDs based on site code and date
type SiteOrderIndex struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	// Core fields
	SiteID    string `json:"site_id" gorm:"type:text;index"`
	GroupName string `json:"group_name" gorm:"type:text;index"` // Format: "{SITE_CODE}-{YYMMDD}"
	Index     int64  `json:"index" gorm:"type:bigint"`          // Current index value

	// Timestamps
	CreatedAt *time.Time `json:"created_at,omitempty" gorm:"type:timestamp"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" gorm:"type:timestamp"`
}

// TableName specifies the database table name for SiteOrderIndex
func (SiteOrderIndex) TableName() string {
	return "site_order_index"
}
