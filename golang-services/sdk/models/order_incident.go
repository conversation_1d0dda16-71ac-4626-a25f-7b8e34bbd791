package models

import (
	"encoding/json"
	"time"
)

// OrderIncident represents issues or incidents related to orders
type OrderIncident struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Incident information
	SiteID         string          `json:"site_id" gorm:"index"`
	OrderID        string          `json:"order_id" gorm:"index"`
	Source         string          `json:"source"`
	Description    string          `json:"description"`
	Images         []string        `json:"images" gorm:"type:text[]"`
	RefundedAmount float64         `json:"refunded_amount"`
	CreatedAtUnix  int64           `json:"created_at_unix"`
	Data           json.RawMessage `json:"data" gorm:"type:jsonb"`
}

// TableName specifies the table name for the OrderIncident model
func (OrderIncident) TableName() string {
	return "order_incidents"
}
