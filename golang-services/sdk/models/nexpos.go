package models

// NexposMenuItem represents a menu item from NexPOS
type NexposMenuItem struct {
	ItemCode string  `json:"item_code"`
	ItemName string  `json:"item_name"`
	Unit     string  `json:"unit"`
	Quantity float64 `json:"quantity"`
	ShopCode string  `json:"shop_code"`
	ShopName string  `json:"shop_name"`
}

// NexposSyncData represents the data returned from SyncAnOrder
type NexposSyncData struct {
	Request       any `json:"request"`
	Response      any `json:"response"`
	StockRequest  any `json:"stock_request,omitempty"`
	StockResponse any `json:"stock_response,omitempty"`
}

// NexposSyncResponse represents the response from SyncAnOrder
type NexposSyncResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    NexposSyncData `json:"data"`
}
