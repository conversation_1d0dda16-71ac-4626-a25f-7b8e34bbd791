package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// CommissionSummary represents a summary of commissions earned by a partner
type CommissionSummary struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Commission data
	BrandID string      `json:"brand_id" gorm:"type:varchar(64);not null;index:idx_brand_user_month_year"`
	UserID  string      `json:"user_id" gorm:"type:varchar(64);not null;index:idx_brand_user_month_year"`
	Balance float64     `json:"balance" gorm:"type:decimal(10,2);not null"`
	Month   int         `json:"month" gorm:"type:int;not null;index:idx_brand_user_month_year"`
	Year    int         `json:"year" gorm:"type:int;not null;index:idx_brand_user_month_year"`
	Details CommDetails `json:"details" gorm:"type:jsonb"`
}

// TableName specifies the table name for the CommissionSummary model
func (CommissionSummary) TableName() string {
	return "commission_summaries"
}

// CommDetails is a type for the details JSON field
type CommDetails []map[string]any

// Value implements the driver.Valuer interface for database serialization
func (cd CommDetails) Value() (driver.Value, error) {
	return json.Marshal(cd)
}

// Scan implements the sql.Scanner interface for database deserialization
func (cd *CommDetails) Scan(value any) error {
	if value == nil {
		*cd = CommDetails{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, cd)
}
