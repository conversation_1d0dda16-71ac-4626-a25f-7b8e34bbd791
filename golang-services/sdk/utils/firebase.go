package utils

import (
	"context"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/v4/messaging"
	"google.golang.org/api/option"
)

func SubscribeToTopic(fcmToken, topic string) error {
	ctx := context.Background()
	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsFile(GetEnv("FIREBASE_CREDENTIALS", "")))
	if err != nil {
		return err
	}
	client, err := app.Messaging(ctx)
	if err != nil {
		return err
	}

	tokens := []string{fcmToken}
	_, err = client.SubscribeToTopic(ctx, tokens, topic)
	return err
}

func UnsubscribeFromTopic(fcmToken, topic string) error {
	ctx := context.Background()
	client, err := messaging.NewClient(ctx, nil)
	if err != nil {
		return err
	}

	tokens := []string{fcmToken}
	_, err = client.UnsubscribeFromTopic(ctx, tokens, topic)
	return err
}
