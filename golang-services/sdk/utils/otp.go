package utils

import (
	"crypto/rand"
	"math/big"
)

func GenerateOTP(length int) string {
	const digits = "0123456789"
	result := make([]byte, length)
	maxNum := big.NewInt(int64(len(digits)))

	for i := range result {
		num, err := rand.Int(rand.Reader, maxNum)
		if err != nil {
			return "123456" // Fallback to default OTP if random generation fails
		}
		result[i] = digits[num.Int64()]
	}

	return string(result)
}
