package utils

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt"
)

// Claims defines custom claims structure
type Claims struct {
	Username string `json:"username"`
	jwt.StandardClaims
}

// GenerateJWT creates a new JWT token for the given username
func GenerateJWT(username string) (string, error) {
	// Get JWT secret from environment
	secret := GetEnv("JWT_SECRET", "")
	if secret == "" {
		return "", fmt.Errorf("JWT_SECRET not configured")
	}

	// Set expiration time (30 days)
	expirationTime := time.Now().Add(30 * 24 * time.Hour)

	// Create claims with user data
	claims := &Claims{
		Username: username,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime.Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "nexpos",
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign the token with secret key
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %v", err)
	}

	return tokenString, nil
}

// ValidateJWT validates the provided token and returns the claims
func ValidateJWT(tokenString string) (*Claims, error) {
	// Get JWT secret from environment
	secret := GetEnv("JWT_SECRET", "")
	if secret == "" {
		return nil, fmt.Errorf("JWT_SECRET not configured")
	}

	// Parse and validate token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (any, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	if err != nil {
		if err == jwt.ErrSignatureInvalid {
			return nil, fmt.Errorf("invalid token signature")
		}
		return nil, fmt.Errorf("failed to parse token: %v", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, fmt.Errorf("invalid claims type")
	}

	return claims, nil
}

// RefreshJWT creates a new token with a renewed expiration time
func RefreshJWT(tokenString string) (string, error) {
	// Validate existing token
	claims, err := ValidateJWT(tokenString)
	if err != nil {
		return "", err
	}

	// Generate new token with same username
	return GenerateJWT(claims.Username)
}

// ParseJWTFromHeader extracts and validates JWT from Authorization header
func ParseJWTFromHeader(authHeader string) (*Claims, error) {
	if authHeader == "" {
		return nil, fmt.Errorf("no authorization header")
	}

	// Remove 'Bearer ' prefix if present
	tokenString := authHeader
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		tokenString = authHeader[7:]
	}

	return ValidateJWT(tokenString)
}
