package excel

import (
	"bytes"
	"fmt"
	"io"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/xuri/excelize/v2"
)

// GenerateExcel creates an Excel file with the given data
func GenerateExcel(sheetName string, headers []string, data [][]interface{}) (*bytes.Buffer, error) {
	// Create a new Excel file
	f := excelize.NewFile()
	defer f.Close()

	// Create a new sheet
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, fmt.Errorf("error creating sheet: %v", err)
	}
	f.SetActiveSheet(index)

	// Delete the default Sheet1
	f.DeleteSheet("Sheet1")

	// Set headers
	for i, header := range headers {
		cell, err := excelize.CoordinatesToCellName(i+1, 1)
		if err != nil {
			return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
		}
		f.<PERSON><PERSON>(sheetName, cell, header)
	}

	// Set data
	for i, row := range data {
		for j, value := range row {
			cell, err := excelize.CoordinatesToCellName(j+1, i+2)
			if err != nil {
				return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
			}
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Apply styles
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		return nil, fmt.Errorf("error creating header style: %v", err)
	}

	dataStyle, err := f.NewStyle(&excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Vertical: "center",
		},
	})
	if err != nil {
		return nil, fmt.Errorf("error creating data style: %v", err)
	}

	// Apply header style
	headerRange, err := excelize.CoordinatesToCellName(1, 1)
	if err != nil {
		return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
	}
	lastHeaderCell, err := excelize.CoordinatesToCellName(len(headers), 1)
	if err != nil {
		return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
	}
	f.SetCellStyle(sheetName, headerRange, lastHeaderCell, headerStyle)

	// Apply data style
	if len(data) > 0 {
		dataStartCell, err := excelize.CoordinatesToCellName(1, 2)
		if err != nil {
			return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
		}
		dataEndCell, err := excelize.CoordinatesToCellName(len(headers), len(data)+1)
		if err != nil {
			return nil, fmt.Errorf("error converting coordinates to cell name: %v", err)
		}
		f.SetCellStyle(sheetName, dataStartCell, dataEndCell, dataStyle)
	}

	// Auto-fit columns
	for i := range headers {
		colName, err := excelize.ColumnNumberToName(i + 1)
		if err != nil {
			return nil, fmt.Errorf("error converting column number to name: %v", err)
		}
		f.SetColWidth(sheetName, colName, colName, 20)
	}

	// Save to buffer
	var buffer bytes.Buffer
	if err := f.Write(&buffer); err != nil {
		return nil, fmt.Errorf("error writing Excel file to buffer: %v", err)
	}

	return &buffer, nil
}

// UploadExcel uploads an Excel file to storage
func UploadExcel(bucket, key string, content io.Reader) (string, error) {
	// Read the content into a buffer
	var buffer bytes.Buffer
	if _, err := io.Copy(&buffer, content); err != nil {
		return "", fmt.Errorf("error reading content: %v", err)
	}

	// Use the SDK's UploadFile utility
	return utils.UploadFile(bucket, key, buffer.Bytes())
}

// FormatTime formats a time value for Excel
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("02/01/2006 15:04:05")
}

// FormatCurrency formats a currency value for Excel
func FormatCurrency(amount float64) string {
	return fmt.Sprintf("%.2f", amount)
}
