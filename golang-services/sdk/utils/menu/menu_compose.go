package menu

import (
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// ComposeSiteMenuItem merges new categories into existing site menu
func ComposeSiteMenuItem(siteMenu models.BrandMenu, categories []models.Category) models.BrandMenu {
	for _, groupMenuCategory := range categories {
		categoryIndex := findCategoryIndex(siteMenu.Categories, groupMenuCategory.Name)

		if categoryIndex < 0 {
			// New category
			groupMenuCategory.Sources = []string{groupMenuCategory.Sources[0]} // Reset sources
			siteMenu.Categories = append(siteMenu.Categories, groupMenuCategory)
		} else {
			// Update existing category
			siteCategory := &siteMenu.Categories[categoryIndex]
			siteCategory.Sources = mergeUniqueSources(siteCategory.Sources, groupMenuCategory.Sources)

			// Merge items
			siteCategory.Items = mergeItems(siteCategory.Items, groupMenuCategory.Items)

			// Merge subcategories if they exist
			if len(groupMenuCategory.SubCategories) > 0 {
				siteCategory.SubCategories = mergeSubCategories(siteCategory.SubCategories, groupMenuCategory.SubCategories)
			}
		}
	}

	return siteMenu
}

// ComposeSiteMenuOptionItem merges new option categories into existing site menu
func ComposeSiteMenuOptionItem(siteMenu models.BrandMenu, optionCategories []models.OptionCategory) models.BrandMenu {
	for _, groupMenuCategory := range optionCategories {
		categoryIndex := findOptionCategoryIndex(siteMenu.OptionCategories, groupMenuCategory.Name)

		if categoryIndex < 0 {
			// New option category
			siteMenu.OptionCategories = append(siteMenu.OptionCategories, groupMenuCategory)
		} else {
			// Update existing option category
			siteCategory := &siteMenu.OptionCategories[categoryIndex]
			siteCategory.Sources = mergeUniqueSources(siteCategory.Sources, groupMenuCategory.Sources)

			// Update category IDs if needed
			if len(siteCategory.CategoryIDs) == 0 && len(groupMenuCategory.CategoryIDs) > 0 {
				siteCategory.CategoryIDs = groupMenuCategory.CategoryIDs
			}

			// Merge options
			siteCategory.Options = mergeOptions(siteCategory.Options, groupMenuCategory.Options)
		}
	}

	return siteMenu
}

// Helper functions
func findCategoryIndex(categories []models.Category, name string) int {
	slug := textToSlug(name)
	for i, category := range categories {
		if textToSlug(category.Name) == slug {
			return i
		}
	}
	return -1
}

func findOptionCategoryIndex(categories []models.OptionCategory, name string) int {
	slug := textToSlug(name)
	for i, category := range categories {
		if textToSlug(category.Name) == slug {
			return i
		}
	}
	return -1
}

func mergeItems(existingItems, newItems []models.MenuItem) []models.MenuItem {
	for _, newItem := range newItems {
		itemIndex := findItemIndex(existingItems, newItem.Name)

		if itemIndex < 0 {
			existingItems = append(existingItems, newItem)
		} else {
			// Update existing item
			existingItem := &existingItems[itemIndex]
			existingItem.Name = newItem.Name
			existingItem.Description = newItem.Description
			existingItem.Price = newItem.Price
			existingItem.Active = newItem.Active

			// Only update image if not from storage
			if !strings.Contains(existingItem.Image, "storage.googleapis.com") {
				existingItem.Image = newItem.Image
			}

			existingItem.Sources = mergeUniqueSources(existingItem.Sources, newItem.Sources)
		}
	}
	return existingItems
}

func mergeOptions(existingOptions, newOptions []models.Option) []models.Option {
	for _, newOption := range newOptions {
		optionIndex := findOptionIndex(existingOptions, newOption.Name)

		if optionIndex < 0 {
			existingOptions = append(existingOptions, newOption)
		} else {
			// Update existing option
			existingOption := &existingOptions[optionIndex]
			existingOption.Name = newOption.Name
			existingOption.Price = newOption.Price
			existingOption.Active = newOption.Active

			// Handle combo items if present
			if len(newOption.Combo) > 0 {
				existingOption.Combo = newOption.Combo
			}

			existingOption.Sources = mergeUniqueSources(existingOption.Sources, newOption.Sources)
		}
	}
	return existingOptions
}

func mergeSubCategories(existing, new []models.SubCategory) []models.SubCategory {
	for _, newSubCat := range new {
		subCatIndex := findSubCategoryIndex(existing, newSubCat.Name)

		if subCatIndex < 0 {
			existing = append(existing, newSubCat)
		} else {
			// Update existing subcategory
			existingSubCat := &existing[subCatIndex]
			existingSubCat.Items = mergeItems(existingSubCat.Items, newSubCat.Items)
			existingSubCat.Sources = mergeUniqueSources(existingSubCat.Sources, newSubCat.Sources)
		}
	}
	return existing
}

func findItemIndex(items []models.MenuItem, name string) int {
	slug := textToSlug(name)
	for i, item := range items {
		if textToSlug(item.Name) == slug {
			return i
		}
	}
	return -1
}

func findOptionIndex(options []models.Option, name string) int {
	slug := textToSlug(name)
	for i, option := range options {
		if textToSlug(option.Name) == slug {
			return i
		}
	}
	return -1
}

func findSubCategoryIndex(subCategories []models.SubCategory, name string) int {
	slug := textToSlug(name)
	for i, subCategory := range subCategories {
		if textToSlug(subCategory.Name) == slug {
			return i
		}
	}
	return -1
}

func mergeUniqueSources(existing, new []string) []string {
	sourceMap := make(map[string]bool)

	// Add all existing sources
	for _, source := range existing {
		sourceMap[source] = true
	}

	// Add new sources
	for _, source := range new {
		sourceMap[source] = true
	}

	// Convert back to slice
	var merged []string
	for source := range sourceMap {
		merged = append(merged, source)
	}

	return merged
}

func textToSlug(text string) string {
	return strings.ReplaceAll(strings.ToLower(text), " ", "_")
}
