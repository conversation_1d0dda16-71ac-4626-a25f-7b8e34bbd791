package zalo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

type ZaloTemplateRequest struct {
	Phone        string            `json:"phone"`
	TemplateID   string            `json:"template_id"`
	TemplateData map[string]string `json:"template_data"`
}

type ZaloResponse struct {
	Error   int    `json:"error"`
	Message string `json:"message"`
}

// SendZaloOTP sends an OTP via Zalo using template message
func SendZaloOTP(zaloToken *models.BrandToken, phone string, otp string) error {
	if zaloToken == nil || zaloToken.AccessToken == "" {
		return fmt.Errorf("invalid_zalo_token")
	}

	// Parse site data to get template ID
	var siteData struct {
		ZaloOTPZnsID string `json:"zalo_otp_zns_id"`
	}
	if err := json.Unmarshal([]byte(zaloToken.SiteData), &siteData); err != nil {
		return fmt.Errorf("invalid_site_data: %v", err)
	}

	if siteData.ZaloOTPZnsID == "" {
		return fmt.Errorf("zalo_otp_template_not_configured")
	}

	// Prepare request data
	reqData := ZaloTemplateRequest{
		Phone:      phone,
		TemplateID: siteData.ZaloOTPZnsID,
		TemplateData: map[string]string{
			"otp": otp,
		},
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("failed_to_marshal_request: %v", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/send-template-message", utils.GetEnv("ZALO_API_URL", "https://api.zalo.com/v2.0/oa/message"))
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed_to_create_request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+zaloToken.AccessToken)

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed_to_send_request: %v", err)
	}
	defer resp.Body.Close()

	// Parse response
	var zaloResp ZaloResponse
	if err := json.NewDecoder(resp.Body).Decode(&zaloResp); err != nil {
		return fmt.Errorf("failed_to_decode_response: %v", err)
	}

	// Check for errors in response
	if zaloResp.Error != 0 {
		return fmt.Errorf("zalo_error: %s", zaloResp.Message)
	}

	return nil
}

// RefreshZaloToken refreshes the Zalo access token
func RefreshZaloToken(zaloToken *models.BrandToken) (*models.BrandToken, error) {
	url := fmt.Sprintf("%s/access_token", utils.GetEnv("ZALO_API_URL", "https://oauth.zalo.com/v4"))

	// Prepare request data
	reqData := map[string]string{
		"app_id":        zaloToken.Username,
		"secret_key":    zaloToken.Password,
		"refresh_token": zaloToken.RefreshToken,
		"grant_type":    "refresh_token",
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("failed_to_marshal_request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed_to_create_request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed_to_send_request: %v", err)
	}
	defer resp.Body.Close()

	// Parse response
	var tokenResp struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		ExpiresIn    int    `json:"expires_in"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed_to_decode_response: %v", err)
	}

	// Update token data
	zaloToken.AccessToken = tokenResp.AccessToken
	zaloToken.RefreshToken = tokenResp.RefreshToken

	return zaloToken, nil
}

// LogZaloError logs Zalo API errors with details
func LogZaloError(action string, err error, details map[string]any) {
	// Add timestamp
	details["timestamp"] = time.Now().Format(time.RFC3339)
	details["action"] = action
	details["error"] = err.Error()

	// Convert to JSON for logging
	jsonDetails, _ := json.Marshal(details)

	// Log error (implement your logging mechanism here)
	fmt.Printf("ZALO_ERROR: %s\n", string(jsonDetails))
}
