// Package utils provides utility functions for the nexpos-backend application
package utils

import (
	"time"
)

// SafeTimeIn safely converts a time to the specified location.
// If the location is nil, it falls back to UTC.
func SafeTimeIn(t time.Time, loc *time.Location) time.Time {
	// Check if location is nil
	if loc == nil {
		return t.In(time.UTC) // Use UTC as fallback
	}

	// Safely convert the time to the desired location
	return t.In(loc)
}

// LoadLocationSafe loads a location by name and returns it.
// If loading fails, it returns UTC and logs the error.
func LoadLocationSafe(name string) *time.Location {
	loc, err := time.LoadLocation(name)
	if err != nil {
		// Could integrate with a proper logger here
		return time.UTC // Return UTC as fallback
	}
	return loc
}

// CurrentTimeIn returns the current time in the specified timezone.
// Falls back to UTC if the location cannot be loaded.
func CurrentTimeIn(timezone string) time.Time {
	loc := LoadLocationSafe(timezone)
	return time.Now().In(loc)
}
