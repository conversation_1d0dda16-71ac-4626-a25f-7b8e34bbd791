package ticket

import (
	"fmt"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"gorm.io/gorm"
)

func GenerateTicketCode(db *gorm.DB, hub *models.Hub, ticketType string) (string, error) {
	today := time.Now().Format("060102") // YYMMDD format

	var count int64
	if err := db.Model(&models.HubStockTicket{}).
		Where("hub_id = ? AND ticket_type = ? AND created_at >= ?",
			hub.ID, ticketType, time.Now().Truncate(24*time.Hour)).
		Count(&count).Error; err != nil {
		return "", err
	}

	number := count + 1
	typePrefix := strings.ToUpper(ticketType[:2])
	return fmt.Sprintf("%s_%s_%02d_%s", typePrefix, today, number, hub.Code), nil
}
