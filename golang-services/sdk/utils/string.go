package utils

import (
	"crypto/rand"
	"encoding/hex"
	"regexp"
	"strings"
	"sync/atomic"
	"time"
)

func SlugifyText(text string) string {
	// Convert to lowercase
	text = strings.ToLower(text)

	// Replace Vietnamese characters
	replacements := map[string]string{
		"à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ": "a",
		"è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ":             "e",
		"ì|í|ị|ỉ|ĩ": "i",
		"ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ": "o",
		"ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ":             "u",
		"ỳ|ý|ỵ|ỷ|ỹ": "y",
		"đ":         "d",
	}

	for pattern, replacement := range replacements {
		re := regexp.MustCompile(pattern)
		text = re.ReplaceAllString(text, replacement)
	}

	// Replace any non-alphanumeric character with underscore
	reg := regexp.MustCompile("[^a-z0-9]+")
	text = reg.ReplaceAllString(text, "_")

	// Remove leading/trailing underscores
	text = strings.Trim(text, "_")

	return text
}

func OneOf(values ...string) string {
	for _, value := range values {
		if value != "" {
			return value
		}
	}
	return ""
}

// counter for the 3-byte counter part (starts at 0, max 16777215)
var objectIDCounter uint32

// GenObjectID generates a 24-character hexadecimal string similar to MongoDB ObjectID
func GenObjectID() string {
	// Buffer to hold the 12 bytes (24 hex chars)
	var id [12]byte

	// 4 bytes timestamp (8 hex chars) - seconds since epoch
	timestamp := uint32(time.Now().Unix())
	id[0] = byte(timestamp >> 24)
	id[1] = byte(timestamp >> 16)
	id[2] = byte(timestamp >> 8)
	id[3] = byte(timestamp)

	// 5 bytes random (10 hex chars)
	_, err := rand.Read(id[4:9])
	if err != nil {
		// Fallback to pseudo-random if crypto/rand fails
		t := time.Now().UnixNano()
		id[4] = byte(t >> 40)
		id[5] = byte(t >> 32)
		id[6] = byte(t >> 24)
		id[7] = byte(t >> 16)
		id[8] = byte(t >> 8)
	}

	// 3 bytes counter (6 hex chars)
	// Atomically increment the counter and ensure it fits in 24 bits (0xFFFFFF)
	counter := atomic.AddUint32(&objectIDCounter, 1) & 0xFFFFFF
	id[9] = byte(counter >> 16)
	id[10] = byte(counter >> 8)
	id[11] = byte(counter)

	// Convert to 24-character hex string
	return hex.EncodeToString(id[:])
}

func GenRandomPassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	if length <= 0 {
		length = 12
	}

	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		for i := range b {
			b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
			time.Sleep(time.Nanosecond)
		}
		return string(b)
	}

	for i := range b {
		b[i] = charset[int(b[i])%len(charset)]
	}

	return string(b)
}
