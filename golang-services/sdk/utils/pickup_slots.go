package utils

import (
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// GetPickupSlots returns available time slots for pickup orders
func GetPickupSlots() []models.ShipmentSlot {
	slots := []models.ShipmentSlot{}

	// Current time
	now := time.Now()

	// Create slots for the next 5 days
	for i := 0; i < 5; i++ {
		date := now.AddDate(0, 0, i)
		dateStr := date.Format("2006-01-02")

		// Generate slots in 1-hour intervals from 9:00 to 20:00
		// Adjust these hours according to your business requirements
		for hour := 9; hour < 20; hour++ {
			// Start time
			fromTime := time.Date(date.Year(), date.Month(), date.Day(), hour, 0, 0, 0, date.Location())

			// Skip slots in the past for today
			if i == 0 && fromTime.Before(now) {
				continue
			}

			// End time (1 hour after start)
			toTime := fromTime.Add(1 * time.Hour)

			// Create the slot
			slot := models.ShipmentSlot{
				Date:         dateStr,
				FromTime:     fromTime.Format("15:04"),
				FromDateTime: fromTime.Format(time.RFC3339),
				ToTime:       toTime.Format("15:04"),
				ToDateTime:   toTime.Format(time.RFC3339),
				IsActive:     true,
			}

			slots = append(slots, slot)
		}
	}

	return slots
}

// GetPickupSlotsWithConfig returns time slots with custom configuration
func GetPickupSlotsWithConfig(daysAhead int, startHour int, endHour int, intervalMinutes int) []models.ShipmentSlot {
	slots := []models.ShipmentSlot{}

	// Current time
	now := time.Now()

	// Create slots for the specified number of days
	for i := 0; i < daysAhead; i++ {
		date := now.AddDate(0, 0, i)
		dateStr := date.Format("2006-01-02")

		// Generate slots with specified interval
		for hour := startHour; hour < endHour; hour++ {
			for minute := 0; minute < 60; minute += intervalMinutes {
				// Start time
				fromTime := time.Date(date.Year(), date.Month(), date.Day(), hour, minute, 0, 0, date.Location())

				// Skip slots in the past for today
				if i == 0 && fromTime.Before(now) {
					continue
				}

				// End time (interval minutes after start)
				toTime := fromTime.Add(time.Duration(intervalMinutes) * time.Minute)

				// Create the slot
				slot := models.ShipmentSlot{
					Date:         dateStr,
					FromTime:     fromTime.Format("15:04"),
					FromDateTime: fromTime.Format(time.RFC3339),
					ToTime:       toTime.Format("15:04"),
					ToDateTime:   toTime.Format(time.RFC3339),
					IsActive:     true,
				}

				slots = append(slots, slot)
			}
		}
	}

	return slots
}
