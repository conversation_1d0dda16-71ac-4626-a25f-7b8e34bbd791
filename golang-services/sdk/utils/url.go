package utils

type RedirectType string

const (
	RedirectTypePortal  RedirectType = "PORTAL"
	RedirectTypeDefault RedirectType = "DEFAULT"
)

// GetWebURLToVerifyAccount returns the appropriate web URL based on redirect type
func GetWebURLToVerifyAccount(redirectType *string) string {
	if redirectType == nil {
		return GetEnv("PORTAL_WEB_URL", "")
	}

	switch RedirectType(*redirectType) {
	case RedirectTypePortal:
		return GetEnv("PORTAL_WEB_URL", "")
	case RedirectTypeDefault:
		return GetEnv("WEB_URL", "")
	default:
		return GetEnv("PORTAL_WEB_URL", "")
	}
}
