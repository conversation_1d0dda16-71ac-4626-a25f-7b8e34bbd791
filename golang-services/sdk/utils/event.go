package utils

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

// EventMessage represents the message structure for events
type EventMessage struct {
	Message string         `json:"message"`
	Data    map[string]any `json:"data"`
}

// SendMessageToTopic sends a message to a topic using Redis pub/sub
func SendMessageToTopic(redisClient *redis.Client, topic string, message string, data map[string]any) error {
	if redisClient == nil {
		return fmt.Errorf("redis client is nil")
	}

	ctx := context.Background()

	// Create a copy of the data map to avoid modifying the original
	dataCopy := make(map[string]any, len(data))
	for k, v := range data {
		dataCopy[k] = v
	}

	// Add topic and message_id to data
	dataCopy["topic"] = topic
	dataCopy["message_id"] = uuid.New().String()

	// Create the message structure
	eventMessage := EventMessage{
		Message: message,
		Data:    dataCopy,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(eventMessage)
	if err != nil {
		return fmt.Errorf("error marshaling message: %v", err)
	}

	// Publish to Redis
	err = redisClient.Publish(ctx, "saas_notifications", jsonData).Err()
	if err != nil {
		return fmt.Errorf("error publishing to Redis: %v", err)
	}

	return nil
}

// SendPrintOrderMessage sends a print order message to the print_order topic
func SendPrintOrderMessage(redisClient *redis.Client, orderID, hubCode, siteID, billTemplate, labelTemplate string, billURL string, labelURLs []string) error {
	message := fmt.Sprintf("Order %s is ready to print", orderID)

	data := map[string]any{
		"bill_template":  nil,
		"label_template": nil,
		"hub_code":       hubCode,
		"order_id":       orderID,
		"site_id":        siteID,
		"bill_url":       nil,
		"label_urls":     nil,
	}

	// Only include bill data if billTemplate is provided
	if billTemplate != "" {
		data["bill_template"] = billTemplate
		data["bill_url"] = billURL
	}

	// Only include label data if labelTemplate is provided
	if labelTemplate != "" {
		data["label_template"] = labelTemplate
		data["label_urls"] = labelURLs
	}

	return SendMessageToTopic(redisClient, "print_order", message, data)
}

// SendPrintBillMessage sends a print bill message to the print_bill topic
func SendPrintBillMessage(redisClient *redis.Client, hubCode string, template string, urls []string) error {
	message := "Document is ready to print"

	data := map[string]any{
		"messages": []map[string]any{
			{
				"template": template,
				"hub_code": hubCode,
				"urls":     urls,
			},
		},
	}

	return SendMessageToTopic(redisClient, "print_bill", message, data)
}
