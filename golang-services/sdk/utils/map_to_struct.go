package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// MapToStruct converts a map[string]any to a struct
// It uses JSON marshaling/unmarshaling to handle the conversion
func MapToStruct(input map[string]any, output any) error {
	// Check if output is a pointer to a struct
	if reflect.TypeOf(output).Kind() != reflect.Ptr {
		return fmt.Errorf("output must be a pointer to a struct")
	}

	// Convert map to JSON
	jsonBytes, err := json.Marshal(input)
	if err != nil {
		return fmt.Errorf("error marshaling map to JSON: %w", err)
	}

	// Convert JSON to struct
	if err := json.Unmarshal(jsonBytes, output); err != nil {
		return fmt.Errorf("error unmarshaling JSON to struct: %w", err)
	}

	return nil
}

// MapToStructDirect converts a map to a struct by directly setting field values
// This is an alternative implementation that doesn't use JSON as an intermediary
func MapToStructDirect(m map[string]any, s any) error {
	structValue := reflect.ValueOf(s).Elem()
	structType := structValue.Type()

	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		fieldValue := structValue.Field(i)

		if !fieldValue.CanSet() {
			continue
		}

		// Get the field name from the JSON tag or use the field name
		tagName := field.Tag.Get("json")
		if tagName == "" {
			tagName = strings.ToLower(field.Name)
		} else {
			// Handle tag options like omitempty
			tagName = strings.Split(tagName, ",")[0]
		}

		// Get value from map
		value, exists := m[tagName]
		if !exists {
			continue
		}

		// Convert and set value
		if err := setFieldValue(fieldValue, value); err != nil {
			return fmt.Errorf("cannot set field %s: %w", field.Name, err)
		}
	}

	return nil
}

// setFieldValue sets a field value with appropriate type conversion
func setFieldValue(field reflect.Value, value any) error {
	valueType := reflect.TypeOf(value)
	fieldType := field.Type()

	// If types match exactly, set directly
	if valueType != nil && valueType.AssignableTo(fieldType) {
		field.Set(reflect.ValueOf(value))
		return nil
	}

	// Handle primitive type conversions
	switch field.Kind() {
	case reflect.String:
		switch v := value.(type) {
		case string:
			field.SetString(v)
		case fmt.Stringer:
			field.SetString(v.String())
		default:
			field.SetString(fmt.Sprintf("%v", v))
		}

	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		switch v := value.(type) {
		case int:
			field.SetInt(int64(v))
		case int64:
			field.SetInt(v)
		case float64:
			field.SetInt(int64(v))
		case string:
			if i, err := strconv.ParseInt(v, 10, 64); err == nil {
				field.SetInt(i)
			} else {
				return err
			}
		}

	case reflect.Float32, reflect.Float64:
		switch v := value.(type) {
		case float64:
			field.SetFloat(v)
		case float32:
			field.SetFloat(float64(v))
		case int:
			field.SetFloat(float64(v))
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				field.SetFloat(f)
			} else {
				return err
			}
		}

	case reflect.Bool:
		switch v := value.(type) {
		case bool:
			field.SetBool(v)
		case string:
			if b, err := strconv.ParseBool(v); err == nil {
				field.SetBool(b)
			} else {
				return err
			}
		}

	case reflect.Struct:
		// For nested structs that are maps
		if m, ok := value.(map[string]any); ok {
			if field.CanAddr() {
				return MapToStructDirect(m, field.Addr().Interface())
			}
		}

	case reflect.Slice:
		// For slices, create a new slice and populate it
		if slice, ok := value.([]any); ok {
			newSlice := reflect.MakeSlice(fieldType, len(slice), len(slice))

			for i, val := range slice {
				elemValue := newSlice.Index(i)
				if err := setFieldValue(elemValue, val); err != nil {
					return err
				}
			}

			field.Set(newSlice)
		}
	}

	return nil
}
