package utils

import (
	"fmt"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// AddStockToDishesResponse represents the response from AddStockToDishes
type AddStockToDishesResponse struct {
	Success       bool     `json:"success"`
	ErrorMessages []string `json:"error_messages"`
	Dishes        []struct {
		Name   string `json:"name"`
		Stocks []struct {
			Code      string  `json:"code"`
			Name      string  `json:"name"`
			UnitPrice float64 `json:"unit_price"`
			Quantity  int     `json:"quantity"`
		} `json:"stocks"`
	} `json:"dishes"`
}

// AddStockToDishes adds stock information to dishes
func AddStockToDishes(dishes []models.OrderDish, brandMenu *models.BrandMenu) (*AddStockToDishesResponse, error) {
	result := &AddStockToDishesResponse{
		Success:       true,
		ErrorMessages: []string{},
		Dishes: []struct {
			Name   string `json:"name"`
			Stocks []struct {
				Code      string  `json:"code"`
				Name      string  `json:"name"`
				UnitPrice float64 `json:"unit_price"`
				Quantity  int     `json:"quantity"`
			} `json:"stocks"`
		}{},
	}

	for _, dish := range dishes {
		item := FindItemInMenuByName(brandMenu.Categories, dish.Name)
		if item == nil {
			result.Success = false
			result.ErrorMessages = append(result.ErrorMessages, fmt.Sprintf("Không tìm thấy món %s trong menu", dish.Name))
			continue
		}

		dishWithStocks := struct {
			Name   string `json:"name"`
			Stocks []struct {
				Code      string  `json:"code"`
				Name      string  `json:"name"`
				UnitPrice float64 `json:"unit_price"`
				Quantity  int     `json:"quantity"`
			} `json:"stocks"`
		}{
			Name: dish.Name,
			Stocks: []struct {
				Code      string  `json:"code"`
				Name      string  `json:"name"`
				UnitPrice float64 `json:"unit_price"`
				Quantity  int     `json:"quantity"`
			}{},
		}

		if len(item.Combo) > 0 {
			for _, combo := range item.Combo {
				dishWithStocks.Stocks = append(dishWithStocks.Stocks, struct {
					Code      string  `json:"code"`
					Name      string  `json:"name"`
					UnitPrice float64 `json:"unit_price"`
					Quantity  int     `json:"quantity"`
				}{
					Code:      combo.Code,
					Name:      combo.Name,
					UnitPrice: combo.Price,
					Quantity:  int(dish.Quantity) * int(combo.Quantity),
				})
			}
		} else if item.Code != "" {
			dishWithStocks.Stocks = append(dishWithStocks.Stocks, struct {
				Code      string  `json:"code"`
				Name      string  `json:"name"`
				UnitPrice float64 `json:"unit_price"`
				Quantity  int     `json:"quantity"`
			}{
				Code:      item.Code,
				Name:      item.Name,
				UnitPrice: item.StockPrice,
				Quantity:  int(dish.Quantity),
			})
		} else {
			result.Success = false
			result.ErrorMessages = append(result.ErrorMessages, fmt.Sprintf("Món %s không có mã code", dish.Name))
			continue
		}

		result.Dishes = append(result.Dishes, dishWithStocks)
	}

	return result, nil
}

// FindItemInMenuByName finds an item in the menu by name
func FindItemInMenuByName(categories []models.Category, itemName string) *models.MenuItem {
	for _, category := range categories {
		for _, item := range category.Items {
			if strings.EqualFold(item.Name, itemName) {
				return &item
			}
		}
	}
	return nil
}

// PickFields returns a new map with only the specified fields
func PickFields(obj any, fields []string) map[string]any {
	result := make(map[string]any)
	for _, field := range fields {
		switch field {
		case "ID":
			if v, ok := obj.(models.Hub); ok {
				result["ID"] = v.ID
			}
		case "Name":
			if v, ok := obj.(models.Hub); ok {
				result["Name"] = v.Name
			}
		}
	}
	return result
}
