package auth

import (
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"gorm.io/gorm"
)

type AuthData struct {
	User          any            `json:"user"`
	AccessToken   string         `json:"access_token"`
	ManagedSites  []models.Site  `json:"managed_sites"`
	ManagedHubs   []models.Hub   `json:"managed_hubs"`
	ManagedBrands []models.Brand `json:"managed_brands"`
}

func GetUserAuth(db *gorm.DB, user *models.User) (*AuthData, error) {
	// var role models.Role
	// if err := db.First(&role, user.RoleID).Error; err != nil {
	// 	return nil, fmt.Errorf("role not found")
	// }

	// Base projections for different entity types
	// siteSelect := []string{"id", "brand_id", "hub_id", "address", "name"}
	// hubSelect := []string{"id", "code", "name", "address", "enable_working_shift"}
	// brandSelect := []string{"id", "name", "address"}

	var managedSites []models.Site
	var managedHubs []models.Hub
	var managedBrands []models.Brand

	// if funk.ContainsString(role.Permissions, "system") {
	// 	// System admin gets access to everything
	// 	db.Select(siteSelect).Find(&managedSites)
	// 	db.Select(hubSelect).Find(&managedHubs)
	// 	db.Select(brandSelect).Find(&managedBrands)
	// } else {
	// 	if funk.ContainsString(role.Selectors, "brand") {
	// 		// Get brands
	// 		db.Select(brandSelect).Where("id IN ?", user.Brands).Find(&managedBrands)

	// 		// Get sites based on brands
	// 		query := db.Select(siteSelect)
	// 		if funk.ContainsString(role.Selectors, "site") {
	// 			query = query.Where("id IN ?", user.Sites)
	// 		} else {
	// 			query = query.Where("brand_id IN ?", user.Brands)
	// 		}
	// 		query.Find(&managedSites)

	// 		// Get hubs based on sites
	// 		if funk.ContainsString(role.Selectors, "hub") {
	// 			var hubIDs []string
	// 			for _, site := range managedSites {
	// 				hubIDs = append(hubIDs, string(site.HubID))
	// 				hubIDs = append(hubIDs, site.HubIDs...)
	// 			}
	// 			db.Select(hubSelect).Where("id IN ?", hubIDs).Find(&managedHubs)
	// 		}
	// 	}

	// 	if funk.ContainsString(role.Selectors, "hub") {
	// 		// Get hubs directly assigned to user
	// 		db.Select(hubSelect).Where("id IN ?", user.Hubs).Find(&managedHubs)

	// 		// Get sites based on hubs
	// 		var sites []models.Site
	// 		db.Select(siteSelect).Where("hub_id IN ?", user.Hubs).Find(&sites)

	// 		if funk.ContainsString(role.Selectors, "site") {
	// 			managedSites = sites
	// 		}

	// 		if funk.ContainsString(role.Selectors, "brand") {
	// 			var brandIDs []string
	// 			for _, site := range sites {
	// 				brandIDs = append(brandIDs, site.BrandID)
	// 			}
	// 			db.Select(brandSelect).Where("id IN ?", brandIDs).Find(&managedBrands)
	// 		}
	// 	}

	// 	if funk.ContainsString(role.Selectors, "site") {
	// 		// Get sites directly assigned to user
	// 		var sites []models.Site
	// 		db.Select(siteSelect).Where("id IN ?", user.Sites).Find(&sites)
	// 		managedSites = sites

	// 		if funk.ContainsString(role.Selectors, "hub") {
	// 			var hubIDs []string
	// 			for _, site := range sites {
	// 				hubIDs = append(hubIDs, string(site.HubID))
	// 				hubIDs = append(hubIDs, site.HubIDs...)
	// 			}
	// 			db.Select(hubSelect).Where("id IN ?", hubIDs).Find(&managedHubs)
	// 		}

	// 		if funk.ContainsString(role.Selectors, "brand") {
	// 			var brandIDs []string
	// 			for _, site := range sites {
	// 				brandIDs = append(brandIDs, site.BrandID)
	// 			}
	// 			db.Select(brandSelect).Where("id IN ?", brandIDs).Find(&managedBrands)
	// 		}
	// 	}
	// }

	// if funk.ContainsString(role.Permissions, "partner") {
	// 	var sites []models.Site
	// 	db.Select(siteSelect).
	// 		Where("brand_id IN ? AND id IN ?", user.Brands, user.Sites).
	// 		Find(&sites)
	// 	managedSites = sites
	// }

	// Update user's managed entities
	user.Sites = make([]string, len(managedSites))
	for i, site := range managedSites {
		user.Sites[i] = string(site.ID)
	}

	user.Hubs = make([]string, len(managedHubs))
	for i, hub := range managedHubs {
		user.Hubs[i] = string(hub.ID)
	}

	user.Brands = make([]string, len(managedBrands))
	for i, brand := range managedBrands {
		user.Brands[i] = string(brand.ID)
	}

	if err := db.Save(user).Error; err != nil {
		return nil, err
	}

	userJSON := utils.StructToJSON(user).Value().(map[string]any)
	userJSON["is_pwd_set"] = user.Password != ""
	// userJSON["role"] = role

	// Generate JWT token
	token, err := utils.GenerateJWT(user.Username)
	if err != nil {
		return nil, err
	}

	return &AuthData{
		User:          userJSON,
		AccessToken:   token,
		ManagedSites:  managedSites,
		ManagedHubs:   managedHubs,
		ManagedBrands: managedBrands,
	}, nil
}
