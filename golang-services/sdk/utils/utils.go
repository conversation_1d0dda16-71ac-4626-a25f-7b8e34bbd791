package utils

import (
	"encoding/json"

	"github.com/tidwall/gjson"
)

func StructToJSON(src any) gjson.Result {
	buff, err := json.Marshal(src)
	if err != nil {
		return gjson.Parse("null")
	}
	return gjson.ParseBytes(buff)
}

func ShortIf[T any](b bool, trueVal, falseVal T) T {
	if b {
		return trueVal
	}
	return falseVal
}

func Contains[T comparable](slice []T, element T) bool {
	for _, v := range slice {
		if v == element {
			return true
		}
	}
	return false
}
