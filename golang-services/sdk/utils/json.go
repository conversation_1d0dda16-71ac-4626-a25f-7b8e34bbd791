package utils

import (
	"encoding/json"
	"strings"
)

// ParseJSON unmarshals a JSON string into the provided destination object
func ParseJSON(jsonStr string, dest any) error {
	// Trim whitespace from the JSON string
	jsonStr = strings.TrimSpace(jsonStr)

	// Handle empty strings
	if jsonStr == "" {
		return nil
	}

	// Unmarshal the JSON string
	return json.Unmarshal([]byte(jsonStr), dest)
}

// MarshalToJSON marshals an object to a JSON string
func MarshalToJSON(obj any) (string, error) {
	bytes, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// PrettyJSON formats JSON with indentation for better readability
func PrettyJSON(obj any) (string, error) {
	bytes, err := json.MarshalIndent(obj, "", "  ")
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}
