package utils

import (
	"regexp"
	"strings"
)

var (
	// Email pattern following RFC 5322 standards
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9.!#$%&'*+/=?^_\x60{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$`)

	// Maximum length for email address as per RFC 5321
	maxEmailLength = 254
)

// IsValidEmail checks if the provided email is valid according to RFC standards
func IsValidEmail(email string) bool {
	if email == "" {
		return false
	}

	// Trim spaces
	email = strings.TrimSpace(email)

	// Check length
	if len(email) > maxEmailLength {
		return false
	}

	// Check regex pattern
	if !emailRegex.MatchString(email) {
		return false
	}

	// Additional checks for common patterns
	// Check for consecutive dots
	if strings.Contains(email, "..") {
		return false
	}

	// Split email into local and domain parts
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	// Check local part length (max 64 characters)
	if len(parts[0]) > 64 {
		return false
	}

	// Check domain part length (max 255 characters)
	if len(parts[1]) > 255 {
		return false
	}

	// Check if domain has at least one dot
	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}
