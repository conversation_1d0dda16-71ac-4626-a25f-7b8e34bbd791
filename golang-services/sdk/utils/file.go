package utils

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"os"
	"strings"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"
)

func UploadFile(bucketName, filename string, buffer []byte) (string, error) {
	ctx := context.Background()

	privateKey := os.Getenv("GOOGLE_PRIVATE_KEY")
	if privateKey == "" {
		return "", fmt.Errorf("GOOGLE_PRIVATE_KEY is not set")
	}
	privateKey = strings.ReplaceAll(privateKey, "\n", "\\n")

	client, err := storage.NewClient(ctx, option.WithCredentialsJSON([]byte(privateKey)))
	if err != nil {
		return "", fmt.Errorf("failed to create client: %v", err)
	}
	defer client.Close()

	hash := md5.Sum(buffer)
	hashString := hex.EncodeToString(hash[:])
	fileName := fmt.Sprintf("uploads/%s_%s", hashString, filename)

	bucket := client.Bucket(bucketName)
	obj := bucket.Object(fileName)

	writer := obj.NewWriter(ctx)

	writer.ObjectAttrs.ContentType = "application/octet-stream"
	writer.ObjectAttrs.CacheControl = "public, max-age=86400"

	if _, err := writer.Write(buffer); err != nil {
		return "", fmt.Errorf("failed to write to bucket: %v", err)
	}

	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %v", err)
	}

	return fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, fileName), nil
}
