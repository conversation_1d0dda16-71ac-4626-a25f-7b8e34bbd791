package bill

import (
	"bytes"
	"fmt"
	"html/template"
	"io"
	"strconv"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

// FormatCurrency formats a number as Vietnamese currency
func FormatCurrency(amount float64) string {
	// Format with thousands separator
	str := strconv.FormatFloat(amount, 'f', 0, 64)

	// Add thousands separators
	var result strings.Builder
	for i, c := range str {
		if i > 0 && (len(str)-i)%3 == 0 {
			result.WriteRune('.')
		}
		result.WriteRune(c)
	}

	return result.String()
}

// HTMLToImage converts HTML content to an image URL using the conversion service
func HTMLToImage(key string, htmlContent string) (string, error) {
	// Create a new resty client
	client := resty.New()

	// Make HTTP request to conversion service
	resp, err := client.R().
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetFormData(map[string]string{
			"order_code":  key,
			"html_string": htmlContent,
		}).
		Post("https://tools.nexpos.io/convert")

	if err != nil {
		return "", fmt.Errorf("error converting HTML to image: %v", err)
	}

	// Return the URL from the response
	return string(resp.Body()), nil
}

// RenderTemplate renders an HTML template with the given data
func RenderTemplate(templatePath string, data interface{}) (string, error) {
	// Create a template with functions
	tmpl, err := template.New(templatePath).Funcs(template.FuncMap{
		"formatCurrency": FormatCurrency,
	}).ParseFiles(templatePath)

	if err != nil {
		return "", fmt.Errorf("error parsing template: %v", err)
	}

	// Execute the template with the data
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("error executing template: %v", err)
	}

	return buf.String(), nil
}

// UploadToStorage uploads a file to Google Cloud Storage
func UploadToStorage(bucket, key string, content io.Reader) (string, error) {
	// Read the content into a buffer
	var buffer bytes.Buffer
	if _, err := io.Copy(&buffer, content); err != nil {
		return "", fmt.Errorf("error reading content: %v", err)
	}

	// Use the SDK's UploadFile utility
	return utils.UploadFile(bucket, key, buffer.Bytes())
}
