package utils

import (
	"bytes"
	"io"
	"net/http"
)

// Request represents an HTTP request
type Request struct {
	Method  string
	URL     string
	Headers map[string]string
	Params  map[string]string
	Body    []byte
}

// DoRequestWithClient sends an HTTP request with the provided HTTP client
func DoRequestWithClient(client *http.Client, req *Request) ([]byte, error) {
	var bodyReader io.Reader
	if req.Body != nil {
		bodyReader = bytes.NewBuffer(req.Body)
	}

	httpReq, err := http.NewRequest(req.Method, req.URL, bodyReader)
	if err != nil {
		return nil, err
	}

	// Add headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// Add query parameters
	if len(req.Params) > 0 {
		q := httpReq.URL.Query()
		for key, value := range req.Params {
			q.Add(key, value)
		}
		httpReq.URL.RawQuery = q.Encode()
	}

	// Set content type if body is present
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// Send request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response
	return io.ReadAll(resp.Body)
}
