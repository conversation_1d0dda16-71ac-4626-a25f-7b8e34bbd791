package localize

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v2"
)

// Localizer handles localization of error messages
type Localizer struct {
	messages map[string]map[string]string
	mutex    sync.RWMutex
}

// NewLocalizer creates a new Localizer instance
func NewLocalizer() (*Localizer, error) {
	l := &Localizer{
		messages: make(map[string]map[string]string),
	}

	// Find the localize.yml file
	// First try the current directory
	err := l.loadFromFile("localize.yml")
	if err != nil {
		// Try the parent directory (for services in subdirectories)
		err = l.loadFromFile("../localize.yml")
		if err != nil {
			return nil, fmt.Errorf("failed to load localize.yml: %v", err)
		}
	}

	return l, nil
}

// loadFromFile loads localization messages from a YAML file
func (l *Localizer) loadFromFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	l.mutex.Lock()
	defer l.mutex.Unlock()

	return yaml.Unmarshal(data, l.messages)
}

// GetMessage returns a localized message for the given error code and language
func (l *Localizer) GetMessage(errorCode, lang string) string {
	l.mutex.RLock()
	defer l.mutex.RUnlock()

	// Default to Vietnamese if language is not specified
	if lang == "" {
		lang = "vi"
	}

	// Check if the language exists
	langMessages, ok := l.messages[lang]
	if !ok {
		return errorCode
	}

	// Check if the error code exists for the language
	message, ok := langMessages[errorCode]
	if !ok {
		return errorCode
	}

	return message
}

// Global instance of Localizer
var globalLocalizer *Localizer
var initOnce sync.Once
var initErr error

// GetLocalizer returns the global Localizer instance
func GetLocalizer() (*Localizer, error) {
	initOnce.Do(func() {
		globalLocalizer, initErr = NewLocalizer()
	})
	return globalLocalizer, initErr
}
