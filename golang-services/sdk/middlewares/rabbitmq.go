package middlewares

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
)

func NewRabbitMQ() *rabbitmq.RabbitClient {
	amqpURI := "********************************************/"

	client, err := rabbitmq.NewRabbitClient(amqpURI)
	if err != nil {
		panic(err)
	}
	return client
}

func RabbitMQMiddleware() gin.HandlerFunc {
	client := NewRabbitMQ()

	return func(c *gin.Context) {
		c.Set("rabbitmq", client)
		c.Next()
	}
}

func GetRabbitMQ(c *gin.Context) *rabbitmq.RabbitClient {
	client, exists := c.Get("rabbitmq")
	if !exists {
		c.AbortWithStatus(http.StatusBadRequest)
		return nil
	}
	return client.(*rabbitmq.RabbitClient)
}
