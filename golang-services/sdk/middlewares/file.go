package middlewares

import (
	"context"
	"fmt"
	"os"

	"cloud.google.com/go/storage"
	"github.com/gin-gonic/gin"
	"google.golang.org/api/option"
)

func GoogleStorageMiddleware() gin.HandlerFunc {
	privateKey := os.Getenv("GOOGLE_PRIVATE_KEY")
	if privateKey == "" {
		panic("GOOGLE_PRIVATE_KEY is not set")
	}
	// privateKey = strings.ReplaceAll(privateKey, "\n", "\\n")
	client, err := storage.NewClient(context.Background(), option.WithCredentialsJSON([]byte(privateKey)))
	if err != nil {
		panic(fmt.Sprintf("Failed to create client: %v", err))
	}

	return func(c *gin.Context) {
		c.Set("storage", client)
		c.Next()
	}
}

func GetStorageClient(c *gin.Context) *storage.Client {
	client, exists := c.Get("storage")
	if !exists {
		c.AbortWithStatus(500)
		return nil
	}
	return client.(*storage.Client)
}
