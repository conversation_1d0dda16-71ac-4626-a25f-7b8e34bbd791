package middlewares

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestErrorLocalizeMiddleware(t *testing.T) {
	// Set up Gin in test mode
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// Add the middleware
	r.Use(ErrorLocalizeMiddleware())

	// Add a test route that returns an error
	r.GET("/test-error", func(c *gin.Context) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "user_is_inactive",
			"success": false,
		})
	})

	// Create a test request
	req, _ := http.NewRequest(http.MethodGet, "/test-error", nil)
	req.Header.Set("Accept-Language", "vi")

	// Create a response recorder
	w := httptest.NewRecorder()

	// Perform the request
	r.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Parse the response body
	var response struct {
		ErrorCode    string `json:"error_code"`
		ErrorMessage string `json:"error_message"`
		Success      bool   `json:"success"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify the response
	assert.Equal(t, "user_is_inactive", response.ErrorCode)
	assert.Equal(t, "Người dùng chưa được kích hoạt.", response.ErrorMessage)
	assert.False(t, response.Success)

	// Ensure the original error field is not present
	assert.NotContains(t, string(w.Body.Bytes()), "\"error\":\"user_is_inactive\"")

	// Ensure the response only contains the expected fields
	assert.NotContains(t, string(w.Body.Bytes()), "\"error\"")
}

func TestErrorLocalizeMiddlewareWithNonErrorResponse(t *testing.T) {
	// Set up Gin in test mode
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// Add the middleware
	r.Use(ErrorLocalizeMiddleware())

	// Add a test route that returns a success response
	r.GET("/test-success", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    "test data",
		})
	})

	// Create a test request
	req, _ := http.NewRequest(http.MethodGet, "/test-success", nil)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Perform the request
	r.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response body
	var response struct {
		Success bool   `json:"success"`
		Data    string `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify the response is unchanged and properly passed through
	assert.True(t, response.Success)
	assert.Equal(t, "test data", response.Data)

	// Ensure the response body is not empty
	assert.NotEmpty(t, w.Body.String())
}
