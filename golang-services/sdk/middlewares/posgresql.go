package middlewares

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func NewDB() *gorm.DB {
	dsn := os.Getenv("POSTGRESQL_URI")
	println(dsn)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		panic("failed to connect to database")
	}
	db = db.Debug()
	sqlDB, err := db.DB()
	if err != nil {
		panic("failed to get database connection: " + err.<PERSON><PERSON>r())
	}
	sqlDB.SetMaxOpenConns(25)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(5 * time.Minute)
	return db
}
func GormMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("db", NewDB())
		c.Next()
	}
}

func GetDB(c *gin.Context) *gorm.DB {
	db, exists := c.Get("db")
	if !exists {
		c.AbortWithStatus(http.StatusBadRequest)
		return nil
	}
	return db.(*gorm.DB)
}
