package middlewares

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/localize"
)

// ErrorResponse represents a simple error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Success bool   `json:"success"`
}

// LocalizedErrorResponse represents a localized error response
type LocalizedErrorResponse struct {
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Success      bool   `json:"success"`
}

// responseBodyWriter is a custom response writer that captures the response body
type responseBodyWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

// Write captures the response body without writing to the original writer
func (r *responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	// Don't write to the original writer yet, just return the length of the data
	return len(b), nil
}

// WriteString captures the response body as a string without writing to the original writer
func (r *responseBodyWriter) WriteString(s string) (int, error) {
	r.body.WriteString(s)
	// Don't write to the original writer yet, just return the length of the string
	return len(s), nil
}

// WriteHeader captures the status code
func (r *responseBodyWriter) WriteHeader(statusCode int) {
	r.statusCode = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

// Localizer interface defines the methods required for localization
type Localizer interface {
	GetMessage(errorCode, lang string) string
}

// ErrorLocalizeMiddlewareWithLocalizer creates a middleware with a specific localizer
func ErrorLocalizeMiddlewareWithLocalizer(localizer Localizer) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a custom response writer to capture the response
		responseWriter := &responseBodyWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
			statusCode:     http.StatusOK, // Default to 200 OK
		}
		c.Writer = responseWriter

		// Process the request
		c.Next()

		// Try to parse the response as JSON
		var errorResp ErrorResponse
		if err := json.Unmarshal(responseWriter.body.Bytes(), &errorResp); err != nil {
			// If not valid JSON, preserve original response
			log.Printf("Failed to parse response as JSON: %v", err)
			return
		}

		// Check if there's an error to localize
		if errorResp.Error == "" {
			_, err := responseWriter.ResponseWriter.Write(responseWriter.body.Bytes())
			if err != nil {
				log.Printf("Failed to write success response: %v", err)
			}
			return
		}

		// Determine the language
		lang := strings.ToLower(c.GetHeader("x-nexpos-language"))
		if lang == "" {
			lang = "vi" // Default fallback
		}

		// Get localized message
		localizedMessage := localizer.GetMessage(errorResp.Error, lang)

		// Create localized response
		localizedResp := LocalizedErrorResponse{
			ErrorCode:    errorResp.Error,
			ErrorMessage: localizedMessage,
			Success:      errorResp.Success,
		}

		// Marshal the new response
		jsonData, err := json.Marshal(localizedResp)
		if err != nil {
			log.Printf("Failed to marshal localized error response: %v", err)
			return
		}

		// Now write the localized response to the original response writer
		// First, reset the headers
		for k := range responseWriter.Header() {
			responseWriter.Header().Del(k)
		}

		// Set content type
		responseWriter.Header().Set("Content-Type", "application/json")

		// Preserve the original status code
		responseWriter.ResponseWriter.WriteHeader(responseWriter.statusCode)

		// Write the new response directly to the original response writer
		// This will be the only response sent to the client
		_, err = responseWriter.ResponseWriter.Write(jsonData)
		if err != nil {
			log.Printf("Failed to write localized response: %v", err)
		}
	}
}

// ErrorLocalizeMiddleware intercepts error responses and replaces them with localized error messages
func ErrorLocalizeMiddleware() gin.HandlerFunc {
	// Get the localizer
	localizer, err := localize.GetLocalizer()
	if err != nil {
		log.Printf("Failed to initialize localizer: %v", err)
		return func(c *gin.Context) {
			c.Next()
		}
	}

	return ErrorLocalizeMiddlewareWithLocalizer(localizer)
}
