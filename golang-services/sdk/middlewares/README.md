# Middleware Components

This directory contains middleware components that can be used across all services.

## Error Localization Middleware

The Error Localization Middleware intercepts error responses and replaces them with localized error messages based on the `localize.yml` file.

### Usage

```go
import (
    "github.com/gin-gonic/gin"
    "github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
)

func main() {
    r := gin.New()

    // Add the middleware
    r.Use(middlewares.ErrorLocalizeMiddleware())

    // ... other middleware and routes

    r.Run(":3000")
}
```

### How It Works

1. The middleware intercepts all responses
2. If the response is an error (status code >= 400) and contains a JSON object with an "error" field
3. It looks up the error code in the localization file
4. It replaces the response with a new JSON object containing:
   - `error_code`: The original error code
   - `error_message`: The localized error message

### Example

Original error response:
```json
{
    "error": "brand_not_found",
    "success": false
}
```

Transformed response:
```json
{
    "error": "brand_not_found",
    "error_code": "brand_not_found",
    "error_message": "<PERSON>hông tìm thấy thương hiệu.",
    "success": false
}
```

Note: The middleware preserves the original error field and all other fields in the response, while adding the localized error message.

### Localization File

The middleware uses the `localize.yml` file located in the project root. This file contains error messages in different languages.

Example structure:
```yaml
vi:
  user_is_inactive: Người dùng chưa được kích hoạt.
  user_not_found: Người dùng không tìm thấy.
  # ... other error codes
```

### Language Selection

The middleware uses the `Accept-Language` header to determine which language to use. If the header is not present or the language is not supported, it defaults to Vietnamese (`vi`).
