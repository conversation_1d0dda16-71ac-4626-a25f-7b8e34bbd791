package middlewares

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func MyCors() gin.HandlerFunc {
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{
		"http://localhost:3000",
		"https://dev.nexpos.io", "https://nexpos.io",
		"https://portal-dev.nexpos.io", "https://portal.nexpos.io",
	}
	corsConfig.AllowCredentials = true
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "x-nexpos-app", " x-nexpos-language", "x-access-token"}

	return cors.New(corsConfig)
}
