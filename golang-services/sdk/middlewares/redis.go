package middlewares

import (
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	redisV8 "github.com/go-redis/redis/v8"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
)

func RedisMiddleware() gin.HandlerFunc {
	uri := strings.ReplaceAll(os.Getenv("REDIS_URI"), "redis://", "")
	pass := os.Getenv("REDIS_PASSWORD")
	redis.InitRedis(uri, pass)

	return func(c *gin.Context) {
		c.Next()
	}
}

// GetRedis gets the Redis client from the context
func GetRedis(c *gin.Context) *redisV8.Client {
	return c.MustGet("redis").(*redisV8.Client)
}
