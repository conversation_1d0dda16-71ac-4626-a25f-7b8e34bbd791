package main

import (
	"encoding/json"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"runtime/debug"

	"github.com/gorilla/mux"
	"github.com/samber/lo"
	"github.com/urfave/cli"
)

var prefixMap = lo.If(os.Getenv("NODE_ENV") == "prod", map[string]string{
	"/v1/brand-service":    "http://10.120.4.233",
	"/v1/order-service":    "http://10.120.1.103",
	"/v1/merchant-service": "http://10.120.6.35",
	"/v1/user-service":     "http://10.120.15.190",
}).Else(map[string]string{
	"/v1/brand-service":    "http://10.120.3.8",
	"/v1/order-service":    "http://10.120.14.243",
	"/v1/merchant-service": "http://10.120.9.7",
	"/v1/user-service":     "http://10.120.13.205",
})

func main() {
	app := cli.NewApp()
	app.Name = "proxifer-service"
	app.Usage = "Proxifer service"
	app.Version = "1.0.0"
	app.Flags = []cli.Flag{
		cli.StringFlag{Name: "port", Usage: "Port to listen on", EnvVar: "PORT", Value: "3000"},
	}
	app.Action = func(c *cli.Context) error {
		router := mux.NewRouter()

		// Apply middlewares to the router
		router.Use(errorRecoveryMiddleware)
		router.Use(corsMiddleware)

		for prefix, target := range prefixMap {
			handler := handlerWithPrefix(target, prefix)
			router.PathPrefix(prefix).HandlerFunc(handler)
		}
		router.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			http.Error(w, "Not found", http.StatusNotFound)
			log.Printf("No matching prefix for %s", r.URL.Path)
		})

		port := ":" + c.String("port")
		log.Printf("Starting balancer service on %s", port)
		return http.ListenAndServe(port, router)
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}

// corsMiddleware adds CORS headers with support for credentials
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get the origin from the request
		origin := r.Header.Get("Origin")

		// Set CORS headers
		if origin != "" {
			w.Header().Set("Access-Control-Allow-Origin", origin)
		} else {
			w.Header().Set("Access-Control-Allow-Origin", "*")
		}
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, x-nexpos-language, x-access-token, isauthorized")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		// Handle preflight OPTIONS request
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		// Pass to next handler
		next.ServeHTTP(w, r)
	})
}

func createProxy(target string) *httputil.ReverseProxy {
	targetURL, err := url.Parse(target)
	if err != nil {
		log.Fatalf("Failed to parse target URL %s: %v", target, err)
	}
	proxy := httputil.NewSingleHostReverseProxy(targetURL)
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		req.Host = targetURL.Host
	}

	proxy.ModifyResponse = func(resp *http.Response) error {
		resp.Header.Del("Access-Control-Allow-Origin")
		resp.Header.Del("Access-Control-Allow-Methods")
		resp.Header.Del("Access-Control-Allow-Headers")
		resp.Header.Del("Access-Control-Allow-Credentials")
		return nil
	}

	// Add error handler to properly handle backend errors
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("Proxy error: %v", err)

		// Set response headers
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)

		// Create error response
		errorResponse := map[string]interface{}{
			"error_code":    "service_unavailable",
			"error_message": "The service is currently unavailable",
			"success":       false,
		}

		// Write the error response
		if jsonData, err := json.Marshal(errorResponse); err == nil {
			w.Write(jsonData)
		} else {
			// If JSON marshaling fails, write a simple error message
			w.Write([]byte(`{"error_code":"service_unavailable","error_message":"The service is currently unavailable","success":false}`))
		}
	}

	return proxy
}

func handlerWithPrefix(target string, prefix string) http.HandlerFunc {
	proxy := createProxy(target)
	return func(w http.ResponseWriter, r *http.Request) {
		// No need to modify the path
		proxy.ServeHTTP(w, r)
	}
}

// errorRecoveryMiddleware catches panics and returns a 500 error with JSON response
func errorRecoveryMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				// Log the error and stack trace
				log.Printf("PANIC: %v\n%s", err, debug.Stack())

				// Set the response headers
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusBadRequest)

				// Create error response
				errorResponse := map[string]interface{}{
					"error_code":    "internal_server_error",
					"error_message": "An unexpected error occurred",
					"success":       false,
				}

				// Write the error response
				if jsonData, err := json.Marshal(errorResponse); err == nil {
					w.Write(jsonData)
				} else {
					// If JSON marshaling fails, write a simple error message
					w.Write([]byte(`{"error_code":"internal_server_error","error_message":"An unexpected error occurred","success":false}`))
				}
			}
		}()

		next.ServeHTTP(w, r)
	})
}
