package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

func GetRoleList(c *gin.Context) {
	// Get predefined roles directly from models package instead of querying database
	roles := models.GetAllRoles()

	// Return the predefined roles with success response
	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       roles,
		"totalPages": 1,
	})
}

// func CreateRole(c *gin.Context) {
// 	db := middlewares.GetDB(c)

// 	var request struct {
// 		Name        string   `json:"name" binding:"required"`
// 		Permissions []string `json:"permissions" binding:"required"`
// 		Selectors   []string `json:"selectors"`
// 	}

// 	if err := c.ShouldBindJ<PERSON>N(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Initialize empty slice if selectors is nil
// 	if request.Selectors == nil {
// 		request.Selectors = make([]string, 0)
// 	}

// 	role := models.Role{
// 		Name:        request.Name,
// 		Permissions: request.Permissions,
// 		Selectors:   request.Selectors,
// 	}

// 	if err := db.Create(&role).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    role,
// 	})
// }

// func UpdateRole(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	roleID := c.Param("role_id")

// 	var request struct {
// 		Name        string   `json:"name" binding:"required"`
// 		Permissions []string `json:"permissions" binding:"required"`
// 		Selectors   []string `json:"selectors"`
// 	}

// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Initialize empty slice if selectors is nil
// 	if request.Selectors == nil {
// 		request.Selectors = make([]string, 0)
// 	}

// 	// First find the role
// 	var role models.Role
// 	if err := db.First(&role, roleID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "Role not found",
// 		})
// 		return
// 	}

// 	// Update role fields
// 	role.Name = request.Name
// 	role.Permissions = request.Permissions
// 	role.Selectors = request.Selectors

// 	// Save updates
// 	if err := db.Save(&role).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    role,
// 	})
// }

// func DeleteRole(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	roleID := c.Param("role_id")

// 	// First check if role exists
// 	var role models.Role
// 	if err := db.First(&role, roleID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "Role not found",
// 		})
// 		return
// 	}

// 	// Delete the role
// 	if err := db.Delete(&role).Error; err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }
