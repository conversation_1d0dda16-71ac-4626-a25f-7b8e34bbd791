package router

import (
	"fmt"
	"net/http"

	"os"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/nexdorpay"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

// NexDorPayClient is the client for interacting with NexDorPay service
var NexDorPayClient *nexdorpay.Client

func init() {
	// Get the NexDorPay service URL from environment or use a default
	nexdorpayURL := os.Getenv("NEXDORPAY_PAYMENT_URL")
	if nexdorpayURL == "" {
		panic("NEXDORPAY_PAYMENT_URL environment variable is not set")
	}

	NexDorPayClient = nexdorpay.New(nexdorpayURL)
}

// SubscriptionPlanRequest represents the common request for subscription plan operations
type SubscriptionPlanRequest struct {
	PlanID models.PlanID             `json:"plan_id" binding:"required"`
	Period models.SubscriptionPeriod `json:"period" binding:"required"` // Monthly or Yearly
}

// SubscriptionPaymentRequest represents the request body for payment callback
type SubscriptionPaymentRequest struct {
	SubscriptionRequestID string `json:"subscription_request_id" binding:"required"`
	TransactionID         string `json:"transaction_id" binding:"required"`
	PaymentMethod         string `json:"payment_method" binding:"required"`
}

// CancelSubscriptionRequestBody represents the request body for cancelling a subscription request
type CancelSubscriptionRequestBody struct {
	RequestID string `json:"request_id" binding:"required"`
}

// GetCurrentSubscription godoc
// @Summary Get current user subscription
// @Description Get the current active subscription for the authenticated user
// @Tags subscriptions
// @Accept json
// @Produce json
// @Success 200 {object} map[string.any "Returns current subscription or null if none exists"
// @Failure 401 {object} map[string.any "User not authenticated"
// @Failure 500 {object} map[string.any "Internal server error"
// @Router /subscription/current [get]
func GetCurrentSubscription(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Get user from the authentication middleware
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userID := user.ID

	// Prepare response data structure
	responseData := gin.H{}

	// Check for any pending subscription requests
	var pendingRequest models.SubscriptionRequest
	pendingResult := db.Where("user_id = ? AND status = ?", userID, models.Pending).First(&pendingRequest)

	// Add pending request to response data if one exists
	if pendingResult.Error == nil {
		responseData["pending_request"] = pendingRequest
	} else {
		responseData["pending_request"] = nil
	}

	// Check if user has an active subscription
	var userSubscription models.UserSubscription
	result := db.Where("user_id = ? AND status = ?", userID, models.Active).First(&userSubscription)

	if result.Error != nil {
		// If no subscription found, set current_plan to null
		responseData["current_plan"] = nil

		// Return the response with success status at top level
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    responseData,
		})
		return
	}

	// Get plan details for the active subscription
	plan, exists := models.GetPlanByID(userSubscription.SubscriptionPlanID)
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Referenced subscription plan not found",
		})
		return
	}

	// Create current plan details
	responseData["current_plan"] = gin.H{
		"subscription_id": userSubscription.ID,
		"plan_id":         userSubscription.SubscriptionPlanID,
		"plan_name":       plan.Name,
		"status":          userSubscription.Status,
		"start_date":      userSubscription.StartDate,
		"end_date":        userSubscription.EndDate,
		"payment_method":  userSubscription.PaymentMethod,
		"limits":          userSubscription.Limits,
		"is_trial":        plan.Period == models.Trial,
		"period":          userSubscription.Period,
	}

	// Return the restructured response with success at top level
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    responseData,
	})
}

// UpdateSubscriptionPlan godoc
// @Summary Update subscription plan
// @Description Updates the user's subscription to a different plan with prorated pricing
// @Tags subscriptions
// @Accept json
// @Produce json
// @Param request body SubscriptionPlanRequest true "Subscription update request"
// @Success 200 {object} map[string.any "Returns update information"
// @Failure 400 {object} map[string.any "Bad request"
// @Failure 401 {object} map[string.any "User not authenticated"
// @Failure 404 {object} map[string.any "Plan not found or no active subscription"
// @Failure 500 {object} map[string.any "Internal server error"
// @Router /subscription/update [post]
func UpdateSubscriptionPlan(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Get user from authentication middleware
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userID := user.ID

	// Parse request body
	var req SubscriptionPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if the requested plan is the trial plan and enforce one-time trial usage
	if req.PlanID == models.TrialPlanID {
		// Check if the user has ever had a trial subscription in the past
		var trialCount int64
		if err := db.Model(&models.UserSubscription{}).
			Where("user_id = ? AND subscription_plan_id = ?", userID, models.TrialPlanID).
			Count(&trialCount).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to check trial usage history: " + err.Error(),
			})
			return
		}

		// If user has already used a trial, prevent them from using it again
		if trialCount > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Free trial can only be used once per account",
			})
			return
		}
	}

	// Check if the user already has a pending subscription request
	var pendingRequest models.SubscriptionRequest
	pendingResult := db.Where("user_id = ? AND status = ?", userID, models.Pending).First(&pendingRequest)

	// If a pending request exists, cancel it automatically
	if pendingResult.Error == nil {
		// Found a pending request, cancel it first
		pendingRequest.Status = models.Canceled
		pendingRequest.PaymentStatus = models.PaymentCancelled

		if err := db.Save(&pendingRequest).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to cancel existing subscription request: " + err.Error(),
			})
			return
		}
	}

	// Validate the requested period
	if req.Period != models.Monthly && req.Period != models.Yearly && req.Period != models.Trial {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid subscription period. Must be 'monthly', 'yearly', or 'trial'",
		})
		return
	}

	// Find user's current active subscription
	var currentSubscription models.UserSubscription
	if err := db.Where("user_id = ? AND status = ?", userID, models.Active).First(&currentSubscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "No active subscription found",
		})
		return
	}

	// Prevent updating to the same plan with the same period
	if currentSubscription.SubscriptionPlanID == req.PlanID && currentSubscription.Period == req.Period {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Cannot update to the same plan with the same period",
		})
		return
	}

	// Verify the requested plan exists
	newPlan, exists := models.GetPlanByID(req.PlanID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Subscription plan not found",
		})
		return
	}

	// Get current plan details
	currentPlan, exists := models.GetPlanByID(currentSubscription.SubscriptionPlanID)
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Current subscription plan details not found",
		})
		return
	}

	// Check if the requested plan is a downgrade from the current plan
	if isPlanDowngrade(currentSubscription.SubscriptionPlanID, req.PlanID) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Cannot downgrade to a lower tier plan",
		})
		return
	}

	// If upgrading to a trial plan, skip the payment process
	if req.PlanID == models.TrialPlanID {
		// Calculate trial end date based on the trial period defined in models
		trialEndDate := models.CalculateEndDateFromNow(models.Trial)

		// Mark the current subscription as postponed to be resumed after trial
		currentSubscription.Status = models.Postponed
		if err := db.Save(&currentSubscription).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   fmt.Sprintf("Failed to update existing subscription: %v", err),
			})
			return
		}

		// Create a new active trial subscription with appropriate details
		newSubscription := models.UserSubscription{
			ID:                      models.ObjectID(utils.GenObjectID()),
			UserID:                  userID,
			SubscriptionPlanID:      models.TrialPlanID,
			Status:                  models.Active,
			StartDate:               time.Now(),
			EndDate:                 &trialEndDate,
			Period:                  models.Trial,
			PaymentMethod:           "trial",                 // No payment required for trial
			PostponedSubscriptionID: &currentSubscription.ID, // Link to previous subscription for continuity
		}

		// Retrieve trial plan details and set appropriate limits
		trialPlan, exists := models.GetPlanByID(models.TrialPlanID)
		if !exists {
			// Reactivate the current subscription if trial plan not found
			currentSubscription.Status = models.Active
			db.Save(&currentSubscription)

			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Trial plan not found in system",
			})
			return
		}

		// Set trial-specific limits from the plan using the proper JSONField type
		newSubscription.Limits = &models.JSONField[models.FeatureLimits]{
			Data: trialPlan.Limits,
		}

		// Persist the new trial subscription to database
		if err := db.Create(&newSubscription).Error; err != nil {
			// Reactivate the current subscription if creating the trial fails
			currentSubscription.Status = models.Active
			if saveErr := db.Save(&currentSubscription).Error; saveErr != nil {
				// Log this error but continue with the main error response
				fmt.Printf("Error restoring subscription after trial creation failure: %v\n", saveErr)
			}

			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   fmt.Sprintf("Failed to create trial subscription: %v", err),
			})
			return
		}

		// Return success response with trial details
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Trial subscription activated successfully",
			"data": gin.H{
				"subscription_id": newSubscription.ID,
				"plan_id":         newSubscription.SubscriptionPlanID,
				"plan_name":       trialPlan.Name,
				"status":          newSubscription.Status,
				"start_date":      newSubscription.StartDate,
				"end_date":        newSubscription.EndDate,
				"period":          newSubscription.Period,
				"is_trial":        true,
			},
		})
		return
	}

	// Calculate days remaining in current subscription
	daysRemaining := 0
	if currentSubscription.EndDate != nil {
		// Calculate days remaining by getting the duration between now and end date
		duration := currentSubscription.EndDate.Sub(time.Now())
		// Convert duration to days and ensure it's not negative
		if duration > 0 {
			daysRemaining = int(duration.Hours() / 24)
		}
	}

	// Calculate payment expired time (10 minutes from now)
	paymentExpiredTime := time.Now().Add(10 * time.Minute)

	// For paid upgrades, we'll implement the new behavior:
	// 1. User pays full price for new upgrade
	// 2. Existing subscription is postponed (not prorated)
	// 3. New subscription becomes active for requested period
	// 4. When new subscription expires, old one becomes active again

	// Calculate end date for the new subscription based on the requested period
	newSubscriptionEndDate := models.CalculateEndDateFromNow(req.Period)

	// Generate a unique request ID
	subscriptionRequestID := utils.GenObjectID()

	// Create a subscription request with pending payment status for the full price
	subscriptionRequest := models.SubscriptionRequest{
		ID:                 models.ObjectID(subscriptionRequestID),
		RequestType:        models.PlanChange,
		UserID:             string(userID),
		UserSubscriptionID: &currentSubscription.ID, // Link to current subscription
		PlanID:             req.PlanID,
		Period:             req.Period,
		Status:             models.Pending,
		PaymentStatus:      models.PaymentPending,
		Amount:             newPlan.GetPriceForPeriod(req.Period), // Use full price
		StartTime:          time.Now(),
		ExpiredTime:        &newSubscriptionEndDate, // Use calculated end date
		PaymentExpiredTime: &paymentExpiredTime,
	}

	if err := db.Create(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create subscription request: " + err.Error(),
		})
		return
	}

	// Create a payment transaction with NexDorPay
	// The callback URL will be the URL that NexDorPay will redirect to after payment
	callbackBaseURL := os.Getenv("API_BASE")
	if callbackBaseURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "API base URL not set",
		})
		return
	}

	// Generate a unique transaction ID for NexDorPay
	transactionID := fmt.Sprintf("%s", utils.GenObjectID()[0:10])

	// Prepare the transaction request
	paymentReq := &nexdorpay.TransactionRequest{
		TransactionID: transactionID,
		Amount:        int(subscriptionRequest.Amount), // Use the full price
		OrderID:       string(subscriptionRequest.ID),
		ServerCallback: fmt.Sprintf("%s/v1/user-service/subscription/payment/webhook",
			callbackBaseURL),
	}

	// Create the payment transaction with NexDorPay
	paymentResp, err := NexDorPayClient.CreateTransaction(paymentReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create payment transaction: " + err.Error(),
		})
		return
	}

	// Update the subscription request with payment information
	subscriptionRequest.TransactionID = &transactionID
	if err := db.Save(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to update subscription request: " + err.Error(),
		})
		return
	}

	// Return payment information for the plan change
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription plan change initiated, payment required",
		"data": gin.H{
			"subscription_id":      currentSubscription.ID,
			"current_plan":         currentPlan.Name,
			"new_plan":             newPlan.Name,
			"status":               currentSubscription.Status,
			"days_remaining":       daysRemaining,
			"subscription_request": subscriptionRequest,
			"payment_info": gin.H{
				"transaction_id":       transactionID,
				"amount":               subscriptionRequest.Amount,
				"qr_code":              paymentResp.Data.QRCode,
				"payment_url":          paymentResp.Data.PayURL,
				"start_time":           subscriptionRequest.StartTime.Format("2006-01-02 15:04:05"),
				"expired_time":         newSubscriptionEndDate.Format("2006-01-02 15:04:05"),
				"payment_expired_time": paymentExpiredTime.Format("2006-01-02 15:04:05"),
			},
		},
	})
}

// CancelSubscriptionRequest godoc
// @Summary Cancel a pending subscription request
// @Description Cancels a pending subscription change or upgrade request
// @Tags subscriptions
// @Accept json
// @Produce json
// @Param request body CancelSubscriptionRequestBody true "Subscription request ID to cancel"
// @Success 200 {object} map[string.any "Request cancelled successfully"
// @Failure 400 {object} map[string.any "Bad request"
// @Failure 401 {object} map[string.any "User not authenticated"
// @Failure 403 {object} map[string.any "Not authorized to cancel this request"
// @Failure 404 {object} map[string.any "Subscription request not found"
// @Failure 409 {object} map[string.any "Request cannot be cancelled (not in pending state)"
// @Failure 500 {object} map[string.any "Internal server error"
// @Router /subscription/cancel [post]
func CancelSubscriptionRequest(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Get user from the authentication middleware
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userID := user.ID

	// Parse request body
	var req CancelSubscriptionRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the subscription request
	var subscriptionRequest models.SubscriptionRequest
	if err := db.Where("id = ?", req.RequestID).First(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Subscription request not found",
		})
		return
	}

	// Check if the request belongs to the authenticated user
	if subscriptionRequest.UserID != string(userID) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "Not authorized to cancel this subscription request",
		})
		return
	}

	// Validate request state - only pending requests can be cancelled
	println(subscriptionRequest.Status, subscriptionRequest.PaymentStatus)
	if subscriptionRequest.Status != models.Pending ||
		subscriptionRequest.PaymentStatus != models.PaymentPending {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"error":   "Only pending subscription requests can be cancelled",
		})
		return
	}

	// Update subscription request status to cancelled
	subscriptionRequest.Status = models.Canceled
	subscriptionRequest.PaymentStatus = models.PaymentCancelled

	if err := db.Save(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to cancel subscription request: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Subscription request cancelled successfully",
		"data": gin.H{
			"request_id":     subscriptionRequest.ID,
			"status":         subscriptionRequest.Status,
			"payment_status": subscriptionRequest.PaymentStatus,
		},
	})
}

// RepaySubscriptionRequest godoc
// @Summary Reinitialize payment for a subscription request
// @Description Creates a new payment transaction for a subscription request in pending payment state
// @Tags subscriptions
// @Accept json
// @Produce json
// @Param request body CancelSubscriptionRequestBody true "Subscription request ID to repay"
// @Success 200 {object} map[string.any "Returns new payment information"
// @Failure 400 {object} map[string.any "Bad request"
// @Failure 401 {object} map[string.any "User not authenticated"
// @Failure 403 {object} map[string.any "Not authorized to repay this request"
// @Failure 404 {object} map[string.any "Subscription request not found"
// @Failure 409 {object} map[string.any "Request cannot be repaid (not in pending payment state)"
// @Failure 500 {object} map[string.any "Internal server error"
// @Router /subscription/repay [post]
func RepaySubscriptionRequest(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Get user from the authentication middleware
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userID := user.ID

	// Parse request body - we can reuse CancelSubscriptionRequestBody as it has the same structure
	var req CancelSubscriptionRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the subscription request
	var subscriptionRequest models.SubscriptionRequest
	if err := db.Where("id = ?", req.RequestID).First(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Subscription request not found",
		})
		return
	}

	// Check if the request belongs to the authenticated user
	if subscriptionRequest.UserID != string(userID) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "Not authorized to repay this subscription request",
		})
		return
	}

	// Validate request state - only requests with pending payment status can be repaid
	// Modified to allow repayment regardless of expiration status
	if subscriptionRequest.Status != models.Pending ||
		subscriptionRequest.PaymentStatus != models.PaymentPending {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"error":   "This subscription request cannot be repaid. It must be in pending payment state.",
		})
		return
	}

	// Calculate new payment expired time (10 minutes from now)
	newPaymentExpiredTime := time.Now().Add(10 * time.Minute)
	subscriptionRequest.PaymentExpiredTime = &newPaymentExpiredTime

	// Create a new payment transaction with NexDorPay
	callbackBaseURL := os.Getenv("API_BASE")

	// Generate a unique transaction ID for NexDorPay
	transactionID := fmt.Sprintf("%s", utils.GenObjectID()[0:10])

	// Prepare the transaction request
	paymentReq := &nexdorpay.TransactionRequest{
		TransactionID: transactionID,
		Amount:        int(subscriptionRequest.Amount),
		OrderID:       string(subscriptionRequest.ID),
		// Client callback for user redirects
		ClientCallback: fmt.Sprintf("%s/v1/user-service/subscription/payment/callback?request_id=%s",
			callbackBaseURL, subscriptionRequest.ID),
		// Server webhook for payment notifications
		ServerCallback: fmt.Sprintf("%s/v1/user-service/subscription/payment/webhook",
			callbackBaseURL),
	}

	// Create the payment transaction with NexDorPay
	paymentResp, err := NexDorPayClient.CreateTransaction(paymentReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create payment transaction: " + err.Error(),
		})
		return
	}

	// Update the subscription request with new payment information
	subscriptionRequest.TransactionID = &transactionID
	if err := db.Save(&subscriptionRequest).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to update subscription request: " + err.Error(),
		})
		return
	}

	// Return payment information to client
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Payment reinitialized successfully",
		"data": gin.H{
			"subscription_request": subscriptionRequest,
			"payment_info": gin.H{
				"transaction_id":       transactionID,
				"amount":               subscriptionRequest.Amount,
				"qr_code":              paymentResp.Data.QRCode,
				"payment_url":          paymentResp.Data.PayURL,
				"start_time":           subscriptionRequest.StartTime.Format("2006-01-02 15:04:05"),
				"expired_time":         subscriptionRequest.ExpiredTime.Format("2006-01-02 15:04:05"),
				"payment_expired_time": newPaymentExpiredTime.Format("2006-01-02 15:04:05"),
			},
		},
	})
}

// isPlanDowngrade checks if the new plan ID represents a lower tier than the current plan ID
func isPlanDowngrade(currentPlanID, newPlanID models.PlanID) bool {
	// Define plan tier hierarchy from lowest to highest
	planTiers := map[models.PlanID]int{
		models.FreePlanID:      0,
		models.TrialPlanID:     1,
		models.ECommercePlanID: 2,
		models.BasicPlanID:     3,
		models.StandardPlanID:  4,
	}

	// Get tier values for both plans
	currentTier, currentExists := planTiers[currentPlanID]
	newTier, newExists := planTiers[newPlanID]

	// If either plan doesn't exist in our tier mapping, return false to be safe
	if !currentExists || !newExists {
		return false
	}

	// If the new tier is lower than the current tier, it's a downgrade
	return newTier < currentTier
}
