package router

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/gmap"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

// GetUserAddressList retrieves all addresses for the current user
func GetUserAddressList(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	// Get current user
	user := middlewares.GetUser(c)

	// Find all addresses for the user
	var addresses []models.UserAddress
	if err := db.Where("user_id = ?", user.ID).Find(&addresses).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return addresses
	c.J<PERSON>N(http.StatusOK, gin.H{
		"success": true,
		"data":    addresses,
	})
}

// CreateUserAddress creates a new address for the current user
func CreateUserAddress(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)

	// Define request structure
	var request struct {
		Address   string `json:"address" binding:"required"`
		Phone     string `json:"phone" binding:"required"`
		Name      string `json:"name" binding:"required"`
		IsDefault bool   `json:"is_default"`
		Note      string `json:"note"`
		UserID    string `json:"user_id"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Determine actual user ID (allow specifying a different user_id if provided)
	actualUserID := user.ID
	if request.UserID != "" {
		actualUserID = request.UserID
	}

	// Create address object
	addressObj := models.AddressObj{
		FormattedAddress: request.Address,
	}

	// Try to get more detailed address information from Google Maps
	if addresses, err := gmap.GetSuggestionAddresses(request.Address); err == nil && len(addresses) > 0 {
		addressObj = addresses[0]
	}

	// Create new address
	newAddress := models.UserAddress{
		ID:         utils.GenObjectID(),
		UserID:     actualUserID,
		Address:    request.Address,
		AddressObj: models.JSONField[models.AddressObj]{Data: addressObj},
		Phone:      request.Phone,
		Name:       request.Name,
		IsDefault:  request.IsDefault,
		Note:       request.Note,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// Begin transaction
	tx := db.Begin()

	// Save address
	if err := tx.Create(&newAddress).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If this is the default address, update other addresses
	if request.IsDefault {
		if err := tx.Model(&models.UserAddress{}).
			Where("user_id = ? AND id != ?", actualUserID, newAddress.ID).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Update user name if not set
	var currentUser models.User
	if err := tx.First(&currentUser, "id = ?", actualUserID).Error; err == nil {
		if currentUser.Name == "" {
			if err := tx.Model(&models.User{}).
				Where("id = ?", actualUserID).
				Update("name", request.Name).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   err.Error(),
				})
				return
			}
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    newAddress,
	})
}

// UpdateUserAddress updates an existing address
func UpdateUserAddress(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)

	// Get address ID from URL
	addressID := c.Param("address_id")

	// Define request structure
	var request struct {
		Address   string `json:"address" binding:"required"`
		Phone     string `json:"phone" binding:"required"`
		Name      string `json:"name" binding:"required"`
		IsDefault bool   `json:"is_default"`
		Note      string `json:"note"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find address
	var address models.UserAddress
	if err := db.First(&address, "id = ?", addressID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "address_not_found",
		})
		return
	}

	// Create address object
	addressObj := models.AddressObj{
		FormattedAddress: request.Address,
	}

	// Try to get more detailed address information from Google Maps
	if addresses, err := gmap.GetSuggestionAddresses(request.Address); err == nil && len(addresses) > 0 {
		addressObj = addresses[0]
	}

	// Begin transaction
	tx := db.Begin()

	// Update address
	if err := tx.Model(&address).Updates(map[string]interface{}{
		"address":     request.Address,
		"address_obj": models.JSONField[models.AddressObj]{Data: addressObj},
		"phone":       request.Phone,
		"name":        request.Name,
		"is_default":  request.IsDefault,
		"note":        request.Note,
		"updated_at":  time.Now(),
	}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If this is the default address, update other addresses
	if request.IsDefault {
		if err := tx.Model(&models.UserAddress{}).
			Where("user_id = ? AND id != ?", user.ID, addressID).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// DeleteUserAddress deletes an address
func DeleteUserAddress(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get address ID from URL
	addressID := c.Param("address_id")

	// Delete address
	if err := db.Delete(&models.UserAddress{}, "id = ?", addressID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
