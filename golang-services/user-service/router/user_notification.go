package router

import (
	"math"
	"net/http"
	"strconv"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetUserNotifications(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Count unread notifications
	var unreadCount int64
	if err := db.Model(&models.UserNotification{}).
		Where("user_id = ? AND status = ?", user.ID, "unread").
		Count(&unreadCount).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get paginated notifications
	page, _ := strconv.Atoi(c.<PERSON>ultQuer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("limit", "100"))
	offset := (page - 1) * limit

	var total int64
	if err := db.Model(&models.UserNotification{}).
		Where("user_id = ?", user.ID).
		Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var notifications []models.UserNotification
	if err := db.Where("user_id = ?", user.ID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	// Create a response with the standard pagination fields
	response := models.PaginationResponse{
		Success:    true,
		Data:       notifications,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      response.Success,
		"data":         response.Data,
		"totalDocs":    response.Total,
		"page":         response.Page,
		"limit":        response.PageSize,
		"totalPages":   response.TotalPages,
		"hasNextPage":  response.HasNext,
		"hasPrevPage":  response.HasPrev,
		"unread_count": unreadCount,
	})
}

func UserReadNotifications(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	var request struct {
		NotificationIDs []uint `json:"notification_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update notifications status to read
	if err := db.Model(&models.UserNotification{}).
		Where("id IN ? AND user_id = ?", request.NotificationIDs, user.ID).
		Update("status", "read").Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// Helper function to create a new notification
func CreateUserNotification(db *gorm.DB, userID uint, notificationType string, title string, message string) error {
	notification := models.UserNotification{
		UserID:           userID,
		NotificationType: notificationType,
		Title:            title,
		Message:          message,
		Status:           "unread",
	}

	return db.Create(&notification).Error
}
