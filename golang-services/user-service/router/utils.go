// Package router provides utility functions for user service
package router

import (
	"fmt"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/email"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/zalo"

	"github.com/gin-gonic/gin"
	"github.com/nyaruka/phonenumbers"
	"gorm.io/gorm"
)

// sendOTP sends a one-time password via Zalo
func sendOTP(db *gorm.DB, brandID uint, phone, otp string) error {
	// Skip if merchant apps are not enabled
	if utils.GetEnv("USE_MERCHANT_APPS", "") != "true" {
		return nil
	}

	// Get brand from database
	var brand models.Brand
	if err := db.First(&brand, brandID).Error; err != nil {
		return fmt.Errorf("brand_not_found")
	}

	// Check if Zalo token is configured
	zaloToken := brand.GetToken("zalo")
	if zaloToken == nil || zaloToken.AccessToken == "" {
		return fmt.Errorf("zalo_token_not_configured")
	}

	// Format phone number for international use
	num, err := phonenumbers.Parse(phone, "VN")
	if err != nil {
		return fmt.Errorf("invalid_phone_number")
	}

	formattedPhone := phonenumbers.Format(num, phonenumbers.E164)

	// Send OTP via Zalo
	return zalo.SendZaloOTP(zaloToken, formattedPhone, otp)
}

// ContactUs handles contact form submissions
func ContactUs(c *gin.Context) {
	// Define request structure
	var request struct {
		Email   string `json:"email" binding:"required,email"`
		Phone   string `json:"phone"`
		Name    string `json:"name" binding:"required"`
		Message string `json:"message" binding:"required"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Send contact email
	go email.SendEmail(request.Email, request.Name, "contact_us", map[string]any{
		"email":   request.Email,
		"phone":   request.Phone,
		"name":    request.Name,
		"message": request.Message,
	})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
