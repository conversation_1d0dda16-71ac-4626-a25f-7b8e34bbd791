// Package router handles user profile and management functions
package router

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/email"

	"github.com/gin-gonic/gin"
)

// GetUserList retrieves a paginated list of users
func GetUserList(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user for ownership filtering
	currentUser := middlewares.GetUser(c)

	// Initialize query
	query := db.Model(&models.User{})

	// Apply ownership-based filtering
	if currentUser.OwnerID == nil {
		// Current user is an owner - show users they've created
		query = query.Where("owner_id = ?", currentUser.ID)
	} else {
		// Current user has an owner - only show users with same owner
		query = query.Where("owner_id = ?", *currentUser.OwnerID)
	}

	// Apply role filter
	if roleIDs := c.QueryArray("roles"); len(roleIDs) > 0 {
		query = query.Where("role IN ?", roleIDs)
	}

	// Apply brand filter (single value)
	if brandID := c.Query("brand"); brandID != "" {
		// Use raw SQL for JSONB array containment to avoid confusion between ? operator and parameter placeholder
		// This syntax checks if the brand ID exists as an element in the JSONB array
		query = query.Where("brands @> ?", fmt.Sprintf("[\"%s\"]", brandID))
	}

	// Apply hub filter (single value)
	if hubID := c.Query("hub"); hubID != "" {
		// Use raw SQL for JSONB array containment to avoid confusion between ? operator and parameter placeholder
		// This syntax checks if the hub ID exists as an element in the JSONB array
		query = query.Where("hubs @> ?", fmt.Sprintf("[\"%s\"]", hubID))
	}

	// Apply search filter
	if search := c.Query("search"); search != "" {
		search = strings.ToLower(search)
		query = query.Where("LOWER(name) LIKE ? OR LOWER(email) LIKE ?",
			fmt.Sprintf("%%%s%%", search),
			fmt.Sprintf("%%%s%%", search))
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	offset := (page - 1) * limit

	// Count total results
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Fetch users with pagination
	var users []models.User
	if err := query.
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&users).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	// Return paginated response
	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       users,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// GetUserByID retrieves details for a specific user
func GetUserByID(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	userID := c.Param("user_id")

	// Get current user for ownership filtering
	currentUser := middlewares.GetUser(c)
	// Find user in database
	var user models.User
	if err := db.First(&user, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "User not found",
		})
		return
	}

	// Check ownership permission - only allow access to users with same ownerID
	if currentUser.OwnerID == nil {
		// Current user is an owner - can only access users they've created
		if user.OwnerID == nil || *user.OwnerID != currentUser.ID {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "no permission to access this user",
			})
			return
		}
	} else {
		// Current user has an owner - can only access users with same owner
		if user.OwnerID == nil || *user.OwnerID != *currentUser.OwnerID {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "no permission to access this user",
			})
			return
		}
	}

	// Get related data
	var hubs []models.Hub
	var brands []models.Brand

	// Fetch hubs associated with user
	if user.Hubs != nil && len(user.Hubs) > 0 {
		// Convert the JSONB array to a slice of string IDs that we can use in the query
		hubIDs := make([]string, len(user.Hubs))
		for i, hubID := range user.Hubs {
			hubIDs[i] = fmt.Sprint(hubID) // Ensure we have string values
		}

		// Use the slice of string IDs in the query
		if err := db.Where("id IN ?", hubIDs).Select("id, name, address").Find(&hubs).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Fetch brands associated with user
	if user.Brands != nil && len(user.Brands) > 0 {
		// Convert the JSONB array to a slice of string IDs that we can use in the query
		brandIDs := make([]string, len(user.Brands))
		for i, brandID := range user.Brands {
			brandIDs[i] = fmt.Sprint(brandID) // Ensure we have string values
		}

		// Use the slice of string IDs in the query
		if err := db.Where("id IN ?", brandIDs).Select("id, name").Find(&brands).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Return user with related data
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"user":       user,
			"hub_list":   hubs,
			"brand_list": brands,
		},
	})
}

// CreateUser allows admins to create new users
func CreateUser(c *gin.Context) {
	// Get database connection and current user
	db := middlewares.GetDB(c)
	currentUser := middlewares.GetUser(c)

	// Check if user is authenticated
	if currentUser == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "unauthorized",
		})
		return
	}

	// Define request structure
	var request struct {
		Email  string   `json:"email" binding:"required,email"`
		Role   string   `json:"role" binding:"required"`
		Brands []string `json:"brands"` // Added brands field
		Hubs   []string `json:"hubs"`   // Added hubs field
		// Password  string     `json:"password" binding:"required"`
		// Name      string     `json:"name" binding:"required"`
		// RoleID    string     `json:"role" binding:"required"`
		// IsActive  bool       `json:"is_active"`
		// ExpiredAt *time.Time `json:"expired_at"`
		// Sites     []string   `json:"sites"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user with email already exists
	var existingUser models.User
	if err := db.Where("email = ?", request.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "email_already_existed",
		})
		return
	}

	// Determine the correct ownerID based on current user's status
	var ownerID *string
	if currentUser.OwnerID == nil {
		// Current user is an owner - set ownerID to current user's ID
		ownerID = &currentUser.ID
	} else {
		// Current user has an owner - set ownerID to current user's ownerID
		ownerID = currentUser.OwnerID
	}

	// Create new user
	newUser := models.User{
		Username: request.Email,
		Email:    request.Email,
		Password: "",
		Name:     request.Email,
		Status:   models.UserStatusPending,
		OwnerID:  ownerID, // Set the correctly determined ownerID
		Role:     request.Role,
	}

	// Set brands if provided
	if len(request.Brands) > 0 {
		newUser.Brands = request.Brands
	}

	// Set hubs if provided
	if len(request.Hubs) > 0 {
		newUser.Hubs = request.Hubs
	}

	// Save user to database
	if err := db.Create(&newUser).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Generate OTP for account activation
	otp := "123456"
	if utils.GetEnv("USE_MERCHANT_APPS", "") == "true" {
		otp = utils.GenerateOTP(6)
	}

	// Set OTP expiry time
	otpExpiry := time.Now().Add(24 * time.Hour)
	userOTP := models.UserOTP{
		ID:         utils.GenObjectID(),
		VerifyType: "register",
		UserUID:    request.Email,
		OTP:        otp,
		ExpiredAt:  otpExpiry,
	}

	// Define redirect type and create a pointer to it
	redirectType := "PORTAL"
	// Send activation email
	email.SendEmail(newUser.Email, "Kích hoạt tài khoản", "register", map[string]any{
		"name": newUser.Name,
		"code": otp,
		"url": fmt.Sprintf("%s/verify-account?code=%s&id=%s&type=register&passwordRequired=true",
			utils.GetWebURLToVerifyAccount(&redirectType), otp, newUser.ID),
	})

	// Save OTP in database
	if err := db.Create(&userOTP).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    newUser,
	})
}

// UpdateUser updates an existing user's details
func UpdateUser(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	userID := c.Param("user_id")

	// Define request structure
	var request struct {
		Name      string            `json:"name"`
		Status    models.UserStatus `json:"status" binding:"omitempty,oneof=active inactive"`
		ExpiredAt *time.Time        `json:"expired_at"`
		Role      string            `json:"role"`
		Hubs      []string          `json:"hubs"`
		Brands    []string          `json:"brands"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get existing user, validate and throw if pending
	existingUser := models.User{}
	if err := db.First(&existingUser, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user is pending
	// if existingUser.Status == models.UserStatusPending {
	// 	c.JSON(http.StatusBadRequest, gin.H{
	// 		"success": false,
	// 		"error":   "user_is_pending",
	// 	})
	// 	return
	// }

	// Create the update fields map for basic fields that don't require special handling
	updateFields := map[string]interface{}{
		"name":       request.Name,
		"status":     request.Status,
		"expired_at": request.ExpiredAt,
		"role":       request.Role,
		"updated_at": time.Now(), // Add explicit updated timestamp
	}

	// Handle JSON array fields properly to ensure correct PostgreSQL JSON format

	// Process brands only if provided in the request
	if request.Brands != nil {
		// Marshal the string slice into a proper JSON array string
		brandsJSON, err := json.Marshal(request.Brands)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to marshal brands: " + err.Error(),
			})
			return
		}
		// Store the properly formatted JSON string
		updateFields["brands"] = string(brandsJSON)
	}

	// Process hubs only if provided in the request
	if request.Hubs != nil {
		// Marshal the string slice into a proper JSON array string
		hubsJSON, err := json.Marshal(request.Hubs)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to marshal hubs: " + err.Error(),
			})
			return
		}
		// Store the properly formatted JSON string
		updateFields["hubs"] = string(hubsJSON)
	}

	// Update user and get updated record
	var user models.User
	if err := db.Model(&models.User{}).Where("id = ?", userID).Updates(updateFields).First(&user).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Generate auth data for response
	authData, err := getUserAuth(db, &user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}

// DeleteUser removes a user (only for pending users)
func DeleteUser(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)
	userID := c.Param("user_id")

	// Find user in database
	existingUser := models.User{}
	if err := db.First(&existingUser, "id = ?", userID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Only allow delete pending users
	if existingUser.Status != models.UserStatusPending {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "only_pending_users_can_be_deleted",
		})
		return
	}

	// Delete user from database
	if err := db.Delete(&existingUser).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCurrentUser retrieves the currently logged-in user
func GetCurrentUser(c *gin.Context) {
	// Get database connection and current user
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Check if user is authenticated
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "user_not_found",
		})
		return
	}

	// Generate auth data for response
	authData, err := getUserAuth(db, user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}
