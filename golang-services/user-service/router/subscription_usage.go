// Package router implements HTTP handlers for the user-service.
package router

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ResourceUsageResponse represents a single resource usage item with its limits.
type ResourceUsageResponse struct {
	ResourceCode models.ResourceCode `json:"resource_code"` // The resource code identifier
	Limit        int                 `json:"limit"`         // Maximum allowed by the subscription plan
	Current      int                 `json:"current"`       // Current usage count
}

// GetResourceUsage handles requests to check usage against subscription limits.
// @Summary Get current resource usage against subscription limits
// @Description Returns the current usage counts for resources limited by the user's subscription plan
// @Tags subscriptions
// @Accept json
// @Produce json
// @Param resources query string false "Comma-separated list of resource codes to check (e.g., products,users,stores)"
// @Success 200 {object} map[string]any "Returns resource usage information"
// @Failure 401 {object} map[string]any "User not authenticated"
// @Failure 404 {object} map[string]any "No active subscription found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /subscription/resource-usage [get]
func GetResourceUsage(c *gin.Context) {
	// Get database connection from middleware
	db := middlewares.GetDB(c)
	if db == nil {
		// Check if database connection exists
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Database connection not available",
		})
		return
	}

	// Get authenticated user from middleware
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	userID := user.ID

	// Parse query parameter 'resources' which should be comma-separated resource codes
	var resourceCodes []models.ResourceCode
	resourcesParam := c.Query("resources")
	if resourcesParam != "" {
		// Split the comma-separated string into individual codes
		resourceStrs := strings.Split(resourcesParam, ",")
		for _, rs := range resourceStrs {
			resourceCode := models.ResourceCode(strings.TrimSpace(rs))
			resourceCodes = append(resourceCodes, resourceCode)
		}
	}

	// Find user's current active subscription
	var currentSubscription models.UserSubscription
	if err := db.Where("user_id = ? AND status = ?", userID, models.Active).First(&currentSubscription).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "No active subscription found",
		})
		return
	}

	// Get subscription plan details to access the limits
	plan, exists := models.GetPlanByID(currentSubscription.SubscriptionPlanID)
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Referenced subscription plan not found",
		})
		return
	}

	// Use limits from the plan directly (assuming data is present as instructed)
	limits := plan.Limits

	// Determine which resources to check based on query parameter
	resourcesToCheck := models.GetAllResourceCodes()
	if len(resourceCodes) > 0 {
		resourcesToCheck = resourceCodes
	}

	// Process each requested resource and build response
	resourceUsages, err := buildResourceUsages(db, string(userID), resourcesToCheck, limits)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve resource usage: " + err.Error(),
		})
		return
	}

	// Return the response with success at top level
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resourceUsages,
	})
}

// buildResourceUsages processes each resource code and builds the response array.
// It returns early if any resource usage check fails to maintain data consistency.
func buildResourceUsages(db *gorm.DB, userID string, resourceCodes []models.ResourceCode, limits models.FeatureLimits) ([]ResourceUsageResponse, error) {
	var resourceUsages []ResourceUsageResponse

	// Process each requested resource
	for _, resourceCode := range resourceCodes {
		// Check resource usage for the current resource code
		usage, err := getResourceUsageCount(db, userID, resourceCode)
		if err != nil {
			// Return error to caller instead of just logging
			return nil, fmt.Errorf("failed to get usage for %s: %w", resourceCode, err)
		}

		// Get limit value directly from the limits map
		limit := limits[resourceCode]

		// Add to response
		resourceUsages = append(resourceUsages, ResourceUsageResponse{
			ResourceCode: resourceCode,
			Limit:        limit,
			Current:      usage,
		})
	}

	return resourceUsages, nil
}

// getResourceUsageCount returns the actual count of resource usage for the given user and resource code
// It performs database queries to count resources based on ownership hierarchies
func getResourceUsageCount(db *gorm.DB, userID string, code models.ResourceCode) (int, error) {
	// Check for nil database connection
	if db == nil {
		return 0, fmt.Errorf("database connection is nil")
	}

	// Step 1: Find the current user to determine ownership
	var user models.User
	if err := db.Where("id = ?", userID).First(&user).Error; err != nil {
		return 0, fmt.Errorf("failed to find user: %w", err)
	}

	// Step 2: Determine owner ID (if ownerID is null, the user is the owner)
	var ownerID string
	if user.OwnerID == nil {
		// User is the owner
		ownerID = user.ID
	} else {
		// User belongs to an owner
		ownerID = *user.OwnerID
	}

	// Get the active subscription for this user/owner
	var subscription models.UserSubscription
	if err := db.Where("user_id = ? AND status = ?", ownerID, models.Active).First(&subscription).Error; err != nil {
		return 0, fmt.Errorf("failed to find active subscription: %w", err)
	}

	// Return count based on resource type
	switch code {
	case models.UsersResourceCode:
		// Count all users under this owner, excluding inactivated ones
		var count int64
		err := db.Model(&models.User{}).
			Where("(id = ? OR owner_id = ?) AND status != ?",
				ownerID, ownerID, models.UserStatusInactive).
			Count(&count).Error

		if err != nil {
			return 0, fmt.Errorf("failed to count users: %w", err)
		}
		return int(count), nil

	case models.StoresResourceCode:
		// Find all brand IDs from the owner and users under the owner
		// First get the users under the owner
		var users []models.User
		if err := db.Where("id = ? OR owner_id = ?", ownerID, ownerID).
			Find(&users).Error; err != nil {
			return 0, fmt.Errorf("failed to find users for owner: %w", err)
		}

		// Collect all brand IDs from the users' Brands JSONArray
		var brandIDs []string
		for _, u := range users {
			// Add brand IDs from each user
			brandIDs = append(brandIDs, u.Brands...)
		}

		// Early return if no brands found
		if len(brandIDs) == 0 {
			return 0, nil
		}

		// Remove duplicate brand IDs
		uniqueBrandIDs := make([]string, 0, len(brandIDs))
		brandIDMap := make(map[string]bool)
		for _, id := range brandIDs {
			if !brandIDMap[id] {
				uniqueBrandIDs = append(uniqueBrandIDs, id)
				brandIDMap[id] = true
			}
		}

		// Count sites attached to those brands
		var count int64
		if err := db.Model(&models.Site{}).
			Where("brand_id IN ?", uniqueBrandIDs).
			Count(&count).Error; err != nil {
			return 0, fmt.Errorf("failed to count sites: %w", err)
		}
		return int(count), nil

	default:
		return 0, fmt.Errorf("unknown or unsupported resource code: %s", code)
	}
}
