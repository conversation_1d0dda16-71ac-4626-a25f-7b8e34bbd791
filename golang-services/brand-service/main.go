package main

import (
	"log"
	"os"

	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/di"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "brand-service"
	app.Usage = "Brand service"
	app.Version = "1.0.0"
	app.Commands = []cli.Command{
		{
			Name:      "Start",
			ShortName: "start",
			Usage:     "Start service",
			Flags: []cli.Flag{
				cli.StringFlag{
					Name:   "port",
					Usage:  "Port the server listens to",
					EnvVar: "PORT",
					Value:  "3000",
				},
			},
			Action: func(c *cli.Context) error {
				port := c.String("port")

				// Initialize the application using Wire
				r, err := di.InitializeApp()
				if err != nil {
					log.Fatalf("failed to initialize application: %v", err)
				}

				return r.Run(":" + port)
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}
