# Brand Service

This service handles brand-related operations including brand management, hub management, site management, and utility endpoints.

## API Endpoints

### Map API

#### GET `/v1/brand-service/map/v2/suggest_addresses`

Returns address suggestions based on the input query.

**Query Parameters:**
- `address` (required): The address query string

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "formatted_address": "123 Test Street, District 1, Ho Chi Minh City, Vietnam",
      "location": {
        "lat": 10.7758439,
        "lng": 106.7017555
      },
      "province_name": "Ho Chi Minh City",
      "district_name": "District 1",
      "ward_name": "Ben Nghe Ward",
      "route": "Test Street"
    },
    ...
  ]
}
```

## Running the Service

```bash
go run main.go start --port 3000
```

## Testing

```bash
go test ./...
```
