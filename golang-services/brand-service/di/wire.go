//go:build wireinject
// +build wireinject

package di

import (
	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/di"
)

// ServiceSet provides the service-specific providers
var ServiceSet = wire.NewSet(
	ProvideRouter,
)

// ProvideRouter creates a new Gin router with all middleware and routes
func ProvideRouter(middlewareProvider *di.MiddlewareProvider) *gin.Engine {
	r := gin.New()

	// Add middleware
	r.Use(middlewareProvider.CorsMiddleware())
	r.Use(middlewareProvider.GormMiddleware())
	r.Use(middlewareProvider.GoogleStorageMiddleware())
	r.Use(middlewareProvider.ErrorLocalizeMiddleware())
	r.Use(middlewareProvider.RedisMiddleware())

	// Load routes
	router.LoadHandlers(r)

	// Add standard Gin middleware
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	return r
}

// InitializeApp sets up the application with all dependencies
func InitializeApp() (*gin.Engine, error) {
	wire.Build(
		di.BaseSet,
		di.RedisProviderSet,
		di.RabbitMQProviderSet,
		ServiceSet,
	)
	return nil, nil
}
