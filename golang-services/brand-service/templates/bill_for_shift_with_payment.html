<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            width: 500px;
            font-weight: bold !important;
            font-size: 18px !important;
            font-family: Arial, sans-serif;
        }

        .section {
            padding: 1rem;
        }

        .title {
            font-size: 24px !important;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .data-row {
            border: 2px solid #000;
            margin-bottom: 0.5rem !important;
            display: flex;
            justify-content: space-between;
        }

        .column {
            font-size: 18px !important;
            padding: 0.5rem 1rem !important;
            flex: 1;
        }

        .column:first-child {
            border-right: 2px solid #000;
        }

        .column.right {
            text-align: right;
        }

        @media print {
            .section {
                padding: 0.5rem !important;
            }

            .data-row,
            .data-row .column:first-child {
                border-color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>

<body>
    <section class="section">
        <div class="container">
            <div class="box">
                <h1 class="title">
                    HÌNH THỨC THANH TOÁN
                </h1>

                <div class="data-row">
                    <div class="column">Hub:</div>
                    <div class="column right">
                        {{.HubName}}
                    </div>
                </div>

                <div class="data-row">
                    <div class="column">Thời gian:</div>
                    <div class="column right">
                        {{.CreatedAt}}
                    </div>
                </div>

                <div class="data-row">
                    <div class="column">Người xuất:</div>
                    <div class="column right">
                        {{.CloseBy}}
                    </div>
                </div>

                <div class="data-row">
                    <div class="column">Tổng đơn:</div>
                    <div class="column right">
                        {{.TotalOrder}} đơn
                    </div>
                </div>

                <h1 class="title">
                    HÌNH THỨC THANH TOÁN
                </h1>

                {{range $method, $data := .PaymentMethods}}
                <div class="data-row">
                    <div class="column">
                        {{$method}}
                    </div>
                    <div class="column right">
                        {{formatCurrency $data.Total}} đ
                    </div>
                </div>
                {{end}}
            </div>
        </div>
    </section>
</body>

</html>
