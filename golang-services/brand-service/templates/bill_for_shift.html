<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title><PERSON><PERSON>o C<PERSON></title>
    <style>
        @page {
            margin: 0;
        }

        body {
            width: 500px;
            font-family: "Arial", sans-serif;
            font-weight: bold;
            font-size: 14px;
            margin: 0;
            padding: 10px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 16px;
        }

        .table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            border: 2px solid black;
            padding: 8px;
            font-size: 16px;
            text-align: center;
        }

        .table th {
            font-weight: bold;
        }

        .amount {
            text-align: right;
        }

        .divider {
            border-top: 2px dashed black;
            margin: 15px 0;
        }

        @media print {
            body {
                width: 100%;
            }

            .table th,
            .table td {
                border: 2px solid black !important;
            }
        }
    </style>
</head>

<body>
    <div class="title">BÁO CÁO BÁN HÀNG</div>

    <div class="divider"></div>

    <div class="info-row">
        <div>Hub:</div>
        <div>{{.HubName}}</div>
    </div>

    <div class="info-row">
        <div>Thời gian:</div>
        <div>{{.CreatedAt}}</div>
    </div>

    {{if .CloseBy}}
    <div class="info-row">
        <div>Người xuất:</div>
        <div>{{.CloseBy}}</div>
    </div>
    {{end}}

    <div class="info-row">
        <div>Tổng đơn:</div>
        <div>{{.TotalOrder}}</div>
    </div>

    <div class="info-row">
        <div>DT trước KM:</div>
        <div>{{formatCurrency .TotalGrossReceived}} VNĐ</div>
    </div>

    <div class="info-row">
        <div>DT sau KM:</div>
        <div>{{formatCurrency .TotalNetReceived}} VNĐ</div>
    </div>

    <div class="divider"></div>

    <table class="table">
        <thead>
            <tr>
                <th>KÊNH BÁN</th>
                <th>SL</th>
                <th>DT SAU KM</th>
            </tr>
        </thead>
        <tbody>
            {{if eq (len .Merchants) 0}}
            <tr>
                <td colspan="3">Không có dữ liệu</td>
            </tr>
            {{else}}
            {{range $merchant, $data := .Merchants}}
            <tr>
                <td>{{$merchant}}</td>
                <td class="amount">{{$data.TotalOrders}}</td>
                <td class="amount">{{formatCurrency $data.TotalNetReceived}} VNĐ</td>
            </tr>
            {{end}}
            {{end}}
        </tbody>
    </table>
</body>

</html>
