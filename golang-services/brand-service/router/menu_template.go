package router

import (
	"errors"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetMenuTemplates godoc
// @Summary Get menu templates
// @Description Retrieves a list of menu templates for a brand
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of menu templates"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /v2/brands/{brand_id}/menu_templates [get]
func GetMenuTemplates(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.<PERSON>m("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Get menu templates for the brand
	var templates []models.MenuTemplate
	var total int64

	db.Model(&models.MenuTemplate{}).Where("brand_id = ?", brandID).Count(&total)
	db.Where("brand_id = ?", brandID).Order("created_at DESC").Offset(offset).Limit(limit).Find(&templates)

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       templates,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// CreateTemplate godoc
// @Summary Create a menu template
// @Description Creates a new menu template for a brand
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param template body object true "Menu template data"
// @Success 200 {object} map[string]any "Returns the created menu template"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /v2/brands/{brand_id}/menu_templates [post]
func CreateTemplate(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}
	// Create menu template
	template := models.MenuTemplate{
		BrandID:     brandID,
		Name:        request.Name,
		Description: request.Description,
	}

	if err := db.Create(&template).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_template",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    template,
	})
}

// UpdateTemplate godoc
// @Summary Update a menu template
// @Description Updates an existing menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param template_id path string true "Template ID"
// @Param template body object true "Menu template data"
// @Success 200 {object} map[string]any "Returns the updated menu template"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/brands/{brand_id}/menu_templates/{template_id} [put]
func UpdateTemplate(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ? AND brand_id = ?", templateID, brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update template
	template.Name = request.Name
	template.Description = request.Description

	if err := db.Save(&template).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_template",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    template,
	})
}

// DeleteTemplate godoc
// @Summary Delete a menu template
// @Description Deletes an existing menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param template_id path string true "Template ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/brands/{brand_id}/menu_templates/{template_id} [delete]
func DeleteTemplate(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ? AND brand_id = ?", templateID, brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Delete template
	tx := db.Begin()

	// Delete all related items
	if err := tx.Where("template_id = ?", templateID).Delete(&models.MenuTemplateItem{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_template_items",
		})
		return
	}

	// Delete all related categories
	if err := tx.Where("template_id = ?", templateID).Delete(&models.MenuTemplateCategory{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_template_categories",
		})
		return
	}

	// Delete all related option items
	if err := tx.Where("template_id = ?", templateID).Delete(&models.MenuTemplateOptionItem{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_template_option_items",
		})
		return
	}

	// Delete all related option categories
	if err := tx.Where("template_id = ?", templateID).Delete(&models.MenuTemplateOption{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_template_option_categories",
		})
		return
	}

	// Delete the template itself
	if err := tx.Delete(&template).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_template",
		})
		return
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_commit_transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Template deleted successfully",
	})
}

func buildMenuTemplate(
	categories []models.MenuTemplateCategory,
	items []models.MenuTemplateItem,
	options []models.MenuTemplateOption,
	optionItems []models.MenuTemplateOptionItem,
	active *bool,
) gin.H {
	// Filter by active status if specified
	filteredCategories := categories
	filteredItems := items
	filteredOptions := options
	filteredOptionItems := optionItems

	if active != nil {
		// Filter categories by active status
		activeCategories := []models.MenuTemplateCategory{}
		for _, c := range categories {
			if c.Active == *active {
				activeCategories = append(activeCategories, c)
			}
		}
		filteredCategories = activeCategories

		// Filter items by active status
		activeItems := []models.MenuTemplateItem{}
		for _, i := range items {
			if i.Active == *active {
				activeItems = append(activeItems, i)
			}
		}
		filteredItems = activeItems

		// Filter options by active status
		activeOptions := []models.MenuTemplateOption{}
		for _, o := range options {
			if o.Active == *active {
				activeOptions = append(activeOptions, o)
			}
		}
		filteredOptions = activeOptions

		// Filter option items by active status
		activeOptionItems := []models.MenuTemplateOptionItem{}
		for _, oi := range optionItems {
			if oi.Active == *active {
				activeOptionItems = append(activeOptionItems, oi)
			}
		}
		filteredOptionItems = activeOptionItems
	}

	// Find main categories (those without parent_id)
	mainCategories := []models.MenuTemplateCategory{}
	for _, c := range filteredCategories {
		if c.ParentID == "" {
			mainCategories = append(mainCategories, c)
		}
	}

	// Create a map of category ID to items
	itemCategoryMap := make(map[string][]models.MenuTemplateItem)
	for _, item := range filteredItems {
		if item.CategoryID != "" {
			itemCategoryMap[item.CategoryID] = append(itemCategoryMap[item.CategoryID], item)
		}
	}

	// Build the menu structure
	menuCategories := []gin.H{}
	for _, c := range mainCategories {
		// Find subcategories for this main category
		subCategories := []gin.H{}
		for _, sc := range filteredCategories {
			if sc.ParentID == c.ID {
				// Get items for this subcategory
				subCategoryItems := itemCategoryMap[sc.ID]

				subCat := gin.H{
					"id":          sc.ID,
					"name":        sc.Name,
					"parent_id":   sc.ParentID,
					"order":       sc.Order,
					"active":      sc.Active,
					"template_id": sc.TemplateID,
					"created_at":  sc.CreatedAt,
					"updated_at":  sc.UpdatedAt,
					"items":       subCategoryItems,
				}
				subCategories = append(subCategories, subCat)
			}
		}

		// Get items for this main category
		categoryItems := itemCategoryMap[c.ID]

		// Create the category object with its items and subcategories
		category := gin.H{
			"id":             c.ID,
			"name":           c.Name,
			"parent_id":      c.ParentID,
			"order":          c.Order,
			"active":         c.Active,
			"template_id":    c.TemplateID,
			"created_at":     c.CreatedAt,
			"updated_at":     c.UpdatedAt,
			"items":          categoryItems,
			"sub_categories": subCategories,
		}
		menuCategories = append(menuCategories, category)
	}

	// Build option categories with their items and linked items
	menuOptionCategories := []gin.H{}
	for _, o := range filteredOptions {
		// Find option items for this option category
		optItems := []models.MenuTemplateOptionItem{}
		for _, oi := range filteredOptionItems {
			if oi.OptionID == o.ID {
				optItems = append(optItems, oi)
			}
		}

		// Find linked items
		linkedItems := []gin.H{}
		for _, itemID := range o.ItemIDs {
			for _, item := range filteredItems {
				if item.ID == itemID {
					linkedItem := gin.H{
						"_id":    item.ID,
						"images": item.Images,
						"name":   item.Name,
						"price":  item.Price,
						"code":   item.Code,
						"order":  item.Order,
					}
					linkedItems = append(linkedItems, linkedItem)
					break
				}
			}
		}

		// Create the option category object with its items
		optionCategory := gin.H{
			"id":           o.ID,
			"name":         o.Name,
			"order":        o.Order,
			"active":       o.Active,
			"rule":         o.Rule,
			"item_ids":     o.ItemIDs,
			"template_id":  o.TemplateID,
			"created_at":   o.CreatedAt,
			"updated_at":   o.UpdatedAt,
			"option_items": optItems,
			"linked_items": linkedItems,
		}
		menuOptionCategories = append(menuOptionCategories, optionCategory)
	}

	return gin.H{
		"categories":        menuCategories,
		"option_categories": menuOptionCategories,
	}
}

// GetTemplate godoc
// @Summary Get menu template details
// @Description Retrieves details of a specific menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param template_id path string true "Template ID"
// @Success 200 {object} map[string]any "Returns the menu template details"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/brands/{brand_id}/menu_templates/{template_id} [get]
func GetTemplate(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Get menu template
	var template models.MenuTemplate
	if err := db.First(&template, "id = ? AND brand_id = ?", templateID, brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Get categories
	categories := []models.MenuTemplateCategory{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&categories)

	// Get items
	items := []models.MenuTemplateItem{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&items)

	// Get option categories
	optionCategories := []models.MenuTemplateOption{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&optionCategories)

	// Get option items
	optionItems := []models.MenuTemplateOptionItem{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&optionItems)

	// Build the menu structure using the helper function
	menu := buildMenuTemplate(categories, items, optionCategories, optionItems, nil)

	// Return the template with the structured menu
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"id":          template.ID,
			"brand_id":    template.BrandID,
			"name":        template.Name,
			"description": template.Description,
			"menu":        menu,
		},
	})
}

// CreateCategory godoc
// @Summary Create a category in a menu template
// @Description Creates a new category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category body object true "Category data"
// @Success 200 {object} map[string]any "Returns the created category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/menu_templates/{template_id}/categories [post]
func CreateCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name     string `json:"name" binding:"required"`
		ParentID string `json:"parent_id"`
		Order    int64  `json:"order"`
		Active   bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// If parent_id is provided, check if it exists
	if request.ParentID != "" {
		var parentCategory models.MenuTemplateCategory
		if err := db.First(&parentCategory, "id = ? AND template_id = ?", request.ParentID, templateID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "parent_category_not_found",
			})
			return
		}
	}

	// Set default order if not provided
	if request.Order == 0 {
		request.Order = time.Now().Unix()
	}

	// Create category
	category := models.MenuTemplateCategory{
		TemplateID: templateID,
		Name:       request.Name,
		ParentID:   request.ParentID,
		Order:      request.Order,
		Active:     request.Active,
	}

	if err := db.Create(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_category",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// GetCategories godoc
// @Summary Get categories in a menu template
// @Description Retrieves a list of categories in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Success 200 {object} map[string]any "Returns list of categories"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/menu_templates/{template_id}/categories [get]
func GetCategories(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Get categories
	var categories []models.MenuTemplateCategory
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&categories)

	// Organize categories into a tree structure
	rootCategories := []models.MenuTemplateCategory{}
	categoryMap := make(map[string][]models.MenuTemplateCategory)

	// Group categories by parent_id
	for _, category := range categories {
		if category.ParentID == "" {
			rootCategories = append(rootCategories, category)
		} else {
			categoryMap[category.ParentID] = append(categoryMap[category.ParentID], category)
		}
	}

	// Build response
	response := gin.H{
		"categories":     rootCategories,
		"sub_categories": categoryMap,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// UpdateCategory godoc
// @Summary Update a category in a menu template
// @Description Updates an existing category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category_id path string true "Category ID"
// @Param category body object true "Category data"
// @Success 200 {object} map[string]any "Returns the updated category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/menu_templates/{template_id}/categories/{category_id} [put]
func UpdateCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	categoryID := c.Param("category_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if category exists
	var category models.MenuTemplateCategory
	if err := db.First(&category, "id = ? AND template_id = ?", categoryID, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name     string `json:"name" binding:"required"`
		ParentID string `json:"parent_id"`
		Order    int64  `json:"order"`
		Active   bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// If parent_id is provided and changed, check if it exists
	if request.ParentID != "" && request.ParentID != category.ParentID {
		var parentCategory models.MenuTemplateCategory
		if err := db.First(&parentCategory, "id = ? AND template_id = ?", request.ParentID, templateID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "parent_category_not_found",
			})
			return
		}
	}

	// Update category
	category.Name = request.Name
	if request.ParentID != "" {
		category.ParentID = request.ParentID
	}
	if request.Order > 0 {
		category.Order = request.Order
	}
	category.Active = request.Active

	if err := db.Save(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_category",
		})
		return
	}

	// Get subcategories if this is a root category
	var subCategories []models.MenuTemplateCategory
	if category.ParentID == "" {
		db.Where("parent_id = ? AND template_id = ?", categoryID, templateID).Find(&subCategories)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"category":       category,
			"sub_categories": subCategories,
		},
	})
}

// DeleteCategory godoc
// @Summary Delete a category in a menu template
// @Description Deletes an existing category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category_id path string true "Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/menu_templates/{template_id}/categories/{category_id} [delete]
func DeleteCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	categoryID := c.Param("category_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if category exists
	var category models.MenuTemplateCategory
	if err := db.First(&category, "id = ? AND template_id = ?", categoryID, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Check if category has subcategories
	var subCategoriesCount int64
	db.Model(&models.MenuTemplateCategory{}).Where("parent_id = ? AND template_id = ?", categoryID, templateID).Count(&subCategoriesCount)
	if subCategoriesCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "category_has_subcategories",
		})
		return
	}

	// Check if category has items
	var itemsCount int64
	db.Model(&models.MenuTemplateItem{}).Where("category_id = ? AND template_id = ?", categoryID, templateID).Count(&itemsCount)
	if itemsCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "category_has_items",
		})
		return
	}

	// Delete category
	if err := db.Delete(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_category",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Category deleted successfully",
	})
}

// CreateItem godoc
// @Summary Create an item in a menu template category
// @Description Creates a new item in a menu template category
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category_id path string true "Category ID"
// @Param item body object true "Item data"
// @Success 200 {object} map[string]any "Returns the created item"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/menu_templates/{template_id}/categories/{category_id}/items [post]
func CreateItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	categoryID := c.Param("category_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if category exists
	var category models.MenuTemplateCategory
	if err := db.First(&category, "id = ? AND template_id = ?", categoryID, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name         string   `json:"name" binding:"required"`
		Code         string   `json:"code"`
		Unit         string   `json:"unit"`
		Sources      []string `json:"sources"`
		Description  string   `json:"description"`
		Images       []string `json:"images"`
		ImagePreview string   `json:"image_preview"`
		Price        float64  `json:"price"`
		Order        int64    `json:"order"`
		Active       bool     `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set default order if not provided
	if request.Order == 0 {
		request.Order = time.Now().Unix()
	}

	// Create item
	item := models.MenuTemplateItem{
		TemplateID:   templateID,
		CategoryID:   categoryID,
		Name:         request.Name,
		Code:         request.Code,
		Unit:         request.Unit,
		Sources:      request.Sources,
		Description:  request.Description,
		Images:       request.Images,
		ImagePreview: request.ImagePreview,
		Price:        request.Price,
		Order:        request.Order,
		Active:       request.Active,
	}

	if err := db.Create(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_item",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    item,
	})
}

// UpdateItem godoc
// @Summary Update an item in a menu template category
// @Description Updates an existing item in a menu template category
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Param item body object true "Item data"
// @Success 200 {object} map[string]any "Returns the updated item"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Item not found"
// @Router /v2/menu_templates/{template_id}/categories/{category_id}/items/{item_id} [put]
func UpdateItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	categoryID := c.Param("category_id")
	itemID := c.Param("item_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if category exists
	var category models.MenuTemplateCategory
	if err := db.First(&category, "id = ? AND template_id = ?", categoryID, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Check if item exists
	var item models.MenuTemplateItem
	if err := db.First(&item, "id = ? AND template_id = ? AND category_id = ?", itemID, templateID, categoryID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "item_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name         string   `json:"name" binding:"required"`
		Code         string   `json:"code"`
		Unit         string   `json:"unit"`
		Sources      []string `json:"sources"`
		Description  string   `json:"description"`
		Images       []string `json:"images"`
		ImagePreview string   `json:"image_preview"`
		Price        float64  `json:"price"`
		Order        int64    `json:"order"`
		Active       bool     `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update item
	item.Name = request.Name
	item.Code = request.Code
	item.Unit = request.Unit
	item.Sources = request.Sources
	item.Description = request.Description
	item.Images = request.Images
	item.ImagePreview = request.ImagePreview
	item.Price = request.Price
	if request.Order > 0 {
		item.Order = request.Order
	}
	item.Active = request.Active

	if err := db.Save(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_item",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    item,
	})
}

// DeleteItem godoc
// @Summary Delete an item in a menu template category
// @Description Deletes an existing item in a menu template category
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Item not found"
// @Router /v2/menu_templates/{template_id}/categories/{category_id}/items/{item_id} [delete]
func DeleteItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	categoryID := c.Param("category_id")
	itemID := c.Param("item_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if category exists
	var category models.MenuTemplateCategory
	if err := db.First(&category, "id = ? AND template_id = ?", categoryID, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Check if item exists
	var item models.MenuTemplateItem
	if err := db.First(&item, "id = ? AND template_id = ? AND category_id = ?", itemID, templateID, categoryID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "item_not_found",
		})
		return
	}

	// Delete item
	if err := db.Delete(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_item",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Item deleted successfully",
	})
}

// CreateOptionCategory godoc
// @Summary Create an option category in a menu template
// @Description Creates a new option category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param option_category body object true "Option category data"
// @Success 200 {object} map[string]any "Returns the created option category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Template not found"
// @Router /v2/menu_templates/{template_id}/option-categories [post]
func CreateOptionCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name    string                        `json:"name" binding:"required"`
		Order   int64                         `json:"order"`
		Active  bool                          `json:"active"`
		Rule    models.MenuTemplateOptionRule `json:"rule"`
		ItemIDs []string                      `json:"item_ids"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set default order if not provided
	if request.Order == 0 {
		request.Order = time.Now().Unix()
	}

	// Create option category
	optionCategory := models.MenuTemplateOption{
		TemplateID: templateID,
		Name:       request.Name,
		Order:      request.Order,
		Active:     request.Active,
		Rule: models.JSONField[models.MenuTemplateOptionRule]{
			Data: request.Rule,
		},
		ItemIDs: request.ItemIDs,
	}

	if err := db.Create(&optionCategory).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_option_category",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    optionCategory,
	})
}

// UpdateOptionCategory godoc
// @Summary Update an option category in a menu template
// @Description Updates an existing option category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param id path string true "Option Category ID"
// @Param option_category body object true "Option category data"
// @Success 200 {object} map[string]any "Returns the updated option category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Option category not found"
// @Router /v2/menu_templates/{template_id}/option-categories/{id} [put]
func UpdateOptionCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	id := c.Param("id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if option category exists
	var optionCategory models.MenuTemplateOption
	if err := db.First(&optionCategory, "id = ? AND template_id = ?", id, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "option_category_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name    string                        `json:"name" binding:"required"`
		Order   int64                         `json:"order"`
		Active  bool                          `json:"active"`
		Rule    models.MenuTemplateOptionRule `json:"rule"`
		ItemIDs []string                      `json:"item_ids"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update option category
	optionCategory.Name = request.Name
	if request.Order > 0 {
		optionCategory.Order = request.Order
	}
	optionCategory.Active = request.Active
	optionCategory.Rule = models.JSONField[models.MenuTemplateOptionRule]{
		Data: request.Rule,
	}
	optionCategory.ItemIDs = request.ItemIDs

	if err := db.Save(&optionCategory).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_option_category",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    optionCategory,
	})
}

// DeleteOptionCategory godoc
// @Summary Delete an option category in a menu template
// @Description Deletes an existing option category in a menu template
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param id path string true "Option Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Option category not found"
// @Router /v2/menu_templates/{template_id}/option-categories/{id} [delete]
func DeleteOptionCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	templateID := c.Param("template_id")
	id := c.Param("id")

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if option category exists
	var optionCategory models.MenuTemplateOption
	if err := db.First(&optionCategory, "id = ? AND template_id = ?", id, templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "option_category_not_found",
		})
		return
	}

	// Delete option category
	if err := db.Delete(&optionCategory).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_option_category",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Option category deleted successfully",
	})
}

// ApplyTemplateToSite godoc
// @Summary Apply a menu template to sites
// @Description Applies a menu template to multiple sites
// @Tags menu-templates
// @Accept json
// @Produce json
// @Param template_id path string true "Template ID"
// @Param site_ids body []string true "Array of Site IDs"
// @Success 200 {object} map[string]any "Success response"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Template or site not found"
// @Router /v2/menu_templates/{template_id}/apply [post]
func ApplyTemplateToSite(c *gin.Context) {
	db := middlewares.GetDB(c)

	templateID := c.Param("template_id")

	// Parse request body to get site IDs
	var requestBody struct {
		SiteIDs []string `json:"site_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Check if template exists
	var template models.MenuTemplate
	if err := db.First(&template, "id = ?", templateID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "template_not_found",
		})
		return
	}

	// Check if sites exist
	var sites []models.Site
	if err := db.Where("id IN ?", requestBody.SiteIDs).Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Verify all requested sites were found
	if len(sites) != len(requestBody.SiteIDs) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "some_sites_not_found",
		})
		return
	}

	// Get all categories from the template
	categories := []models.MenuTemplateCategory{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&categories)

	// Get all items from the template
	items := []models.MenuTemplateItem{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&items)

	// Get all option categories from the template
	optionCategories := []models.MenuTemplateOption{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&optionCategories)

	// Get all option items from the template
	optionItems := []models.MenuTemplateOptionItem{}
	db.Where("template_id = ?", templateID).Order(`"order" ASC`).Find(&optionItems)

	// menu := buildMenuTemplate(categories, items, optionCategories, optionItems, nil)

	// Process each site
	processedSites := []string{}
	for _, site := range sites {
		// Begin transaction for this site
		tx := db.Begin()

		// Get existing site menu data for history
		oldCategories := []models.SiteMenuCategory{}
		oldItems := []models.SiteMenuItem{}
		oldOptions := []models.SiteMenuOption{}
		oldOptionItems := []models.SiteMenuOptionItem{}

		tx.Where("site_id = ?", site.ID).Find(&oldCategories)
		tx.Where("site_id = ?", site.ID).Find(&oldItems)
		tx.Where("site_id = ?", site.ID).Find(&oldOptions)
		tx.Where("site_id = ?", site.ID).Find(&oldOptionItems)

		// Delete existing menu data for this site
		tx.Where("site_id = ?", site.ID).Delete(&models.SiteMenuCategory{})
		tx.Where("site_id = ?", site.ID).Delete(&models.SiteMenuItem{})
		tx.Where("site_id = ?", site.ID).Delete(&models.SiteMenuOption{})
		tx.Where("site_id = ?", site.ID).Delete(&models.SiteMenuOptionItem{})

		// Create site menu categories
		categoryIDMap := make(map[string]string) // Map template category ID to site category ID
		for _, category := range categories {
			siteCategory := models.SiteMenuCategory{
				SiteID:      site.ID,
				Name:        category.Name,
				ParentID:    categoryIDMap[category.ParentID], // Map parent ID if it exists
				Order:       category.Order,
				Active:      category.Active,
				TemplateID:  templateID,
				TemplateCID: category.ID,
			}

			if err := tx.Create(&siteCategory).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "failed_to_create_site_category",
					"site_id": site.ID,
				})
				return
			}

			categoryIDMap[category.ID] = siteCategory.ID
		}

		// Create site menu items
		itemIDMap := make(map[string]string) // Map template item ID to site item ID
		for _, item := range items {
			// Find matching old item to preserve channels
			var channels []models.SiteMenuItemChannel
			for _, oldItem := range oldItems {
				if oldItem.TemplateIID == item.ID {
					channels = oldItem.Channels
					break
				}
			}
			siteItem := models.SiteMenuItem{
				SiteID:       site.ID,
				CategoryID:   categoryIDMap[item.CategoryID],
				Name:         item.Name,
				Code:         item.Code,
				Unit:         item.Unit,
				Sources:      item.Sources,
				Description:  item.Description,
				Images:       item.Images,
				ImagePreview: item.ImagePreview,
				Price:        item.Price,
				Order:        item.Order,
				Active:       item.Active,
				TemplateID:   templateID,
				TemplateIID:  item.ID,

				Channels: channels,
			}

			if err := tx.Create(&siteItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "failed_to_create_site_item",
					"site_id": site.ID,
				})
				return
			}

			itemIDMap[item.ID] = siteItem.ID
		}

		// Create site menu option categories
		optionCategoryIDMap := make(map[string]string) // Map template option category ID to site option category ID
		for _, optionCategory := range optionCategories {
			// Map item IDs to new site item IDs
			mappedItemIDs := []string{}
			for _, itemID := range optionCategory.ItemIDs {
				if mappedID, ok := itemIDMap[itemID]; ok {
					mappedItemIDs = append(mappedItemIDs, mappedID)
				}
			}

			siteOptionCategory := models.SiteMenuOption{
				SiteID:      site.ID,
				Name:        optionCategory.Name,
				Order:       optionCategory.Order,
				Active:      optionCategory.Active,
				Rule:        optionCategory.Rule,
				ItemIDs:     mappedItemIDs,
				TemplateID:  templateID,
				TemplateOID: optionCategory.ID,
			}

			if err := tx.Create(&siteOptionCategory).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "failed_to_create_site_option_category",
					"site_id": site.ID,
				})
				return
			}

			optionCategoryIDMap[optionCategory.ID] = siteOptionCategory.ID
		}

		// Create site menu option items
		for _, optionItem := range optionItems {
			siteOptionItem := models.SiteMenuOptionItem{
				SiteID:      site.ID,
				OptionID:    optionCategoryIDMap[optionItem.OptionID],
				Name:        optionItem.Name,
				Code:        optionItem.Code,
				Price:       optionItem.Price,
				Order:       optionItem.Order,
				Active:      optionItem.Active,
				TemplateID:  templateID,
				TemplateOID: optionItem.ID,
			}

			if err := tx.Create(&siteOptionItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "failed_to_create_site_option_item",
					"site_id": site.ID,
				})
				return
			}
		}

		// Update site menu with template ID and history
		var siteMenu models.SiteMenu
		result := tx.Where("site_id = ?", site.ID).First(&siteMenu)
		if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "failed_to_find_site_menu",
				"site_id": site.ID,
			})
			return
		}

		// Create or update site menu
		now := time.Now()
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Create new site menu
			siteMenu = models.SiteMenu{
				SiteID:     site.ID,
				TemplateID: templateID,
				ModifiedOn: &now,
			}
		} else {
			// Update existing site menu
			siteMenu.TemplateID = templateID
			siteMenu.ModifiedOn = &now

			// Update channels to mark as not synced
			for i := range siteMenu.Channels {
				siteMenu.Channels[i].Synced = false
				siteMenu.Channels[i].ModifiedOn = &now
			}
		}

		// Add history entry
		historyEntry := map[string]any{
			"template_id": templateID,
			"applied_on":  now,
		}

		// Initialize histories if nil
		if siteMenu.Histories == nil {
			siteMenu.Histories = []any{}
		}

		// Add new history entry at the beginning
		siteMenu.Histories = append([]any{historyEntry}, siteMenu.Histories...)

		if err := tx.Save(&siteMenu).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "failed_to_update_site_menu",
				"site_id": site.ID,
			})
			return
		}

		// Commit transaction for this site
		if err := tx.Commit().Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "failed_to_commit_transaction",
				"site_id": site.ID,
			})
			return
		}

		processedSites = append(processedSites, site.ID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Menu template applied to sites successfully",
		"data": gin.H{
			"sites_count":             len(processedSites),
			"sites":                   processedSites,
			"categories_count":        len(categories),
			"items_count":             len(items),
			"option_categories_count": len(optionCategories),
			"option_items_count":      len(optionItems),
		},
	})
}
