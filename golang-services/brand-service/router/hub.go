package router

import (
	"fmt"
	"math"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/gmap"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// GetHubList godoc
// @Summary Get list of hubs
// @Description Retrieves a paginated list of hubs with optional filtering
// @Tags hubs
// @Accept json
// @Produce json
// @Param name query string false "Filter hubs by name"
// @Param brand_ids query []string false "Filter hubs by brand IDs"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(200)
// @Success 200 {object} PaginationResponse "Returns hubs list and pagination info"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /hubs [get]
func GetHubList(c *gin.Context) {
	db := middlewares.GetDB(c)

	query := db.Model(&models.Hub{})

	// Apply filters
	// hubPermissions := middlewares.GetHubPermissions(c)
	user := middlewares.GetUser(c)
	var hubIDs = user.Hubs

	query = query.Where("id = ANY(?)", pq.Array(hubIDs))

	if name := c.Query("name"); name != "" {
		escapedName := regexp.QuoteMeta(name)
		query = query.Where("name ILIKE ?", fmt.Sprintf("%%%s%%", escapedName))
	}

	if brandIDs := c.QueryArray("brand_ids"); len(brandIDs) > 0 {
		query = query.Where("brand_ids = ANY(?)", pq.Array(brandIDs))
	}

	// Pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "200"))
	offset := (page - 1) * limit

	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to count hubs",
		})
		return
	}

	var hubs []models.Hub
	if err := query.Order("name ASC").Offset(offset).Limit(limit).Find(&hubs).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve hubs",
		})
		return
	}

	// Create a map to store site counts for each hub
	hubSiteCounts := make(map[string]int64)

	// Get all hub IDs for the query
	var hubIDsForCount []string
	for _, hub := range hubs {
		hubIDsForCount = append(hubIDsForCount, hub.ID)
	}

	if len(hubIDsForCount) > 0 {
		type HubSiteCount struct {
			HubID string `gorm:"column:hub_id"`
			Count int64  `gorm:"column:count"`
		}

		var counts []HubSiteCount
		countQuery := "SELECT hub_id, COUNT(*) as count FROM sites WHERE hub_id IN ? GROUP BY hub_id"
		if err := db.Raw(countQuery, hubIDsForCount).Scan(&counts).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to count sites for hubs",
			})
			return
		}

		for _, count := range counts {
			hubSiteCounts[count.HubID] = count.Count
		}
	}

	// Create response objects with site counts
	hubResponses := make([]models.HubDetail, len(hubs))
	for i, hub := range hubs {
		hubResponses[i] = models.HubDetail{
			Hub:       hub,
			SiteCount: hubSiteCounts[hub.ID], // Will be 0 if no sites found
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       hubResponses,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// GetHubDetail godoc
// @Summary Get hub details
// @Description Retrieves a hub by ID
// @Tags hubs
// @Accept json
// @Produce json
// @Param hub_id path string true "Hub ID"
// @Success 200 {object} map[string]any "Returns the hub"
// @Failure 404 {object} map[string]any "Hub not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /hubs/{hub_id} [get]
func GetHubDetail(c *gin.Context) {
	db := middlewares.GetDB(c)
	hubID := c.Param("hub_id")

	var hub models.Hub
	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Hub not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    hub,
	})
}

// CreateHub godoc
// @Summary Create a new hub
// @Description Creates a new hub with location data and associates it with the current user
// @Tags hubs
// @Accept json
// @Produce json
// @Success 200 {object} map[string]any "Returns the created hub"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /hubs [post]
func CreateHub(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	var hub models.Hub
	if err := c.ShouldBindJSON(&hub); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update location if address provided
	if hub.AddressObj.Data.FormattedAddress != "" {
		location, err := gmap.GetLocation(hub.AddressObj.Data.FormattedAddress)
		if err == nil {
			hub.AddressObj.Data.Location = models.AddressLocation{
				Lat: location.Geometry.Location.Lat,
				Lng: location.Geometry.Location.Lng,
			}
		}
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&hub).Error; err != nil {
			return err
		}

		if user != nil {
			// Add hub to user's hubs array
			user.Hubs = append(user.Hubs, hub.ID)
			if err := tx.Model(&models.User{}).Where("id = ?", user.ID).Update("hubs", user.Hubs).Error; err != nil {
				return err
			}

			// Create a HubStaff record with owner role
			// hubStaff := models.HubStaff{
			// 	HubID:  hub.ID,
			// 	UserID: user.ID,
			// 	Role:   models.StaffRoleOwner,
			// }

			// if err := tx.Create(&hubStaff).Error; err != nil {
			// 	return err
			// }
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    hub,
	})
}

// UpdateHub godoc
// @Summary Update a hub
// @Description Updates an existing hub by ID, including location data if address is provided
// @Tags hubs
// @Accept json
// @Produce json
// @Param hub_id path string true "Hub ID"
// @Success 200 {object} map[string]any "Returns the updated hub"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 403 {object} map[string]any "Forbidden - User does not have required role"
// @Failure 404 {object} map[string]any "Hub not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /hubs/{hub_id} [put]
func UpdateHub(c *gin.Context) {
	db := middlewares.GetDB(c)
	// user := middlewares.GetUser(c)
	hubID := c.Param("hub_id")

	// Check if user has permission to update this hub
	// if user != nil {
	// 	// Get user's hub permissions
	// 	hubRoles := middlewares.GetHubPermissions(c)

	// 	// Check if user is owner or manager of this hub
	// 	role, exists := hubRoles[hubID]
	// 	if !exists || (role != models.StaffRoleOwner && role != models.StaffRoleManager) {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"success": false,
	// 			"error":   "You must be an owner or manager to update this hub",
	// 		})
	// 		return
	// 	}
	// }

	var hub models.Hub
	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Hub not found",
		})
		return
	}

	if err := c.ShouldBindJSON(&hub); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update location if address provided
	if hub.AddressObj.Data.FormattedAddress != "" {
		location, err := gmap.GetLocation(hub.AddressObj.Data.FormattedAddress)
		if err == nil {
			hub.AddressObj.Data.Location = models.AddressLocation{
				Lat: location.Geometry.Location.Lat,
				Lng: location.Geometry.Location.Lng,
			}
		}
	}

	if err := db.Save(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    hub,
	})
}

// DeleteHub godoc
// @Summary Delete a hub
// @Description Deletes a hub by ID
// @Tags hubs
// @Accept json
// @Produce json
// @Param hub_id path string true "Hub ID"
// @Success 200 {object} map[string]any "Success message"
// @Failure 403 {object} map[string]any "Forbidden - User does not have required role"
// @Failure 404 {object} map[string]any "Hub not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /hubs/{hub_id} [delete]
func DeleteHub(c *gin.Context) {
	db := middlewares.GetDB(c)
	// user := middlewares.GetUser(c)
	hubID := c.Param("hub_id")

	// Check if user has permission to delete this hub
	// if user != nil {
	// 	// Get user's hub permissions
	// 	hubRoles := middlewares.GetHubPermissions(c)

	// 	// Check if user is owner or manager of this hub
	// 	role, exists := hubRoles[hubID]
	// 	if !exists || (role != models.StaffRoleOwner && role != models.StaffRoleManager) {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"success": false,
	// 			"error":   "You must be an owner or manager to delete this hub",
	// 		})
	// 		return
	// 	}
	// }

	// Check if hub exists
	var hub models.Hub
	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Hub not found",
		})
		return
	}

	if err := db.Delete(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// HubOverviewResponse represents the response structure for the hub overview endpoint
type HubOverviewResponse struct {
	Success bool            `json:"success"`
	Data    HubOverviewData `json:"data"`
}

// HubOverviewData contains the overview data for a hub
type HubOverviewData struct {
	// Order counts by status (matching the UI design)
	TotalOrders     int64 `json:"total_orders"`     // 470 - Total number of orders (all statuses)
	PreOrders       int64 `json:"pre_orders"`       // 33 - Number of pre-orders
	WaitingOrders   int64 `json:"waiting_orders"`   // 6 - Number of orders waiting for confirmation
	CompletedOrders int64 `json:"completed_orders"` // 62 - Number of completed orders
	CanceledOrders  int64 `json:"canceled_orders"`  // 8 - Number of canceled orders

	// Site-specific order data
	SiteOrderSummary []SiteOrderSummary `json:"site_order_summary"`
}

// SiteOrderSummary contains order summary data for a specific site
type SiteOrderSummary struct {
	// Site identification
	SiteID   string `json:"site_id"`
	SiteName string `json:"site_name"`

	// Order statistics
	TotalOrders     int64   `json:"total_orders"`     // Total orders for this site (e.g., 40, 24, 41)
	CompletedOrders int64   `json:"completed_orders"` // Completed orders for this site (e.g., 24, 12, 28)
	TotalRevenue    float64 `json:"total_revenue"`    // Total revenue from completed orders (e.g., 15,000,000)
}

func GetHubOverview(c *gin.Context) {
	db := middlewares.GetDB(c)
	hubID := c.Param("hub_id")

	// Verify hub exists
	var hub models.Hub
	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Hub not found",
		})
		return
	}

	// Get current date range (start of day to now)
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	startUnix := startOfDay.Add(-30 * 24 * time.Hour).Unix()
	endUnix := now.Unix()
	// Get sites associated with this hub
	var sites []models.Site
	if err := db.Where("hub_id = ?", hubID).Find(&sites).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retrieve sites for hub",
		})
		return
	}

	// Extract site IDs
	var siteIDs []string
	siteMap := make(map[string]models.Site)
	for _, site := range sites {
		siteIDs = append(siteIDs, site.ID)
		siteMap[site.ID] = site
	}

	// Initialize response data
	overviewData := HubOverviewData{
		TotalOrders:      0,
		PreOrders:        0,
		WaitingOrders:    0,
		CompletedOrders:  0,
		CanceledOrders:   0,
		SiteOrderSummary: []SiteOrderSummary{},
	}

	// If no sites found, return empty data
	if len(siteIDs) == 0 {
		c.JSON(http.StatusOK, HubOverviewResponse{
			Success: true,
			Data:    overviewData,
		})
		return
	}

	// Query to get order counts by status
	type OrderStatusCount struct {
		Status string `gorm:"column:status"`
		Count  int64  `gorm:"column:count"`
	}

	var statusCounts []OrderStatusCount
	statusQuery := `
		SELECT status, COUNT(*) as count
		FROM orders
		WHERE site_id IN ?
		AND (data_mapping->>'delivery_time_unix')::int >= ?
		AND (data_mapping->>'delivery_time_unix')::int <= ?
		GROUP BY status
	`
	if err := db.Raw(statusQuery, siteIDs, startUnix, endUnix).Scan(&statusCounts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retrieve order counts: " + err.Error(),
		})
		return
	}

	// Process status counts
	for _, sc := range statusCounts {
		overviewData.TotalOrders += sc.Count

		switch sc.Status {
		case "PRE_ORDER":
			overviewData.PreOrders = sc.Count
		case "PENDING", "WAITING_PAYMENT":
			overviewData.WaitingOrders += sc.Count
		case "FINISH":
			overviewData.CompletedOrders = sc.Count
		case "CANCEL":
			overviewData.CanceledOrders = sc.Count
		}
	}

	// Query to get order summary by site
	type SiteOrderCount struct {
		SiteID          string  `gorm:"column:site_id"`
		TotalOrders     int64   `gorm:"column:total_orders"`
		CompletedOrders int64   `gorm:"column:completed_orders"`
		TotalRevenue    float64 `gorm:"column:total_revenue"`
	}

	var siteOrderCounts []SiteOrderCount
	siteQuery := `
		SELECT
			site_id,
			COUNT(*) as total_orders,
			SUM(CASE WHEN status = 'FINISH' THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 'FINISH' THEN (data_mapping->>'total')::float ELSE 0 END) as total_revenue
		FROM orders
		WHERE site_id IN ?
		AND (data_mapping->>'delivery_time_unix')::int >= ?
		AND (data_mapping->>'delivery_time_unix')::int <= ?
		GROUP BY site_id
	`
	if err := db.Raw(siteQuery, siteIDs, startUnix, endUnix).Scan(&siteOrderCounts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retrieve site order counts: " + err.Error(),
		})
		return
	}

	// Process site order counts
	for _, soc := range siteOrderCounts {
		site, exists := siteMap[soc.SiteID]
		if !exists {
			continue
		}

		siteSummary := SiteOrderSummary{
			SiteID:          soc.SiteID,
			SiteName:        site.Name,
			TotalOrders:     soc.TotalOrders,
			CompletedOrders: soc.CompletedOrders,
			TotalRevenue:    soc.TotalRevenue,
		}

		overviewData.SiteOrderSummary = append(overviewData.SiteOrderSummary, siteSummary)
	}

	c.JSON(http.StatusOK, HubOverviewResponse{
		Success: true,
		Data:    overviewData,
	})
}
