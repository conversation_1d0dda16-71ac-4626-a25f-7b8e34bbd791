// order-service/router/site.go
package router

import (
	"fmt"
	"net/http"
	"os"
	"time"

	brandutils "github.com/nexdorvn/nexpos-backend/golang-services/brand-service/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// checkOverlappingDateRanges checks if the given date range overlaps with any existing special working hours
// Returns true if there's an overlap, along with the overlapping record
func checkOverlappingDateRanges(db *gorm.DB, siteID, fromDate, toDate string, excludeID string) (bool, models.SiteSpecialWorkingHour, error) {
	// Parse input dates
	from, err := time.Parse("2006-01-02", fromDate)
	if err != nil {
		return false, models.SiteSpecialWorkingHour{}, fmt.Errorf("invalid from_date format: %v", err)
	}

	to, err := time.Parse("2006-01-02", toDate)
	if err != nil {
		return false, models.SiteSpecialWorkingHour{}, fmt.Errorf("invalid to_date format: %v", err)
	}

	// Check if from_date is after to_date
	if from.After(to) {
		return false, models.SiteSpecialWorkingHour{}, fmt.Errorf("from_date cannot be after to_date")
	}

	// Get all special working hours for this site
	var existingHours []models.SiteSpecialWorkingHour
	query := db.Where("site_id = ?", siteID)

	// Exclude the current record if updating
	if excludeID != "" {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Find(&existingHours).Error; err != nil {
		return false, models.SiteSpecialWorkingHour{}, err
	}
	// Check each existing record for overlap
	for _, existing := range existingHours {
		// Parse existing dates
		existingFrom, err := time.Parse("2006-01-02", existing.FromDate)
		if err != nil {
			continue
		}

		existingTo, err := time.Parse("2006-01-02", existing.ToDate)
		if err != nil {
			continue
		}

		// Check all possible overlap scenarios:
		// 1. New range completely contains existing range
		// 2. Existing range completely contains new range
		// 3. New range starts before existing range but ends during it
		// 4. New range starts during existing range but ends after it
		if (from.Unix() <= existingFrom.Unix() && to.Unix() >= existingTo.Unix()) || // New range contains existing
			(from.Unix() >= existingFrom.Unix() && to.Unix() <= existingTo.Unix()) || // Existing range contains new
			(from.Unix() < existingFrom.Unix() && to.Unix() > existingFrom.Unix() && to.Unix() <= existingTo.Unix()) || // New starts before, ends during
			(from.Unix() >= existingFrom.Unix() && from.Unix() < existingTo.Unix() && to.Unix() >= existingTo.Unix()) { // New starts during, ends after
			return true, existing, nil
		}
	}

	return false, models.SiteSpecialWorkingHour{}, nil
}

func GetStoreSpecialOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	specialWorkingHours := []models.SiteSpecialWorkingHour{}

	db.Find(&specialWorkingHours, "site_id = ?", siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    specialWorkingHours,
	})
}

// CreateStoreSpecialOpeningHours creates a new special opening hours entry
func CreateStoreSpecialOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Parse request
	var request struct {
		Name      string               `json:"name"`
		FromDate  string               `json:"from_date"` // Format: YYYY-MM-DD
		ToDate    string               `json:"to_date"`   // Format: YYYY-MM-DD
		OpenType  string               `json:"open_type"` // close_all_time, open_all_time, open_at_time
		Source    string               `json:"source"`    // shopee, shopee_fresh, grab, grab_mart
		OpenHours []models.SpecialHour `json:"open_hours,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Validate request
	if request.Name == "" || request.FromDate == "" || request.ToDate == "" || request.OpenType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "missing_required_fields",
		})
		return
	}

	if request.OpenType == models.OpenTypeCloseAllTime || request.OpenType == models.OpenTypeOpenAllTime {
		request.OpenHours = []models.SpecialHour{}
	} else if len(request.OpenHours) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "open_hours_required",
		})
		return
	}

	// Check for overlapping open hours
	for i := 0; i < len(request.OpenHours); i++ {
		for j := i + 1; j < len(request.OpenHours); j++ {
			// Parse time strings
			from1, err1 := time.Parse("15:04", request.OpenHours[i].Start)
			to1, err2 := time.Parse("15:04", request.OpenHours[i].End)
			from2, err3 := time.Parse("15:04", request.OpenHours[j].Start)
			to2, err4 := time.Parse("15:04", request.OpenHours[j].End)

			// Check for parsing errors
			if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_time_format",
				})
				return
			}

			// Validate that From is not after To
			if from1.After(to1) || from2.After(to2) {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_time_range",
				})
				return
			}

			// Check for overlap (covers all cases)
			if (from1.Before(to2) || from1.Equal(to2)) && (to1.After(from2) || to1.Equal(from2)) {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "overlapping_open_hours",
				})
				return
			}
		}
	}

	if len(request.OpenHours) > 3 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "max_3_open_hours",
		})
		return
	}

	// Validate OpenType
	if request.OpenType != models.OpenTypeCloseAllTime &&
		request.OpenType != models.OpenTypeOpenAllTime &&
		request.OpenType != models.OpenTypeOpenAtTime {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_open_type",
		})
		return
	}

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check for overlapping date ranges
	hasOverlap, overlappingHour, err := checkOverlappingDateRanges(db, siteID, request.FromDate, request.ToDate, "")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_date_range: " + err.Error(),
		})
		return
	}

	if hasOverlap {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": fmt.Sprintf("The date range (%s to %s) overlaps with an existing configuration: %s (%s to %s)",
				request.FromDate, request.ToDate, overlappingHour.Name, overlappingHour.FromDate, overlappingHour.ToDate),
		})
		return
	}

	// Create new special working hour
	specialWorkingHour := models.SiteSpecialWorkingHour{
		SiteID:    siteID,
		Source:    site.MainSource,
		Name:      request.Name,
		FromDate:  request.FromDate,
		ToDate:    request.ToDate,
		OpenType:  request.OpenType,
		OpenHours: request.OpenHours,
		SyncAt:    nil,
	}

	// Save to database
	if err := db.Create(&specialWorkingHour).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "failed_to_create_special_working_hour: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    specialWorkingHour,
	})
}

// UpdateStoreSpecialOpeningHours updates an existing special opening hours entry
func UpdateStoreSpecialOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	specialHourID := c.Param("id")

	// Parse request
	var request struct {
		Name      string               `json:"name"`
		FromDate  string               `json:"from_date"` // Format: YYYY-MM-DD
		ToDate    string               `json:"to_date"`   // Format: YYYY-MM-DD
		OpenType  string               `json:"open_type"` // close_all_time, open_all_time, open_at_time
		Source    string               `json:"source"`    // shopee, shopee_fresh, grab, grab_mart
		OpenHours []models.SpecialHour `json:"open_hours,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Validate request
	if request.Name == "" || request.FromDate == "" || request.ToDate == "" || request.OpenType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "missing_required_fields",
		})
		return
	}

	if request.OpenType == models.OpenTypeCloseAllTime || request.OpenType == models.OpenTypeOpenAllTime {
		request.OpenHours = []models.SpecialHour{}
	} else if len(request.OpenHours) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "open_hours_required",
		})
		return
	}

	if len(request.OpenHours) > 3 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "max_3_open_hours",
		})
		return
	}

	// Validate OpenType
	if request.OpenType != models.OpenTypeCloseAllTime &&
		request.OpenType != models.OpenTypeOpenAllTime &&
		request.OpenType != models.OpenTypeOpenAtTime {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_open_type",
		})
		return
	}

	// Check for overlapping open hours
	for i := 0; i < len(request.OpenHours); i++ {
		for j := i + 1; j < len(request.OpenHours); j++ {
			// Parse time strings
			from1, err1 := time.Parse("15:04", request.OpenHours[i].Start)
			to1, err2 := time.Parse("15:04", request.OpenHours[i].End)
			from2, err3 := time.Parse("15:04", request.OpenHours[j].Start)
			to2, err4 := time.Parse("15:04", request.OpenHours[j].End)

			// Check for parsing errors
			if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_time_format",
				})
				return
			}

			// Validate that From is not after To
			if from1.After(to1) || from2.After(to2) {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_time_range",
				})
				return
			}

			// Check for overlap (covers all cases)
			if (from1.Before(to2) || from1.Equal(to2)) && (to1.After(from2) || to1.Equal(from2)) {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "overlapping_open_hours",
				})
				return
			}
		}
	}

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Find the special working hour
	var specialWorkingHour models.SiteSpecialWorkingHour
	if err := db.First(&specialWorkingHour, "id = ? AND site_id = ?", specialHourID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "special_working_hour_not_found",
		})
		return
	}

	// Check for overlapping date ranges
	hasOverlap, overlappingHour, err := checkOverlappingDateRanges(db, siteID, request.FromDate, request.ToDate, specialHourID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_date_range: " + err.Error(),
		})
		return
	}

	if hasOverlap {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "date_range_overlap",
			"message": fmt.Sprintf("The date range (%s to %s) overlaps with an existing configuration: %s (%s to %s)",
				request.FromDate, request.ToDate, overlappingHour.Name, overlappingHour.FromDate, overlappingHour.ToDate),
		})
		return
	}

	// Update special working hour
	specialWorkingHour.Name = request.Name
	specialWorkingHour.FromDate = request.FromDate
	specialWorkingHour.ToDate = request.ToDate
	specialWorkingHour.OpenType = request.OpenType
	specialWorkingHour.Source = site.MainSource
	specialWorkingHour.OpenHours = request.OpenHours
	specialWorkingHour.SyncAt = nil

	// Save to database
	if err := db.Save(&specialWorkingHour).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "failed_to_update_special_working_hour: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    specialWorkingHour,
	})
}

// DeleteStoreSpecialOpeningHours deletes an existing special opening hours entry
func DeleteStoreSpecialOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	specialHourID := c.Param("id")

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Find the special working hour
	var specialWorkingHour models.SiteSpecialWorkingHour
	if err := db.First(&specialWorkingHour, "id = ? AND site_id = ?", specialHourID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "special_working_hour_not_found",
		})
		return
	}

	// Delete the special working hour
	if err := db.Delete(&specialWorkingHour).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "failed_to_delete_special_working_hour: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "special_working_hour_deleted",
	})
}

// SyncStoreSpecialOpeningHours syncs special opening hours from merchant platforms to the database
func SyncStoreSpecialOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check if merchant apps are enabled
	if os.Getenv("USE_MERCHANT_APPS") != "true" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	// Define merchant functions
	merchantFunctions := map[string]string{
		"shopee":       "shopee",
		"shopee_fresh": "shopee",
		"grab":         "grab",
		"grab_mart":    "grab",
	}

	// Initialize special working hours array
	specialWorkingHours := []models.SiteSpecialWorkingHour{}

	// Get special hours for each merchant
	for source, merchantType := range merchantFunctions {
		// Get token for the merchant
		tokenAccount, err := token.GetTokenBySite(db, site, source)
		if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
			continue
		}

		// Create merchant client
		merchantClient := merchant.NewMerchant(merchantType)
		if merchantClient == nil {
			continue
		}

		// Get store details to access special opening hours
		storeDetails, err := merchantClient.GetStore(&models.Token{
			AccessToken: tokenAccount.AccessToken,
			SiteID:      tokenAccount.SiteID,
		})
		if err != nil || storeDetails == nil || storeDetails.Raw == nil {
			continue
		}

		// Extract special opening hours from raw data
		var specialHours any
		switch source {
		case "shopee_food", "shopee_fresh":
			if rawData, ok := storeDetails.Raw.(map[string]any); ok {
				if data, ok := rawData["special_hours"].([]any); ok && len(data) > 0 {
					specialHours = data
				}
			}
		case "grab", "grab_mart":
			if rawData, ok := storeDetails.Raw.(map[string]any); ok {
				if merchant, ok := rawData["merchant"].(map[string]any); ok {
					if hours, ok := merchant["specialOpeningHours"].([]any); ok && len(hours) > 0 {
						specialHours = hours
					}
				}
			}
		}

		// Map special working hours to standard format
		if specialHours != nil {
			mappedHours := brandutils.MapSpecialWorkingHour(source, specialHours)
			if len(mappedHours) > 0 {
				// Set site ID for each mapped hour
				for i := range mappedHours {
					mappedHours[i].SiteID = siteID
					syncAt := time.Now()
					mappedHours[i].SyncAt = &syncAt
				}
				specialWorkingHours = append(specialWorkingHours, mappedHours...)
			}
		}
	}

	// Begin transaction
	tx := db.Begin()

	// Delete existing synced special working hours for this site
	if err := tx.Where("site_id = ? AND is_sync = ?", siteID, true).Delete(&models.SiteSpecialWorkingHour{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "failed_to_delete_existing_special_working_hours: " + err.Error(),
		})
		return
	}

	// Insert new special working hours
	if len(specialWorkingHours) > 0 {
		if err := tx.Create(&specialWorkingHours).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "failed_to_create_special_working_hours: " + err.Error(),
			})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "failed_to_commit_transaction: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    specialWorkingHours,
	})
}
