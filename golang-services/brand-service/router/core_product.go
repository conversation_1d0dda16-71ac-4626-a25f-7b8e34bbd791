package router

import (
	"math"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Generate a random string of specified length from the given charset
func generateRandomString(length int, charset string) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[r.Intn(len(charset))]
	}
	return string(b)
}

// GetCoreProducts godoc
// @Summary Get core products
// @Description Retrieves a list of core products for a brand
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Param source query string false "Filter by source"
// @Param category query string false "Filter by category"
// @Param available_for_sale query bool false "Filter by availability for sale"
// @Param search query string false "Search by name, code, or barcode"
// @Param types query string false "Filter by types (comma-separated)"
// @Success 200 {object} map[string]any "Returns paginated list of core products"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-products [get]
func GetCoreProducts(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Build query
	query := db.Model(&models.CoreProduct{}).Where("brand_id = ?", brandID)

	// Apply filters
	if source := c.Query("source"); source != "" {
		query = query.Where("source = ?", source)
	}
	if category := c.Query("category"); category != "" {
		query = query.Where("category = ?", category)
	}
	if availableForSale := c.Query("available_for_sale"); availableForSale != "" {
		query = query.Where("available_for_sale = ?", availableForSale == "true")
	}
	if types := c.QueryArray("types"); len(types) > 0 {
		query = query.Where("type IN ?", types)
	}
	if search := c.Query("search"); search != "" {
		query = query.Where(
			db.Where("name ILIKE ?", "%"+search+"%").
				Or("code ILIKE ?", "%"+search+"%").
				Or("bar_code ILIKE ?", "%"+search+"%"),
		)
	}

	// Count total items
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var coreProducts []models.CoreProduct
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&coreProducts).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       coreProducts,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// CreateCoreProduct godoc
// @Summary Create a core product
// @Description Creates a new core product for a brand
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param core_product body models.CoreProduct true "Core product data"
// @Success 200 {object} map[string]any "Returns the created core product"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Brand not found"
// @Router /brands/{brand_id}/core-products [post]
func CreateCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Parse request body
	var coreProduct models.CoreProduct
	if err := c.ShouldBindJSON(&coreProduct); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	coreProduct.BrandID = brandID

	// Generate code if not provided
	if coreProduct.Code == "" {
		coreProduct.Code = generateRandomString(8, "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	}

	// Check if code already exists
	var existingProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", coreProduct.Code, brandID).First(&existingProduct).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_code_existed",
		})
		return
	}

	// Validate ingredients if provided
	if len(coreProduct.Ingredients) > 0 {
		var ingredientCodes []string
		for _, ingredient := range coreProduct.Ingredients {
			ingredientCodes = append(ingredientCodes, ingredient.Code)
		}

		var existingIngredients []models.CoreProduct
		if err := db.Where("code IN ? AND status = ? AND brand_id = ?", ingredientCodes, "active", brandID).Find(&existingIngredients).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
			return
		}

		if len(existingIngredients) != len(coreProduct.Ingredients) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "ingredient_not_found",
			})
			return
		}
	}

	// Create core product
	if err := db.Create(&coreProduct).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// UpdateCoreProduct godoc
// @Summary Update a core product
// @Description Updates an existing core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Param core_product body models.CoreProduct true "Core product data"
// @Success 200 {object} map[string]any "Returns the updated core product"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [put]
func UpdateCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Parse request body
	var updateData models.CoreProduct
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Validate ingredients if provided
	if len(updateData.Ingredients) > 0 {
		var ingredientCodes []string
		for _, ingredient := range updateData.Ingredients {
			ingredientCodes = append(ingredientCodes, ingredient.Code)
		}

		var existingIngredients []models.CoreProduct
		if err := db.Where("code IN ? AND status = ? AND brand_id = ?", ingredientCodes, "active", brandID).Find(&existingIngredients).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
			return
		}

		if len(existingIngredients) != len(updateData.Ingredients) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "ingredient_not_found",
			})
			return
		}
	}

	// Find and update the core product
	var coreProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", code, brandID).First(&coreProduct).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "core_product_not_found",
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
		}
		return
	}

	// Update fields
	coreProduct.Name = updateData.Name
	coreProduct.Category = updateData.Category
	coreProduct.Unit = updateData.Unit
	coreProduct.Price = updateData.Price
	coreProduct.Images = updateData.Images
	coreProduct.SalePrice = updateData.SalePrice
	coreProduct.Weight = updateData.Weight
	coreProduct.Description = updateData.Description
	coreProduct.Height = updateData.Height
	coreProduct.AvailableForSale = updateData.AvailableForSale
	coreProduct.Type = updateData.Type
	coreProduct.Status = updateData.Status
	coreProduct.BarCode = updateData.BarCode
	coreProduct.Length = updateData.Length
	coreProduct.Ingredients = updateData.Ingredients
	coreProduct.Source = updateData.Source
	coreProduct.QuantityUnlimited = updateData.QuantityUnlimited

	// Save the updated core product
	if err := db.Save(&coreProduct).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// DeleteCoreProduct godoc
// @Summary Delete a core product
// @Description Deletes an existing core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [delete]
func DeleteCoreProduct(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Delete the core product
	result := db.Where("code = ? AND brand_id = ?", code, brandID).Delete(&models.CoreProduct{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductDetails godoc
// @Summary Get core product details
// @Description Retrieves details of a specific core product
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param code path string true "Core product code"
// @Success 200 {object} map[string]any "Returns the core product details"
// @Failure 404 {object} map[string]any "Core product not found"
// @Router /brands/{brand_id}/core-products/{code} [get]
func GetCoreProductDetails(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	code := c.Param("code")

	// Find the core product
	var coreProduct models.CoreProduct
	if err := db.Where("code = ? AND brand_id = ?", code, brandID).First(&coreProduct).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "core_product_not_found",
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "database_error",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    coreProduct,
	})
}

// GetCoreProductCategories godoc
// @Summary Get core product categories
// @Description Retrieves a list of core product categories for a brand
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product categories"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-categories [get]
func GetCoreProductCategories(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductCategory{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var categories []models.CoreProductCategory
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&categories).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       categories,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// CreateCoreProductCategory godoc
// @Summary Create a core product category
// @Description Creates a new core product category for a brand
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param category body models.CoreProductCategory true "Core product category data"
// @Success 200 {object} map[string]any "Returns the created core product category"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-categories [post]
func CreateCoreProductCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var category models.CoreProductCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	category.BrandID = brandID

	// Check if category already exists
	var existingCategory models.CoreProductCategory
	if err := db.Where("name = ? AND brand_id = ?", category.Name, brandID).First(&existingCategory).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_category_existed",
		})
		return
	}

	// Create category
	if err := db.Create(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// DeleteCoreProductCategory godoc
// @Summary Delete a core product category
// @Description Deletes an existing core product category
// @Tags core-product-categories
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /brands/{brand_id}/core-product-categories/{id} [delete]
func DeleteCoreProductCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the category
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductCategory{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_category_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductSources godoc
// @Summary Get core product sources
// @Description Retrieves a list of core product sources for a brand
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product sources"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-sources [get]
func GetCoreProductSources(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductSource{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var sources []models.CoreProductSource
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&sources).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	hasNextPage := page < totalPages
	hasPrevPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"data":          sources,
		"total":         total,
		"limit":         limit,
		"page":          page,
		"pages":         totalPages,
		"has_next_page": hasNextPage,
		"has_prev_page": hasPrevPage,
	})
}

// CreateCoreProductSource godoc
// @Summary Create a core product source
// @Description Creates a new core product source for a brand
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param source body models.CoreProductSource true "Core product source data"
// @Success 200 {object} map[string]any "Returns the created core product source"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-sources [post]
func CreateCoreProductSource(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var source models.CoreProductSource
	if err := c.ShouldBindJSON(&source); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	source.BrandID = brandID

	// Check if source already exists
	var existingSource models.CoreProductSource
	if err := db.Where("name = ? AND brand_id = ?", source.Name, brandID).First(&existingSource).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_source_existed",
		})
		return
	}

	// Create source
	if err := db.Create(&source).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    source,
	})
}

// DeleteCoreProductSource godoc
// @Summary Delete a core product source
// @Description Deletes an existing core product source
// @Tags core-product-sources
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Source ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Source not found"
// @Router /brands/{brand_id}/core-product-sources/{id} [delete]
func DeleteCoreProductSource(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the source
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductSource{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_source_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetCoreProductUnits godoc
// @Summary Get core product units
// @Description Retrieves a list of core product units for a brand
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} map[string]any "Returns paginated list of core product units"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-units [get]
func GetCoreProductUnits(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100 // Max 100 items per page
	}
	offset := (page - 1) * limit

	// Count total items
	var total int64
	if err := db.Model(&models.CoreProductUnit{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Get paginated results
	var units []models.CoreProductUnit
	if err := db.Where("brand_id = ?", brandID).Order("name ASC").Offset(offset).Limit(limit).Find(&units).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	hasNextPage := page < totalPages
	hasPrevPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"data":          units,
		"total":         total,
		"limit":         limit,
		"page":          page,
		"pages":         totalPages,
		"has_next_page": hasNextPage,
		"has_prev_page": hasPrevPage,
	})
}

// CreateCoreProductUnit godoc
// @Summary Create a core product unit
// @Description Creates a new core product unit for a brand
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param unit body models.CoreProductUnit true "Core product unit data"
// @Success 200 {object} map[string]any "Returns the created core product unit"
// @Failure 400 {object} map[string]any "Bad request"
// @Router /brands/{brand_id}/core-product-units [post]
func CreateCoreProductUnit(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse request body
	var unit models.CoreProductUnit
	if err := c.ShouldBindJSON(&unit); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Set brand ID
	unit.BrandID = brandID

	// Check if unit already exists
	var existingUnit models.CoreProductUnit
	if err := db.Where("name = ? AND brand_id = ?", unit.Name, brandID).First(&existingUnit).Error; err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "core_product_unit_existed",
		})
		return
	}

	// Create unit
	if err := db.Create(&unit).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    unit,
	})
}

// DeleteCoreProductUnit godoc
// @Summary Delete a core product unit
// @Description Deletes an existing core product unit
// @Tags core-product-units
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param id path string true "Unit ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Unit not found"
// @Router /brands/{brand_id}/core-product-units/{id} [delete]
func DeleteCoreProductUnit(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	id := c.Param("id")

	// Delete the unit
	result := db.Where("id = ? AND brand_id = ?", id, brandID).Delete(&models.CoreProductUnit{})
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_unit_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// GetImportTemplate godoc
// @Summary Get import template for core products
// @Description Returns a template structure for importing core products
// @Tags core-products
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any "Returns import template structure"
// @Router /brands/{brand_id}/core-products/import-template [get]
func GetImportTemplate(c *gin.Context) {
	// Define the template structure
	template := map[string]any{
		"Core Product": map[string]string{
			"Loại hàng (Bắt buộc)":    "",
			"Danh mục":                "",
			"Mã sản phẩm":             "",
			"Mã vạch":                 "",
			"Tên sản phẩm (Bắt buộc)": "",
			"Đơn vị tính":             "",
			"Nhà cung cấp":            "",
			"Giá vốn":                 "",
			"Giá bán":                 "",
			"Mô tả":                   "",
			"Hình ảnh":                "",
			"Trọng lượng":             "",
			"Chiều cao":               "",
			"Chiều dài":               "",
			"Cho phép bán (Bắt buộc)": "",
			"Trạng thái":              "",
			"Không giới hạn số lượng": "",
		},
		"Ingredient": map[string]string{
			"Mã":             "",
			"Mã nguyên liệu": "",
			"Số lượng":       "",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    template,
	})
}

// ImportCoreProducts godoc
// @Summary Import core products
// @Description Imports core products from a file
// @Tags core-products
// @Accept multipart/form-data
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param file formData file true "Excel file with core products data"
// @Success 200 {object} map[string]any "Returns import results"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /brands/{brand_id}/core-products/import [post]
func ImportCoreProducts(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "file_required",
		})
		return
	}

	// Open the file
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "file_open_error",
		})
		return
	}
	defer src.Close()

	// TODO: Implement Excel parsing and core product creation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Import functionality will be implemented with Excel parsing library",
	})
}

// ExportCoreProducts godoc
// @Summary Export core products
// @Description Exports core products to a file
// @Tags core-products
// @Accept json
// @Produce application/octet-stream
// @Param brand_id path string true "Brand ID"
// @Success 200 {file} application/octet-stream "Excel file with core products data"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /brands/{brand_id}/core-products/export [get]
func ExportCoreProducts(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Check if brand exists
	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Get all core products for the brand
	var coreProducts []models.CoreProduct
	if err := db.Where("brand_id = ?", brandID).Find(&coreProducts).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "database_error",
		})
		return
	}

	// TODO: Implement Excel generation

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Export functionality will be implemented with Excel generation library",
	})
}
