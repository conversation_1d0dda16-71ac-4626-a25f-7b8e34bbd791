package router

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
)

func GetHubPrinters(c *gin.Context) {
	if code := c.Query("code"); code == "" {
		c.JSON(200, gin.H{"success": true, "data": []any{}})
		return
	}

	cacheKey := fmt.Sprintf("hubs:%s", c.<PERSON>("code"))
	hub, err := redis.GetObj(cacheKey)

	if err != nil {
		db := middlewares.GetDB(c)
		var hubModel models.Hub
		if err := db.Where("code = ?", c.Query("code")).First(&hubModel).Error; err != nil {
			c.J<PERSON>(200, gin.H{"success": true, "data": []any{}})
			return
		}

		// Convert to map for caching
		hubData := map[string]any{
			"_id":        hubModel.ID,
			"name":       hubModel.Name,
			"code":       hubModel.Code,
			"printer_ip": hubModel.PrinterIP,
		}

		if err := redis.SetObj(cacheKey, hubData, time.Hour); err != nil {
			// Log error but continue
			fmt.Printf("Failed to cache hub data: %v\n", err)
		}

		hub = hubData
	}

	var printers []string
	if printerIP, ok := hub["printer_ip"].(string); ok && printerIP != "" {
		printers = strings.Split(printerIP, ";")
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": []map[string]any{
			{
				"printers": printers,
				"hub_id":   hub["_id"],
				"hub_name": hub["name"],
				"hub_code": hub["code"],
			},
		},
	})
}

func GetHubPrintList(c *gin.Context) {
	db := middlewares.GetDB(c)
	hubID := c.Param("hub_id")

	// Find prints from last 30 minutes
	thirtyMinutesAgo := time.Now().Add(-30 * time.Minute)
	var printList []models.PrintQueue

	if err := db.Where("hub_id = ? AND status = ? AND created_at > ?",
		hubID, "created", thirtyMinutesAgo).
		Find(&printList).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update status to printed if any prints found
	if len(printList) > 0 {
		if err := db.Model(&models.PrintQueue{}).
			Where("id IN ?", getPrintIDs(printList)).
			Update("status", "printed").Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    printList,
	})
}

func GetHubPrintListV2(c *gin.Context) {
	db := middlewares.GetDB(c)
	code := c.Query("code")

	// Find hub by code
	var hub models.Hub
	if err := db.Where("code = ?", code).First(&hub).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Hub not found",
		})
		return
	}

	// Find prints from last hour
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	var printList []models.PrintQueue

	if err := db.Where("hub_id = ? AND status = ? AND created_at > ?",
		hub.ID, "created", oneHourAgo).
		Find(&printList).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update status to printed if any prints found
	if len(printList) > 0 {
		if err := db.Model(&models.PrintQueue{}).
			Where("id IN ?", getPrintIDs(printList)).
			Update("status", "printed").Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    printList,
	})
}

// Helper function to extract print IDs
func getPrintIDs(prints []models.PrintQueue) []string {
	ids := make([]string, len(prints))
	for i, print := range prints {
		ids[i] = string(print.ID)
	}
	return ids
}
