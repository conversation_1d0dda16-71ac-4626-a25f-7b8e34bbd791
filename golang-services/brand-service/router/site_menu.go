package router

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Category represents a menu category with items and subcategories
type Category struct {
	ID            string     `json:"id"`
	Name          string     `json:"name"`
	Order         int64      `json:"order"`
	Active        bool       `json:"active"`
	Items         []Item     `json:"items"`
	SubCategories []Category `json:"sub_categories"`
}

// Item represents a menu item
type Item struct {
	ID           string    `json:"id"`
	Name         string    `json:"name"`
	Code         string    `json:"code"`
	Unit         string    `json:"unit"`
	Sources      []string  `json:"sources"`
	Description  string    `json:"description"`
	Images       []string  `json:"images"`
	ImagePreview string    `json:"image_preview"`
	Price        float64   `json:"price"`
	Order        int64     `json:"order"`
	Active       bool      `json:"active"`
	Channels     []Channel `json:"channels,omitempty"`
}

// Channel represents a sales channel for an item
type Channel struct {
	Channel    string         `json:"channel"`
	Active     bool           `json:"active"`
	Price      float64        `json:"price"`
	Name       string         `json:"name,omitempty"`
	Categories []string       `json:"categories,omitempty"`
	Additional map[string]any `json:"additional,omitempty"`
}

// OptionCategory represents an option category with option items
type OptionCategory struct {
	ID          string       `json:"id"`
	Name        string       `json:"name"`
	Order       int64        `json:"order"`
	Active      bool         `json:"active"`
	Rule        any          `json:"rule"`
	ItemIDs     []string     `json:"item_ids"`
	OptionItems []OptionItem `json:"option_items"`
}

// OptionItem represents an option item
type OptionItem struct {
	ID     string  `json:"id"`
	Name   string  `json:"name"`
	Code   string  `json:"code"`
	Price  float64 `json:"price"`
	Order  int64   `json:"order"`
	Active bool    `json:"active"`
}

// Menu represents a complete menu structure
type Menu struct {
	Categories       []Category       `json:"categories"`
	OptionCategories []OptionCategory `json:"option_categories"`
	Channels         []Channel        `json:"channels,omitempty"`
}

// buildMenu constructs a menu structure from the database models
func buildMenu(categories []models.SiteMenuCategory, items []models.SiteMenuItem, optionCategories []models.SiteMenuOption, optionItems []models.SiteMenuOptionItem) Menu {
	newCategories := []Category{}

	// First pass: create all categories
	for _, cat := range categories {
		category := Category{
			ID:            cat.ID,
			Name:          cat.Name,
			Order:         cat.Order,
			Active:        cat.Active,
			Items:         []Item{},
			SubCategories: []Category{},
		}

		// Add items to their categories
		for _, item := range items {
			menuItem := Item{
				ID:           item.ID,
				Name:         item.Name,
				Code:         item.Code,
				Unit:         item.Unit,
				Sources:      item.Sources,
				Description:  item.Description,
				Images:       item.Images,
				ImagePreview: item.ImagePreview,
				Price:        item.Price,
				Order:        item.Order,
				Active:       item.Active,
				Channels:     []Channel{}, // Will be populated later if needed
			}

			if item.CategoryID == category.ID {
				category.Items = append(category.Items, menuItem)
			}
		}

		newCategories = append(newCategories, category)
	}

	// Build option categories
	newOptionCategories := []OptionCategory{}

	for _, oc := range optionCategories {
		optionCategory := OptionCategory{
			ID:          oc.ID,
			Name:        oc.Name,
			Order:       oc.Order,
			Active:      oc.Active,
			Rule:        oc.Rule,
			ItemIDs:     oc.ItemIDs,
			OptionItems: []OptionItem{},
		}
		// Add option items to their option categories
		for _, oi := range optionItems {
			optionItem := OptionItem{
				ID:     oi.ID,
				Name:   oi.Name,
				Code:   oi.Code,
				Price:  oi.Price,
				Order:  oi.Order,
				Active: oi.Active,
			}

			if oi.OptionID == optionCategory.ID {
				optionCategory.OptionItems = append(optionCategory.OptionItems, optionItem)
			}
		}

		newOptionCategories = append(newOptionCategories, optionCategory)
	}

	return Menu{
		Categories:       newCategories,
		OptionCategories: newOptionCategories,
	}
}

// filterMenuByChannel filters the menu to only include items available on a specific channel
func filterMenuByChannel(menu Menu, channel string) Menu {
	// Filter categories and their items
	filteredCategories := []Category{}

	for _, category := range menu.Categories {
		// Filter items in this category
		filteredItems := []Item{}
		for _, item := range category.Items {
			hasChannel := false
			for _, ch := range item.Channels {
				if ch.Channel == channel && ch.Active {
					hasChannel = true
					break
				}
			}

			if hasChannel || len(item.Channels) == 0 { // Include if no channels specified
				filteredItems = append(filteredItems, item)
			}
		}

		// Filter subcategories
		filteredSubCategories := []Category{}
		for _, subCategory := range category.SubCategories {
			// Filter items in this subcategory
			filteredSubItems := []Item{}
			for _, item := range subCategory.Items {
				hasChannel := false
				for _, ch := range item.Channels {
					if ch.Channel == channel && ch.Active {
						hasChannel = true
						break
					}
				}

				if hasChannel || len(item.Channels) == 0 { // Include if no channels specified
					filteredSubItems = append(filteredSubItems, item)
				}
			}

			// Only include subcategory if it has items
			if len(filteredSubItems) > 0 {
				subCategory.Items = filteredSubItems
				filteredSubCategories = append(filteredSubCategories, subCategory)
			}
		}

		// Only include category if it has items or subcategories
		if len(filteredItems) > 0 || len(filteredSubCategories) > 0 {
			category.Items = filteredItems
			category.SubCategories = filteredSubCategories
			filteredCategories = append(filteredCategories, category)
		}
	}

	// Return filtered menu
	return Menu{
		Categories:       filteredCategories,
		OptionCategories: menu.OptionCategories, // Option categories are not filtered by channel
	}
}

// markMenuModified marks a site menu as modified
func markMenuModified(db *gorm.DB, siteID string) {
	// Find existing site menu or create a new one
	var siteMenu models.SiteMenu
	result := db.Where("site_id = ?", siteID).First(&siteMenu)

	if result.Error != nil {
		// Create new site menu if it doesn't exist
		now := time.Now()
		siteMenu = models.SiteMenu{
			SiteID:     siteID,
			ModifiedOn: &now,
			Channels:   []models.SiteMenuChannel{},
		}
		db.Create(&siteMenu)
	} else {
		// Update existing site menu
		now := time.Now()
		siteMenu.ModifiedOn = &now
		db.Save(&siteMenu)
	}
}

// GetMenu godoc
// @Summary Get site menu
// @Description Retrieves the menu for a site
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param channel query string false "Filter by channel"
// @Success 200 {object} map[string]any "Returns the site menu"
// @Failure 404 {object} map[string]any "Site not found"
// @Router /v2/sites/{site_id}/menu [get]
func GetMenu(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	channel := c.Query("channel")

	// Check if site exists
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Get all categories for the site
	var categories []models.SiteMenuCategory
	db.Where("site_id = ? AND active = ?", siteID, true).Order(`"order" ASC`).Find(&categories)

	// Get all items for the site
	var items []models.SiteMenuItem
	db.Where("site_id = ? AND active = ?", siteID, true).Order(`"order" ASC`).Find(&items)

	// Get all option categories for the site
	var optionCategories []models.SiteMenuOption
	db.Where("site_id = ? AND active = ?", siteID, true).Order(`"order" ASC`).Find(&optionCategories)

	// Get all option items for the site
	var optionItems []models.SiteMenuOptionItem
	db.Where("site_id = ? AND active = ?", siteID, true).Order(`"order" ASC`).Find(&optionItems)

	// Build the menu structure
	menu := buildMenu(categories, items, optionCategories, optionItems)

	// Filter by channel if specified
	if channel != "" {
		menu = filterMenuByChannel(menu, channel)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    menu,
	})
}

// CreateSiteCategory godoc
// @Summary Create a category in a site menu
// @Description Creates a new category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category body object true "Category data"
// @Success 200 {object} map[string]any "Returns the created category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Site not found"
// @Router /v2/sites/{site_id}/menu/categories [post]
func CreateSiteCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Check if site exists
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name     string `json:"name" binding:"required"`
		ParentID string `json:"parent_id"`
		Order    int64  `json:"order"`
		Active   bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// If parent_id is provided, check if it exists
	if request.ParentID != "" {
		var parentCategory models.SiteMenuCategory
		if err := db.First(&parentCategory, "id = ? AND site_id = ?", request.ParentID, siteID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "parent_category_not_found",
			})
			return
		}
	}

	// Set default order if not provided
	if request.Order == 0 {
		request.Order = time.Now().Unix()
	}

	// Create category
	category := models.SiteMenuCategory{
		SiteID:   siteID,
		Name:     request.Name,
		ParentID: request.ParentID,
		Order:    request.Order,
		Active:   request.Active,
	}

	if err := db.Create(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_category",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// UpdateSiteCategory godoc
// @Summary Update a category in a site menu
// @Description Updates an existing category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param category body object true "Category data"
// @Success 200 {object} map[string]any "Returns the updated category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id} [put]
func UpdateSiteCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")

	// Check if category exists
	var category models.SiteMenuCategory
	if err := db.First(&category, "id = ? AND site_id = ?", categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name     string `json:"name"`
		ParentID string `json:"parent_id"`
		Order    int64  `json:"order"`
		Active   bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// If parent_id is provided and changed, check if it exists
	if request.ParentID != "" && request.ParentID != category.ParentID {
		var parentCategory models.SiteMenuCategory
		if err := db.First(&parentCategory, "id = ? AND site_id = ?", request.ParentID, siteID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "parent_category_not_found",
			})
			return
		}
	}

	// Update category fields if provided
	if request.Name != "" {
		category.Name = request.Name
	}

	if request.ParentID != "" || request.ParentID == "" && category.ParentID != "" {
		category.ParentID = request.ParentID
	}

	if request.Order != 0 {
		category.Order = request.Order
	}

	// Only update active status if it's explicitly provided in the request
	if c.Request.Body != nil {
		category.Active = request.Active
	}

	// Save the updated category
	if err := db.Save(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_category",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// DeleteSiteCategory godoc
// @Summary Delete a category in a site menu
// @Description Deletes an existing category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id} [delete]
func DeleteSiteCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")

	// Check if category exists
	var category models.SiteMenuCategory
	if err := db.First(&category, "id = ? AND site_id = ?", categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Check if there are any subcategories that depend on this category
	var subcategoriesCount int64
	db.Model(&models.SiteMenuCategory{}).Where("parent_id = ?", categoryID).Count(&subcategoriesCount)

	if subcategoriesCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "category_has_subcategories",
		})
		return
	}

	// Check if there are any items in this category
	var itemsCount int64
	db.Model(&models.SiteMenuItem{}).Where("category_id = ?", categoryID).Count(&itemsCount)

	if itemsCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "category_has_items",
		})
		return
	}

	// Delete the category
	if err := db.Delete(&category).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_category",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// CreateSiteItem godoc
// @Summary Create an item in a site menu category
// @Description Creates a new item in a site menu category
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param item body object true "Item data"
// @Success 200 {object} map[string]any "Returns the created item"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Category not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id}/items [post]
func CreateSiteItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")

	// Check if site exists
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Check if category exists
	var category models.SiteMenuCategory
	if err := db.First(&category, "id = ? AND site_id = ?", categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "category_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name         string   `json:"name" binding:"required"`
		Code         string   `json:"code" binding:"required"`
		Unit         string   `json:"unit"`
		Sources      []string `json:"sources"`
		Description  string   `json:"description"`
		Images       []string `json:"images"`
		ImagePreview string   `json:"image_preview"`
		Price        float64  `json:"price"`
		Order        int64    `json:"order"`
		Active       bool     `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Check if core product exists
	var coreProduct models.CoreProduct
	if err := db.First(&coreProduct, "brand_id = ? AND code = ?", site.BrandID, request.Code).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "core_product_not_found",
		})
		return
	}

	// Check if core product type is allowed
	if coreProduct.Type == "nguyen_lieu" || coreProduct.Type == "ban_thanh_pham" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_menu_item_type_not_allowed",
		})
		return
	}

	// Set default order if not provided
	if request.Order == 0 {
		request.Order = time.Now().Unix()
	}

	// Create item
	item := models.SiteMenuItem{
		SiteID:       siteID,
		CategoryID:   categoryID,
		Name:         request.Name,
		Code:         request.Code,
		Unit:         request.Unit,
		Sources:      request.Sources,
		Description:  request.Description,
		Images:       request.Images,
		ImagePreview: request.ImagePreview,
		Price:        request.Price,
		Order:        request.Order,
		Active:       request.Active,
	}

	if err := db.Create(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_create_item",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    item,
	})
}

// UpdateSiteItem godoc
// @Summary Update an item in a site menu category
// @Description Updates an existing item in a site menu category
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Param item body object true "Item data"
// @Success 200 {object} map[string]any "Returns the updated item"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Item not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id}/items/{item_id} [put]
func UpdateSiteItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")
	itemID := c.Param("item_id")

	// Check if item exists
	var item models.SiteMenuItem
	if err := db.First(&item, "id = ? AND category_id = ? AND site_id = ?", itemID, categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "item_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Name         string   `json:"name"`
		Code         string   `json:"code"`
		Unit         string   `json:"unit"`
		Sources      []string `json:"sources"`
		Description  string   `json:"description"`
		Images       []string `json:"images"`
		ImagePreview string   `json:"image_preview"`
		Price        float64  `json:"price"`
		Order        int64    `json:"order"`
		Active       bool     `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update item fields if provided
	if request.Name != "" {
		item.Name = request.Name
	}

	if request.Code != "" && request.Code != item.Code {
		// Check if core product exists for the new code
		var site models.Site
		if err := db.First(&site, "id = ?", siteID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "site_not_found",
			})
			return
		}

		var coreProduct models.CoreProduct
		if err := db.First(&coreProduct, "brand_id = ? AND code = ?", site.BrandID, request.Code).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "core_product_not_found",
			})
			return
		}

		// Check if core product type is allowed
		if coreProduct.Type == "nguyen_lieu" || coreProduct.Type == "ban_thanh_pham" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "site_menu_item_type_not_allowed",
			})
			return
		}

		item.Code = request.Code
	}

	if request.Unit != "" {
		item.Unit = request.Unit
	}

	if request.Sources != nil {
		item.Sources = request.Sources
	}

	if request.Description != "" {
		item.Description = request.Description
	}

	if request.Images != nil {
		item.Images = request.Images
	}

	if request.ImagePreview != "" {
		item.ImagePreview = request.ImagePreview
	}

	if request.Price != 0 {
		item.Price = request.Price
	}

	if request.Order != 0 {
		item.Order = request.Order
	}

	// Only update active status if it's explicitly provided in the request
	if c.Request.Body != nil {
		item.Active = request.Active
	}

	// Save the updated item
	if err := db.Save(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_item",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    item,
	})
}

// DeleteSiteItem godoc
// @Summary Delete an item in a site menu category
// @Description Deletes an existing item in a site menu category
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Item not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id}/items/{item_id} [delete]
func DeleteSiteItem(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")
	itemID := c.Param("item_id")

	// Check if item exists
	var item models.SiteMenuItem
	if err := db.First(&item, "id = ? AND category_id = ? AND site_id = ?", itemID, categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "item_not_found",
		})
		return
	}

	// Delete the item
	if err := db.Delete(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_delete_item",
		})
		return
	}

	// Mark menu as modified
	markMenuModified(db, siteID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// UpdateItemSaleChannel godoc
// @Summary Update an item's sale channel
// @Description Updates the sale channel for a menu item
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Param channel body object true "Channel data"
// @Success 200 {object} map[string]any "Success response"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Item not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id}/items/{item_id}/channel [post]
func UpdateItemSaleChannel(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")
	categoryID := c.Param("category_id")
	itemID := c.Param("item_id")

	// Check if item exists
	var item models.SiteMenuItem
	if err := db.First(&item, "id = ? AND category_id = ? AND site_id = ?", itemID, categoryID, siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "item_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Channel    string         `json:"channel" binding:"required"`
		Active     bool           `json:"active"`
		Price      float64        `json:"price"`
		Name       string         `json:"name"`
		Categories []string       `json:"categories"`
		Additional map[string]any `json:"additional"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update or add the channel
	channelFound := false
	for i, ch := range item.Channels {
		if ch.Channel == request.Channel {
			// Update existing channel
			item.Channels[i].Active = request.Active
			item.Channels[i].Price = request.Price
			item.Channels[i].Name = request.Name
			item.Channels[i].Categories = request.Categories
			item.Channels[i].Additional = request.Additional
			channelFound = true
			break
		}
	}

	if !channelFound {
		// Add new channel
		item.Channels = append(item.Channels, models.SiteMenuItemChannel{
			Channel:    request.Channel,
			Active:     request.Active,
			Price:      request.Price,
			Name:       request.Name,
			Categories: request.Categories,
			Additional: request.Additional,
		})
	}

	// Save the updated item
	if err := db.Save(&item).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_item_channel",
		})
		return
	}

	// Mark menu as modified and mark the specific channel as modified
	var siteMenu models.SiteMenu
	result := db.Where("site_id = ?", siteID).First(&siteMenu)
	if result.Error == nil {
		siteMenu.MarkChannelModified(request.Channel)
		db.Save(&siteMenu)
	} else {
		markMenuModified(db, siteID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    item,
	})
}

// BulkUpdateItemSaleChannel godoc
// @Summary Bulk update items' sale channels
// @Description Updates the sale channels for multiple menu items
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param items body array true "Items data with channels"
// @Success 200 {object} map[string]any "Success response"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Site not found"
// @Router /v2/sites/{site_id}/menu/bulk-update-channel [post]
func BulkUpdateItemSaleChannel(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Check if site exists
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Parse request body
	var request struct {
		Channel    string         `json:"channel" binding:"required"`
		Active     bool           `json:"active"`
		Price      float64        `json:"price"`
		Name       string         `json:"name"`
		Categories []string       `json:"categories"`
		Additional map[string]any `json:"additional"`
		ItemIDs    []string       `json:"item_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Get all items to update
	var items []models.SiteMenuItem
	if err := db.Where("site_id = ? AND id IN ?", siteID, request.ItemIDs).Find(&items).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_fetch_items",
		})
		return
	}

	// Update each item's channel
	for i := range items {
		channelFound := false
		for j, ch := range items[i].Channels {
			if ch.Channel == request.Channel {
				// Update existing channel
				items[i].Channels[j].Active = request.Active
				items[i].Channels[j].Price = request.Price
				items[i].Channels[j].Name = request.Name
				items[i].Channels[j].Categories = request.Categories
				items[i].Channels[j].Additional = request.Additional
				channelFound = true
				break
			}
		}

		if !channelFound {
			// Add new channel
			items[i].Channels = append(items[i].Channels, models.SiteMenuItemChannel{
				Channel:    request.Channel,
				Active:     request.Active,
				Price:      request.Price,
				Name:       request.Name,
				Categories: request.Categories,
				Additional: request.Additional,
			})
		}

		// Save the updated item
		if err := db.Save(&items[i]).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "failed_to_update_items",
			})
			return
		}
	}

	// Mark menu as modified and mark the specific channel as modified
	var siteMenu models.SiteMenu
	result := db.Where("site_id = ?", siteID).First(&siteMenu)
	if result.Error == nil {
		siteMenu.MarkChannelModified(request.Channel)
		db.Save(&siteMenu)
	} else {
		markMenuModified(db, siteID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"count":   len(items),
	})
}

// DeleteItemSaleChannel godoc
// @Summary Delete an item's sale channel
// @Description Deletes a sale channel for a menu item
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param category_id path string true "Category ID"
// @Param item_id path string true "Item ID"
// @Param channel path string true "Channel"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Item or channel not found"
// @Router /v2/sites/{site_id}/menu/categories/{category_id}/items/{item_id}/channel/{channel} [delete]
func DeleteItemSaleChannel(c *gin.Context) {
	// Parameters will be used in the actual implementation
	// _ = middlewares.GetDB(c)
	// _ = c.Param("site_id")
	// _ = c.Param("category_id")
	// _ = c.Param("item_id")
	// _ = c.Param("channel")

	// TODO: Implement site menu item sale channel deletion
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Site menu item sale channel deletion will be implemented",
	})
}

// PublishMenu godoc
// @Summary Publish a site menu
// @Description Publishes the menu for a site
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Site not found"
// @Router /v2/sites/{site_id}/menu/publish [post]
func PublishMenu(c *gin.Context) {
	// Parameters will be used in the actual implementation
	// _ = middlewares.GetDB(c)
	// _ = c.Param("site_id")

	// TODO: Implement site menu publishing
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Site menu publishing will be implemented",
	})
}

// CreateSiteOptionCategory godoc
// @Summary Create an option category in a site menu
// @Description Creates a new option category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param option_category body object true "Option category data"
// @Success 200 {object} map[string]any "Returns the created option category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Site not found"
// @Router /v2/sites/{site_id}/menu/option-categories [post]
func CreateSiteOptionCategory(c *gin.Context) {
	// Parameters will be used in the actual implementation
	// _ = middlewares.GetDB(c)
	// _ = c.Param("site_id")

	// TODO: Implement site menu option category model and creation
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Site menu option category creation will be implemented",
	})
}

// UpdateSiteOptionCategory godoc
// @Summary Update an option category in a site menu
// @Description Updates an existing option category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param id path string true "Option Category ID"
// @Param option_category body object true "Option category data"
// @Success 200 {object} map[string]any "Returns the updated option category"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Option category not found"
// @Router /v2/sites/{site_id}/menu/option-categories/{id} [put]
func UpdateSiteOptionCategory(c *gin.Context) {
	// Parameters will be used in the actual implementation
	// _ = middlewares.GetDB(c)
	// _ = c.Param("site_id")
	// _ = c.Param("id")

	// TODO: Implement site menu option category model and update
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Site menu option category update will be implemented",
	})
}

// DeleteSiteOptionCategory godoc
// @Summary Delete an option category in a site menu
// @Description Deletes an existing option category in a site menu
// @Tags site-menu
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param id path string true "Option Category ID"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Option category not found"
// @Router /v2/sites/{site_id}/menu/option-categories/{id} [delete]
func DeleteSiteOptionCategory(c *gin.Context) {
	// Parameters will be used in the actual implementation
	// _ = middlewares.GetDB(c)
	// _ = c.Param("site_id")
	// _ = c.Param("id")

	// TODO: Implement site menu option category model and deletion
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Site menu option category deletion will be implemented",
	})
}
