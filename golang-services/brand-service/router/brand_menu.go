package router

import (
	"fmt"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/menu"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func GetBrandMenuSiteAppMenu(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var request struct {
		SiteIDs []string `json:"site_ids"`
		Sources []string `json:"sources"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Default sources if not provided
	if len(request.Sources) == 0 {
		request.Sources = []string{"grab", "grab_mart", "shopee_food", "shopee_fresh", "gojek", "be"}
	}

	// Get brand and menu
	var brand models.Brand
	if err := db.First(&brand, brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).First(&brandMenu).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Brand menu not found",
		})
		return
	}

	// Get sites
	query := db.Model(&models.Site{}).Where("brand_id = ?", brandID)
	if len(request.SiteIDs) > 0 {
		query = query.Where("id IN ?", request.SiteIDs)
	}
	var sites []models.Site
	if err := query.Limit(50).Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Collect menus from all sources
	allSiteMenu := models.BrandMenu{
		Categories:       brandMenu.Categories,
		OptionCategories: brandMenu.OptionCategories,
	}

	merchantApps := getMerchantApps()

	for _, site := range sites {
		for _, source := range request.Sources {
			if merchantFunc, ok := merchantApps[source]; ok {
				token, err := models.GetTokenBySite(db, &site, source)
				if err != nil {
					continue
				}

				merchantMenu, err := merchantFunc(token)
				if err != nil {
					continue
				}

				mappedMenu := mapping.MapMenu(source, merchantMenu)
				allSiteMenu = menu.ComposeSiteMenuItem(allSiteMenu, mappedMenu.Categories)
				allSiteMenu = menu.ComposeSiteMenuOptionItem(allSiteMenu, mappedMenu.OptionCategories)
			}
		}
	}

	// Update brand menu
	if err := db.Model(&brandMenu).Updates(map[string]any{
		"categories":        allSiteMenu.Categories,
		"option_categories": allSiteMenu.OptionCategories,
	}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"categories":        allSiteMenu.Categories,
			"option_categories": allSiteMenu.OptionCategories,
		},
	})
}

func GetBrandMenuAllSiteAppMenu(c *gin.Context) {
	brandID := c.Param("brand_id")
	fileKey := fmt.Sprintf("brand_menu/%d/all_site_menu_items.xlsx", time.Now().Unix())

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fmt.Sprintf("https://storage.googleapis.com/nexpos-files/%s", fileKey),
	})

	// Trigger background export
	go PublishExportBrandMenu(fileKey, brandID)
}

func PostBrandMenuFromAllSiteApps(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var request struct {
		Categories       []models.Category       `json:"categories"`
		OptionCategories []models.OptionCategory `json:"option_categories"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(request.Categories) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"categories":        []models.Category{},
				"option_categories": []models.OptionCategory{},
			},
		})
		return
	}

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).First(&brandMenu).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Brand menu not found",
		})
		return
	}

	brandMenu.Categories = request.Categories
	brandMenu.OptionCategories = request.OptionCategories

	if err := db.Save(&brandMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"categories":        brandMenu.Categories,
			"option_categories": brandMenu.OptionCategories,
		},
	})
}

func PublishExportBrandMenu(fileKey string, brandID string) error {
	// Implement your background export logic here
	// This should be integrated with your message queue system
	return nil
}

// Helper functions

func getMerchantApps() map[string]func(*models.TokenAccount) (map[string]any, error) {
	// TODO:
	return map[string]func(*models.TokenAccount) (map[string]any, error){
		// "grab":         GetGrabMenu,
		// "grab_mart":    GetGrabMartMenu,
		// "shopee":       GetShopeeMenu,
		// "shopee_fresh": GetShopeeMenu,
		// "gojek":        GetGojekMenu,
		// "be":           GetBeMenu,
		// "haravan":      GetHaravanMenu,
	}
}

func GetBrandMenuCategoryList(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).FirstOrCreate(&brandMenu, models.BrandMenu{
		BrandID:          brandID,
		Categories:       []models.Category{},
		OptionCategories: []models.OptionCategory{},
	}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brandMenu,
	})
}

func CreateUpdateBrandMenuCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var request struct {
		Categories       []models.Category       `json:"categories"`
		OptionCategories []models.OptionCategory `json:"option_categories"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Ensure IDs exist
	for i := range request.Categories {
		if request.Categories[i].ID == "" {
			request.Categories[i].ID = uuid.New().String()
		}
		for j := range request.Categories[i].Items {
			if request.Categories[i].Items[j].ID == "" {
				request.Categories[i].Items[j].ID = uuid.New().String()
			}
		}
		for j := range request.Categories[i].SubCategories {
			if request.Categories[i].SubCategories[j].ID == "" {
				request.Categories[i].SubCategories[j].ID = uuid.New().String()
			}
			for k := range request.Categories[i].SubCategories[j].Items {
				if request.Categories[i].SubCategories[j].Items[k].ID == "" {
					request.Categories[i].SubCategories[j].Items[k].ID = uuid.New().String()
				}
			}
		}
	}

	for i := range request.OptionCategories {
		if request.OptionCategories[i].ID == "" {
			request.OptionCategories[i].ID = uuid.New().String()
		}
	}

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).FirstOrCreate(&brandMenu).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := db.Model(&brandMenu).Updates(models.BrandMenu{
		Categories:       request.Categories,
		OptionCategories: request.OptionCategories,
	}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brandMenu,
	})
}

func DeleteBrandMenuCategory(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")
	categoryID := c.Param("category_id")

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).First(&brandMenu).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Brand menu not found",
		})
		return
	}

	// Find and remove the category
	foundIndex := -1
	for i, cat := range brandMenu.Categories {
		if cat.ID == categoryID {
			foundIndex = i
			break
		}
	}

	if foundIndex >= 0 {
		brandMenu.Categories = append(brandMenu.Categories[:foundIndex], brandMenu.Categories[foundIndex+1:]...)
		if err := db.Save(&brandMenu).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brandMenu.Categories,
	})
}

func ApplyBrandMenuForAllSites(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", brandID).First(&brandMenu).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Brand menu not found",
		})
		return
	}

	var sites []models.Site
	if err := db.Where("brand_id = ?", brandID).Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update existing site menus
	if err := db.Model(&models.SiteMenuGroup{}).
		Where("site_id IN ?", getSiteIDs(sites)).
		Updates(map[string]any{
			"categories":        brandMenu.Categories,
			"option_categories": brandMenu.OptionCategories,
		}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create menus for sites without one
	var existingSiteIDs []string
	db.Model(&models.SiteMenuGroup{}).
		Where("site_id IN ?", getSiteIDs(sites)).
		Pluck("site_id", &existingSiteIDs)

	for _, site := range sites {
		if !utils.Contains(existingSiteIDs, site.ID) {
			siteMenu := models.SiteMenuGroup{
				SiteID:           site.ID,
				Categories:       brandMenu.Categories,
				OptionCategories: brandMenu.OptionCategories,
			}
			if err := db.Create(&siteMenu).Error; err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   err.Error(),
				})
				return
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// Helper functions
func getSiteIDs(sites []models.Site) []string {
	ids := make([]string, len(sites))
	for i, site := range sites {
		ids[i] = site.ID
	}
	return ids
}
