package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/gmap"

	"github.com/gin-gonic/gin"
)

func GetMapSuggestAddressesV2(c *gin.Context) {
	address := c.Query("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Address parameter is required",
		})
		return
	}

	addresses, err := gmap.GetSuggestionAddresses(address)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    addresses,
	})
}
