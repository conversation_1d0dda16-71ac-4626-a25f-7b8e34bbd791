package router

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/service"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
)

// GetAllHubsWorkingShift handles the API endpoint for getting all hubs working shifts
func GetAllHubsWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	user := middlewares.GetUser(c)

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	result, err := workingShiftService.GetAllHubsWorkingShift(user.ID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetLastWorkingShift handles the API endpoint for getting the last working shift for a hub
func GetLastWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	hubID := c.Param("hub_id")

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	result, err := workingShiftService.GetLastWorkingShift(hubID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetWorkingShifts handles the API endpoint for getting working shifts for a hub
func GetWorkingShifts(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	hubID := c.Param("hub_id")

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	result, err := workingShiftService.GetWorkingShifts(hubID, page, limit, status)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"data":       result.Data,
		"totalPages": result.TotalPages,
		"page":       result.Page,
		"limit":      result.PageSize,
		"total":      result.Total,
	})
}

// CreateWorkingShift handles the API endpoint for creating a working shift
func CreateWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	user := middlewares.GetUser(c)
	hubID := c.Param("hub_id")

	// Parse request body
	var request struct {
		InitialAmount float64 `json:"initial_amount"`
		Note          string  `json:"note"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	result, err := workingShiftService.CreateWorkingShift(hubID, user.ID, request.InitialAmount, request.Note)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// UpdateWorkingShift handles the API endpoint for updating a working shift
func UpdateWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	user := middlewares.GetUser(c)
	hubID := c.Param("hub_id")
	shiftID := c.Param("shift_id")

	// Parse request body
	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	result, err := workingShiftService.UpdateWorkingShift(hubID, shiftID, user.ID, request)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// DeleteWorkingShift handles the API endpoint for deleting a working shift
func DeleteWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	hubID := c.Param("hub_id")
	shiftID := c.Param("shift_id")

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	err := workingShiftService.DeleteWorkingShift(hubID, shiftID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Working shift deleted successfully",
	})
}

// GetWorkingShiftsReport handles the API endpoint for getting a report of working shifts
func GetWorkingShiftsReport(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	hubIDs := c.QueryArray("hub_ids")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	// If no hub_ids provided, use user's hubs
	if len(hubIDs) == 0 {
		hubIDs = user.Hubs
	}

	// Parse time parameters
	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid start_time format",
		})
		return
	}

	endTime, err := time.Parse(time.RFC3339, endTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid end_time format",
		})
		return
	}

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	fileURL, err := workingShiftService.GetWorkingShiftsReport(hubIDs, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": map[string]string{
			"url": fileURL,
		},
	})
}

// PrintWorkingShift handles the API endpoint for printing a working shift
func PrintWorkingShift(c *gin.Context) {
	db := middlewares.GetDB(c)
	redisClient := middlewares.GetRedis(c)
	hubID := c.Param("hub_id")
	shiftID := c.Param("shift_id")

	workingShiftService := service.NewWorkingShiftService(db, redisClient)
	err := workingShiftService.PrintWorkingShift(hubID, shiftID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get the URLs (in a real implementation, these would be generated by the print service)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": []string{
			"https://example.com/shift_report.png",
			"https://example.com/shift_payment.png",
		},
	})
}
