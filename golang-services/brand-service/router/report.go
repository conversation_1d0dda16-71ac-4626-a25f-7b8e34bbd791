package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/service"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

// GetReport godoc
// @Summary Get hub report
// @Description Retrieves a report for a specific hub within a time range
// @Tags reports
// @Accept json
// @Produce json
// @Param hub_id query string true "Hub ID"
// @Param start_time query string true "Start time (RFC3339 format)"
// @Param end_time query string true "End time (RFC3339 format)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports [get]
func GetReport(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user has access to the hub if a specific hub is requested
	if query.HubID != "" && query.HubID != "all" && !contains(user.Hubs, query.HubID) {
		// c.JSON(http.StatusForbidden, gin.H{
		// 	"success": false,
		// 	"error":   "You don't have permission to access this hub",
		// })
		// return
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Generate report
	report, err := reportService.GetReport(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return report
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// Helper function to check if a slice contains a string
func contains(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}
