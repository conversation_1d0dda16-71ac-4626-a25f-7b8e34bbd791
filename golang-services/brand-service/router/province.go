package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"gorm.io/gorm"
)

// GetProvinces handles the request to get all provinces
// This endpoint returns a list of all provinces in the system
func GetProvinces(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	var provinces []models.Province
	if err := db.Find(&provinces).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to retrieve provinces",
		})
		return
	}

	// Map to response format
	response := make([]models.ProvinceResponse, len(provinces))
	for i, province := range provinces {
		response[i] = models.ProvinceResponse{
			ID:   province.Code, // Using code as ID to match Node.js implementation
			Name: province.Name,
		}
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}
