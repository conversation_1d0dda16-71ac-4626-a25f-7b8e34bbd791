package router

import (
	"fmt"
	"math"
	"net/http"
	"regexp"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"gorm.io/gorm"
)

// GetBrandList godoc
// @Summary Get list of brands
// @Description Retrieves a paginated list of brands with optional filtering
// @Tags brands
// @Accept json
// @Produce json
// @Param name query string false "Filter brands by name"
// @Param selected query string false "Include a specific brand by ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(100) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 500 {object} map[string]any
// @Router /brands [get]
func GetBrandList(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	query := db.Model(&models.Brand{})

	brandIDs := user.Brands

	query = query.Where("id = ANY(?)", pq.Array(brandIDs))

	if name := c.Query("name"); name != "" {
		escapedName := regexp.QuoteMeta(name)
		query = query.Where("name ILIKE ?", fmt.Sprintf("%%%s%%", escapedName))
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	if limit > 100 {
		limit = 100
	}
	offset := (page - 1) * limit

	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to count brands",
		})
		return
	}

	brands := []models.Brand{}
	if err := query.Order("name ASC").Offset(offset).Limit(limit).Find(&brands).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve brands",
		})
		return
	}

	if selectedID := c.Query("selected"); selectedID != "" {
		var selectedBrand models.Brand
		if err := db.First(&selectedBrand, "id = ?", selectedID).Error; err == nil {
			brands = append(brands, selectedBrand)
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PaginationResponse{
		Success:    true,
		Data:       brands,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}

	c.JSON(http.StatusOK, response)
}

// GetBrandDetail godoc
// @Summary Get a brand
// @Description Retrieves a brand by ID
// @Tags brands
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any
// @Failure 404 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /brands/{brand_id} [get]
func GetBrandDetail(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brand,
	})
}

// CreateBrand godoc
// @Summary Create a new brand
// @Description Creates a new brand and associates it with the current user
// @Tags brands
// @Accept json
// @Produce json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /brands [post]
func CreateBrand(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	var brand models.Brand
	if err := c.ShouldBindJSON(&brand); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&brand).Error; err != nil {
			return err
		}

		if user != nil {
			// Add brand to user's brands array
			user.Brands = append(user.Brands, brand.ID)
			if err := tx.Model(&models.User{}).Where("id = ?", user.ID).Update("brands", user.Brands).Error; err != nil {
				return err
			}

			// Create a BrandStaff record with owner role
			// brandStaff := models.BrandStaff{
			// 	BrandID: brand.ID,
			// 	UserID:  user.ID,
			// 	Role:    models.StaffRoleOwner,
			// }

			// if err := tx.Create(&brandStaff).Error; err != nil {
			// 	return err
			// }
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brand,
	})
}

// UpdateBrand godoc
// @Summary Update a brand
// @Description Updates an existing brand by ID
// @Tags brands
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 403 {object} map[string]any "Forbidden - User does not have required role"
// @Failure 404 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /brands/{brand_id} [put]
func UpdateBrand(c *gin.Context) {
	db := middlewares.GetDB(c)
	// user := middlewares.GetUser(c)
	brandID := c.Param("brand_id")

	// FIXME: Uncomment this block when brand permissions are implemented
	// if user != nil {
	// 	// Get user's brand permissions
	// 	brandRoles := middlewares.GetBrandPermissions(c)

	// 	// Check if user is owner or manager of this brand
	// 	role, exists := brandRoles[brandID]
	// 	if !exists || (role != models.StaffRoleOwner && role != models.StaffRoleManager) {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"success": false,
	// 			"error":   "You must be an owner or manager to update this brand",
	// 		})
	// 		return
	// 	}
	// }

	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	if err := c.ShouldBindJSON(&brand); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := db.Save(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brand,
	})
}

// DeleteBrand godoc
// @Summary Delete a brand
// @Description Deletes a brand by ID
// @Tags brands
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Success 200 {object} map[string]any
// @Failure 403 {object} map[string]any "Forbidden - User does not have required role"
// @Failure 404 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /brands/{brand_id} [delete]
func DeleteBrand(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)
	brandID := c.Param("brand_id")

	// Check if user has permission to delete this brand
	// FIXME: Uncomment this block when brand permissions are implemented
	if user != nil {
		// Get user's brand permissions
		// brandRoles := middlewares.GetBrandPermissions(c)

		// Check if user is owner or manager of this brand
		// role, exists := brandRoles[brandID]
		// if !exists || (role != models.StaffRoleOwner && role != models.StaffRoleManager) {
		// 	c.JSON(http.StatusForbidden, gin.H{
		// 		"success": false,
		// 		"error":   "You must be an owner or manager to delete this brand",
		// 	})
		// 	return
		// }
	}

	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	if err := db.Delete(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// UpdateBanners godoc
// @Summary Update brand banners
// @Description Updates the banners for a specific brand
// @Tags brands
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param banners body map[string]any true "Banners object with array of BrandBanner"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 404 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /brands/{brand_id}/banners [put]
func UpdateBanners(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var requestBody struct {
		Banners []models.BrandBanner `json:"banners"`
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var brand models.Brand
	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	brand.Banners = requestBody.Banners
	if err := db.Save(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    brand.Banners,
	})
}
