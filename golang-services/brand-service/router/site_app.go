// order-service/router/site.go
package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

func SiteActiveMoMoAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var req models.MoMoToken
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}
	if req.PartnerCode == "" || req.AccessKey == "" || req.SecretKey == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Update site token
	site.MoMoToken = &models.JSONField[models.MoMoToken]{
		Data: req,
	}

	// Save site
	if err = db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request" + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

func SiteDeActiveMoMoAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Update site token
	site.MoMoToken = nil

	// Save site
	if err := db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}
