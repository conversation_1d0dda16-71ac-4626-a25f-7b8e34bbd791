// package router

// import (
// 	"fmt"
// 	"math"
// 	"net/http"
// 	"nexpos/sdk/middlewares"
// 	"nexpos/sdk/models"
// 	"nexpos/sdk/utils"
// 	"nexpos/sdk/utils/email"
// 	"regexp"
// 	"strconv"
// 	"time"

// 	"github.com/gin-gonic/gin"
// 	"github.com/samber/lo"
// 	"golang.org/x/crypto/bcrypt"
// 	"gorm.io/gorm"
// )

// // HubStaffResponse represents a staff member with user information
// type HubStaffResponse struct {
// 	ID        string    `json:"id"`
// 	HubID     string    `json:"hub_id"`
// 	UserID    string    `json:"user_id"`
// 	CreatedAt time.Time `json:"created_at"`
// 	UpdatedAt time.Time `json:"updated_at"`
// 	Role      string    `json:"role"`

// 	// User information
// 	Username string `json:"username"`
// 	Email    string `json:"email"`
// 	Phone    string `json:"phone"`
// 	Name     string `json:"name"`
// 	IsActive bool   `json:"is_active"`
// }

// // GetHubStaffList retrieves a paginated list of hub staff with user information for a specific hub
// func GetHubStaffList(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	user := middlewares.GetUser(c)
// 	hubID := c.Param("hub_id")

// 	// Check if hub exists
// 	var hub models.Hub
// 	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "Hub not found",
// 		})
// 		return
// 	}

// 	// Check authorization
// 	if user != nil {
// 		// Get user's hub permissions
// 		hubRoles := middlewares.GetHubPermissions(c)

// 		// Check if user has any role for this hub
// 		_, exists := hubRoles[hubID]
// 		if !exists {
// 			c.JSON(http.StatusForbidden, gin.H{
// 				"success": false,
// 				"error":   "Unauthorized to access this hub's staff",
// 			})
// 			return
// 		}
// 	}

// 	// Find all hub staff records for this hub
// 	var hubStaffs []models.HubStaff
// 	query := db.Where("hub_id = ?", hubID)

// 	// Pagination
// 	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
// 	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
// 	if limit > 100 {
// 		limit = 100
// 	}
// 	offset := (page - 1) * limit

// 	// Count total records
// 	var total int64
// 	if err := query.Model(&models.HubStaff{}).Count(&total).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to count staff",
// 		})
// 		return
// 	}

// 	// Get paginated hub staff records
// 	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&hubStaffs).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve staff",
// 		})
// 		return
// 	}

// 	// Prepare response with user information
// 	staffResponses := make([]HubStaffResponse, 0, len(hubStaffs))
// 	for _, staff := range hubStaffs {
// 		var user models.User
// 		// Skip if user not found (shouldn't happen with proper foreign key constraints)
// 		if err := db.First(&user, "id = ?", staff.UserID).Error; err != nil {
// 			continue
// 		}

// 		// Apply name filter if provided
// 		if name := c.Query("name"); name != "" {
// 			escapedName := regexp.QuoteMeta(name)
// 			if !regexp.MustCompile(fmt.Sprintf("(?i)%s", escapedName)).MatchString(user.Name) {
// 				continue
// 			}
// 		}

// 		// Don't include password in response
// 		user.Password = ""

// 		// Create response object
// 		staffResponse := HubStaffResponse{
// 			ID:        staff.ID,
// 			HubID:     staff.HubID,
// 			UserID:    staff.UserID,
// 			CreatedAt: staff.CreatedAt,
// 			UpdatedAt: staff.UpdatedAt,
// 			Role:      string(staff.Role),
// 			Username:  user.Username,
// 			Email:     user.Email,
// 			Phone:     user.Phone,
// 			Name:      user.Name,
// 			IsActive:  user.IsActive,
// 		}

// 		staffResponses = append(staffResponses, staffResponse)
// 	}

// 	// If we applied name filter after fetching records, we need to adjust pagination
// 	if name := c.Query("name"); name != "" {
// 		total = int64(len(staffResponses))
// 	}

// 	totalPages := int(math.Ceil(float64(total) / float64(limit)))

// 	response := models.PaginationResponse{
// 		Success:    true,
// 		Data:       staffResponses,
// 		Total:      total,
// 		Page:       page,
// 		PageSize:   limit,
// 		TotalPages: totalPages,
// 		HasNext:    page < totalPages,
// 		HasPrev:    page > 1,
// 	}

// 	c.JSON(http.StatusOK, response)
// }

// // GetHubStaffDetail retrieves a single hub staff by ID
// func GetHubStaffDetail(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	hubID := c.Param("hub_id")
// 	staffID := c.Param("staff_id")

// 	// First check if the hub staff record exists
// 	var hubStaff models.HubStaff
// 	if err := db.Where("id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 		// If not found by ID, try to find by user_id (for backward compatibility)
// 		if err := db.Where("user_id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 			// As a fallback, check if there's a user with this ID that has the hub in their hubs array
// 			var user models.User
// 			if err := db.Where("id = ? AND hubs @> ?::jsonb", staffID, fmt.Sprintf(`["%s"]`, hubID)).First(&user).Error; err != nil {
// 				c.JSON(http.StatusNotFound, gin.H{
// 					"success": false,
// 					"error":   "Staff not found",
// 				})
// 				return
// 			}

// 			// Create a HubStaff record for this user if it doesn't exist
// 			hubStaff = models.HubStaff{
// 				HubID:  hubID,
// 				UserID: user.ID,
// 				Role:   models.StaffRoleStaff, // Default role
// 			}

// 			if err := db.Create(&hubStaff).Error; err != nil {
// 				c.JSON(http.StatusInternalServerError, gin.H{
// 					"success": false,
// 					"error":   "Failed to create hub staff record",
// 				})
// 				return
// 			}
// 		}
// 	}

// 	// Now get the user information
// 	var user models.User
// 	if err := db.First(&user, "id = ?", hubStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "User not found",
// 		})
// 		return
// 	}

// 	// Don't include password in response
// 	user.Password = ""

// 	// Create response object
// 	staffResponse := HubStaffResponse{
// 		ID:        hubStaff.ID,
// 		HubID:     hubStaff.HubID,
// 		UserID:    hubStaff.UserID,
// 		CreatedAt: hubStaff.CreatedAt,
// 		UpdatedAt: hubStaff.UpdatedAt,
// 		Role:      string(hubStaff.Role),
// 		Username:  user.Username,
// 		Email:     user.Email,
// 		Phone:     user.Phone,
// 		Name:      user.Name,
// 		IsActive:  user.IsActive,
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    staffResponse,
// 	})
// }

// // HubStaffRequest represents the request body for creating a hub staff
// type HubStaffRequest struct {
// 	Email string           `json:"email" binding:"required"`
// 	Phone string           `json:"phone"`
// 	Name  string           `json:"name" binding:"required"`
// 	Role  models.StaffRole `json:"role" binding:"required"`
// }

// func createHubUser(db *gorm.DB, hubID string, request HubStaffRequest) error {
// 	var staff models.User
// 	var hub models.Hub
// 	if err := db.First(&hub, "id = ?", hubID).Error; err != nil {
// 		return fmt.Errorf("hub_not_found")
// 	}

// 	if db.Where("username = ?", request.Email).First(&staff).Error == nil {
// 		return fmt.Errorf("user_is_existed")
// 	}

// 	roleName := string(request.Role)
// 	if roleName != string(models.StaffRoleManager) && roleName != string(models.StaffRoleStaff) {
// 		return fmt.Errorf("role must be either 'manager' or 'staff'")
// 	}

// 	var role models.Role
// 	if err := db.Where(models.Role{Name: roleName}).
// 		Attrs(models.Role{
// 			Permissions: []string{roleName},
// 		}).
// 		FirstOrCreate(&role).Error; err != nil {
// 		return err
// 	}

// 	tempPassword := utils.GenRandomPassword(8)
// 	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(tempPassword), bcrypt.DefaultCost)
// 	if err != nil {
// 		return err
// 	}

// 	user := models.User{
// 		Username: request.Email,
// 		Name:     request.Name,
// 		Email:    request.Email,
// 		Phone:    request.Phone,
// 		Address:  "",
// 		Password: string(hashedPassword),
// 		// RoleID removed as it's now in HubStaff
// 		CreatedBy: nil,
// 		IsActive:  false,
// 		Hubs:      []string{hubID}, // Store as jsonb array
// 	}

// 	if err := db.Create(&user).Error; err != nil {
// 		return err
// 	}

// 	// Create HubStaff record to link user to hub with role information
// 	hubStaff := models.HubStaff{
// 		HubID:  hubID,
// 		UserID: user.ID,
// 		Role:   request.Role,
// 	}

// 	if err := db.Create(&hubStaff).Error; err != nil {
// 		db.Delete(&user)
// 		return err
// 	}

// 	email.SendEmail(request.Email, fmt.Sprintf(`Welcome to our hub: %s`, hub.Name), "invite_member", map[string]any{
// 		"hub_name":      hub.Name,
// 		"email":         request.Email,
// 		"temp_password": tempPassword,
// 		"url":           utils.GetEnv("PORTAL_WEB_URL", ""),
// 	})
// 	return nil
// }

// // CreateHubStaff creates a new hub staff
// func CreateHubStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	hubID := c.Param("hub_id")

// 	var staffRequest HubStaffRequest
// 	if err := c.ShouldBindJSON(&staffRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	if err := createHubUser(db, hubID, staffRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

// // BulkCreateHubStaff creates multiple hub staff members at once
// func BulkCreateHubStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	hubID := c.Param("hub_id")

// 	type BulkStaffRequest struct {
// 		Emails []string `json:"emails" binding:"required"`
// 		Role   string   `json:"role" binding:"required"`
// 	}

// 	var request BulkStaffRequest
// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	if request.Role != string(models.StaffRoleManager) && request.Role != string(models.StaffRoleStaff) {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "role must be either 'manager' or 'staff'",
// 		})
// 		return
// 	}

// 	if len(request.Emails) == 0 {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "emails array cannot be empty",
// 		})
// 		return
// 	}

// 	createdStaff := []string{}
// 	canNotCreatedStaff := []string{}
// 	for _, email := range request.Emails {
// 		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
// 		if !emailRegex.MatchString(email) {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   fmt.Sprintf("invalid email format: %s", email),
// 			})
// 			return
// 		}

// 		staffRequest := HubStaffRequest{
// 			Email: email,
// 			Phone: "",
// 			Name:  email,
// 			Role:  models.StaffRole(request.Role),
// 		}
// 		if err := createHubUser(db, hubID, staffRequest); err != nil {
// 			canNotCreatedStaff = append(canNotCreatedStaff, email)
// 			continue
// 		}

// 		createdStaff = append(createdStaff, email)
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data": gin.H{
// 			"created":     createdStaff,
// 			"not_created": canNotCreatedStaff,
// 		},
// 	})
// }

// // UpdateHubStaff updates an existing hub staff
// func UpdateHubStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	hubID := c.Param("hub_id")
// 	staffID := c.Param("staff_id")

// 	// First find the HubStaff record
// 	var hubStaff models.HubStaff
// 	if err := db.Where("id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 		// If not found by ID, try to find by user_id (for backward compatibility)
// 		if err := db.Where("user_id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 			// As a fallback, check if there's a user with this ID that has the hub in their hubs array
// 			var user models.User
// 			if err := db.Where("id = ? AND hubs @> ?::jsonb", staffID, fmt.Sprintf(`["%s"]`, hubID)).First(&user).Error; err != nil {
// 				c.JSON(http.StatusNotFound, gin.H{
// 					"success": false,
// 					"error":   "Staff not found",
// 				})
// 				return
// 			}

// 			// Create a HubStaff record for this user if it doesn't exist
// 			hubStaff = models.HubStaff{
// 				HubID:  hubID,
// 				UserID: user.ID,
// 			}

// 			if err := db.Create(&hubStaff).Error; err != nil {
// 				c.JSON(http.StatusInternalServerError, gin.H{
// 					"success": false,
// 					"error":   "Failed to create hub staff record",
// 				})
// 				return
// 			}
// 		}
// 	}

// 	// Get the user associated with this hub staff
// 	var user models.User
// 	if err := db.First(&user, "id = ?", hubStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "User not found",
// 		})
// 		return
// 	}

// 	// Define a struct for the update request
// 	type UpdateRequest struct {
// 		Name     string           `json:"name"`
// 		Email    string           `json:"email"`
// 		Phone    string           `json:"phone"`
// 		Role     models.StaffRole `json:"role"`
// 		RoleID   string           `json:"role_id"`
// 		IsActive *bool            `json:"is_active"`
// 	}

// 	var updateRequest UpdateRequest
// 	if err := c.ShouldBindJSON(&updateRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Update user information
// 	userUpdates := models.User{
// 		ID: user.ID,
// 	}

// 	if updateRequest.Name != "" {
// 		userUpdates.Name = updateRequest.Name
// 	}

// 	if updateRequest.Email != "" {
// 		userUpdates.Email = updateRequest.Email
// 		userUpdates.Username = updateRequest.Email
// 	}

// 	if updateRequest.Phone != "" {
// 		userUpdates.Phone = updateRequest.Phone
// 	}

// 	if updateRequest.IsActive != nil {
// 		userUpdates.IsActive = *updateRequest.IsActive
// 	}

// 	// Ensure user has this hub in their hubs array
// 	if !utils.Contains(user.Hubs, hubID) {
// 		userUpdates.Hubs = append(user.Hubs, hubID)
// 	} else {
// 		userUpdates.Hubs = user.Hubs
// 	}

// 	// Update the user
// 	if err := db.Model(&user).Updates(userUpdates).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Update HubStaff role information
// 	hubStaffUpdates := models.HubStaff{
// 		ID: hubStaff.ID,
// 	}

// 	// Validate role if provided
// 	if updateRequest.Role != "" {
// 		if updateRequest.Role != models.StaffRoleManager && updateRequest.Role != models.StaffRoleStaff {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   "Role must be either 'manager' or 'staff'",
// 			})
// 			return
// 		}
// 		hubStaffUpdates.Role = updateRequest.Role
// 	}

// 	// Validate roleID if provided
// 	if updateRequest.RoleID != "" {
// 		var role models.Role
// 		if err := db.First(&role, "id = ?", updateRequest.RoleID).Error; err != nil {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   "Invalid role",
// 			})
// 			return
// 		}
// 	}

// 	// Update the hub staff
// 	if err := db.Model(&hubStaff).Updates(hubStaffUpdates).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Get updated hub staff with user info
// 	if err := db.First(&hubStaff, "id = ?", hubStaff.ID).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve updated staff",
// 		})
// 		return
// 	}

// 	if err := db.First(&user, "id = ?", hubStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve updated user",
// 		})
// 		return
// 	}

// 	// Don't include password in response
// 	user.Password = ""

// 	// Create response object
// 	staffResponse := HubStaffResponse{
// 		ID:        hubStaff.ID,
// 		HubID:     hubStaff.HubID,
// 		UserID:    hubStaff.UserID,
// 		CreatedAt: hubStaff.CreatedAt,
// 		UpdatedAt: hubStaff.UpdatedAt,
// 		Role:      string(hubStaff.Role),
// 		Username:  user.Username,
// 		Email:     user.Email,
// 		Phone:     user.Phone,
// 		Name:      user.Name,
// 		IsActive:  user.IsActive,
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    staffResponse,
// 	})
// }

// // DeleteHubStaff deletes a hub staff
// func DeleteHubStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	hubID := c.Param("hub_id")
// 	staffID := c.Param("staff_id")

// 	// First find the HubStaff record
// 	var hubStaff models.HubStaff
// 	if err := db.Where("id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 		// If not found by ID, try to find by user_id (for backward compatibility)
// 		if err := db.Where("user_id = ? AND hub_id = ?", staffID, hubID).First(&hubStaff).Error; err != nil {
// 			// As a fallback, check if there's a user with this ID that has the hub in their hubs array
// 			var user models.User
// 			if err := db.Where("id = ? AND hubs @> ?::jsonb", staffID, fmt.Sprintf(`["%s"]`, hubID)).First(&user).Error; err != nil {
// 				c.JSON(http.StatusNotFound, gin.H{
// 					"success": false,
// 					"error":   "Staff not found",
// 				})
// 				return
// 			}

// 			// Create a HubStaff record for this user if it doesn't exist
// 			hubStaff = models.HubStaff{
// 				HubID:  hubID,
// 				UserID: user.ID,
// 				Role:   models.StaffRoleStaff, // Default role
// 			}

// 			if err := db.Create(&hubStaff).Error; err != nil {
// 				c.JSON(http.StatusInternalServerError, gin.H{
// 					"success": false,
// 					"error":   "Failed to create hub staff record",
// 				})
// 				return
// 			}
// 		}
// 	}

// 	// Get the user associated with this hub staff
// 	var user models.User
// 	if err := db.First(&user, "id = ?", hubStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "User not found",
// 		})
// 		return
// 	}

// 	// Delete the HubStaff record
// 	if err := db.Delete(&hubStaff).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to delete hub staff record",
// 		})
// 		return
// 	}

// 	// Update the user's hubs array
// 	updatedHubs := lo.Without(user.Hubs, hubID)
// 	if len(updatedHubs) == 0 && len(user.Brands) == 0 { // Check both hubs and brands
// 		// If user has no more hubs or brands, delete the user
// 		if err := db.Delete(&user).Error; err != nil {
// 			c.JSON(http.StatusInternalServerError, gin.H{
// 				"success": false,
// 				"error":   err.Error(),
// 			})
// 			return
// 		}
// 	} else {
// 		// Otherwise, just update the hubs array
// 		if err := db.Model(&user).Update("hubs", updatedHubs).Error; err != nil {
// 			c.JSON(http.StatusInternalServerError, gin.H{
// 				"success": false,
// 				"error":   err.Error(),
// 			})
// 			return
// 		}
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

// dummy function to avoid unused import error
package router

func hubstaff() {
}
