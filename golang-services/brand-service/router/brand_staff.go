// package router

// import (
// 	"fmt"
// 	"math"
// 	"net/http"
// 	"nexpos/sdk/middlewares"
// 	"nexpos/sdk/models"
// 	"nexpos/sdk/utils"
// 	"nexpos/sdk/utils/email"
// 	"regexp"
// 	"strconv"
// 	"time"

// 	"github.com/gin-gonic/gin"
// 	"github.com/samber/lo"
// 	"golang.org/x/crypto/bcrypt"
// 	"gorm.io/gorm"
// )

// // StaffResponse represents a staff member with user information
// type StaffResponse struct {
// 	ID        string    `json:"id"`
// 	BrandID   string    `json:"brand_id"`
// 	UserID    string    `json:"user_id"`
// 	CreatedAt time.Time `json:"created_at"`
// 	UpdatedAt time.Time `json:"updated_at"`
// 	Role      string    `json:"role"`

// 	// User information
// 	Username string `json:"username"`
// 	Email    string `json:"email"`
// 	Phone    string `json:"phone"`
// 	Name     string `json:"name"`
// 	IsActive bool   `json:"is_active"`
// }

// // GetBrandStaffList retrieves a paginated list of brand staff with user information for a specific brand
// func GetBrandStaffList(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	user := middlewares.GetUser(c)
// 	brandID := c.Param("brand_id")

// 	// Check if brand exists
// 	var brand models.Brand
// 	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "Brand not found",
// 		})
// 		return
// 	}

// 	// Check authorization
// 	if user != nil {
// 		// Get user's brand permissions
// 		brandRoles := middlewares.GetBrandPermissions(c)

// 		// Check if user has any role for this brand
// 		_, exists := brandRoles[brandID]
// 		if !exists {
// 			c.JSON(http.StatusForbidden, gin.H{
// 				"success": false,
// 				"error":   "Unauthorized to access this brand's staff",
// 			})
// 			return
// 		}
// 	}

// 	// Find all brand staff records for this brand
// 	brandStaffs := []models.BrandStaff{}
// 	query := db.Where("brand_id = ?", brandID)

// 	// Pagination
// 	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
// 	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
// 	if limit > 100 {
// 		limit = 100
// 	}
// 	offset := (page - 1) * limit

// 	// Count total records
// 	var total int64
// 	if err := query.Model(&models.BrandStaff{}).Count(&total).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to count staff",
// 		})
// 		return
// 	}

// 	// Get paginated brand staff records
// 	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&brandStaffs).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve staff",
// 		})
// 		return
// 	}

// 	// Prepare response with user information
// 	staffResponses := make([]StaffResponse, 0, len(brandStaffs))
// 	for _, staff := range brandStaffs {
// 		var user models.User
// 		// Skip if user not found (shouldn't happen with proper foreign key constraints)
// 		if err := db.First(&user, "id = ?", staff.UserID).Error; err != nil {
// 			continue
// 		}

// 		// Apply name filter if provided
// 		if name := c.Query("name"); name != "" {
// 			escapedName := regexp.QuoteMeta(name)
// 			if !regexp.MustCompile(fmt.Sprintf("(?i)%s", escapedName)).MatchString(user.Name) {
// 				continue
// 			}
// 		}

// 		// Don't include password in response
// 		user.Password = ""

// 		// Create response object
// 		staffResponse := StaffResponse{
// 			ID:        staff.ID,
// 			BrandID:   staff.BrandID,
// 			UserID:    staff.UserID,
// 			CreatedAt: staff.CreatedAt,
// 			UpdatedAt: staff.UpdatedAt,
// 			Role:      string(staff.Role),
// 			Username:  user.Username,
// 			Email:     user.Email,
// 			Phone:     user.Phone,
// 			Name:      user.Name,
// 			IsActive:  user.IsActive,
// 		}

// 		staffResponses = append(staffResponses, staffResponse)
// 	}

// 	// If we applied name filter after fetching records, we need to adjust pagination
// 	if name := c.Query("name"); name != "" {
// 		total = int64(len(staffResponses))
// 	}

// 	totalPages := int(math.Ceil(float64(total) / float64(limit)))

// 	response := models.PaginationResponse{
// 		Success:    true,
// 		Data:       staffResponses,
// 		Total:      total,
// 		Page:       page,
// 		PageSize:   limit,
// 		TotalPages: totalPages,
// 		HasNext:    page < totalPages,
// 		HasPrev:    page > 1,
// 	}

// 	c.JSON(http.StatusOK, response)
// }

// // GetBrandStaffDetail retrieves a single brand staff by ID
// func GetBrandStaffDetail(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	brandID := c.Param("brand_id")
// 	staffID := c.Param("staff_id")

// 	// First check if the brand staff record exists
// 	var brandStaff models.BrandStaff
// 	if err := db.Where("id = ? AND brand_id = ?", staffID, brandID).First(&brandStaff).Error; err != nil {
// 		// If not found by ID, try to find by user_id (for backward compatibility)
// 		if err := db.Where("user_id = ? AND brand_id = ?", staffID, brandID).First(&brandStaff).Error; err != nil {
// 			// As a fallback, check if there's a user with this ID that has the brand in their brands array
// 			var user models.User
// 			if err := db.Where("id = ? AND ? = ANY (brands)", staffID, brandID).First(&user).Error; err != nil {
// 				c.JSON(http.StatusNotFound, gin.H{
// 					"success": false,
// 					"error":   "Staff not found",
// 				})
// 				return
// 			}

// 			// Create a BrandStaff record for this user if it doesn't exist
// 			brandStaff = models.BrandStaff{
// 				BrandID: brandID,
// 				UserID:  user.ID,
// 			}

// 			if err := db.Create(&brandStaff).Error; err != nil {
// 				c.JSON(http.StatusInternalServerError, gin.H{
// 					"success": false,
// 					"error":   "Failed to create brand staff record",
// 				})
// 				return
// 			}
// 		}
// 	}

// 	// Now get the user information
// 	var user models.User
// 	if err := db.First(&user, "id = ?", brandStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "User not found",
// 		})
// 		return
// 	}

// 	// Don't include password in response
// 	user.Password = ""

// 	// Create response object
// 	staffResponse := StaffResponse{
// 		ID:        brandStaff.ID,
// 		BrandID:   brandStaff.BrandID,
// 		UserID:    brandStaff.UserID,
// 		CreatedAt: brandStaff.CreatedAt,
// 		UpdatedAt: brandStaff.UpdatedAt,
// 		Role:      string(brandStaff.Role),
// 		Username:  user.Username,
// 		Email:     user.Email,
// 		Phone:     user.Phone,
// 		Name:      user.Name,
// 		IsActive:  user.IsActive,
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    staffResponse,
// 	})
// }

// // BrandStaffRequest represents the request body for creating a brand staff
// type BrandStaffRequest struct {
// 	Email string           `json:"email" binding:"required"`
// 	Phone string           `json:"phone"`
// 	Name  string           `json:"name" binding:"required"`
// 	Role  models.StaffRole `json:"role" binding:"required"`
// }

// func createBrandUser(db *gorm.DB, brandID string, request BrandStaffRequest) error {
// 	var staff models.User
// 	var brand models.Brand
// 	if err := db.First(&brand, "id = ?", brandID).Error; err != nil {
// 		return fmt.Errorf("brand_not_found")
// 	}

// 	if db.Where("username = ?", request.Email).First(&staff).Error == nil {
// 		return fmt.Errorf("user_is_existed")
// 	}

// 	roleName := string(request.Role)
// 	if roleName != string(models.StaffRoleManager) && roleName != string(models.StaffRoleStaff) {
// 		return fmt.Errorf("role must be either 'manager' or 'staff'")
// 	}

// 	var role models.Role
// 	if err := db.Where(models.Role{Name: roleName}).
// 		Attrs(models.Role{
// 			Permissions: []string{roleName},
// 		}).
// 		FirstOrCreate(&role).Error; err != nil {
// 		return err
// 	}

// 	tempPassword := utils.GenRandomPassword(8)
// 	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(tempPassword), bcrypt.DefaultCost)
// 	if err != nil {
// 		return err
// 	}

// 	user := models.User{
// 		Username: request.Email,
// 		Name:     request.Name,
// 		Email:    request.Email,
// 		Phone:    request.Phone,
// 		Address:  "",
// 		Password: string(hashedPassword),
// 		// RoleID removed as it's now in BrandStaff
// 		CreatedBy: nil,
// 		IsActive:  false,
// 		Brands:    []string{brandID},
// 	}

// 	if err := db.Create(&user).Error; err != nil {
// 		return err
// 	}

// 	// Create BrandStaff record to link user to brand with role information
// 	brandStaff := models.BrandStaff{
// 		BrandID: brandID,
// 		UserID:  user.ID,
// 		Role:    request.Role,
// 	}

// 	if err := db.Create(&brandStaff).Error; err != nil {
// 		db.Delete(&user)
// 		return err
// 	}

// 	email.SendEmail(request.Email, fmt.Sprintf(`Welcome to our brand: %s`, brand.Name), "invite_member", map[string]any{
// 		"brand_name":    brand.Name,
// 		"email":         request.Email,
// 		"temp_password": tempPassword,
// 		"url":           utils.GetEnv("PORTAL_WEB_URL", ""),
// 	})
// 	return nil
// }

// // CreateBrandStaff creates a new brand staff
// func CreateBrandStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	brandID := c.Param("brand_id")

// 	var staffRequest BrandStaffRequest
// 	if err := c.ShouldBindJSON(&staffRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	if err := createBrandUser(db, brandID, staffRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

// // BulkCreateBrandStaff creates multiple brand staff members at once
// func BulkCreateBrandStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	brandID := c.Param("brand_id")

// 	type BulkStaffRequest struct {
// 		Emails []string `json:"emails" binding:"required"`
// 		Role   string   `json:"role" binding:"required"`
// 	}

// 	var request BulkStaffRequest
// 	if err := c.ShouldBindJSON(&request); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	if request.Role != string(models.StaffRoleManager) && request.Role != string(models.StaffRoleStaff) {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "role must be either 'manager' or 'staff'",
// 		})
// 		return
// 	}

// 	if len(request.Emails) == 0 {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   "emails array cannot be empty",
// 		})
// 		return
// 	}

// 	createdStaff := []string{}
// 	canNotCreatedStaff := []string{}
// 	for _, email := range request.Emails {
// 		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
// 		if !emailRegex.MatchString(email) {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   fmt.Sprintf("invalid email format: %s", email),
// 			})
// 			return
// 		}

// 		staffRequest := BrandStaffRequest{
// 			Email: email,
// 			Phone: "",
// 			Name:  email,
// 			Role:  models.StaffRole(request.Role),
// 		}
// 		if err := createBrandUser(db, brandID, staffRequest); err != nil {
// 			canNotCreatedStaff = append(canNotCreatedStaff, email)
// 			continue
// 		}

// 		createdStaff = append(createdStaff, email)
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data": gin.H{
// 			"created":     createdStaff,
// 			"not_created": canNotCreatedStaff,
// 		},
// 	})
// }

// // UpdateBrandStaff updates an existing brand staff
// func UpdateBrandStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	brandID := c.Param("brand_id")
// 	staffID := c.Param("staff_id")

// 	// First find the BrandStaff record
// 	var brandStaff models.BrandStaff
// 	if err := db.Where("id = ? AND brand_id = ?", staffID, brandID).First(&brandStaff).Error; err != nil {
// 		// If not found by ID, try to find by user_id (for backward compatibility)
// 		if err := db.Where("user_id = ? AND brand_id = ?", staffID, brandID).First(&brandStaff).Error; err != nil {
// 			// As a fallback, check if there's a user with this ID that has the brand in their brands array
// 			var user models.User
// 			if err := db.Where("id = ? AND ? = ANY (brands)", staffID, brandID).First(&user).Error; err != nil {
// 				c.JSON(http.StatusNotFound, gin.H{
// 					"success": false,
// 					"error":   "Staff not found",
// 				})
// 				return
// 			}

// 			// Create a BrandStaff record for this user if it doesn't exist
// 			brandStaff = models.BrandStaff{
// 				BrandID: brandID,
// 				UserID:  user.ID,
// 			}

// 			if err := db.Create(&brandStaff).Error; err != nil {
// 				c.JSON(http.StatusInternalServerError, gin.H{
// 					"success": false,
// 					"error":   "Failed to create brand staff record",
// 				})
// 				return
// 			}
// 		}
// 	}

// 	// Get the user associated with this brand staff
// 	var user models.User
// 	if err := db.First(&user, "id = ?", brandStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "User not found",
// 		})
// 		return
// 	}

// 	// Define a struct for the update request
// 	type UpdateRequest struct {
// 		Name     string           `json:"name"`
// 		Email    string           `json:"email"`
// 		Phone    string           `json:"phone"`
// 		Role     models.StaffRole `json:"role"`
// 		RoleID   string           `json:"role_id"`
// 		IsActive *bool            `json:"is_active"`
// 	}

// 	var updateRequest UpdateRequest
// 	if err := c.ShouldBindJSON(&updateRequest); err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Update user information
// 	userUpdates := models.User{
// 		ID: user.ID,
// 	}

// 	if updateRequest.Name != "" {
// 		userUpdates.Name = updateRequest.Name
// 	}

// 	if updateRequest.Email != "" {
// 		userUpdates.Email = updateRequest.Email
// 		userUpdates.Username = updateRequest.Email
// 	}

// 	if updateRequest.Phone != "" {
// 		userUpdates.Phone = updateRequest.Phone
// 	}

// 	if updateRequest.IsActive != nil {
// 		userUpdates.IsActive = *updateRequest.IsActive
// 	}

// 	// Ensure user has this brand in their brands array
// 	if !utils.Contains(user.Brands, brandID) {
// 		userUpdates.Brands = append(user.Brands, brandID)
// 	} else {
// 		userUpdates.Brands = user.Brands
// 	}

// 	// Update the user
// 	if err := db.Model(&user).Updates(userUpdates).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Update BrandStaff role information
// 	brandStaffUpdates := models.BrandStaff{
// 		ID: brandStaff.ID,
// 	}

// 	// Validate role if provided
// 	if updateRequest.Role != "" {
// 		if updateRequest.Role != models.StaffRoleManager && updateRequest.Role != models.StaffRoleStaff {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   "Role must be either 'manager' or 'staff'",
// 			})
// 			return
// 		}
// 		brandStaffUpdates.Role = updateRequest.Role
// 	}

// 	// Validate roleID if provided
// 	if updateRequest.RoleID != "" {
// 		var role models.Role
// 		if err := db.First(&role, "id = ?", updateRequest.RoleID).Error; err != nil {
// 			c.JSON(http.StatusBadRequest, gin.H{
// 				"success": false,
// 				"error":   "Invalid role",
// 			})
// 			return
// 		}
// 	}

// 	// Update the brand staff
// 	if err := db.Model(&brandStaff).Updates(brandStaffUpdates).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   err.Error(),
// 		})
// 		return
// 	}

// 	// Get updated brand staff with user info
// 	if err := db.First(&brandStaff, "id = ?", brandStaff.ID).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve updated staff",
// 		})
// 		return
// 	}

// 	if err := db.First(&user, "id = ?", brandStaff.UserID).Error; err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{
// 			"success": false,
// 			"error":   "Failed to retrieve updated user",
// 		})
// 		return
// 	}

// 	// Don't include password in response
// 	user.Password = ""

// 	// Create response object
// 	staffResponse := StaffResponse{
// 		ID:        brandStaff.ID,
// 		BrandID:   brandStaff.BrandID,
// 		UserID:    brandStaff.UserID,
// 		CreatedAt: brandStaff.CreatedAt,
// 		UpdatedAt: brandStaff.UpdatedAt,
// 		Role:      string(brandStaff.Role),
// 		Username:  user.Username,
// 		Email:     user.Email,
// 		Phone:     user.Phone,
// 		Name:      user.Name,
// 		IsActive:  user.IsActive,
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 		"data":    staffResponse,
// 	})
// }

// // DeleteBrandStaff deletes a brand staff
// func DeleteBrandStaff(c *gin.Context) {
// 	db := middlewares.GetDB(c)
// 	brandID := c.Param("brand_id")
// 	staffID := c.Param("staff_id")

// 	var staff models.User
// 	if err := db.Where("id = ? AND ? = ANY (brands)", staffID, brandID).First(&staff).Error; err != nil {
// 		c.JSON(http.StatusNotFound, gin.H{
// 			"success": false,
// 			"error":   "Staff not found",
// 		})
// 		return
// 	}

// 	updatedBrands := lo.Without(staff.Brands, brandID)
// 	if len(updatedBrands) == 0 {
// 		if err := db.Delete(&staff).Error; err != nil {
// 			c.JSON(http.StatusInternalServerError, gin.H{
// 				"success": false,
// 				"error":   err.Error(),
// 			})
// 			return
// 		}
// 	} else {
// 		if err := db.Model(&staff).Update("brands", updatedBrands).Error; err != nil {
// 			c.JSON(http.StatusInternalServerError, gin.H{
// 				"success": false,
// 				"error":   err.Error(),
// 			})
// 			return
// 		}
// 	}

// 	c.JSON(http.StatusOK, gin.H{
// 		"success": true,
// 	})
// }

package router

func brandstaff() {
}
