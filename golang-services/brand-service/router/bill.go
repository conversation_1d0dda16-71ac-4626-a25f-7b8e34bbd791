package router

import (
	"fmt"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// GetBillList godoc
// @Summary Get list of bills for a brand
// @Description Retrieves a paginated list of bill configurations for a specific brand
// @Tags bills
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(100)
// @Success 200 {object} map[string]any "Returns bills list and pagination info"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /brands/{brand_id}/bills [get]
func GetBillList(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	// Parse pagination params
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
	offset := (page - 1) * limit

	// Count total records
	var total int64
	if err := db.Model(&models.BrandBillConfig{}).
		Where("brand_id = ?", brandID).
		Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get paginated bills
	var bills []models.BrandBillConfig
	if err := db.Where("brand_id = ?", brandID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&bills).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bills,
		"total":   total,
		"page":    page,
		"limit":   limit,
		"pages":   totalPages,
		"hasNext": page < totalPages,
		"hasPrev": page > 1,
	})
}

// CreateBill godoc
// @Summary Create a new bill configuration
// @Description Creates a new bill configuration for a specific brand
// @Tags bills
// @Accept json
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param bill body map[string]any true "Bill configuration object"
// @Success 200 {object} map[string]any "Returns the created bill"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /brands/{brand_id}/bills [post]
func CreateBill(c *gin.Context) {
	db := middlewares.GetDB(c)
	brandID := c.Param("brand_id")

	var request struct {
		Name            string `json:"name" binding:"required"`
		BillType        string `json:"bill_type" binding:"required"`
		BillSize        int    `json:"bill_size"`
		ShowBankPayment bool   `json:"show_bank_payment"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Read default template based on bill type
	templateFile := "bill_default.ckeditor.html"
	if request.BillType == "bill_for_label" {
		templateFile = "label_default.ckeditor.html"
	}

	contentHTML, err := os.ReadFile(filepath.Join("files", templateFile))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to read template file: %v", err),
		})
		return
	}

	bill := models.BrandBillConfig{
		Name:            request.Name,
		BrandID:         brandID,
		BillType:        request.BillType,
		BillSize:        request.BillSize,
		ShowBankPayment: request.ShowBankPayment,
		ContentHTML:     string(contentHTML),
		Active:          false,
	}

	if err := db.Create(&bill).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bill,
	})
}

// UpdateBill godoc
// @Summary Update a bill configuration
// @Description Updates an existing bill configuration by ID
// @Tags bills
// @Accept json
// @Produce json
// @Param bill_id path string true "Bill ID"
// @Param bill body map[string]any true "Updated bill configuration"
// @Success 200 {object} map[string]any "Returns the updated bill"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Bill not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /bills/{bill_id} [put]
func UpdateBill(c *gin.Context) {
	db := middlewares.GetDB(c)
	billID := c.Param("bill_id")

	var request struct {
		Name            string `json:"name"`
		BillType        string `json:"bill_type"`
		BillSize        int    `json:"bill_size"`
		ShowBankPayment bool   `json:"show_bank_payment"`
		ContentHTML     string `json:"content_html"`
		Active          bool   `json:"active"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find and update bill
	var bill models.BrandBillConfig
	if err := db.First(&bill, billID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Bill config not found",
		})
		return
	}

	// Update fields
	bill.Name = request.Name
	bill.BillType = request.BillType
	bill.BillSize = request.BillSize
	bill.ShowBankPayment = request.ShowBankPayment
	bill.ContentHTML = request.ContentHTML
	bill.Active = request.Active

	if err := db.Save(&bill).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bill,
	})
}

// DeleteBill godoc
// @Summary Delete a bill configuration
// @Description Deletes a bill configuration by ID
// @Tags bills
// @Accept json
// @Produce json
// @Param bill_id path string true "Bill ID"
// @Success 200 {object} map[string]any "Success message"
// @Failure 404 {object} map[string]any "Bill not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /bills/{bill_id} [delete]
func DeleteBill(c *gin.Context) {
	db := middlewares.GetDB(c)
	billID := c.Param("bill_id")

	var bill models.BrandBillConfig
	if err := db.First(&bill, billID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Bill config not found",
		})
		return
	}

	if err := db.Delete(&bill).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bill,
	})
}
