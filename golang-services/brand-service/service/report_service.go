package service

import (
	lmodels "github.com/nexdorvn/nexpos-backend/golang-services/brand-service/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ReportService handles report-related operations
type ReportService struct {
	DB *gorm.DB
}

// NewReportService creates a new ReportService
func NewReportService(db *gorm.DB) *ReportService {
	return &ReportService{
		DB: db,
	}
}

// getMerchantDisplayName returns a user-friendly display name for a merchant
func getMerchantDisplayName(merchantCode string) string {
	displayNames := map[string]string{
		"grab":         "Grab",
		"grab_food":    "Grab Food",
		"grab_mart":    "Grab Mart",
		"shopee":       "Shopee",
		"shopee_fresh": "Shopee Fresh",
		"be":           "Be",
		"momo":         "MoM<PERSON>",
		"gojek":        "Gojek",
	}

	if name, exists := displayNames[merchantCode]; exists {
		return name
	}
	return merchantCode
}

// GetReport generates a report for a specific hub within a time range
func (s *ReportService) GetReport(query lmodels.ReportQuery) (*lmodels.ReportResponse, error) {
	// Define merchants to include in the report
	merchants := []string{"all", "grab", "grab_food", "grab_mart", "shopee", "shopee_fresh", "be", "momo"}

	// Initialize response
	response := &lmodels.ReportResponse{
		MerchantSummaries: []lmodels.MerchantSummary{},
		SiteSummaries:     []lmodels.SiteSummary{},
	}

	// Build the query
	dbQuery := s.DB.Where("status = ? AND data_mapping->>'delivery_time_unix' >= ? AND data_mapping->>'delivery_time_unix' < ?",
		"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add hub filter if specified
	if query.HubID != "" && query.HubID != "all" {
		dbQuery = dbQuery.Where("hub_id = ?", query.HubID)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("source IN (?)", query.Apps)
	}

	// Query orders
	var orders []models.Order
	if err := dbQuery.Find(&orders).Error; err != nil {
		return nil, err
	}

	// Group orders by merchant
	ordersByMerchant := make(map[string][]models.Order)
	for _, merchant := range merchants {
		if merchant == "all" {
			ordersByMerchant[merchant] = orders
		} else {
			var merchantOrders []models.Order
			for _, order := range orders {
				if order.Source == merchant {
					merchantOrders = append(merchantOrders, order)
				}
			}
			ordersByMerchant[merchant] = merchantOrders
		}
	}

	// Group orders by site
	ordersBySite := make(map[string][]models.Order)
	for _, order := range orders {
		if _, exists := ordersBySite[order.SiteID]; !exists {
			ordersBySite[order.SiteID] = []models.Order{}
		}
		ordersBySite[order.SiteID] = append(ordersBySite[order.SiteID], order)
	}

	// Get site information
	var siteIDs []string
	for siteID := range ordersBySite {
		siteIDs = append(siteIDs, siteID)
	}

	var sites []models.Site
	if len(siteIDs) > 0 {
		if err := s.DB.Where("id IN ?", siteIDs).Find(&sites).Error; err != nil {
			return nil, err
		}
	}

	// Create site map for quick lookup
	siteMap := make(map[string]models.Site)
	for _, site := range sites {
		siteMap[site.ID] = site
	}

	// Calculate totals for each merchant and create merchant summaries
	var totalRevenue, totalRevenueBeforePromo, totalRevenueAfterPromo float64
	var totalOrderCount int

	for _, merchant := range merchants {
		merchantOrders := ordersByMerchant[merchant]
		if merchant != "all" {
			totalOrderCount += len(merchantOrders)
		}

		var totalOriginalPrice, totalNetReceived, totalGrossReceived, totalCoFund, totalCommission float64

		for _, order := range merchantOrders {
			if order.DataMapping.Data.FinanceData.OriginalPrice > 0 {
				totalOriginalPrice += order.DataMapping.Data.FinanceData.OriginalPrice
			}
			if order.DataMapping.Data.FinanceData.NetReceived > 0 {
				totalNetReceived += order.DataMapping.Data.FinanceData.NetReceived
			}
			if order.DataMapping.Data.FinanceData.GrossReceived > 0 {
				totalGrossReceived += order.DataMapping.Data.FinanceData.GrossReceived
			}
			// Safely access finance data fields
			if order.DataMapping.Data.FinanceData.CoFundPromotionPrice > 0 {
				totalCoFund += order.DataMapping.Data.FinanceData.CoFundPromotionPrice
			}
			totalCommission += order.DataMapping.Data.FinanceData.Commission
		}

		if merchant != "all" {
			totalRevenue += totalNetReceived
			totalRevenueBeforePromo += totalOriginalPrice
			totalRevenueAfterPromo += totalGrossReceived
		}

		// Add merchant summary
		if merchant != "all" && len(merchantOrders) > 0 {
			merchantName := getMerchantDisplayName(merchant)
			response.MerchantSummaries = append(response.MerchantSummaries, lmodels.MerchantSummary{
				Name:    merchantName,
				Count:   len(merchantOrders),
				Revenue: totalNetReceived,
			})
		}
	}

	// Calculate site summaries
	for siteID, siteOrders := range ordersBySite {
		site, exists := siteMap[siteID]
		if !exists {
			continue
		}

		var siteRevenue, siteRevenueBeforePromo, siteRevenueAfterPromo float64
		for _, order := range siteOrders {
			if order.DataMapping.Data.FinanceData.OriginalPrice > 0 {
				siteRevenueBeforePromo += order.DataMapping.Data.FinanceData.OriginalPrice
			}
			if order.DataMapping.Data.FinanceData.NetReceived > 0 {
				siteRevenue += order.DataMapping.Data.FinanceData.NetReceived
			}
			if order.DataMapping.Data.FinanceData.GrossReceived > 0 {
				siteRevenueAfterPromo += order.DataMapping.Data.FinanceData.GrossReceived
			}
		}

		response.SiteSummaries = append(response.SiteSummaries, lmodels.SiteSummary{
			Name:               site.Name,
			OrderCount:         len(siteOrders),
			RevenueBeforePromo: siteRevenueBeforePromo,
			RevenueAfterPromo:  siteRevenueAfterPromo,
			TotalRevenue:       siteRevenue,
		})
	}

	// Set summary data
	response.TotalRevenue = totalRevenue
	response.OrderCount = totalOrderCount
	response.RevenueBeforePromo = totalRevenueBeforePromo
	response.RevenueAfterPromo = totalRevenueAfterPromo

	return response, nil
}
