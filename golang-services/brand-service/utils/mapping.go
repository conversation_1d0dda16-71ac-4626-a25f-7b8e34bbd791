package utils

import (
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/golang-module/carbon"
)

// Day of week mapping
var DayOfWeekMap = []string{"sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"}
var DayOfWeekMap2 = []string{"monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"}

// MapWorkingHour maps merchant-specific working hours to a standardized format
func MapWorkingHour(source string, workingHours any) map[string]models.WorkingHour {
	// Initialize with default values for all days
	newWorkingHour := make(map[string]models.WorkingHour)
	for _, day := range DayOfWeekMap {
		newWorkingHour[day] = models.WorkingHour{
			Start:  "",
			End:    "",
			Closed: false,
		}
	}

	switch source {
	case "shopee_food", "shopee_fresh":
		if shopeeHours, ok := workingHours.([]map[string]any); ok {
			for _, hour := range shopeeHours {
				weekday := int(hour["weekday"].(float64)) - 1
				if weekday < 0 || weekday >= len(DayOfWeekMap2) {
					continue
				}

				dayOfWeek := DayOfWeekMap2[weekday]
				isClosed := hour["is_closed"].(bool)

				var start, end string
				if intervals, ok := hour["intervals"].([]any); ok && len(intervals) > 0 {
					if interval, ok := intervals[0].(map[string]any); ok {
						startSec := int64(interval["start_relative_sec"].(float64))
						endSec := int64(interval["end_relative_sec"].(float64))

						startTime := time.Unix(startSec, 0).UTC()
						endTime := time.Unix(endSec, 0).UTC()

						start = startTime.Format("15:04")
						end = endTime.Format("15:04")
					}
				}

				newWorkingHour[dayOfWeek] = models.WorkingHour{
					Start:  start,
					End:    end,
					Closed: isClosed,
				}
			}
		}

	case "grab", "grab_mart":
		if grabHours, ok := workingHours.([]map[string]any); ok {
			for i, hour := range grabHours {
				if i >= len(DayOfWeekMap) {
					continue
				}

				dayOfWeek := DayOfWeekMap[i]

				var start, end string
				var closed bool

				if ranges, ok := hour["ranges"].([]any); ok {
					closed = len(ranges) == 0
					if len(ranges) > 0 {
						if rangeItem, ok := ranges[0].(map[string]any); ok {
							start = rangeItem["start"].(string)
							end = rangeItem["end"].(string)
						}
					}
				}

				newWorkingHour[dayOfWeek] = models.WorkingHour{
					Start:  start,
					End:    end,
					Closed: closed,
				}
			}
		}

	case "be":
		if beHours, ok := workingHours.([]interface{}); ok {
			for i, hour := range beHours {
				if i >= len(DayOfWeekMap) {
					continue
				}

				dayOfWeek := DayOfWeekMap[i]
				hourMap := hour.(map[string]interface{})

				var start, end string
				var closed bool

				if timings, ok := hourMap["timings"].([]interface{}); ok {
					closed = len(timings) == 0
					if len(timings) > 0 {
						if timing, ok := timings[0].(map[string]interface{}); ok {
							startTime := timing["start_time"].(string)
							endTime := timing["end_time"].(string)

							// Format time to HH:MM
							start = carbon.ParseByFormat(startTime, "15:04:05").Format("15:04")
							end = carbon.ParseByFormat(endTime, "15:04:05").Format("15:04")
						}
					}
				}

				newWorkingHour[dayOfWeek] = models.WorkingHour{
					Start:  start,
					End:    end,
					Closed: closed,
				}
			}
		}
	}

	return newWorkingHour
}

// ReverseMapWorkingHour converts standardized working hours back to merchant-specific format
func ReverseMapWorkingHour(source string, workingHours map[string]models.WorkingHour) any {
	switch source {
	case "shopee_food", "shopee_fresh":
		result := make([]map[string]any, 0, 7)

		for i := 0; i < 7; i++ {
			dayOfWeek := DayOfWeekMap2[i]
			day := workingHours[dayOfWeek]

			var startSec, endSec int64
			if !day.Closed && day.Start != "" && day.End != "" {
				startDuration, _ := time.ParseDuration(day.Start + "m")
				endDuration, _ := time.ParseDuration(day.End + "m")
				startSec = int64(startDuration.Seconds())
				endSec = int64(endDuration.Seconds())
			}

			item := map[string]any{
				"weekday":        i + 1,
				"config_enabled": day.Closed,
			}

			if !day.Closed {
				item["intervals"] = []map[string]any{
					{
						"start_relative_sec": startSec,
						"end_relative_sec":   endSec,
					},
				}
			} else {
				item["intervals"] = nil
			}

			result = append(result, item)
		}

		return result

	case "grab", "grab_mart":
		result := make([]map[string]any, 0, 7)

		for i := 0; i < len(DayOfWeekMap); i++ {
			dayOfWeek := DayOfWeekMap[i]
			day := workingHours[dayOfWeek]

			item := map[string]any{
				"ranges": []map[string]any{},
			}

			if !day.Closed && day.Start != "" && day.End != "" {
				item["ranges"] = []map[string]any{
					{
						"start": day.Start,
						"end":   day.End,
					},
				}
			}

			result = append(result, item)
		}

		return result

	case "be":
		result := make([]map[string]any, 0, 7)

		for i := 0; i < len(DayOfWeekMap); i++ {
			dayOfWeek := DayOfWeekMap[i]
			day := workingHours[dayOfWeek]

			dayNames := map[string]string{
				"monday":    "Thứ hai",
				"tuesday":   "Thứ ba",
				"wednesday": "Thứ tư",
				"thursday":  "Thứ năm",
				"friday":    "Thứ sáu",
				"saturday":  "Thứ bảy",
				"sunday":    "Chủ nhật",
			}

			item := map[string]any{
				"day_id":  i + 1,
				"name":    dayNames[dayOfWeek],
				"timings": []map[string]any{},
			}

			if !day.Closed && day.Start != "" && day.End != "" {
				item["timings"] = []map[string]any{
					{
						"time_id":    -1,
						"start_time": day.Start,
						"end_time":   day.End,
					},
				}
			}

			result = append(result, item)
		}

		return result
	}

	return nil
}

// MapSpecialWorkingHour maps merchant-specific special working hours to a standardized format
func MapSpecialWorkingHour(source string, workingHours any) []models.SiteSpecialWorkingHour {
	result := []models.SiteSpecialWorkingHour{}

	switch source {
	case "shopee_food", "shopee_fresh":
		if shopeeHours, ok := workingHours.([]map[string]any); ok {
			for _, item := range shopeeHours {
				dateStart := int64(item["date_start"].(float64)) / 1000
				dateEnd := int64(item["date_end"].(float64)) / 1000
				dateType := int(item["date_type"].(float64))

				siteSpecialWorkingHour := models.SiteSpecialWorkingHour{
					Source:    source,
					Name:      "Đóng mở cửa đặt biệt",
					FromDate:  time.Unix(dateStart, 0).Format("2006-01-02"),
					ToDate:    time.Unix(dateEnd, 0).Format("2006-01-02"),
					OpenHours: nil,
				}
				if dateType == 1 {
					siteSpecialWorkingHour.OpenType = models.OpenTypeCloseAllTime
				} else {
					siteSpecialWorkingHour.OpenType = models.OpenTypeOpenAllTime
				}
				result = append(result, siteSpecialWorkingHour)
			}
		}

	case "grab", "grab_mart":
		if grabHours, ok := workingHours.([]map[string]any); ok {
			for _, item := range grabHours {
				metadata := item["metadata"].(map[string]any)
				startDate := item["startDate"].(string)
				endDate := item["endDate"].(string)
				openingHours := item["openingHours"].(map[string]any)

				startTime, _ := time.Parse("2006-01-02", startDate)
				endTime, _ := time.Parse("2006-01-02", endDate)

				siteSpecialWorkingHour := models.SiteSpecialWorkingHour{
					Source:    source,
					Name:      metadata["description"].(string),
					FromDate:  carbon.Time2Carbon(startTime).Format("2006-01-02"),
					ToDate:    carbon.Time2Carbon(endTime).Format("2006-01-02"),
					OpenHours: nil,
				}
				if openingHours["openPeriodType"].(string) == "ClosedAllDay" {
					siteSpecialWorkingHour.OpenType = models.OpenTypeCloseAllTime
				} else {
					siteSpecialWorkingHour.OpenType = models.OpenTypeOpenAllTime
				}

				result = append(result, siteSpecialWorkingHour)
			}
		}
	}

	return result
}
