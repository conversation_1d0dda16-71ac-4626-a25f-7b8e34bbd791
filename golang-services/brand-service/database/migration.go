package database

import (
	"log"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"gorm.io/gorm"
)

// AutoMigrate performs automatic database migration for all models
func AutoMigrate(db *gorm.DB) {
	log.Println("Running database migrations...")

	// List of models to migrate
	models := []any{
		// // Core models
		// &models.User{},
		// &models.Role{},

		// // Brand related models
		// &models.Brand{},
		// &models.BrandStaff{},
		// &models.BrandMenu{},
		// &models.BrandCommission{},

		// // Hub related models
		// &models.Hub{},
		// &models.HubStaff{},
		// &models.HubStock{},
		// &models.HubStockHistory{},
		// &models.HubStockTicket{},
		&models.WorkingShift{},
		&models.Province{},

		// // Site related models
		// &models.Site{},
		// &models.SiteMenuGroup{},
		// &models.SiteMenuCategory{},
		// &models.SiteMenuItem{},
		// &models.SiteMenuOption{},
		// &models.SiteMenuOptionItem{},

		// // Other models
		// &models.UserOTP{},
		// &models.FCMToken{},
		// &models.CoreProduct{},
	}

	// Run migrations
	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			log.Printf("Error migrating model %T: %v", model, err)
		} else {
			log.Printf("Successfully migrated model %T", model)
		}
	}

	log.Println("Database migrations completed")
}
