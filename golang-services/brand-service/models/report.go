package models

import (
	"time"
)

// ReportQuery represents the query parameters for the report endpoint
type ReportQuery struct {
	HubID      string    `form:"hub_id"`
	Apps       []string  `form:"apps"`
	TimeFilter string    `form:"time_filter"`
	From       time.Time `form:"from" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"`
	To         time.Time `form:"to" binding:"required" time_format:"2006-01-02T15:04:05Z07:00"`
}

// MerchantSummary represents the summary data for a specific merchant/app
type MerchantSummary struct {
	Name    string  `json:"name"`
	Count   int     `json:"count"`
	Revenue float64 `json:"revenue"`
}

// SiteSummary represents the summary data for a specific site
type SiteSummary struct {
	Name               string  `json:"name"`
	OrderCount         int     `json:"order_count"`
	RevenueBeforePromo float64 `json:"revenue_before_promo"`
	RevenueAfterPromo  float64 `json:"revenue_after_promo"`
	TotalRevenue       float64 `json:"total_revenue"`
}

// ChartData represents the chart data for the report
type ChartData struct {
	DataLabels         []string    `json:"data_labels"`
	AxisLabels         []string    `json:"axis_labels"`
	Datasets           [][]float64 `json:"datasets"`
	OrderCountDatasets [][]int     `json:"order_count_datasets"`
}

// ReportResponse represents the response data for the report endpoint
type ReportResponse struct {
	// Summary data
	TotalRevenue       float64 `json:"total_revenue"`
	OrderCount         int     `json:"order_count"`
	RevenueBeforePromo float64 `json:"revenue_before_promo"`
	RevenueAfterPromo  float64 `json:"revenue_after_promo"`

	// Detailed data
	MerchantSummaries []MerchantSummary `json:"merchant_summaries"`
	SiteSummaries     []SiteSummary     `json:"site_summaries"`
}
