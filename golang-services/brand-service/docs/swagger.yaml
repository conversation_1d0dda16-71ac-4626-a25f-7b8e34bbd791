definitions:
  router.PaginationResponse:
    description: Pagination response wrapper
    properties:
      data: {}
      hasNextPage:
        example: true
        type: boolean
      hasPrevPage:
        example: false
        type: boolean
      limit:
        example: 10
        type: integer
      page:
        example: 1
        type: integer
      success:
        example: true
        type: boolean
      totalDocs:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
info:
  contact: {}
paths:
  /bills/{bill_id}:
    delete:
      consumes:
      - application/json
      description: Deletes a bill configuration by ID
      parameters:
      - description: Bill ID
        in: path
        name: bill_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Bill not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a bill configuration
      tags:
      - bills
    put:
      consumes:
      - application/json
      description: Updates an existing bill configuration by ID
      parameters:
      - description: Bill ID
        in: path
        name: bill_id
        required: true
        type: string
      - description: Updated bill configuration
        in: body
        name: bill
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Returns the updated bill
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Bill not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Update a bill configuration
      tags:
      - bills
  /brands:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of brands with optional filtering
      parameters:
      - description: Filter brands by name
        in: query
        name: name
        type: string
      - description: Include a specific brand by ID
        in: query
        name: selected
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 100
        description: Items per page
        in: query
        maximum: 100
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/router.PaginationResponse'
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get list of brands
      tags:
      - brands
    post:
      consumes:
      - application/json
      description: Creates a new brand and associates it with the current user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new brand
      tags:
      - brands
  /brands/{brand_id}:
    delete:
      consumes:
      - application/json
      description: Deletes a brand by ID
      parameters:
      - description: Brand ID
        in: path
        name: brand_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a brand
      tags:
      - brands
    put:
      consumes:
      - application/json
      description: Updates an existing brand by ID
      parameters:
      - description: Brand ID
        in: path
        name: brand_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update a brand
      tags:
      - brands
  /brands/{brand_id}/banners:
    put:
      consumes:
      - application/json
      description: Updates the banners for a specific brand
      parameters:
      - description: Brand ID
        in: path
        name: brand_id
        required: true
        type: string
      - description: Banners object with array of BrandBanner
        in: body
        name: banners
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Update brand banners
      tags:
      - brands
  /brands/{brand_id}/bills:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of bill configurations for a specific
        brand
      parameters:
      - description: Brand ID
        in: path
        name: brand_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 100
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Returns bills list and pagination info
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get list of bills for a brand
      tags:
      - bills
    post:
      consumes:
      - application/json
      description: Creates a new bill configuration for a specific brand
      parameters:
      - description: Brand ID
        in: path
        name: brand_id
        required: true
        type: string
      - description: Bill configuration object
        in: body
        name: bill
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Returns the created bill
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new bill configuration
      tags:
      - bills
  /hubs:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of hubs with optional filtering
      parameters:
      - description: Filter hubs by name
        in: query
        name: name
        type: string
      - collectionFormat: csv
        description: Filter hubs by brand IDs
        in: query
        items:
          type: string
        name: brand_ids
        type: array
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 200
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Returns hubs list and pagination info
          schema:
            $ref: '#/definitions/router.PaginationResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get list of hubs
      tags:
      - hubs
    post:
      consumes:
      - application/json
      description: Creates a new hub with location data and associates it with the
        current user
      produces:
      - application/json
      responses:
        "200":
          description: Returns the created hub
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new hub
      tags:
      - hubs
  /hubs/{hub_id}:
    delete:
      consumes:
      - application/json
      description: Deletes a hub by ID
      parameters:
      - description: Hub ID
        in: path
        name: hub_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a hub
      tags:
      - hubs
    put:
      consumes:
      - application/json
      description: Updates an existing hub by ID, including location data if address
        is provided
      parameters:
      - description: Hub ID
        in: path
        name: hub_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns the updated hub
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Hub not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Update a hub
      tags:
      - hubs
  /sites:
    get:
      consumes:
      - application/json
      description: Retrieves a paginated list of sites with optional filtering
      parameters:
      - description: Filter sites by brand ID
        in: query
        name: brand_id
        type: string
      - description: Filter sites by active status (true/false)
        in: query
        name: active
        type: string
      - collectionFormat: csv
        description: Filter sites by type
        in: query
        items:
          type: string
        name: type
        type: array
      - description: Filter sites by partner hub commission
        in: query
        name: apply_ph_commission
        type: string
      - description: Filter sites by core product usage
        in: query
        name: use_core_product
        type: string
      - description: Search sites by name or code
        in: query
        name: name
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 200
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Returns sites list and pagination info
          schema:
            $ref: '#/definitions/router.PaginationResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get list of sites
      tags:
      - sites
    post:
      consumes:
      - application/json
      description: Creates a new site and associates it with the current user
      produces:
      - application/json
      responses:
        "200":
          description: Returns the created site
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request or site code already exists
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create a new site
      tags:
      - sites
  /sites/{site_id}:
    delete:
      consumes:
      - application/json
      description: Soft deletes a site by ID (sets deleted_at timestamp)
      parameters:
      - description: Site ID
        in: path
        name: site_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Delete a site
      tags:
      - sites
    put:
      consumes:
      - application/json
      description: Updates an existing site by ID
      parameters:
      - description: Site ID
        in: path
        name: site_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns the updated site
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Site not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Update a site
      tags:
      - sites
  /sites/{site_id}/store-open:
    put:
      consumes:
      - application/json
      description: Updates the pause status for different apps for a specific site
      parameters:
      - description: Site ID
        in: path
        name: site_id
        required: true
        type: string
      - description: Request object with apps status
        in: body
        name: request
        required: true
        schema:
          additionalProperties: true
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Returns the updated site
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Site not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Update store open status for apps
      tags:
      - sites
  /sites/errors:
    get:
      consumes:
      - application/json
      description: Retrieves a list of site errors related to merchant connections
      produces:
      - application/json
      responses:
        "200":
          description: Returns list of error messages
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get site errors
      tags:
      - sites
  /sites/exports:
    get:
      consumes:
      - application/json
      description: Generates an Excel export of all sites with their configurations
      parameters:
      - description: Filter sites by name
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns file URL
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Export sites to Excel
      tags:
      - sites
  /sites/get:
    get:
      consumes:
      - application/json
      description: Retrieves a single site by code or ID with special domain handling
      parameters:
      - description: Site code
        in: query
        name: code
        type: string
      - description: Site ID
        in: query
        name: id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns error if site not found
          schema:
            additionalProperties: true
            type: object
      summary: Get a single site
      tags:
      - sites
swagger: "2.0"
