package route

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/myredis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/tidwall/gjson"

	"github.com/gin-gonic/gin"
)

func CronTransactionList(c *gin.Context) {
	redisClient := middlewares.GetRedis(c)

	lastTransStr := myredis.Get(redisClient, "transactions:latest")
	if lastTransStr != "" {
		if gjson.Parse(lastTransStr).Get("created_at").Time().After(time.Now().Add(-time.Minute * 20)) {
			GetTransactionList(c)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
