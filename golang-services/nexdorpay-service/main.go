package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/route"
	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/myredis"

	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "NexDorPay Service"
	app.Usage = "A service for NexDorPay"
	app.Commands = []cli.Command{
		{
			Name:  "start",
			Usage: "Start the NexDorPay Service",
			Action: func(c *cli.Context) error {
				startAPIService()
				return nil
			},
		},
	}

	err := app.Run(os.Args)
	if err != nil {
		panic(err)
	}
}

func startAPIService() {
	router := gin.Default()
	router.Use(cors.Default())

	rdb := myredis.New(strings.ReplaceAll(os.Getenv("REDIS_URI"), "redis://", ""), os.Getenv("REDIS_PASSWORD"), 2)
	pong, err := rdb.Ping(context.Background()).Result()
	if err != nil {
		log.Fatalf("Could not connect to Redis: %v", err)
	}
	fmt.Println("Connected to Redis:", pong)

	router.Use(func(c *gin.Context) {
		c.Set("redis", rdb)
		c.Next()
	})

	router.GET("/", func(c *gin.Context) {
		c.File("./web/build/index.html")
	})
	router.Static("/static", "./web/build/static")

	router.GET("/api/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
	})

	router.POST("/api/transactions", route.CreateTransaction)
	router.GET("/api/transactions/:transaction_id", route.GetTransaction)
	router.GET("/api/transactions", route.GetTransactionList)
	router.GET("/api/cron/transactions", route.CronTransactionList)
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}
	router.Run(":" + port)
}
