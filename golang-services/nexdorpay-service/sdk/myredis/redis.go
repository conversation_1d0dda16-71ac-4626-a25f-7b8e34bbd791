package myredis

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/go-redis/redis/v8"
)

func New(uri, password string, db int) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     strings.ReplaceAll(os.Getenv("REDIS_URI"), "redis://", ""), // Redis server address
		Password: os.Getenv("REDIS_PASSWORD"),                                // No password set
		DB:       2,                                                          // Use default DB
	})
	pong, err := rdb.Ping(context.Background()).Result()
	if err != nil {
		log.Fatalf("Could not connect to Redis: %v", err)
	}
	fmt.Println("Connected to Redis:", pong)
	return rdb
}

func Close(rdb *redis.Client) {
	rdb.Close()
}

func Set(rdb *redis.Client, key string, value any) error {
	return rdb.Set(context.Background(), key, value, 0).Err()
}

func Get(rdb *redis.Client, key string) string {
	result, err := rdb.Get(context.Background(), key).Result()
	if err != nil {
		return ""
	}
	return result
}
