package google

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"
)

var PrivateKey = strings.ReplaceAll(os.Getenv("GOOGLE_PRIVATE_KEY"), `\\n`, `\n`)

func UploadFile(bucketName, bucketKey string, buff []byte) (string, error) {
	ctx := context.Background()

	client, err := storage.NewClient(ctx, option.WithCredentialsJSON([]byte(PrivateKey)))
	if err != nil {
		return "", fmt.Errorf("storage.NewClient: %v", err)
	}
	defer client.Close()

	bucket := client.Bucket(bucketName)
	object := bucket.Object(bucketKey)

	wc := object.NewWriter(ctx)
	if _, err = io.Copy(wc, bytes.NewBuffer(buff)); err != nil {
		return "", fmt.Errorf("io.Copy: %v", err)
	}
	if err := wc.Close(); err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("Writer.Close: %v", err)
	}

	// if err := object.ACL().Set(ctx, storage.AllUsers, storage.RoleReader); err != nil {
	// 	return "", fmt.Errorf("Object ACL.Set: %v", err)
	// }

	url := fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, bucketKey)
	return url, nil
}
