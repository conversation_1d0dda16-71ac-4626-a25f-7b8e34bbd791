package vietqr

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
)

func GenerateViQR(message string, total int) ([]byte, error) {
	resp, err := http.Get(fmt.Sprintf("https://api.vietqr.io/image/970422-798839399-q4WEfiT.jpg?addInfo=%s&amount=%d", url.QueryEscape(message), total))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Check if the response status is OK
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error: received non-200 response code")
	}

	// Read the image data from the response body
	imageData, err := io.ReadAll(io.Reader(resp.Body))
	if err != nil {
		return nil, fmt.Errorf("error reading image data: %w", err)
	}
	return imageData, nil
}
