package sdk

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

type MBBankService struct {
	SessionID string
	APIGee    string `json:"apiGee"`
	Disabled  bool
	FailCount int
	client    *resty.Client
}

type CaptchaResponse struct {
	ImageBase64      string `json:"imageBase64"`
	EncryptedCaptcha string `json:"encryptedCaptcha"`
}

type LoginResponse struct {
	SessionID string `json:"sessionId"`
}

type Transaction struct {
	Source               string          `json:"source"`
	TransactionID        string          `json:"transaction_id"`
	PartnerTransactionID string          `json:"partner_transaction_id"`
	Date                 time.Time       `json:"date"`
	Amount               float64         `json:"amount"`
	Balance              float64         `json:"balance"`
	Description          string          `json:"description"`
	Currency             string          `json:"currency"`
	Raw                  json.RawMessage `json:"raw"`
}

const (
	MBBankSource      = "MBBank"
	MBBankAccount     = "*********"
	MBBankSubAccount  = "DUYETLENH"
	MBBankAccountName = "CONG TY TNHH NEXDOR VIET NAM"
)

func NewMBBankService() *MBBankService {
	client := resty.New().
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true}).
		SetHeader("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")

	return &MBBankService{
		client: client,
	}
}

func (s *MBBankService) getCaptcha() (*CaptchaResponse, error) {
	current := time.Now().Format("**************s000")[0:17]

	resp, err := s.client.R().
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetHeader("Origin", "https://ebank.mbbank.com.vn").
		SetHeader("Referer", "https://ebank.mbbank.com.vn/cp/pl/login?logout=1").
		SetHeader("Sec-Fetch-Site", "same-origin").
		SetHeader("X-Request-Id", current).
		SetHeader("Accept", "application/json, text/plain, */*").
		SetHeader("Accept-Language", "en-US,en;q=0.9").
		SetHeader("Connection", "keep-alive").
		SetBody(map[string]string{
			"refNo":    current,
			"deviceId": "0c48576cd7bec6ae176cb263527e0139",
		}).
		Post("https://ebank.mbbank.com.vn/corp/common/generateCaptcha")

	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}

	jsonResp := gjson.ParseBytes(resp.Body())
	return &CaptchaResponse{
		ImageBase64:      jsonResp.Get("imageBase64").String(),
		EncryptedCaptcha: jsonResp.Get("encryptedCaptcha").String(),
	}, nil
}

func (s *MBBankService) readCaptcha(captchaBuff []byte) (string, error) {
	resp, err := s.client.R().
		SetFileReader("image", "captcha.png", bytes.NewReader(captchaBuff)).
		Post("https://www.blackbox.ai/api/upload")

	if err != nil {
		return "", err
	}

	var result map[string]string
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return "", err
	}

	if text, ok := result["response"]; ok {
		text = strings.ReplaceAll(text, " ", "")
		if len(text) > 0 {
			return text[:len(text)/2], nil
		}
	}

	return "", fmt.Errorf("invalid captcha response")
}

func (s *MBBankService) Login() (string, string, error) {
	// Get and process captcha
	captchaResp, err := s.getCaptcha()
	if err != nil {
		return "", "", fmt.Errorf("error getting captcha: %v", err)
	}

	captchaBuff, err := base64.StdEncoding.DecodeString(captchaResp.ImageBase64)
	if err != nil {
		return "", "", fmt.Errorf("error decoding captcha image: %v", err)
	}

	captchaText, err := s.readCaptcha(captchaBuff)
	if err != nil {
		return "", "", fmt.Errorf("error reading captcha: %v", err)
	}

	current := time.Now().Format("**************000")[0:17]

	loginResp, err := s.client.R().
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetHeader("X-Request-Id", MBBankSubAccount+"-"+current).
		SetHeader("Origin", "https://ebank.mbbank.com.vn").
		SetHeader("Referer", "https://ebank.mbbank.com.vn/cp/pl/login?returnUrl=%2F").
		SetHeader("Accept", "application/json, text/plain, */*").
		SetHeader("Accept-Language", "en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7").
		SetHeader("biz-platform", "biz-1.0").
		SetHeader("biz-version", "1.********.1987").
		SetHeader("Connection", "keep-alive").
		SetBody(map[string]string{
			"captcha":          captchaText,
			"corpId":           "**********",
			"refNo":            MBBankSubAccount + "-" + current,
			"deviceId":         "0c48576cd7bec6ae176cb263527e0139",
			"userId":           MBBankSubAccount,
			"password":         "77784db4211a91a1889c99d8faa546bc",
			"encryptedCaptcha": captchaResp.EncryptedCaptcha,
		}).
		Post("https://ebank.mbbank.com.vn/corp/common/v3/login")

	// loginJSON, err := json.Marshal(loginBody)
	if err != nil {
		return "", "", fmt.Errorf("error making login request: %v", err)
	}

	loginResult := gjson.ParseBytes(loginResp.Body())
	sessionID := loginResult.Get("sessionId").String()
	if sessionID == "" {
		return "", "", fmt.Errorf("login failed: %s", loginResult.Get("message").String())
	}

	tokenResp, err := s.client.R().
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetHeader("X-Request-Id", MBBankSubAccount+"-"+current).
		SetHeader("Authorization", "Bearer "+sessionID).
		SetHeader("Origin", "https://ebank.mbbank.com.vn").
		SetHeader("Referer", "https://ebank.mbbank.com.vn/cp/pl/login?returnUrl=%2F").
		SetHeader("Accept", "application/json, text/plain, */*").
		SetHeader("Accept-Language", "en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7").
		SetHeader("biz-platform", "biz-1.0").
		SetHeader("biz-version", "1.********.1987").
		SetHeader("Connection", "keep-alive").
		SetBody(map[string]string{
			"refNo": MBBankSubAccount + "-" + current,
		}).
		Post("https://ebank.mbbank.com.vn/corp/common/get-token-apigee")

	// Create login request
	// _, err = http.NewRequest("POST",
	// 	"https://ebank.mbbank.com.vn/corp/common/v3/login",
	// 	bytes.NewBuffer(loginJSON))
	if err != nil {
		return "", "", fmt.Errorf("error making token request: %v", err)
	}

	tokenResult := gjson.ParseBytes(tokenResp.Body())
	apiGee := tokenResult.Get("apigeeAuthResponse.accessToken").String()
	if apiGee == "" {
		return "", "", fmt.Errorf("failed to get API token: %s", tokenResult.Get("message").String())
	}

	return sessionID, apiGee, nil
}

func (s *MBBankService) GetTransactionList() ([]Transaction, error) {
	// Check fail count
	if s.FailCount > 50 {
		// s.Disabled = true
		// return nil, fmt.Errorf("Service is disabled due to too many failed attempts")
	}

	if s.SessionID == "" {
		sessionID, apiGee, err := s.Login()
		if err != nil || sessionID == "" {
			s.FailCount++
			return nil, fmt.Errorf("login failed: %v", err)
		}
		s.SessionID = sessionID
		s.APIGee = apiGee
		s.FailCount = 0
	}

	current := time.Now().Format("**************.000")[0:17]

	resp, err := s.client.R().
		SetQueryParams(map[string]string{
			"acctNo":   MBBankAccount,
			"fromDate": time.Now().AddDate(0, -1, 0).Format("02/01/2006 15:04"),
			"toDate":   time.Now().Format("02/01/2006") + " 23:59",
			"page":     "1",
			"size":     "15",
			"top":      "",
			"currency": "VND",
		}).
		SetHeader("ClientMessageId", MBBankSubAccount+"-"+current).
		SetHeader("Authorization", "Bearer "+s.APIGee).
		SetHeader("sessionId", s.SessionID).
		SetHeader("Content-Type", "application/json; charset=UTF-8").
		SetHeader("Accept", "application/json, text/plain, */*").
		SetHeader("Origin", "https://api-public.mbbank.com.vn").
		SetHeader("Referer", "https://api-public.mbbank.com.vn").
		Get("https://api-public.mbbank.com.vn/ms/transaction-statement/v1/transaction-statement")

	if err != nil {
		s.FailCount++
		s.SessionID = ""
		return nil, fmt.Errorf("error making request: %v", err)
	}

	if resp.StatusCode() != 200 {
		s.FailCount++
		s.SessionID = ""
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode())
	}

	transactions := []Transaction{}
	result := gjson.ParseBytes(resp.Body())
	transactionList := result.Get("data.transactionHistoryList").Array()

	for _, v := range transactionList {
		date, err := time.Parse("02/01/2006 15:04:05", v.Get("transactionDate").String())
		if err != nil {
			continue
		}

		transaction := Transaction{
			Source:        "MBBank",
			TransactionID: v.Get("transactionRefNo").String(),
			Date:          date,
			Amount:        v.Get("creditAmount").Float(),
			Balance:       v.Get("availableBalance").Float(),
			Description:   v.Get("description").String(),
			Currency:      v.Get("currency").String(),
			Raw:           json.RawMessage(v.String()),
		}

		// Process description for partner transaction ID
		rawText := strings.ReplaceAll(strings.ToLower(transaction.Description), " ", "")
		re := regexp.MustCompile(`nexdor(.{10})`)
		if matches := re.FindStringSubmatch(rawText); len(matches) > 1 {
			transaction.PartnerTransactionID = matches[1]
		}

		transactions = append(transactions, transaction)
	}

	return transactions, nil
}
