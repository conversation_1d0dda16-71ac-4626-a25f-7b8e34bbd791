import React, { useState, useEffect } from 'react';
import moment from 'moment';
import './Payment.css';
const PaymentPage = () => {
    const [paymentStatus, setPaymentStatus] = useState('pending');
    const [timeRemaining, setTimeRemaining] = useState(600); // 10 minutes in seconds
    const [countdown, setCountdown] = useState(5);
    const [redirectUrl, setRedirectUrl] = useState('');
    const [transaction, setTransaction] = useState({
        qrcode: '',
        message: '',
        amount: 0,
    });

    const fetchTransactionDetail = async () => {
        try {
            const queryParams = new URLSearchParams(window.location.search);
            const transactionId = queryParams.get('tx');
            if (transactionId) {
                const response = await fetch(`/api/transactions/${transactionId}`);
                const resp = await response.json();
                console.log(resp)
                setTransaction(resp.data)
                const remain = moment(resp.data.expired_at).diff(moment(), 'seconds')
                setTimeRemaining(remain < 0 ? 0 : remain);
                setRedirectUrl(resp.data.client_callback);
            }
        } catch (error) {
            console.error('Error fetching transaction detail:', error);
        }
    }


    useEffect(() => {
        fetchTransactionDetail()

        const interval = setInterval(() => {
            checkPaymentStatus();
        }, 2000);

        const timer = setInterval(() => {
            setTimeRemaining((prevTime) => prevTime - 1);
        }, 1000);

        return () => {
            clearInterval(interval);
            clearInterval(timer);
        };
    }, []);

    useEffect(() => {
        if (timeRemaining === 0) {
            setPaymentStatus('failed');
        }
    }, [timeRemaining]);

    useEffect(() => {
        if (paymentStatus === 'success' && redirectUrl) {
            const countdownTimer = setInterval(() => {
                setCountdown((prevCountdown) => prevCountdown - 1);
            })
            const redirectTimer = setTimeout(() => {
                handleRedirect(true);
            }, 5000);
            return () => {
                clearTimeout(redirectTimer);
                clearInterval(countdownTimer);
            };
        }
    }, [paymentStatus, redirectUrl]);

    const checkPaymentStatus = async () => {
        try {
            // Call your API to check payment status
            const queryParams = new URLSearchParams(window.location.search);
            const transactionId = queryParams.get('tx');
            const response = await fetch(`/api/transactions/${transactionId}`);
            const data = await response.json();

            if (data.data.status === 'COMPLETED') {
                setPaymentStatus('success');
            } else if (data.data.status === 'CANCELLED' || timeRemaining === 0) {
                setPaymentStatus('failed');
            }
        } catch (error) {
            console.error('Error checking payment status:', error);
        }
    };

    const handleRedirect = (success) => {
        if (redirectUrl) {
            window.location.href = `${redirectUrl}?success=${success}&order_id=${transaction.order_id}&payment_method=NEXDORPAY&tx=${transaction.transaction_id}`;
        }
    };

    const formatTime = (time) => {
        const minutes = Math.floor(time / 60);
        const seconds = time % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    return (
        <div className="flex justify-center items-center h-screen bg-gray-100">
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-3xl">
                <div className="flex justify-between items-center mb-8">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-800">THANH TOÁN ĐƠN HÀNG</h1>
                        <p className="text-gray-600">Thanh toán trước {moment(transaction.expired_at).format('dddd, MMMM Do YYYY, h:mm:ss')}</p>
                    </div>
                    <span className="text-2xl font-semibold text-gray-600">{transaction.amount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })} </span>
                </div>
                <div className="bg-yellow-100 rounded-lg p-4 mb-8">
                    <p className="text-gray-600">
                        Mở App Ngân hàng bất kỳ hoặc MoMo để quét mã QR hoặc chuyển khoản chính xác số tiền bên dưới
                    </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <img class="max-w-[80%] object-fill cursor-pointer" rel="preload" src={transaction.qrcode} alt="qrcode" />
                        {/* <QRCode value="VietQR code" size={256} className="mb-4" /> */}
                    </div>
                    <div>
                        {paymentStatus === 'pending' && (
                            <div className="flex flex-col items-center mb-6">
                                <p className="text-gray-600 mb-2">Thời hạn thanh toán:</p>
                                <p className="text-6xl font-bold">{formatTime(timeRemaining)}</p>
                            </div>
                        )}
                        {paymentStatus === 'success' && (
                            <div className="bg-green-100 rounded-lg p-4 mb-6">
                                <p className="text-green-600">Thanh toán thành công!</p>
                            </div>
                        )}
                        {paymentStatus === 'failed' && (
                            <div className="bg-red-100 rounded-lg p-4 mb-6">
                                <p className="text-red-600">Thanh toán thất bại!</p>
                            </div>
                        )}
                        {paymentStatus === 'success' && redirectUrl && (
                            <button
                                className="bg-blue-500 text-white py-2 px-4 rounded-lg mt-6 w-full"
                                onClick={() => handleRedirect(true)}
                            >
                                Bấm để quay về trang trước khi thanh toán, hệ thống sẽ tự động chuyển hướng sau {countdown} giây
                            </button>
                        )}
                    </div>
                </div>
                <button onClick={() => handleRedirect(false)} className="bg-blue-500 text-white py-2 px-4 rounded-lg mt-6 w-full">Hủy</button>
                <p className="text-gray-600 mt-4">Lưu ý: Nhập chính xác số tiền {transaction.amount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })} khi chuyển khoản</p>
            </div>
        </div>
    );
};

export default PaymentPage;