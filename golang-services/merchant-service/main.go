package main

import (
	"log"
	"os"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/merchant-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/merchant-service/router/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/urfave/cli"
	"gorm.io/gorm"
)

func main() {
	app := cli.NewApp()
	app.Name = "merchant-service"
	app.Usage = "Merchant service"
	app.Version = "1.0.0"
	app.Commands = []cli.Command{
		{
			Name:      "Start",
			ShortName: "start",
			Usage:     "Start service",
			Flags: []cli.Flag{
				cli.StringFlag{
					Name:   "port",
					Usage:  "Port the server listens to",
					EnvVar: "PORT",
					Value:  "3000",
				},
			},
			Action: func(c *cli.Context) error {
				port := c.String("port")

				r := gin.New()
				r.Use(middlewares.MyCors())

				r.Use(middlewares.RabbitMQMiddleware())
				r.Use(middlewares.GormMiddleware())
				r.Use(middlewares.GoogleStorageMiddleware())
				r.Use(middlewares.RedisMiddleware())
				r.Use(middlewares.ErrorLocalizeMiddleware())

				router.LoadHandlers(r)

				r.Use(gin.Logger())
				r.Use(gin.Recovery())

				// if os.Getenv("NODE_ENV") == "prod" {
				go listenMessages()
				// }

				return r.Run(":" + port)
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func listenMessages() {
	client := middlewares.NewRabbitMQ()
	db := middlewares.NewDB()

	// Subscribe to all required message queues
	subscribeToQueue(client, db, "cron_site_order", func(m *message.Message) {
		log.Printf("Received cron_site_order message: %s", string(m.Payload))
		handlers.CronSiteOrders(client, db, string(m.Payload))
		m.Ack()
	})

	subscribeToQueue(client, db, "cron_site_ecom_order", func(m *message.Message) {
		log.Printf("Received cron_site_ecom_order message: %s", string(m.Payload))
		// TODO: Implement handler
		m.Ack()
	})

	subscribeToQueue(client, db, "cron_site_orders_by_days", func(m *message.Message) {
		log.Printf("Received cron_site_orders_by_days message: %s", string(m.Payload))
		handlers.ProcessSiteOrdersByDays(client, db, m.Payload)
		m.Ack()
	})

	subscribeToQueue(client, db, "cron_site_finances", func(m *message.Message) {
		log.Printf("Received cron_site_finances message: %s", string(m.Payload))
		handlers.ProcessSiteFinances(client, db, m.Payload)
		m.Ack()
	})

	subscribeToQueue(client, db, "cron_site_order_feedbacks", func(m *message.Message) {
		log.Printf("Received cron_site_order_feedbacks message: %s", string(m.Payload))
		handlers.ProcessFeedbacks(client, db, m.Payload)
		m.Ack()
	})

	subscribeToQueue(client, db, "cron_site_order_incident_list", func(m *message.Message) {
		log.Printf("Received cron_site_order_incident_list message: %s", string(m.Payload))
		handlers.ProcessIncidents(client, db, m.Payload)
		m.Ack()
	})

	// Block forever
	select {}
}

// Helper function to subscribe to a queue and handle errors
func subscribeToQueue(client *rabbitmq.RabbitClient, db *gorm.DB, topic string, handler func(*message.Message)) {
	messages, err := client.Subscribe(topic, "merchant_service")
	if err != nil {
		log.Printf("Error subscribing to %s: %v", topic, err)
		return
	}

	// Process messages in a goroutine
	go rabbitmq.ProcessMessages(messages, handler)
}
