package router

import (
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/merchant-service/router/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	required := middlewares.RequireAuth
	api := r.Group("v1/merchant-service")
	{
		api.GET("health", handlers.HealthCheck)
		// Cron jobs
		cronJobs := api.Group("cron")
		{
			cronJobs.GET("/token_account/refresh", required("internal"), handlers.CronRefreshTokenAccountsV2)
			cronJobs.GET("/orders", required("internal"), handlers.CronSiteOrdersHandler)
			cronJobs.GET("/bank/transactions", required("internal"), handlers.CronBankTransactions)
			cronJobs.GET("/ecom/orders", required("internal"), handlers.CronSiteEcomOrdersHandler)
			cronJobs.GET("/orders_in_x_days", required("internal"), handlers.CronSiteOrdersInXDays)
			cronJobs.GET("/site/finances", required("internal"), handlers.CronSiteFinances)
			cronJobs.GET("/site/feedbacks", required("internal"), handlers.CronSiteFeedbacks)
			cronJobs.GET("/site/incidents", required("internal"), handlers.CronSiteIncidents)
		}

		// Merchant Menu Management
		menuGroup := api.Group("menu")
		{
			menuGroup.POST("/sync", required(), handlers.SyncMenu)
			menuGroup.POST("/active", required(), handlers.ActiveMenuItems)
			menuGroup.POST("/active-option", required(), handlers.ActiveMenuOptionItems)
			menuGroup.POST("/delete", required(), handlers.DeleteMenuCategoryItem)
		}

		// NutiFood Integration
		nutifoodGroup := api.Group("nutifood")
		{
			nutifoodGroup.POST("/menu-items", required(), handlers.GetNutifoodMenuItems)
			nutifoodGroup.POST("/menu-items-v2", required(), handlers.GetNutifoodMenuItemsV2)
			nutifoodGroup.POST("/sync-order", required(), handlers.SyncNutifoodOrder)
			nutifoodGroup.POST("/sync-order-v2", required(), handlers.SyncNutifoodOrderV2)
		}

		// NexPOS Integration
		nexposGroup := api.Group("nexpos")
		{
			nexposGroup.POST("/menu-items", required(), handlers.GetNexposMenuItems)
			nexposGroup.POST("/sync-order", required(), handlers.SyncNexposOrder)
		}
	}

	return r
}
