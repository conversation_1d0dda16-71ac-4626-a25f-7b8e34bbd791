package handlers

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

// CronRefreshTokenAccounts refreshes token accounts that have failed multiple times
func CronRefreshTokenAccounts(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:get_site_order_by_source_errors"

	// Get cached items from queue
	cachedItems, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(cachedItems) == 0 {
		c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
			"success": true,
			"data":    []any{},
		})
		return
	}

	// Check if first item has enough count to trigger refresh
	if count, ok := cachedItems[0]["count"].(float64); !ok || count < 50 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    []any{},
		})
		return
	}

	// Pick items from queue for processing
	selectedSiteErrors, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         5,
		"min_duration": 0,
		"delete_queue": true,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected items
	tasks := []*utils.Task{}
	for _, siteError := range selectedSiteErrors {
		tokenCode, ok := siteError["token_code"].(string)
		if !ok || tokenCode == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			_, err := token.RefreshNewToken(db, tokenCode)
			if err != nil {
				return err
			}
			return nil
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    selectedSiteErrors,
	})
}

// CronRefreshTokenAccountsV2 refreshes expired tokens one at a time
func CronRefreshTokenAccountsV2(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Find one expired token
	var tokenAccount models.TokenAccount
	now := time.Now()

	dbErr := db.Where("source IN (?) AND expired_at <= ?",
		[]string{"grab_express", "ahamove", "be", "grab", "grab_mart_official", "zalo"},
		now).
		Order("updated_at ASC").
		First(&tokenAccount).Error

	if dbErr != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	// Refresh the token
	_, refreshErr := token.RefreshNewToken(db, tokenAccount.TokenCode)
	if refreshErr != nil {
		// Log error but still return success
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tokenAccount.TokenCode,
	})
}
