package handlers

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/spf13/cast"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// CronSiteOrdersHandler handles the API endpoint for processing site orders
func CronSiteOrdersHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	var sites []models.Site
	if err := db.Where("active = ?", true).
		Where("(shopee_token->>'token_code' != '') OR (grab_token->>'token_code' != '') OR (be_token->>'token_code' != '')").
		Select("id, name").
		Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range sites {
		tasks = append(tasks, utils.NewTask(func() error {
			return rb.Publish("cron_site_order", []byte(cast.ToString(site.ID)))
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// CronSiteEcomOrdersHandler handles the API endpoint for processing ecommerce site orders
func CronSiteEcomOrdersHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:site_ecom_orders"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with active sites that have ecom tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		fiveMinutesAgo := time.Now().Add(-5 * time.Minute)

		if err := db.Where("active = ?", true).
			Where("last_cron_ecom_order IS NULL OR last_cron_ecom_order <= ?", fiveMinutesAgo).
			Where("tokens @> ?", `[{"source": "shopee_ecom", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "lazada", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "tiktok", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         5,
		"min_duration": 300,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Update last_cron_ecom_order timestamp
			if err := db.Model(&models.Site{}).Where("id = ?", siteID).
				Update("last_cron_ecom_order", time.Now()).Error; err != nil {
				log.Printf("Error updating last_cron_ecom_order: %v", err)
			}

			return rb.Publish("cron_site_ecom_order", []byte(siteID))
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// CronSiteOrdersInXDays handles the API endpoint for processing site orders in a date range
func CronSiteOrdersInXDays(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:site_orders_in_x_days"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with active sites
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("active = ?", true).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         2,
		"min_duration": 4 * 60 * 60, // 4 hours
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID and date range
			message := map[string]any{
				"site_id": siteID,
				"from":    time.Now().AddDate(0, 0, -20).Format(time.RFC3339),
				"to":      time.Now().Format(time.RFC3339),
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_orders_by_days", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 2)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// ProcessSiteOrdersByDays is a background handler for processing site orders by days
func ProcessSiteOrdersByDays(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Implementation will be added in a separate function
	// This will be called by the message queue listener
	return nil
}
