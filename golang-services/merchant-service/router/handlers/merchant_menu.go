package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/grab"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/shopee"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
)

// SyncMenu synchronizes a menu item with the merchant platform
func SyncMenu(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		SiteID string `json:"site_id" binding:"required"`
		Source string `json:"source" binding:"required"`
		ItemID string `json:"item_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get site
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get token
	tokenObj, err := token.GetTokenBySite(db, site, req.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get token: " + err.Error(),
		})
		return
	}

	// Get site menu
	var siteMenu models.BrandMenu
	if err := db.Where("site_id = ?", req.SiteID).First(&siteMenu).Error; err != nil {
		// Try to get brand menu
		var site models.Site
		if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to get site: " + err.Error(),
			})
			return
		}

		if err := db.Where("brand_id = ?", site.BrandID).First(&siteMenu).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to get menu: " + err.Error(),
			})
			return
		}
	}

	// Sync menu based on source
	var syncErr error
	switch req.Source {
	case "grab", "grab_mart":
		client := grab.NewGrabMerchantClient()
		syncErr = client.SyncMenu(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, &siteMenu, req.ItemID)
	case "shopee_food", "shopee_fresh":
		client := shopee.ShopeeMerchantClient{}
		syncErr = client.SyncMenu(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, &siteMenu, req.ItemID)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported source: " + req.Source,
		})
		return
	}

	if syncErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to sync menu: " + syncErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// ActiveMenuItems activates or deactivates menu items
func ActiveMenuItems(c *gin.Context) {
	// Parse request
	var req struct {
		SiteID         string           `json:"site_id" binding:"required"`
		Source         string           `json:"source" binding:"required"`
		UpdateItems    []map[string]any `json:"update_items" binding:"required"`
		UpdateAllItems bool             `json:"update_all_items"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	db := middlewares.GetDB(c)

	// Get site
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get token
	tokenObj, err := token.GetTokenBySite(db, site, req.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get token: " + err.Error(),
		})
		return
	}

	// Activate menu items based on source
	var updatedItems []map[string]any
	var activeErr error

	switch req.Source {
	case "grab", "grab_mart":
		client := grab.NewGrabMerchantClient()
		updatedItems, activeErr = client.ActiveMenuItems(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, req.UpdateItems, req.UpdateAllItems)
	case "shopee_food", "shopee_fresh":
		client := shopee.ShopeeMerchantClient{}
		updatedItems, activeErr = client.ActiveMenuItems(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, req.UpdateItems, req.UpdateAllItems)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported source: " + req.Source,
		})
		return
	}

	if activeErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to activate menu items: " + activeErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updatedItems,
	})
}

// ActiveMenuOptionItems activates or deactivates menu option items
func ActiveMenuOptionItems(c *gin.Context) {
	// Parse request
	var req struct {
		SiteID         string           `json:"site_id" binding:"required"`
		Source         string           `json:"source" binding:"required"`
		UpdateItems    []map[string]any `json:"update_items" binding:"required"`
		UpdateAllItems bool             `json:"update_all_items"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	db := middlewares.GetDB(c)

	// Get site
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get token
	tokenObj, err := token.GetTokenBySite(db, site, req.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get token: " + err.Error(),
		})
		return
	}

	// Activate menu option items based on source
	var updatedItems []map[string]any
	var activeErr error

	switch req.Source {
	case "grab", "grab_mart":
		client := grab.NewGrabMerchantClient()
		updatedItems, activeErr = client.ActiveMenuOptionItems(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, req.UpdateItems, req.UpdateAllItems)
	case "shopee_food", "shopee_fresh":
		client := shopee.ShopeeMerchantClient{}
		updatedItems, activeErr = client.ActiveMenuOptionItems(&models.Token{
			AccessToken: tokenObj.AccessToken,
		}, req.UpdateItems)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported source: " + req.Source,
		})
		return
	}

	if activeErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to activate menu option items: " + activeErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    updatedItems,
	})
}

// DeleteMenuCategoryItem deletes a menu item from a category
func DeleteMenuCategoryItem(c *gin.Context) {
	// Parse request
	var req struct {
		SiteID       string `json:"site_id" binding:"required"`
		Source       string `json:"source" binding:"required"`
		CategoryName string `json:"category_name" binding:"required"`
		ItemName     string `json:"item_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	db := middlewares.GetDB(c)

	// Get site
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get token
	tokenObj, err := token.GetTokenBySite(db, site, req.Source)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get token: " + err.Error(),
		})
		return
	}

	// Delete menu item based on source
	var deleteErr error

	switch req.Source {
	case "grab", "grab_mart":
		client := grab.NewGrabMerchantClient()
		deleteErr = client.DeleteMenuCategoryItem(&models.Token{
			AccessToken: tokenObj.AccessToken,
			SiteID:      tokenObj.SiteID,
		}, req.CategoryName, req.ItemName)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported source: " + req.Source,
		})
		return
	}

	if deleteErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to delete menu item: " + deleteErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
