package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// CronSiteFeedbacks handles the API endpoint for processing site order feedbacks
func CronSiteFeedbacks(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)
	queueKey := "cron_job:site_order_feedbacks"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with sites that have relevant tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("tokens @> ?", `[{"source": "shopee", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "shopee_fresh", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab_mart", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         1,
		"min_duration": 3600, // 1 hour
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID and limit
			message := map[string]any{
				"site_id": siteID,
				"limit":   100,
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_order_feedbacks", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 1)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// CronSiteIncidents handles the API endpoint for processing site order incidents
func CronSiteIncidents(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)
	queueKey := "cron_job:site_order_incident_list"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with sites that have relevant tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("tokens @> ?", `[{"source": "grab", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab_mart", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         10,
		"min_duration": 600, // 10 minutes
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID
			message := map[string]any{
				"site_id": siteID,
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_order_incident_list", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 10)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// ProcessFeedbacks is a background handler for processing site order feedbacks
func ProcessFeedbacks(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Parse the message
	var msg struct {
		SiteID string `json:"site_id"`
		Limit  int    `json:"limit"`
	}

	if err := json.Unmarshal(message, &msg); err != nil {
		return err
	}

	// Find the site
	var site models.Site
	if err := db.Where("id = ?", msg.SiteID).First(&site).Error; err != nil {
		return err
	}

	// Process each source
	sources := []string{"grab", "grab_mart", "shopee_food", "shopee_fresh", "be"}
	for _, source := range sources {
		if err := getFeedbacksBySource(db, &site, source, msg.Limit); err != nil {
			log.Printf("Error getting feedbacks for site %s, source %s: %v", site.ID, source, err)
		}
	}

	return nil
}

// ProcessIncidents is a background handler for processing site order incidents
func ProcessIncidents(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Parse the message
	var msg struct {
		SiteID string `json:"site_id"`
	}

	if err := json.Unmarshal(message, &msg); err != nil {
		return err
	}

	// Find the site
	var site models.Site
	if err := db.Where("id = ?", msg.SiteID).First(&site).Error; err != nil {
		return err
	}

	// Process each source
	sources := []string{"grab", "grab_mart"}
	for _, source := range sources {
		if err := getIncidentsBySource(db, &site, source); err != nil {
			log.Printf("Error getting incidents for site %s, source %s: %v", site.ID, source, err)
		}
	}

	return nil
}

// getFeedbacksBySource retrieves order feedbacks for a specific site and source
func getFeedbacksBySource(db *gorm.DB, site *models.Site, source string, limit int) error {
	// Get token for the site and source
	tokenAccount, err := token.GetTokenBySite(db, *site, source)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		return fmt.Errorf("token not found or invalid for site %s, source %s", site.ID, source)
	}

	// Skip if token has too many failures
	if tokenAccount.FailCount > 100 {
		return fmt.Errorf("token has too many failures for site %s, source %s", site.ID, source)
	}

	// Get merchant client for the source
	iMerchant := merchant.NewMerchant(source)
	if iMerchant == nil {
		return fmt.Errorf("merchant client not found for source %s", source)
	}

	// Get order feedbacks
	var feedbackList []any

	// This part would need to be implemented in the merchant interface
	// For now, we'll just log that we would fetch feedbacks
	log.Printf("Would fetch feedbacks for site %s, source %s, limit %d", site.ID, source, limit)

	// Process feedbacks
	if len(feedbackList) > 0 {
		// Map feedback to OrderFeedback model
		// Save to database if not exists
		log.Printf("Would process %d feedbacks for site %s, source %s", len(feedbackList), site.ID, source)
	}

	return nil
}

// getIncidentsBySource retrieves order incidents for a specific site and source
func getIncidentsBySource(db *gorm.DB, site *models.Site, source string) error {
	// Get token for the site and source
	tokenAccount, err := token.GetTokenBySite(db, *site, source)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		return fmt.Errorf("token not found or invalid for site %s, source %s", site.ID, source)
	}

	// Skip if token has too many failures
	if tokenAccount.FailCount > 100 {
		return fmt.Errorf("token has too many failures for site %s, source %s", site.ID, source)
	}

	// Get merchant client for the source
	iMerchant := merchant.NewMerchant(source)
	if iMerchant == nil {
		return fmt.Errorf("merchant client not found for source %s", source)
	}

	// Get order incidents
	var incidentList []any

	// This part would need to be implemented in the merchant interface
	// For now, we'll just log that we would fetch incidents
	log.Printf("Would fetch incidents for site %s, source %s", site.ID, source)

	// Process incidents
	if len(incidentList) > 0 {
		// Map incident to OrderIncident model
		// Save to database if not exists
		log.Printf("Would process %d incidents for site %s, source %s", len(incidentList), site.ID, source)
	}

	return nil
}
