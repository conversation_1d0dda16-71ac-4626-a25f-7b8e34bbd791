package handlers

import (
	"context"
	"net/http"
	"os"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

// GetNutifoodMenuItems retrieves menu items from NutiFood
func GetNutifoodMenuItems(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		Shop  string   `json:"shop" binding:"required"`
		Items []string `json:"items" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create NutiFood client
	client := merchant.NewNutifoodClient(
		os.Getenv("NUTIFOOD_URL"),
		os.Getenv("NUTIFOOD_API_KEY"),
		db,
	)

	// Get menu items
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.GetMenuItems(ctx, req.Shop, req.Items)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get menu items: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetNutifoodMenuItemsV2 retrieves menu items from multiple shops
func GetNutifoodMenuItemsV2(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		Shops []string `json:"shops" binding:"required"`
		Items []string `json:"items" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create NutiFood client
	client := merchant.NewNutifoodClient(
		os.Getenv("NUTIFOOD_URL"),
		os.Getenv("NUTIFOOD_API_KEY"),
		db,
	)

	// Get menu items
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.GetMenuItemsV2(ctx, req.Shops, req.Items)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get menu items: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SyncNutifoodOrder synchronizes an order with NutiFood
func SyncNutifoodOrder(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		OrderID string `json:"order_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get order
	var order models.Order
	if err := db.Where("order_id = ?", req.OrderID).First(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get order: " + err.Error(),
		})
		return
	}

	// Get site
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get hub
	var hub models.Hub
	if err := db.Where("id = ?", site.HubID).First(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get hub: " + err.Error(),
		})
		return
	}

	// Get brand menu
	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", site.BrandID).First(&brandMenu).Error; err != nil {
		// Create brand menu if it doesn't exist
		brandMenu = models.BrandMenu{
			BrandID:          site.BrandID,
			Categories:       []models.Category{},
			OptionCategories: []models.OptionCategory{},
		}
		if err := db.Create(&brandMenu).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to create brand menu: " + err.Error(),
			})
			return
		}
	}

	// Create NutiFood client
	client := merchant.NewNutifoodClient(
		os.Getenv("NUTIFOOD_URL"),
		os.Getenv("NUTIFOOD_API_KEY"),
		db,
	)

	// Sync order
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.SyncAnOrder(ctx, &order, &site, &hub, &brandMenu)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to sync order: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SyncNutifoodOrderV2 synchronizes an order with NutiFood using the V2 API
func SyncNutifoodOrderV2(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		OrderID string `json:"order_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get order
	var order models.Order
	if err := db.Where("order_id = ?", req.OrderID).First(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get order: " + err.Error(),
		})
		return
	}

	// Get site
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get site: " + err.Error(),
		})
		return
	}

	// Get hub
	var hub models.Hub
	if err := db.Where("id = ?", site.HubID).First(&hub).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get hub: " + err.Error(),
		})
		return
	}

	// Get brand menu
	var brandMenu models.BrandMenu
	if err := db.Where("brand_id = ?", site.BrandID).First(&brandMenu).Error; err != nil {
		// Create brand menu if it doesn't exist
		brandMenu = models.BrandMenu{
			BrandID:          site.BrandID,
			Categories:       []models.Category{},
			OptionCategories: []models.OptionCategory{},
		}
		if err := db.Create(&brandMenu).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to create brand menu: " + err.Error(),
			})
			return
		}
	}

	// Create NutiFood client
	client := merchant.NewNutifoodClient(
		os.Getenv("NUTIFOOD_URL"),
		os.Getenv("NUTIFOOD_API_KEY"),
		db,
	)

	// Sync order
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.SyncAnOrderV2(ctx, &order, &site, &hub, &brandMenu)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to sync order: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}
