package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

// GetNexposMenuItems retrieves menu items from NexPOS
func GetNexposMenuItems(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		Shop  string   `json:"shop" binding:"required"`
		Items []string `json:"items" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create NexPOS client
	client := merchant.NewNexposClient(db)

	// Get menu items
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.GetMenuItems(ctx, req.Shop, req.Items)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get menu items: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"menu_items": result,
	})
}

// SyncNexposOrder synchronizes an order with NexPOS
func SyncNexposOrder(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Parse request
	var req struct {
		OrderID string `json:"order_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get order
	var order models.Order
	if err := db.Where("order_id = ?", req.OrderID).First(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to get order: " + err.Error(),
		})
		return
	}

	// Create NexPOS client
	client := merchant.NewNexposClient(db)

	// Sync order
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := client.SyncAnOrder(ctx, &order)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to sync order: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}
