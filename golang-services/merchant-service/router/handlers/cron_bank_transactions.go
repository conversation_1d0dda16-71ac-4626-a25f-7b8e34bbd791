package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
)

// CronBankTransactions handles the API endpoint for processing bank transactions
func CronBankTransactions(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:bank_transactions"

	fmt.Printf("%s Get Bank Transactions\n", time.Now().Format("2006-01-02 15:04:05"))

	// Call the payment API to get transactions
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("GET", "https://pay.nexdor.tech/api/transactions", nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	req.Header.Add("Authorization", fmt.Sprintf("NEXDOR %s", os.Getenv("NEXDOR_API_KEY")))

	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Parse response
	var response struct {
		Data []map[string]any `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	transactions := response.Data
	if transactions == nil {
		transactions = []map[string]any{}
	}

	// Get old transactions from Redis
	oldTransactionsJSON := redis.GetByKey(queueKey)
	// No error handling needed as GetByKey returns empty string on error

	var oldTransactions []map[string]any
	if oldTransactionsJSON != "" {
		if err := json.Unmarshal([]byte(oldTransactionsJSON), &oldTransactions); err != nil {
			oldTransactions = []map[string]any{}
		}
	} else {
		oldTransactions = []map[string]any{}
	}

	// If no new transactions, return early
	if len(oldTransactions) == len(transactions) {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    []any{},
		})
		return
	}

	// Save new transactions to Redis
	transactionsJSON, err := json.Marshal(transactions)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := redis.SetKeyValue(queueKey, string(transactionsJSON)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update order payments based on transactions
	for _, transaction := range transactions {
		partnerTransactionID, ok := transaction["partner_transaction_id"].(string)
		if !ok || partnerTransactionID == "" {
			continue
		}

		// Find and update order payment
		var orderPayment models.OrderPayment
		if err := db.Where("transaction_id = ?", partnerTransactionID).First(&orderPayment).Error; err != nil {
			continue
		}

		// Update payment status
		orderPayment.Status = "COMPLETED"
		if err := db.Save(&orderPayment).Error; err != nil {
			continue
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    []any{},
	})
}
