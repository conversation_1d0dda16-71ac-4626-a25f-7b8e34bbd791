package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// CronSiteFinances handles the API endpoint for processing site finances
func CronSiteFinances(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)

	// Find orders that need transaction processing
	var orders []models.Order
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)

	if err := db.Where("source IN (?) AND data->'transaction' IS NULL AND created_at >= ? AND status = ? AND (last_cron_transaction IS NULL OR last_cron_transaction <= ?)",
		[]string{"grab", "grab_mart", "tiktok"},
		thirtyDaysAgo,
		"FINISH",
		time.Now().Add(-3*time.Minute)).
		Order("created_at ASC").
		Limit(1).
		Find(&orders).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Update last_cron_transaction timestamp
	if len(orders) > 0 {
		orderIDs := funk.Map(orders, func(order models.Order) string {
			return string(order.ID)
		}).([]string)

		if err := db.Model(&models.Order{}).Where("id IN ?", orderIDs).
			Update("last_cron_transaction", time.Now()).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// Group orders by site_id
	ordersBySite := make(map[string][]models.Order)
	for _, order := range orders {
		ordersBySite[order.SiteID] = append(ordersBySite[order.SiteID], order)
	}

	// Process each site's orders
	tasks := []*utils.Task{}

	for siteID, siteOrders := range ordersBySite {
		siteID := siteID // Create a copy for the closure
		siteOrders := siteOrders

		tasks = append(tasks, utils.NewTask(func() error {
			// Find earliest and latest order times
			var earliestTime, latestTime int64
			orderIDs := []string{}

			for i, order := range siteOrders {
				orderIDs = append(orderIDs, order.OrderID)

				// Extract order times from data_mapping
				var orderTimeSort, deliveryTimeUnix int64

				// Use a default value if we can't extract from data
				orderTimeSort = time.Now().Unix() - 86400 // 1 day ago
				deliveryTimeUnix = time.Now().Unix()

				if i == 0 || orderTimeSort < earliestTime {
					earliestTime = orderTimeSort
				}

				if i == 0 || deliveryTimeUnix > latestTime {
					latestTime = deliveryTimeUnix
				}
			}

			// Create a message with site ID and date range
			message := map[string]any{
				"site_id":   siteID,
				"from":      time.Unix(earliestTime, 0).Format(time.RFC3339),
				"to":        time.Unix(latestTime, 0).Format(time.RFC3339),
				"order_ids": orderIDs,
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_finances", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	// Extract order IDs for response
	orderIDs := funk.Map(orders, func(order models.Order) string {
		return order.OrderID
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orderIDs,
	})
}

// ProcessSiteFinances is a background handler for processing site finances
func ProcessSiteFinances(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Implementation will be added in a separate function
	// This will be called by the message queue listener
	return nil
}
