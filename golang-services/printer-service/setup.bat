@echo off

@REM https://storage.googleapis.com/nexpos-web/setup.bat

set "nssmPath=C:\pos-printer-service\nssm.exe"
set "servicePath=C:\pos-printer-service"
set "serviceExe=pos-printer-service.exe"
set "serviceConfig=pos-printer-service.cfg"
set "downloadURL=https://storage.googleapis.com/nexpos-web/printer-service-v2.zip"
set /p "hubCode=Enter HubCode: "

REM Check if nssm.exe exists in the folder
if exist "%nssmPath%" (
    REM Stop and delete the service
    "%nssmPath%" stop pos-printer-service
    "%nssmPath%" remove pos-printer-service confirm

    REM Delete the pos-printer-service folder
    rmdir /s /q "%servicePath%"
)
REM Create the pos-printer-service folder
mkdir "%servicePath%"


REM Download the zip file
powershell -Command "(New-Object System.Net.WebClient).DownloadFile('%downloadURL%', '%servicePath%\printer-service-v2.zip')"

REM Extract the zip file
powershell -Command "Expand-Archive -Path '%servicePath%\printer-service-v2.zip' -DestinationPath '%servicePath%' -Force"

REM Replace the content of pos-printer-service.cfg with the HubCode
echo %hubCode% > "%servicePath%\%serviceConfig%"

REM Install the service
"%nssmPath%" install pos-printer-service "%servicePath%\%serviceExe%"

REM Pause for 5 seconds
ping -n 5 127.0.0.1 > nul

REM Start the service
"%nssmPath%" start pos-printer-service

pause