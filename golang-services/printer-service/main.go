package main

import (
	"encoding/json"
	"fmt"
	"image"
	"image/png"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/printer-service/escpos"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

const VERSION = "2"

func main() {
	configs := readConfig("pos-printer-service.cfg")
	fmt.Println(configs)
	for {
		fmt.Println(time.Now())
		for _, config := range configs {
			for _, printer := range config.Printers {
				url := fmt.Sprintf("https://api.nexpos.io/api/hubs/%s/prints?version=%s&hub_code=%s", config.HubID, VERSION, strings.TrimSpace(config.HubCode))
				fmt.Println(url)
				resp, err := resty.New().R().Get(url)

				if err != nil {
					log.Println("Error:", err)
					continue
				}

				apiResponse := APIResponse{}
				if err := json.Unmarshal(resp.Body(), &apiResponse); err != nil {
					log.Println("Error parsing API response:", err)
					continue
				}

				fmt.Println(apiResponse.Data)
				if len(apiResponse.Data) > 0 {
					socket, err := net.Dial("tcp", printer)
					if err != nil {
						println(err.Error())
					}

					p := escpos.New(socket)
					for _, doc := range apiResponse.Data {
						update := map[string]any{
							"status":        "printed",
							"error_message": "success",
						}

						img, err := DownloadAndDecodeImage(doc.FileURL)

						if err != nil {
							update["status"] = "error"
							update["error_message"] = err.Error()
						} else if _, err := p.PrintImage(img); err != nil {
							update["status"] = "error"
							update["error_message"] = err.Error()
						} else if err := p.PrintAndCut(); err != nil {
							update["status"] = "error"
							update["error_message"] = err.Error()
						}

						if _, err := resty.New().R().SetBody(update).
							Put(fmt.Sprintf("https://api.nexpos.io/api/sites/%s/prints/%s", config.HubID, doc.ID)); err != nil {
							fmt.Println(err)
						}
					}
					if socket != nil {
						socket.Close()
					}
				}

			}
		}

		time.Sleep(time.Duration(5000) * time.Millisecond)
	}
}

type PrintData struct {
	ID      string `json:"_id"`
	FileURL string `json:"file_url"`
	// Add other properties as needed
}

type APIResponse struct {
	Data []PrintData `json:"data"`
	// Add other properties as needed
}

type Config struct {
	Printers []string `json:"printers"`
	HubID    string   `json:"hub_id"`
	HubCode  string   `json:"hub_code"`
}

func readConfig(filename string) []Config {
	hubCfg, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Fatal("Error reading config file:", err)
	}

	resp, err := resty.New().R().Get(fmt.Sprintf("https://api.nexpos.io/api/hub/printers?code=%s", strings.TrimSpace(string(hubCfg))))
	if err != nil {
		log.Fatal("Error get printers:", err)
	}
	fmt.Println(string(resp.Body()))
	var configs []Config
	if err = json.Unmarshal([]byte(gjson.ParseBytes(resp.Body()).Get("data").String()), &configs); err != nil {
		log.Fatal("Error parsing config file:", err)
	}

	return configs
}

func DownloadAndDecodeImage(url string) (image.Image, error) {
	response, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	img, err := png.Decode(response.Body)
	if err != nil {
		return nil, err
	}

	return img, nil
}
