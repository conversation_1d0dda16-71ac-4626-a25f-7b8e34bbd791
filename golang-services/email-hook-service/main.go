package main

import (
	"bytes"
	"io"
	"log"
	"net/http"
	"sort"
	"sync"
	"time"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"github.com/emersion/go-message/mail"
	"github.com/gin-gonic/gin"
	"github.com/k3a/html2text"
)

type EmailConfig struct {
	Email        string
	AppPassword  string
	PollInterval time.Duration
}

type EmailData struct {
	Subject     string         `json:"subject"`
	From        string         `json:"from"`
	Date        time.Time      `json:"date"`
	PlainText   string         `json:"plainText"`
	HTML        string         `json:"html"`
	ExtractData map[string]any `json:"extractData,omitempty"`
}

var (
	emailCache     []EmailData
	emailCacheLock sync.RWMutex
	config         EmailConfig
)

func main() {
	config = EmailConfig{
		Email:        "<EMAIL>",
		AppPassword:  "ntks yxep bzoc xxyu",
		PollInterval: 30 * time.Second,
	}

	go monitorEmails()

	r := gin.Default()
	r.GET("/emails", getLastEmails)
	log.Println("Starting server on :8080")
	r.Run(":8080")
}

func getLastEmails(c *gin.Context) {
	emailCacheLock.RLock()
	defer emailCacheLock.RUnlock()

	limit := 50
	if len(emailCache) < limit {
		limit = len(emailCache)
	}

	sort.Slice(emailCache, func(i, j int) bool {
		return emailCache[i].Date.After(emailCache[j].Date)
	})

	c.JSON(http.StatusOK, gin.H{
		"emails": emailCache[:limit],
		"count":  limit,
		"total":  len(emailCache),
	})
}

func monitorEmails() {
	log.Printf("Starting email monitoring for %s", config.Email)
	for {
		fetchLatestEmails()
		time.Sleep(config.PollInterval)
	}
}

func fetchLatestEmails() {
	// Connect to Gmail IMAP server
	c, err := client.DialTLS("imap.gmail.com:993", nil)
	if err != nil {
		log.Printf("Failed to connect: %v", err)
		return
	}
	defer c.Logout()

	// Login
	if err := c.Login(config.Email, config.AppPassword); err != nil {
		log.Printf("Failed to login: %v", err)
		return
	}

	mbox, err := c.Select("[Gmail]/Tất cả thư", false)
	if err != nil {
		log.Printf("Failed to select [Gmail]/All Mail: %v", err)
		return
	}

	// Calculate sequence number for the last 50 messages
	startSeqNum := uint32(1)
	if mbox.Messages > 50 {
		startSeqNum = mbox.Messages - 49 // Fetch last 50 emails
	}

	// Create sequence set
	seqSet := new(imap.SeqSet)
	seqSet.AddRange(startSeqNum, mbox.Messages)

	// Items to fetch
	fetchItems := []imap.FetchItem{
		imap.FetchEnvelope,
		imap.FetchRFC822,
	}

	messages := make(chan *imap.Message, 50)
	done := make(chan error, 1)

	go func() {
		done <- c.Fetch(seqSet, fetchItems, messages)
	}()

	var newEmails []EmailData
	for msg := range messages {
		plainText, html, err := getEmailContent(msg)
		if plainText == "" {
			plainText = html2text.HTML2Text(html)
		}
		if err != nil {
			log.Printf("Error getting email content: %v", err)
			continue
		}

		from := ""
		if len(msg.Envelope.From) > 0 {
			from = msg.Envelope.From[0].MailboxName + "@" + msg.Envelope.From[0].HostName
		}

		emailData := EmailData{
			Subject:   msg.Envelope.Subject,
			From:      from,
			Date:      msg.Envelope.Date,
			PlainText: plainText,
			HTML:      html,
		}

		newEmails = append(newEmails, emailData)
	}

	if err := <-done; err != nil {
		log.Printf("Error fetching messages: %v", err)
		return
	}

	if len(newEmails) > 0 {
		emailCacheLock.Lock()
		emailCache = newEmails
		emailCacheLock.Unlock()
	}
}
func getEmailContent(msg *imap.Message) (plainText, html string, err error) {
	r := msg.GetBody(&imap.BodySectionName{})
	if r == nil {
		return "", "", nil
	}

	var buf bytes.Buffer
	_, err = io.Copy(&buf, r)
	if err != nil {
		return "", "", err
	}

	mr, err := mail.CreateReader(bytes.NewReader(buf.Bytes()))
	if err != nil {
		return "", "", err
	}

	for {
		p, err := mr.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", "", err
		}

		switch h := p.Header.(type) {
		case *mail.InlineHeader:
			contentType, _, _ := h.ContentType()
			b, _ := io.ReadAll(p.Body)
			if contentType == "text/plain" {
				plainText = string(b)
			} else if contentType == "text/html" {
				html = string(b)
			}
		}
	}

	return plainText, html, nil
}
