package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/email-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "email-service"
	app.Usage = "Email service"
	app.Version = "1.0.0"
	app.Commands = []cli.Command{
		{
			Name:      "Start",
			ShortName: "start",
			Usage:     "Start service",
			Flags: []cli.Flag{
				cli.StringFlag{
					Name:   "port",
					Usage:  "Port the server listens to",
					EnvVar: "PORT",
					Value:  "3000",
				},
			},
			Action: func(c *cli.Context) error {
				port := c.String("port")

				r := gin.New()
				r.Use(middlewares.MyCors())

				router.LoadHandlers(r)

				r.Use(gin.Logger())
				r.Use(gin.Recovery())
				return r.Run(":" + port)
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}
