package handlers

import (
	"bytes"
	"html/template"
	"net/http"
	"net/smtp"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

type EmailRequest struct {
	To           string            `json:"to" binding:"required"`
	CC           []string          `json:"cc"`
	Subject      string            `json:"subject" binding:"required"`
	HTML         string            `json:"html"`
	TemplateName string            `json:"template_name" binding:"required"`
	Data         map[string]string `json:"data"`
}

// Email configuration constants
const (
	SMTPHost     = "smtp.gmail.com"
	SMTPPort     = "587"
	EmailFrom    = "<EMAIL>"
	EmailAppPass = "mjrc ffgt ovqs lphz" // Gmail App Password
)

func SendEmail(c *gin.Context) {
	var req EmailRequest
	if err := c.ShouldBind<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.<PERSON><PERSON>r(),
		})
		return
	}

	// Prepare email headers
	auth := smtp.PlainAuth("", <PERSON>ailFrom, EmailAppPass, SMTPHost)
	to := []string{req.To}
	if len(req.CC) > 0 {
		to = append(to, req.CC...)
	}

	var body bytes.Buffer
	var tmpl *template.Template
	var err error

	if req.TemplateName == "" {
		if req.HTML == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Both TemplateName and HTML content are empty",
			})
			return
		}

		tmpl, err = template.New("htmlContent").Parse(req.HTML)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to parse HTML content: " + err.Error(),
			})
			return
		}
	} else {
		templatePath := filepath.Join("templates", req.TemplateName+".html")
		tmpl, err = template.ParseFiles(templatePath)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to load template: " + err.Error(),
			})
			return
		}
	}

	err = tmpl.Execute(&body, req.Data)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to render template: " + err.Error(),
		})
		return
	}

	fromHeader := `EveryTech <<EMAIL>>`
	msg := []byte("From: " + fromHeader + "\r\n" +
		"To: " + req.To + "\r\n" +
		"Cc: " + joinCC(req.CC) + "\r\n" +
		"Subject: " + req.Subject + "\r\n" +
		"MIME-Version: 1.0\r\n" +
		"Content-Type: text/html; charset=\"utf-8\"\r\n" +
		"\r\n" +
		body.String() + "\r\n")

	err = smtp.SendMail(
		SMTPHost+":"+SMTPPort,
		auth,
		"<EMAIL>",
		to,
		msg,
	)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email queued for delivery",
	})
}

func joinCC(cc []string) string {
	if len(cc) == 0 {
		return ""
	}
	return strings.Join(cc, ",")
}
