package router

import (
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/email-service/router/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	internal := middlewares.InternalServiceAuth()
	api := r.Group("v1/email-service")
	{
		api.GET("health", handlers.HealthCheck)
		api.POST("send", internal, handlers.SendEmail)
	}

	return r
}
