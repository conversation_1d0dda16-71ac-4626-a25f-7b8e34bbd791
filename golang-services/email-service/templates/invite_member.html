<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Welcome to {{.brand_name}}</title>
   <style>
      body {
         font-family: Arial, sans-serif;
         background-color: #f4f4f4;
         margin: 0;
         padding: 0;
      }

      .container {
         max-width: 600px;
         margin: 0 auto;
         background-color: #ffffff;
         padding: 20px;
         border-radius: 8px;
         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header {
         text-align: center;
         padding: 20px 0;
      }

      .header h1 {
         color: #333333;
         font-size: 24px;
         margin: 0;
      }

      .content {
         padding: 20px;
         text-align: center;
      }

      .content p {
         color: #666666;
         font-size: 16px;
         line-height: 1.5;
      }

      .temp-password {
         font-size: 24px;
         font-weight: bold;
         color: #2c3e50;
         margin: 20px 0;
      }

      .button {
         display: inline-block;
         padding: 12px 24px;
         background-color: #3498db;
         color: #ffffff;
         text-decoration: none;
         border-radius: 5px;
         font-size: 16px;
         margin: 20px 0;
      }

      .footer {
         text-align: center;
         padding: 20px;
         font-size: 12px;
         color: #999999;
      }
   </style>
</head>

<body>
   <div class="container">
      <div class="header">
         <h1>Welcome to {{.brand_name}}!</h1>
      </div>
      <div class="content">
         <p>Thank you for joining {{.brand_name}}. You’ve been invited to access your account. Below are your login
            details:</p>

         <p><strong>Your Email:</strong></p>
         <p>{{.email}}</p>

         <p><strong>Your Temporary Password:</strong></p>
         <div class="temp-password">{{.temp_password}}</div>

         <p>Please use the button below to log in and set up your account:</p>
         <a href="{{.url}}" class="button">Log In Now</a>

         <p>If the button doesn't work, you can copy and paste the following link into your browser:</p>
         <p><a href="{{.url}}">{{.url}}</a></p>

         <p>For security, we recommend changing your temporary password after logging in.</p>
      </div>
      <div class="footer">
         <p>This email was sent by {{.brand_name}}. If you did not expect this invitation, please ignore this email.</p>
         <p>© 2025 {{.brand_name}}. All rights reserved.</p>
      </div>
   </div>
</body>

</html>