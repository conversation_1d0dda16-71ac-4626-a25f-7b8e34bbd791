<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Invoice</title>
    <style>
        /* Define clean, professional styles following <PERSON>'s minimalist approach */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .logo {
            max-height: 60px;
        }
        .invoice-title {
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .invoice-details-left, .invoice-details-right {
            flex-basis: 48%;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .invoice-table th, .invoice-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .invoice-table th {
            background-color: #f5f5f5;
        }
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            color: #777;
            font-size: 14px;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .payment-info {
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .highlight {
            color: #3498db;
        }
        .upgrade-features {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .upgrade-features h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 5px;
        }
        .feature-list li {
            padding: 4px 0;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            color: #2ecc71;
            font-weight: bold;
            display: inline-block;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header section with company logo and invoice title -->
        <div class="header">
            <h1>NexDor Technology</h1>
            <h2 class="invoice-title">{{ if eq .email_type "upgrade" }}Chúc mừng! Bạn đã nâng cấp thành công{{ else }}{{ .invoice_type }} Invoice{{ end }}</h2>
        </div>
        
        <!-- Invoice details section -->
        <div class="invoice-details">
            <div class="invoice-details-left">
                <p>{{ if eq .email_type "upgrade" }}<strong>Kính gửi:</strong>{{ else }}<strong>Billed To:</strong>{{ end }}<br>
                {{ .user_name }}</p>
                <p>{{ if eq .email_type "upgrade" }}
                    <strong>Gói dịch vụ:</strong> {{ .plan_name }}<br>
                    <strong>Ngày bắt đầu:</strong> {{ .start_date }}<br>
                    <strong>Ngày hết hạn:</strong> {{ .end_date }}
                   {{ else }}
                    <strong>Invoice Number:</strong> {{ .invoice_number }}<br>
                    <strong>Invoice Date:</strong> {{ .invoice_date }}<br>
                    <strong>Due Date:</strong> {{ .due_date }}
                   {{ end }}</p>
            </div>
            <div class="invoice-details-right">
                <p><strong>{{ .company_name }}</strong><br>
                {{ .company_address }}<br>
                {{ .company_email }}</p>
            </div>
        </div>
        
        {{ if eq .email_type "upgrade" }}
        <!-- Upgrade message -->
        <p>Cảm ơn bạn đã tin tưởng và nâng cấp lên gói <strong>{{ .plan_name }}</strong> của chúng tôi! Gói mới của bạn đã được kích hoạt thành công, và bạn có thể bắt đầu tận hưởng ngay các tính năng tuyệt vời dưới đây:</p>
        
        <div class="upgrade-features">
            <h3>Chi tiết gói của bạn:</h3>
            <ul class="feature-list">
                <li>Gian hàng tăng từ {{ .old_limit }} lên {{ .new_limit }}</li>
                <li>Chi phí: {{ .amount }} {{ .currency }}/{{ .period_text }}</li>
            </ul>
        </div>
        
        <p>Hóa đơn thanh toán của bạn đã được gửi kèm trong email này (vui lòng kiểm tra tệp đính kèm) hoặc bạn có thể xem lại trong phần Quản lý tài khoản trên ứng dụng của chúng tôi.</p>
        
        <p>Nếu bạn có bất kỳ câu hỏi nào hoặc cần hỗ trợ để khám phá các tính năng mới, đừng ngần ngại liên hệ với chúng tôi qua {{ .support_email }} hoặc {{ .support_phone }}. Chúng tôi luôn sẵn sàng đồng hành cùng bạn!</p>
        
        <p>Chúc bạn có những trải nghiệm tuyệt vời!</p>
        
        <p>Trân trọng,<br>
        Đội ngũ {{ .company_name }}</p>
        {{ else }}
        <!-- Invoice items table -->
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Period</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ .plan_name }} ({{ .invoice_type }})</td>
                    <td>{{ .plan_period }}</td>
                    <td>{{ .amount }} {{ .currency }}</td>
                </tr>
                <tr class="total-row">
                    <td colspan="2" style="text-align: right;"><strong>Total:</strong></td>
                    <td>{{ .total_amount }} {{ .currency }}</td>
                </tr>
            </tbody>
        </table>
        
        <!-- Payment information section -->
        <div class="payment-info">
            <h3>Payment Information</h3>
            <p><strong>Payment Method:</strong> {{ .payment_method }}<br>
            <strong>Transaction ID:</strong> {{ .transaction_id }}<br>
            <strong>Status:</strong> <span class="highlight">{{ .payment_status }}</span></p>
        </div>
        {{ end }}
    </div>
</body>
</html>