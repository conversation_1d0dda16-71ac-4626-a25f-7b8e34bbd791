package models

import (
	"time"
)

// Migration represents a database migration record
type Migration struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Version   string    `gorm:"uniqueIndex" json:"version"`
	Name      string    `json:"name"`
	Direction string    `json:"direction"` // "up" or "down"
	AppliedAt time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"applied_at"`
}

// MigrationStatus represents the status of a migration file
type MigrationStatus struct {
	Version   string `json:"version"`
	Name      string `json:"name"`
	Direction string `json:"direction"` // "up" or "down"
	Applied   bool   `json:"applied"`
}

// MigrationRequest represents a request to run migrations
type MigrationRequest struct {
	Direction string `json:"direction"` // "up" or "down"
	Version   string `json:"version"`   // Optional: specific version to migrate to
}

// MigrationResponse represents the response from a migration operation
type MigrationResponse struct {
	Success   bool              `json:"success"`
	Message   string            `json:"message"`
	Migrations []MigrationStatus `json:"migrations,omitempty"`
}
