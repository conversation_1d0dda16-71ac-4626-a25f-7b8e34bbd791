package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/migration-service/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/migration-service/services"
	"gorm.io/gorm"
)

// MigrationHandler handles migration-related API endpoints
type MigrationHandler struct {
	migrationService *services.MigrationService
}

// NewMigrationHandler creates a new migration handler
func NewMigrationHandler(db *gorm.DB) *MigrationHandler {
	return &MigrationHandler{
		migrationService: services.NewMigrationService(db),
	}
}

// HealthCheck handles the health check endpoint
func (h *MigrationHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
	})
}

// GetMigrationStatus returns the status of all migrations
func (h *MigrationHandler) GetMigrationStatus(c *gin.Context) {
	migrations, err := h.migrationService.GetMigrationStatus()
	if err != nil {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"migrations": migrations,
	})
}

// RunMigrations runs migrations in the specified direction
func (h *MigrationHandler) RunMigrations(c *gin.Context) {
	var req models.MigrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate direction
	if req.Direction != "up" && req.Direction != "down" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": "Invalid direction: must be 'up' or 'down'",
		})
		return
	}

	// Run migrations
	migrations, err := h.migrationService.RunMigrations(req.Direction, req.Version)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Migrations completed successfully",
		"migrations": migrations,
	})
}

// RunSpecificMigration runs a specific migration
func (h *MigrationHandler) RunSpecificMigration(c *gin.Context) {
	version := c.Param("version")
	var req struct {
		Direction string `json:"direction"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate direction
	if req.Direction != "up" && req.Direction != "down" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": "Invalid direction: must be 'up' or 'down'",
		})
		return
	}

	// Run the migration
	if err := h.migrationService.RunSpecificMigration(version, req.Direction); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Migration completed successfully",
	})
}
