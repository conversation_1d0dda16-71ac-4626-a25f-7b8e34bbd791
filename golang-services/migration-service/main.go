package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/migration-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/urfave/cli"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	app := cli.NewApp()
	app.Name = "migration-service"
	app.Usage = "Migration service"
	app.Version = "1.0.0"
	app.Commands = []cli.Command{
		{
			Name:      "Start",
			ShortName: "start",
			Usage:     "Start service",
			Flags: []cli.Flag{
				cli.StringFlag{
					Name:   "port",
					Usage:  "Port the server listens to",
					EnvVar: "PORT",
					Value:  "3000",
				},
			},
			Action: func(c *cli.Context) error {
				port := c.String("port")

				r := gin.New()
				r.Use(gin.Logger())
				r.Use(gin.Recovery())
				r.Use(middlewares.MyCors())

				// Connect to database
				dsn := os.Getenv("POSTGRESQL_URI")

				db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
				if err != nil {
					log.Fatalf("Failed to connect to database: %v", err)
				}

				// Set up middleware to provide DB connection
				r.Use(func(c *gin.Context) {
					c.Set("db", db)
					c.Next()
				})

				// User-related models
				db.AutoMigrate(
					&models.User{},
					&models.UserOTP{},
					&models.FCMToken{},
					&models.PartnerAPIKey{},
					&models.UserAddress{},
				)

				// Brand-related models
				db.AutoMigrate(
					&models.Brand{},
					&models.BrandStaff{},
					&models.BrandBillConfig{},
					&models.BrandCommission{},
				)

				// Hub-related models
				db.AutoMigrate(
					&models.Hub{},
					&models.HubStaff{},
					&models.HubStock{},
					&models.HubStockHistory{},
					&models.HubStockTicket{},
				)

				// Site-related models
				db.AutoMigrate(
					&models.Site{},
					&models.SiteMenuGroup{},
					&models.SiteMenuCategory{},
					&models.SiteMenuItem{},
					&models.SiteMenuOption{},
					&models.SiteMenuOptionItem{},
				)

				// Order-related models
				db.AutoMigrate(
					&models.Order{},
					&models.OrderShipment{},
				)

				// Product-related models
				db.AutoMigrate(
					&models.CoreProduct{},
				)

				// Commission-related models
				db.AutoMigrate(
					&models.CommissionSummary{},
				)

				// Token-related models
				db.AutoMigrate(
					&models.TokenAccount{},
				)

				// Voucher-related models
				db.AutoMigrate(
					&models.VoucherPromotion{},
				)

				// Print-related models
				db.AutoMigrate(
					&models.PrintQueue{},
					&models.PrintQueueV2{},
				)

				// Order-index models
				db.AutoMigrate(
					&models.SiteOrderIndex{},
				)

				// Schedule-related models
				db.AutoMigrate(
					&models.ScheduleJob{},
					&models.ScheduleJobHistory{},
				)

				// Load routes
				router.LoadHandlers(r, db)

				return r.Run(":" + port)
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}
