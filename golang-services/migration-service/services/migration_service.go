package services

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/migration-service/models"
	"gorm.io/gorm"
)

// MigrationService handles database migrations
type MigrationService struct {
	DB            *gorm.DB
	MigrationsDir string
}

// NewMigrationService creates a new migration service
func NewMigrationService(db *gorm.DB) *MigrationService {
	// Ensure migrations table exists
	db.AutoMigrate(&models.Migration{})

	return &MigrationService{
		DB:            db,
		MigrationsDir: "migrations",
	}
}

// GetMigrationStatus returns the status of all migrations
func (s *MigrationService) GetMigrationStatus() ([]models.MigrationStatus, error) {
	// Get all applied migrations from the database
	var appliedMigrations []models.Migration
	if err := s.DB.Find(&appliedMigrations).Error; err != nil {
		return nil, fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// Create a map of applied migrations for quick lookup
	appliedMap := make(map[string]bool)
	for _, m := range appliedMigrations {
		key := fmt.Sprintf("%s_%s", m.Version, m.Direction)
		appliedMap[key] = true
	}

	// Get all migration files
	upMigrations, err := s.getMigrationFiles("up")
	if err != nil {
		return nil, err
	}

	downMigrations, err := s.getMigrationFiles("down")
	if err != nil {
		return nil, err
	}

	// Combine and sort all migrations
	var allMigrations []models.MigrationStatus

	// Add up migrations
	for _, file := range upMigrations {
		version, name := parseFilename(file)
		key := fmt.Sprintf("%s_up", version)
		allMigrations = append(allMigrations, models.MigrationStatus{
			Version:   version,
			Name:      name,
			Direction: "up",
			Applied:   appliedMap[key],
		})
	}

	// Add down migrations
	for _, file := range downMigrations {
		version, name := parseFilename(file)
		key := fmt.Sprintf("%s_down", version)
		allMigrations = append(allMigrations, models.MigrationStatus{
			Version:   version,
			Name:      name,
			Direction: "down",
			Applied:   appliedMap[key],
		})
	}

	// Sort migrations by version
	sort.Slice(allMigrations, func(i, j int) bool {
		return allMigrations[i].Version < allMigrations[j].Version
	})

	return allMigrations, nil
}

// RunMigrations runs migrations in the specified direction
func (s *MigrationService) RunMigrations(direction string, targetVersion string) ([]models.MigrationStatus, error) {
	// Validate direction
	if direction != "up" && direction != "down" {
		return nil, fmt.Errorf("invalid direction: %s", direction)
	}

	// Get all migration files in the specified direction
	migrationFiles, err := s.getMigrationFiles(direction)
	if err != nil {
		return nil, err
	}

	// Sort migration files by version
	sort.Strings(migrationFiles)

	// If we're migrating down, reverse the order
	if direction == "down" {
		for i, j := 0, len(migrationFiles)-1; i < j; i, j = i+1, j-1 {
			migrationFiles[i], migrationFiles[j] = migrationFiles[j], migrationFiles[i]
		}
	}

	// Get all applied migrations
	var appliedMigrations []models.Migration
	if err := s.DB.Find(&appliedMigrations).Error; err != nil {
		return nil, fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// Create a map of applied migrations for quick lookup
	appliedMap := make(map[string]bool)
	for _, m := range appliedMigrations {
		key := fmt.Sprintf("%s_%s", m.Version, m.Direction)
		appliedMap[key] = true
	}

	// Run migrations
	var results []models.MigrationStatus
	for _, file := range migrationFiles {
		version, name := parseFilename(file)
		key := fmt.Sprintf("%s_%s", version, direction)

		// If we have a target version and we've reached it, stop
		if targetVersion != "" {
			if direction == "up" && version > targetVersion {
				break
			}
			if direction == "down" && version < targetVersion {
				break
			}
		}

		// Skip if already applied
		if appliedMap[key] {
			results = append(results, models.MigrationStatus{
				Version:   version,
				Name:      name,
				Direction: direction,
				Applied:   true,
			})
			continue
		}

		// Run the migration
		if err := s.runMigration(file, version, name, direction); err != nil {
			return results, fmt.Errorf("failed to run migration %s: %w", file, err)
		}

		// Add to results
		results = append(results, models.MigrationStatus{
			Version:   version,
			Name:      name,
			Direction: direction,
			Applied:   true,
		})
	}

	return results, nil
}

// RunSpecificMigration runs a specific migration
func (s *MigrationService) RunSpecificMigration(version string, direction string) error {
	// Validate direction
	if direction != "up" && direction != "down" {
		return fmt.Errorf("invalid direction: %s", direction)
	}

	// Get all migration files in the specified direction
	migrationFiles, err := s.getMigrationFiles(direction)
	if err != nil {
		return err
	}

	// Find the migration file with the specified version
	var targetFile string
	for _, file := range migrationFiles {
		fileVersion, _ := parseFilename(file)
		if fileVersion == version {
			targetFile = file
			break
		}
	}

	if targetFile == "" {
		return fmt.Errorf("migration with version %s not found", version)
	}

	// Check if already applied
	var count int64
	s.DB.Model(&models.Migration{}).Where("version = ? AND direction = ?", version, direction).Count(&count)
	if count > 0 {
		return fmt.Errorf("migration %s already applied", version)
	}

	// Run the migration
	_, name := parseFilename(targetFile)
	return s.runMigration(targetFile, version, name, direction)
}

// getMigrationFiles returns all migration files in the specified direction
func (s *MigrationService) getMigrationFiles(direction string) ([]string, error) {
	dirPath := filepath.Join(s.MigrationsDir, direction)

	// Check if directory exists
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("migrations directory %s does not exist", dirPath)
	}

	// Read all files in the directory
	files, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read migrations directory: %w", err)
	}

	// Filter SQL files and sort by name
	var migrationFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".sql") {
			migrationFiles = append(migrationFiles, file.Name())
		}
	}

	return migrationFiles, nil
}

// runMigration executes a single migration file
func (s *MigrationService) runMigration(filename, version, name, direction string) error {
	// Read the migration file
	filePath := filepath.Join(s.MigrationsDir, direction, filename)
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read migration file: %w", err)
	}

	// Execute the migration within a transaction
	tx := s.DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Execute the SQL
	if err := tx.Exec(string(content)).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to execute migration: %w", err)
	}

	// Record the migration
	migration := models.Migration{
		Version:   version,
		Name:      name,
		Direction: direction,
		AppliedAt: time.Now(),
	}
	if err := tx.Create(&migration).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to record migration: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// parseFilename extracts version and name from a migration filename
// Example: 001_create_users_table.sql -> "001", "create_users_table"
func parseFilename(filename string) (version, name string) {
	// Remove .sql extension
	base := strings.TrimSuffix(filename, ".sql")

	// Split by underscore
	parts := strings.SplitN(base, "_", 2)
	if len(parts) < 2 {
		return base, ""
	}

	return parts[0], parts[1]
}
