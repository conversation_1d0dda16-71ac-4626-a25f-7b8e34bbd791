package router

import (
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/migration-service/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"gorm.io/gorm"
)

// LoadHandlers registers all API routes
func LoadHandlers(r gin.IRouter, db *gorm.DB) gin.IRouter {
	// Create handlers
	migrationHandler := handlers.NewMigrationHandler(db)

	// Set up middleware
	internal := middlewares.InternalServiceAuth()

	// Define API routes
	api := r.Group("v1/migration-service")
	{
		// Health check
		api.GET("health", migrationHandler.HealthCheck)

		// Migration endpoints (protected by internal auth)
		migrations := api.Group("migrations", internal)
		{
			// Get migration status
			migrations.GET("", migrationHandler.GetMigrationStatus)
			
			// Run migrations
			migrations.POST("", migrationHandler.RunMigrations)
			
			// Run specific migration
			migrations.POST(":version", migrationHandler.RunSpecificMigration)
		}
	}

	return r
}
