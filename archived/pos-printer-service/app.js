const axios = require('axios')
const fs = require('fs')
const { ThermalPrinter, PrinterTypes, CharacterSet, BreakLine } = require('node-thermal-printer')

const config = require("./config.json")

async function printReceiptForKitchen(doc) {
    const printer = new ThermalPrinter({
        type: "epson", // 'star' or 'epson'
        interface: config.printer,
        options: {
            timeout: 10000,
        },
        width: 48, // Number of characters in one line - default: 48
        characterSet: CharacterSet.SLOVENIA, // Character set - default: SLOVENIA
        breakLine: BreakLine.WORD, // Break line after WORD or CHARACTERS. Disabled with NONE - default: WORD
        removeSpecialCharacters: false, // Removes special characters - default: false
        lineCharacter: '-', // Use custom character for drawing lines - default: -
    });
    // printer.print('\x1B\x40');
    let isConnected = await printer.isPrinterConnected();

    const resp = await axios({
        url: doc.file_url,
        method: 'GET',
        responseType: 'arraybuffer',
    });

    fs.writeFileSync('image.png', Buffer.from(resp.data, 'binary'));

    await printer.printImage('image.png');
    printer.cut();
    await printer.execute();
}

(async () => {
    while (true) {
        try {
            const resp = await axios({
                url: `https://nexpos.io/api/sites/${config.site_id}/prints?printer_code=${config.printer_code}`,
                method: "get"
            })
            for (const doc of resp.data.data) {
                let update = {
                    status: 'printed',
                    error_message: 'success'
                }
                try {
                    await printReceiptForKitchen(doc)
                } catch (error) {
                    update.status = 'error'
                    update.error_message = error.message
                }
                await axios.put(`https://nexpos.io/api/sites/${config.site_id}/prints/${doc._id}?printer_code=${config.printer_code}`, update)
            }
        } catch (err) {
            console.log("Error...........................")
        }
        await new Promise(resolve => setTimeout(resolve, config.fetch_interval))
    }

})();

