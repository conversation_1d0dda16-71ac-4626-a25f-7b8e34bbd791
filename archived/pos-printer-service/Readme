# How to Install and Run the POS Printer Service

## 1. Download and Install NSSM

1. Go to the [NSSM download page](http://nssm.cc/download/?page=download).
2. Click the "Download NSSM" button.
3. Extract the NSSM archive to a location of your choice.
4. Add the path to the NSSM executable to your PATH environment variable.

## 2. Copy the POS Printer Service Folder

1. Download the [POS Printer Service](https://github.com/bard-ai/pos-printer-service) repository.
2. Copy the `pos-printer-service` folder to the root of your C: drive.

## 3. Edit the config.json file

1. Open the `config.json` file in a text editor.
2. Edit the `printer` and `site_id` properties to match your printer's IP address and site ID.

Example:

```
{
    "printer": "tcp://*************",
    "site_id": "642da80fb2964af7086ffaa3",
    "fetch_interval": 5000
}
```
## 4. Install Node.js

1. Go to the [Node.js download page](https://nodejs.org/en/download).
2. Click the "Download" button for the latest version of Node.js.<PERSON>
3. Run the installer.

## 5. Install the POS Printer Service

1. Open a command prompt window as an administrator.
2. Navigate to the `pos-printer-service` folder.
3. Run the following command:
```
nssm install pos-printer-service c:\Program Files\nodejs\node.exe C:\pos-printer-service\app.js
```
## 6. Start the POS Printer Service

1. Run the following command:
```
net start pos-printer-service
```

The POS Printer Service will now be installed and running on your computer. You can verify that the service is running by opening the Services Manager (services.msc) and looking for the "POS Printer Service" service. The service should be displayed as "Running".